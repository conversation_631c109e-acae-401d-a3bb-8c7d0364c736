stages:
  - build
  - deploy

variables:
  DOCKER_HOST: tcp://docker:2375/
  DOCKER_TLS_CERTDIR: ""

default:
  image: docker:latest
  services:
    - docker:dind

.default_build_template:
  script:
    - echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin "$CI_REGISTRY"
    - docker build -f $DOCKERFILE_PATH -t $IMAGE_TAG .
    - docker push $IMAGE_TAG

# pull_code:
#   stage: deploy
#   image: alpine:latest
#   needs:
#     - job: deploy_tls
#       optional: true  
#   variables:
#     DEPLOY_DIR: "/opt/app/tls"
#   rules:
#     - if: '$CI_COMMIT_BRANCH != "master"'
#       when: never
#     - changes:
#       - Dockerfile
#       - "app/**/*"
#       - "config/**/*"
#       - "database/**/*"
#       - "public/**/*"
#       - "resources/**/*"
#       - "routes/**/*"
#       - "storage/**/*"
#       - "tests/**/*"
#   before_script:
#     - apk add --no-cache openssh
#     - mkdir -p ~/.ssh
#     - echo "$SSH_PRIVATE_KEY" | base64 -d > ~/.ssh/id_ed25519
#     - chmod 600 ~/.ssh/id_ed25519
#     - echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
#   script:
#     - ssh -i ~/.ssh/id_ed25519 -o StrictHostKeyChecking=no -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" \
#       "cd $DEPLOY_DIR && echo \"$SSH_PASSWORD\" | sudo -S git config --global --add safe.directory $DEPLOY_DIR && echo \"$SSH_PASSWORD\" | sudo -S git pull origin master"

build_tls:
  extends: .default_build_template
  stage: build
  variables:
    DOCKERFILE_PATH: Dockerfile
    IMAGE_TAG: registry.gitlab.com/aronomeniaina/tls:latest
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - Dockerfile
      - "app/**/*"
      - "config/**/*"
      - "database/**/*"
      - "public/**/*"
      - "resources/**/*"
      - "routes/**/*"
      - "storage/**/*"
      - "tests/**/*"

deploy_tls:
  stage: deploy
  image: alpine:latest
  needs:
    - build_tls
  variables:
    DEPLOY_DIR: "/opt/app/tls"
  before_script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | base64 -d > ~/.ssh/id_ed25519
    - chmod 600 ~/.ssh/id_ed25519
    - echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
  script:
    - ssh -i ~/.ssh/id_ed25519 -o StrictHostKeyChecking=no -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" \
        "cd $DEPLOY_DIR && echo \"$SSH_PASSWORD\" | sudo -S docker compose up --pull always -d tls"
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - Dockerfile
      - "app/**/*"
      - "config/**/*"
      - "database/**/*"
      - "public/**/*"
      - "resources/**/*"
      - "routes/**/*"
      - "storage/**/*"
      - "tests/**/*"

build_appelle:
  extends: .default_build_template
  stage: build
  variables:
    DOCKERFILE_PATH: services/appelle/Dockerfile
    IMAGE_TAG: registry.gitlab.com/aronomeniaina/tls/appelle:latest
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/appelle/Dockerfile
      - export/appelle.js

deploy_appelle:
  stage: deploy
  image: alpine:latest
  needs:
    - build_appelle
  variables:
    DEPLOY_DIR: "/opt/app/tls"
  before_script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | base64 -d > ~/.ssh/id_ed25519
    - chmod 600 ~/.ssh/id_ed25519
    - echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
  script:
    - ssh -i ~/.ssh/id_ed25519 -o StrictHostKeyChecking=no -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" \
        "cd $DEPLOY_DIR && echo \"$SSH_PASSWORD\" | sudo -S docker compose up --pull always -d appelle"
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/appelle/Dockerfile
      - export/appelle.js

build_biometrique:
  extends: .default_build_template
  stage: build
  variables:
    DOCKERFILE_PATH: services/biometrique/Dockerfile
    IMAGE_TAG: registry.gitlab.com/aronomeniaina/tls/biometrique:latest
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
      when: never
    - changes:
      - services/biometrique/Dockerfile
      - export/biometrique.js

deploy_biometrique:
  stage: deploy
  image: alpine:latest
  needs:
    - build_biometrique
  variables:
    DEPLOY_DIR: "/opt/app/tls"
  before_script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | base64 -d > ~/.ssh/id_ed25519
    - chmod 600 ~/.ssh/id_ed25519
    - echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
  script:
    - ssh -i ~/.ssh/id_ed25519 -o StrictHostKeyChecking=no -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" \
        "cd $DEPLOY_DIR && echo \"$SSH_PASSWORD\" | sudo -S docker compose up --pull always -d biometrique"
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
      when: never
    - changes:
      - services/biometrique/Dockerfile
      - export/biometrique.js


build_bouton_province:
  extends: .default_build_template
  stage: build
  variables:
    DOCKERFILE_PATH: services/bouton_province/Dockerfile
    IMAGE_TAG: registry.gitlab.com/aronomeniaina/tls/bouton_province:latest
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/bouton_province/Dockerfile
      - export/bouton.js

deploy_bouton_province:
  stage: deploy
  image: alpine:latest
  needs:
    - build_bouton_province
  variables:
    DEPLOY_DIR: "/opt/app/tls"
  before_script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | base64 -d > ~/.ssh/id_ed25519
    - chmod 600 ~/.ssh/id_ed25519
    - echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
  script:
    - ssh -i ~/.ssh/id_ed25519 -o StrictHostKeyChecking=no -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" \
        "cd $DEPLOY_DIR && echo \"$SSH_PASSWORD\" | sudo -S docker compose up --pull always -d bouton_province"
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/bouton_province/Dockerfile
      - export/bouton.js

build_bouton_tana:
  extends: .default_build_template
  stage: build
  variables:
    DOCKERFILE_PATH: services/bouton_tana/Dockerfile
    IMAGE_TAG: registry.gitlab.com/aronomeniaina/tls/bouton_tana:latest
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/bouton_tana/Dockerfile
      - export/bouton.js

deploy_bouton_tana:
  stage: deploy
  image: alpine:latest
  needs:
    - build_bouton_tana
  variables:
    DEPLOY_DIR: "/opt/app/tls"
  before_script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | base64 -d > ~/.ssh/id_ed25519
    - chmod 600 ~/.ssh/id_ed25519
    - echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
  script:
    - ssh -i ~/.ssh/id_ed25519 -o StrictHostKeyChecking=no -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" \
        "cd $DEPLOY_DIR && echo \"$SSH_PASSWORD\" | sudo -S docker compose up --pull always -d bouton_tana"
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/bouton_tana/Dockerfile
      - export/bouton.js

build_delete_vigilance:
  extends: .default_build_template
  stage: build
  variables:
    DOCKERFILE_PATH: services/delete_vigilance/Dockerfile
    IMAGE_TAG: registry.gitlab.com/aronomeniaina/tls/delete_vigilance:latest
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/delete_vigilance/Dockerfile
      - delete_vigilance.php

deploy_delete_vigilance:
  stage: deploy
  image: alpine:latest
  needs:
    - build_delete_vigilance
  variables:
    DEPLOY_DIR: "/opt/app/tls"
  before_script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | base64 -d > ~/.ssh/id_ed25519
    - chmod 600 ~/.ssh/id_ed25519
    - echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
  script:
    - ssh -i ~/.ssh/id_ed25519 -o StrictHostKeyChecking=no -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" \
        "cd $DEPLOY_DIR && echo \"$SSH_PASSWORD\" | sudo -S docker compose up --pull always -d delete_vigilance"
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/delete_vigilance/Dockerfile
      - delete_vigilance.php

build_diag_biometrique:
  extends: .default_build_template
  stage: build
  variables:
    DOCKERFILE_PATH: services/diag_biometrique/Dockerfile
    IMAGE_TAG: registry.gitlab.com/aronomeniaina/tls/diag_biometrique:latest
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/diag_biometrique/Dockerfile
      - export/diag_biometrique.js

deploy_diag_biometrique:
  stage: deploy
  image: alpine:latest
  needs:
    - build_diag_biometrique
  variables:
    DEPLOY_DIR: "/opt/app/tls"
  before_script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | base64 -d > ~/.ssh/id_ed25519
    - chmod 600 ~/.ssh/id_ed25519
    - echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
  script:
    - ssh -i ~/.ssh/id_ed25519 -o StrictHostKeyChecking=no -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" \
        "cd $DEPLOY_DIR && echo \"$SSH_PASSWORD\" | sudo -S docker compose up --pull always -d diag_biometrique"
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/diag_biometrique/Dockerfile
      - export/diag_biometrique.js

build_diag_centrale:
  extends: .default_build_template
  stage: build
  variables:
    DOCKERFILE_PATH: services/diag_centrale/Dockerfile
    IMAGE_TAG: registry.gitlab.com/aronomeniaina/tls/diag_centrale:latest
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/diag_centrale/Dockerfile
      - export/diag_centrale.js

deploy_diag_centrale:
  stage: deploy
  image: alpine:latest
  needs:
    - build_diag_centrale
  variables:
    DEPLOY_DIR: "/opt/app/tls"
  before_script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | base64 -d > ~/.ssh/id_ed25519
    - chmod 600 ~/.ssh/id_ed25519
    - echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
  script:
    - ssh -i ~/.ssh/id_ed25519 -o StrictHostKeyChecking=no -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" \
        "cd $DEPLOY_DIR && echo \"$SSH_PASSWORD\" | sudo -S docker compose up --pull always -d diag_centrale"
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/diag_centrale/Dockerfile
      - export/diag_centrale.js

build_gprs_server:
  extends: .default_build_template
  stage: build
  variables:
    DOCKERFILE_PATH: services/gprs/Dockerfile
    IMAGE_TAG: registry.gitlab.com/aronomeniaina/tls/gprs:latest
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/gprs/Dockerfile
      - GprsServer.java

deploy_gprs_server:
  stage: deploy
  image: alpine:latest
  needs:
    - build_gprs_server
  variables:
    DEPLOY_DIR: "/opt/app/tls"
  before_script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | base64 -d > ~/.ssh/id_ed25519
    - chmod 600 ~/.ssh/id_ed25519
    - echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
  script:
    - ssh -i ~/.ssh/id_ed25519 -o StrictHostKeyChecking=no -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" \
        "cd $DEPLOY_DIR && echo \"$SSH_PASSWORD\" | sudo -S docker compose up --pull always -d gprs_server"
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/gprs/Dockerfile
      - GprsServer.java

build_manque_sanction:
  extends: .default_build_template
  stage: build
  variables:
    DOCKERFILE_PATH: services/manque_sanction/Dockerfile
    IMAGE_TAG: registry.gitlab.com/aronomeniaina/tls/manque_sanction:latest
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/manque_sanction/Dockerfile
      - export/sanction/manque.js

deploy_manque_sanction:
  stage: deploy
  image: alpine:latest
  needs:
    - build_manque_sanction
  variables:
    DEPLOY_DIR: "/opt/app/tls"
  before_script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | base64 -d > ~/.ssh/id_ed25519
    - chmod 600 ~/.ssh/id_ed25519
    - echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
  script:
    - ssh -i ~/.ssh/id_ed25519 -o StrictHostKeyChecking=no -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" \
        "cd $DEPLOY_DIR && echo \"$SSH_PASSWORD\" | sudo -S docker compose up --pull always -d manque_sanction"
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/manque_sanction/Dockerfile
      - export/sanction/manque.js

build_pointage_eclosia:
  extends: .default_build_template
  stage: build
  variables:
    DOCKERFILE_PATH: services/pointage_eclosia/Dockerfile
    IMAGE_TAG: registry.gitlab.com/aronomeniaina/tls/pointage_eclosia:latest
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/pointage_eclosia/Dockerfile
      - export/pointage_eclosia.js

deploy_pointage_eclosia:
  stage: deploy
  image: alpine:latest
  needs:
    - build_pointage_eclosia
  variables:
    DEPLOY_DIR: "/opt/app/tls"
  before_script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | base64 -d > ~/.ssh/id_ed25519
    - chmod 600 ~/.ssh/id_ed25519
    - echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
  script:
    - ssh -i ~/.ssh/id_ed25519 -o StrictHostKeyChecking=no -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" \
        "cd $DEPLOY_DIR && echo \"$SSH_PASSWORD\" | sudo -S docker compose up --pull always -d pointage_eclosia"
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/pointage_eclosia/Dockerfile
      - export/pointage_eclosia.js

build_pointage:
  extends: .default_build_template
  stage: build
  variables:
    DOCKERFILE_PATH: services/pointage/Dockerfile
    IMAGE_TAG: registry.gitlab.com/aronomeniaina/tls/pointage:latest
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/pointage/Dockerfile
      - export/pointage.js

deploy_pointage:
  stage: deploy
  image: alpine:latest
  needs:
    - build_pointage
  variables:
    DEPLOY_DIR: "/opt/app/tls"
  before_script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | base64 -d > ~/.ssh/id_ed25519
    - chmod 600 ~/.ssh/id_ed25519
    - echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
  script:
    - ssh -i ~/.ssh/id_ed25519 -o StrictHostKeyChecking=no -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" \
        "cd $DEPLOY_DIR && echo \"$SSH_PASSWORD\" | sudo -S docker compose up --pull always -d pointage"
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/pointage/Dockerfile
      - export/pointage.js

build_pointage_tmv:
  extends: .default_build_template
  stage: build
  variables:
    DOCKERFILE_PATH: services/pointage_tmv/Dockerfile
    IMAGE_TAG: registry.gitlab.com/aronomeniaina/tls/pointage_tmv:latest
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/pointage_tmv/Dockerfile
      - export/pointage_tmv.js

deploy_pointage_tmv:
  stage: deploy
  image: alpine:latest
  needs:
    - build_pointage_tmv
  variables:
    DEPLOY_DIR: "/opt/app/tls"
  before_script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | base64 -d > ~/.ssh/id_ed25519
    - chmod 600 ~/.ssh/id_ed25519
    - echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
  script:
    - ssh -i ~/.ssh/id_ed25519 -o StrictHostKeyChecking=no -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" \
        "cd $DEPLOY_DIR && echo \"$SSH_PASSWORD\" | sudo -S docker compose up --pull always -d pointage_tmv"
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/pointage_tmv/Dockerfile
      - export/pointage_tmv.js
        
build_rapport_mailing:
  extends: .default_build_template
  stage: build
  variables:
    DOCKERFILE_PATH: services/rapport_mailing/Dockerfile
    IMAGE_TAG: registry.gitlab.com/aronomeniaina/tls/rapport_mailing:latest
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/rapport_mailing/Dockerfile
      - export/rapport_mailing.js

deploy_rapport_mailing:
  stage: deploy
  image: alpine:latest
  needs:
    - build_rapport_mailing
  variables:
    DEPLOY_DIR: "/opt/app/tls"
  before_script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | base64 -d > ~/.ssh/id_ed25519
    - chmod 600 ~/.ssh/id_ed25519
    - echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
  script:
    - ssh -i ~/.ssh/id_ed25519 -o StrictHostKeyChecking=no -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" \
        "cd $DEPLOY_DIR && echo \"$SSH_PASSWORD\" | sudo -S docker compose up --pull always -d rapport_mailing"
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/rapport_mailing/Dockerfile
      - export/rapport_mailing.js

build_rapport:
  extends: .default_build_template
  stage: build
  variables:
    DOCKERFILE_PATH: services/rapport/Dockerfile
    IMAGE_TAG: registry.gitlab.com/aronomeniaina/tls/rapport:latest
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/rapport/Dockerfile
      - export/rapport.js

deploy_rapport:
  stage: deploy
  image: alpine:latest
  needs:
    - build_rapport
  variables:
    DEPLOY_DIR: "/opt/app/tls"
  before_script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | base64 -d > ~/.ssh/id_ed25519
    - chmod 600 ~/.ssh/id_ed25519
    - echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
  script:
    - ssh -i ~/.ssh/id_ed25519 -o StrictHostKeyChecking=no -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" \
        "cd $DEPLOY_DIR && echo \"$SSH_PASSWORD\" | sudo -S docker compose up --pull always -d rapport"
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/rapport/Dockerfile
      - export/rapport.js

build_rcv_biometrique:
  extends: .default_build_template
  stage: build
  variables:
    DOCKERFILE_PATH: services/srv_biometrique/rcv/biometrique/Dockerfile
    IMAGE_TAG: registry.gitlab.com/aronomeniaina/tls/rcv_biometrique:latest
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/srv_biometrique/rcv/biometrique/Dockerfile
      - rcv/biometrique.js

deploy_rcv_biometrique:
  stage: deploy
  image: alpine:latest
  needs:
    - build_rcv_biometrique
  variables:
    DEPLOY_DIR: "/opt/app/tls"
  before_script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | base64 -d > ~/.ssh/id_ed25519
    - chmod 600 ~/.ssh/id_ed25519
    - echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
  script:
    - ssh -i ~/.ssh/id_ed25519 -o StrictHostKeyChecking=no -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" \
        "cd $DEPLOY_DIR && echo \"$SSH_PASSWORD\" | sudo -S docker compose up --pull always -d rcv_biometrique"
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/srv_biometrique/rcv/biometrique/Dockerfile
      - rcv/biometrique.js

build_rcv_gprs:
  extends: .default_build_template
  stage: build
  variables:
    DOCKERFILE_PATH: services/srv_biometrique/rcv/gprs/Dockerfile
    IMAGE_TAG: registry.gitlab.com/aronomeniaina/tls/rcv_gprs:latest
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/srv_biometrique/rcv/gprs/Dockerfile
      - rcv/gprs.js

deploy_rcv_gprs:
  stage: deploy
  image: alpine:latest
  needs:
    - build_rcv_gprs
  variables:
    DEPLOY_DIR: "/opt/app/tls"
  before_script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | base64 -d > ~/.ssh/id_ed25519
    - chmod 600 ~/.ssh/id_ed25519
    - echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
  script:
    - ssh -i ~/.ssh/id_ed25519 -o StrictHostKeyChecking=no -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" \
        "cd $DEPLOY_DIR && echo \"$SSH_PASSWORD\" | sudo -S docker compose up --pull always -d rcv_gprs"
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/srv_biometrique/rcv/gprs/Dockerfile
      - rcv/gprs.js

build_********************:
  extends: .default_build_template
  stage: build
  variables:
    DOCKERFILE_PATH: services/srv_biometrique/rcv/biometrique_late/Dockerfile
    IMAGE_TAG: registry.gitlab.com/aronomeniaina/tls/********************:latest
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/srv_biometrique/rcv/biometrique_late/Dockerfile
      - rcv/biometrique_late.js

deploy_********************:
  stage: deploy
  image: alpine:latest
  needs:
    - build_********************
  variables:
    DEPLOY_DIR: "/opt/app/tls"
  before_script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | base64 -d > ~/.ssh/id_ed25519
    - chmod 600 ~/.ssh/id_ed25519
    - echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
  script:
    - ssh -i ~/.ssh/id_ed25519 -o StrictHostKeyChecking=no -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" \
        "cd $DEPLOY_DIR && echo \"$SSH_PASSWORD\" | sudo -S docker compose up --pull always -d ********************"
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/srv_biometrique/rcv/biometrique_late/Dockerfile
      - rcv/biometrique_late.js

build_rcv_test_biometrique:
  extends: .default_build_template
  stage: build
  variables:
    DOCKERFILE_PATH: services/srv_biometrique/rcv/biometrique_test/Dockerfile
    IMAGE_TAG: registry.gitlab.com/aronomeniaina/tls/rcv_test_biometrique:latest
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/srv_biometrique/rcv/biometrique_test/Dockerfile
      - rcv/biometrique_test.js

deploy_rcv_test_biometrique:
  stage: deploy
  image: alpine:latest
  needs:
    - build_rcv_test_biometrique
  variables:
    DEPLOY_DIR: "/opt/app/tls"
  before_script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | base64 -d > ~/.ssh/id_ed25519
    - chmod 600 ~/.ssh/id_ed25519
    - echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
  script:
    - ssh -i ~/.ssh/id_ed25519 -o StrictHostKeyChecking=no -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" \
        "cd $DEPLOY_DIR && echo \"$SSH_PASSWORD\" | sudo -S docker compose up --pull always -d rcv_test_biometrique"
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/srv_biometrique/rcv/biometrique_test/Dockerfile
      - rcv/biometrique_test.js

build_server_biometrique:
  extends: .default_build_template
  stage: build
  variables:
    DOCKERFILE_PATH: services/srv_biometrique/server/Dockerfile
    IMAGE_TAG: registry.gitlab.com/aronomeniaina/tls/server_biometrique:latest
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/srv_biometrique/server/Dockerfile
      - biometrique/server.js

deploy_server_biometrique:
  stage: deploy
  image: alpine:latest
  needs:
    - build_server_biometrique
  variables:
    DEPLOY_DIR: "/opt/app/tls"
  before_script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | base64 -d > ~/.ssh/id_ed25519
    - chmod 600 ~/.ssh/id_ed25519
    - echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
  script:
    - ssh -i ~/.ssh/id_ed25519 -o StrictHostKeyChecking=no -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" \
        "cd $DEPLOY_DIR && echo \"$SSH_PASSWORD\" | sudo -S docker compose up --pull always -d server_biometrique"
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/srv_biometrique/server/Dockerfile
      - biometrique/server.js

build_sync_a_rappeler:
  extends: .default_build_template
  stage: build
  variables:
    DOCKERFILE_PATH: services/sync_a_rappeler/Dockerfile
    IMAGE_TAG: registry.gitlab.com/aronomeniaina/tls/sync_a_rappeler:latest
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/sync_a_rappeler/Dockerfile
      - sync/cdr/sync_a_rappeler.js

deploy_sync_a_rappeler:
  stage: deploy
  image: alpine:latest
  needs:
    - build_sync_a_rappeler
  variables:
    DEPLOY_DIR: "/opt/app/tls"
  before_script:
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | base64 -d > ~/.ssh/id_ed25519
    - chmod 600 ~/.ssh/id_ed25519
    - echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
  script:
    - ssh -i ~/.ssh/id_ed25519 -o StrictHostKeyChecking=no -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" \
        "cd $DEPLOY_DIR && echo \"$SSH_PASSWORD\" | sudo -S docker compose up --pull always -d sync_a_rappeler"
  rules:
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: never
    - changes:
      - services/sync_a_rappeler/Dockerfile
      - sync/cdr/sync_a_rappeler.js
