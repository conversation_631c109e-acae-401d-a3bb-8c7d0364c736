<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="bce59834-f017-4b31-9179-b0f2e7b72d26" name="Default" comment="" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="TRACKING_ENABLED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileEditorManager">
    <leaf SIDE_TABS_SIZE_LIMIT_KEY="375">
      <file leaf-file-name="Log.php" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/app/Log.php">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="357">
              <caret line="22" column="63" lean-forward="false" selection-start-line="22" selection-start-column="59" selection-end-line="22" selection-end-column="63" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="Site.php" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/app/Site.php">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="168">
              <caret line="13" column="24" lean-forward="false" selection-start-line="13" selection-start-column="24" selection-end-line="13" selection-end-column="24" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="User.php" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/app/User.php">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="231">
              <caret line="13" column="36" lean-forward="false" selection-start-line="13" selection-start-column="24" selection-end-line="13" selection-end-column="36" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="UserController.php" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/app/Http/Controllers/UserController.php">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="273">
              <caret line="18" column="55" lean-forward="false" selection-start-line="18" selection-start-column="55" selection-end-line="18" selection-end-column="55" />
              <folding>
                <element signature="e#123#136#0#PHP" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="LogController.php" pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/app/Http/Controllers/LogController.php">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="483">
              <caret line="23" column="5" lean-forward="true" selection-start-line="23" selection-start-column="5" selection-end-line="23" selection-end-column="5" />
              <folding>
                <element signature="e#40#59#0#PHP" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="Historique.php" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/app/Historique.php">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="273">
              <caret line="18" column="58" lean-forward="false" selection-start-line="18" selection-start-column="58" selection-end-line="18" selection-end-column="58" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="Alarm.php" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/app/Alarm.php">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="105">
              <caret line="10" column="39" lean-forward="false" selection-start-line="10" selection-start-column="39" selection-end-line="10" selection-end-column="39" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="api.php" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/routes/api.php">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="504">
              <caret line="24" column="27" lean-forward="false" selection-start-line="24" selection-start-column="18" selection-end-line="24" selection-end-column="27" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="web.php" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/routes/web.php">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="0">
              <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FindInProjectRecents">
    <findStrings>
      <find>event</find>
      <find>istra</find>
      <find>codeTevent</find>
    </findStrings>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/app/Http/Log.php" />
        <option value="$PROJECT_DIR$/app/Event.php" />
        <option value="$PROJECT_DIR$/app/User.php" />
        <option value="$PROJECT_DIR$/app/Site.php" />
        <option value="$PROJECT_DIR$/app/Http/Controllers/UserController.php" />
        <option value="$PROJECT_DIR$/routes/api.php" />
        <option value="$PROJECT_DIR$/app/Log.php" />
        <option value="$PROJECT_DIR$/app/Historique.php" />
        <option value="$PROJECT_DIR$/app/Http/Controllers/LogController.php" />
      </list>
    </option>
  </component>
  <component name="JsBuildToolGruntFileManager" detection-done="true" sorting="DEFINITION_ORDER" />
  <component name="JsBuildToolPackageJson" detection-done="true" sorting="DEFINITION_ORDER">
    <package-json value="$PROJECT_DIR$/package.json" />
  </component>
  <component name="JsGulpfileManager">
    <detection-done>true</detection-done>
    <sorting>DEFINITION_ORDER</sorting>
  </component>
  <component name="NodeModulesDirectoryManager">
    <handled-path value="$PROJECT_DIR$/node_modules" />
  </component>
  <component name="PhpWorkspaceProjectConfiguration" backward_compatibility_performed="true" />
  <component name="ProjectFrameBounds" extendedState="6">
    <option name="x" value="-9" />
    <option name="y" value="-9" />
    <option name="width" value="1294" />
    <option name="height" value="689" />
  </component>
  <component name="ProjectInspectionProfilesVisibleTreeState">
    <entry key="Project Default">
      <profile-state>
        <expanded-state>
          <State>
            <id />
          </State>
        </expanded-state>
        <selected-state>
          <State>
            <id>AngularJS</id>
          </State>
        </selected-state>
      </profile-state>
    </entry>
  </component>
  <component name="ProjectView">
    <navigator currentView="ProjectPane" proportions="" version="1">
      <flattenPackages />
      <showMembers />
      <showModules />
      <showLibraryContents />
      <hideEmptyPackages />
      <abbreviatePackageNames />
      <autoscrollToSource />
      <autoscrollFromSource />
      <sortByType />
      <manualOrder />
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="tls" type="b2602c69:ProjectViewProjectNode" />
              <item name="tls" type="2a2b976b:PhpTreeStructureProvider$1" />
            </path>
            <path>
              <item name="tls" type="b2602c69:ProjectViewProjectNode" />
              <item name="tls" type="2a2b976b:PhpTreeStructureProvider$1" />
              <item name="app" type="2a2b976b:PhpTreeStructureProvider$1" />
            </path>
            <path>
              <item name="tls" type="b2602c69:ProjectViewProjectNode" />
              <item name="tls" type="2a2b976b:PhpTreeStructureProvider$1" />
              <item name="app" type="2a2b976b:PhpTreeStructureProvider$1" />
              <item name="Http" type="2a2b976b:PhpTreeStructureProvider$1" />
            </path>
            <path>
              <item name="tls" type="b2602c69:ProjectViewProjectNode" />
              <item name="tls" type="2a2b976b:PhpTreeStructureProvider$1" />
              <item name="app" type="2a2b976b:PhpTreeStructureProvider$1" />
              <item name="Http" type="2a2b976b:PhpTreeStructureProvider$1" />
              <item name="Controllers" type="2a2b976b:PhpTreeStructureProvider$1" />
            </path>
            <path>
              <item name="tls" type="b2602c69:ProjectViewProjectNode" />
              <item name="tls" type="2a2b976b:PhpTreeStructureProvider$1" />
              <item name="routes" type="2a2b976b:PhpTreeStructureProvider$1" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
      <pane id="Scope" />
      <pane id="Scratches" />
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="settings.editor.selected.configurable" value="preferences.sourceCode" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="nodejs_interpreter_path" value="C:/Program Files/nodejs/node" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\wamp64\www\tls\app" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\wamp64\www\tls\app" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="ShelveChangesManager" show_recycled="false">
    <option name="remove_strategy" value="false" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="bce59834-f017-4b31-9179-b0f2e7b72d26" name="Default" comment="" />
      <created>1589353659668</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1589353659668</updated>
      <workItem from="1589353661526" duration="5632000" />
      <workItem from="1589467800242" duration="2187000" />
      <workItem from="1589477806896" duration="66000" />
      <workItem from="1589523876345" duration="6898000" />
      <workItem from="1589607380158" duration="6482000" />
      <workItem from="1589786066455" duration="10567000" />
      <workItem from="1589891916758" duration="8010000" />
      <workItem from="1589951383694" duration="7973000" />
    </task>
    <servers />
  </component>
  <component name="TimeTrackingManager">
    <option name="totallyTimeSpent" value="47815000" />
  </component>
  <component name="ToolWindowManager">
    <frame x="-9" y="-9" width="1938" height="1060" extended-state="6" />
    <layout>
      <window_info id="Project" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="true" show_stripe_button="true" weight="0.21927084" sideWeight="0.5" order="3" side_tool="false" content_ui="combo" />
      <window_info id="TODO" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="9" side_tool="false" content_ui="tabs" />
      <window_info id="Event Log" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.32897604" sideWeight="0.5" order="0" side_tool="true" content_ui="tabs" />
      <window_info id="Database" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="0" side_tool="false" content_ui="tabs" />
      <window_info id="Version Control" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="npm" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="0" side_tool="true" content_ui="tabs" />
      <window_info id="Run" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="5" side_tool="false" content_ui="tabs" />
      <window_info id="Structure" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="2" side_tool="false" content_ui="tabs" />
      <window_info id="Terminal" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="2" side_tool="false" content_ui="tabs" />
      <window_info id="Favorites" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="1" side_tool="true" content_ui="tabs" />
      <window_info id="Debug" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="6" side_tool="false" content_ui="tabs" />
      <window_info id="Cvs" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="7" side_tool="false" content_ui="tabs" />
      <window_info id="Message" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="3" side_tool="false" content_ui="tabs" />
      <window_info id="Commander" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="Inspection" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="8" side_tool="false" content_ui="tabs" />
      <window_info id="Hierarchy" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="3" side_tool="false" content_ui="combo" />
      <window_info id="Find" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.32897604" sideWeight="0.5" order="4" side_tool="false" content_ui="tabs" />
      <window_info id="Ant Build" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="2" side_tool="false" content_ui="tabs" />
    </layout>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="VcsContentAnnotationSettings">
    <option name="myLimit" value="**********" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager />
    <watches-manager />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/routes/api.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="462">
          <caret line="22" column="60" lean-forward="false" selection-start-line="22" selection-start-column="60" selection-end-line="22" selection-end-column="60" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/routes/web.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Log.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="336">
          <caret line="21" column="25" lean-forward="false" selection-start-line="21" selection-start-column="20" selection-end-line="21" selection-end-column="25" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Site.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="168">
          <caret line="13" column="24" lean-forward="false" selection-start-line="13" selection-start-column="24" selection-end-line="13" selection-end-column="24" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/User.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="231">
          <caret line="13" column="36" lean-forward="false" selection-start-line="13" selection-start-column="24" selection-end-line="13" selection-end-column="36" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Http/Controllers/LogController.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="651">
          <caret line="33" column="5" lean-forward="true" selection-start-line="33" selection-start-column="5" selection-end-line="33" selection-end-column="5" />
          <folding>
            <element signature="e#40#59#0#PHP" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Historique.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="231">
          <caret line="16" column="10" lean-forward="false" selection-start-line="16" selection-start-column="4" selection-end-line="16" selection-end-column="10" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Alarm.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="105">
          <caret line="10" column="39" lean-forward="false" selection-start-line="10" selection-start-column="39" selection-end-line="10" selection-end-column="39" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/routes/api.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="420">
          <caret line="20" column="27" lean-forward="false" selection-start-line="20" selection-start-column="27" selection-end-line="20" selection-end-column="27" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/routes/web.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Http/Controllers/UserController.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="252">
          <caret line="18" column="58" lean-forward="true" selection-start-line="18" selection-start-column="58" selection-end-line="18" selection-end-column="58" />
          <folding>
            <element signature="e#123#136#0#PHP" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Log.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Site.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="168">
          <caret line="13" column="24" lean-forward="false" selection-start-line="13" selection-start-column="24" selection-end-line="13" selection-end-column="24" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/User.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="231">
          <caret line="13" column="36" lean-forward="false" selection-start-line="13" selection-start-column="24" selection-end-line="13" selection-end-column="36" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Http/Controllers/UserController.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="5" lean-forward="true" selection-start-line="0" selection-start-column="5" selection-end-line="0" selection-end-column="5" />
          <folding>
            <element signature="e#123#136#0#PHP" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Http/Controllers/LogController.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="189">
          <caret line="11" column="60" lean-forward="false" selection-start-line="11" selection-start-column="60" selection-end-line="11" selection-end-column="60" />
          <folding>
            <element signature="e#40#59#0#PHP" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Historique.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="231">
          <caret line="16" column="10" lean-forward="false" selection-start-line="16" selection-start-column="4" selection-end-line="16" selection-end-column="10" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Alarm.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="105">
          <caret line="10" column="39" lean-forward="false" selection-start-line="10" selection-start-column="39" selection-end-line="10" selection-end-column="39" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/routes/api.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="420">
          <caret line="20" column="44" lean-forward="false" selection-start-line="20" selection-start-column="44" selection-end-line="20" selection-end-column="44" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/routes/web.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Log.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="189">
          <caret line="14" column="1" lean-forward="false" selection-start-line="14" selection-start-column="1" selection-end-line="16" selection-end-column="38" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/User.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="231">
          <caret line="13" column="36" lean-forward="false" selection-start-line="13" selection-start-column="24" selection-end-line="13" selection-end-column="36" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Http/Controllers/LogController.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="294">
          <caret line="16" column="5" lean-forward="false" selection-start-line="2" selection-start-column="31" selection-end-line="16" selection-end-column="5" />
          <folding>
            <element signature="e#40#59#0#PHP" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Historique.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="231">
          <caret line="16" column="10" lean-forward="false" selection-start-line="16" selection-start-column="4" selection-end-line="16" selection-end-column="10" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Alarm.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="105">
          <caret line="10" column="39" lean-forward="false" selection-start-line="10" selection-start-column="39" selection-end-line="10" selection-end-column="39" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/routes/api.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="441">
          <caret line="21" column="27" lean-forward="false" selection-start-line="21" selection-start-column="27" selection-end-line="21" selection-end-column="27" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/routes/web.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Http/Controllers/UserController.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="252">
          <caret line="18" column="37" lean-forward="false" selection-start-line="18" selection-start-column="37" selection-end-line="18" selection-end-column="37" />
          <folding>
            <element signature="e#123#136#0#PHP" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Log.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/User.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="231">
          <caret line="13" column="36" lean-forward="false" selection-start-line="13" selection-start-column="24" selection-end-line="13" selection-end-column="36" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Http/Controllers/UserController.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="252">
          <caret line="18" column="64" lean-forward="false" selection-start-line="18" selection-start-column="64" selection-end-line="18" selection-end-column="64" />
          <folding>
            <element signature="e#123#136#0#PHP" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Http/Controllers/LogController.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="294">
          <caret line="16" column="5" lean-forward="true" selection-start-line="2" selection-start-column="31" selection-end-line="16" selection-end-column="5" />
          <folding>
            <element signature="e#40#59#0#PHP" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Historique.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="231">
          <caret line="16" column="10" lean-forward="false" selection-start-line="16" selection-start-column="4" selection-end-line="16" selection-end-column="10" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Alarm.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="105">
          <caret line="10" column="39" lean-forward="false" selection-start-line="10" selection-start-column="39" selection-end-line="10" selection-end-column="39" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/routes/api.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="441">
          <caret line="21" column="27" lean-forward="false" selection-start-line="21" selection-start-column="27" selection-end-line="21" selection-end-column="27" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/routes/web.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Log.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Http/Controllers/LogController.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="714">
          <caret line="36" column="24" lean-forward="false" selection-start-line="36" selection-start-column="24" selection-end-line="36" selection-end-column="24" />
          <folding>
            <element signature="e#40#59#0#PHP" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Historique.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="231">
          <caret line="16" column="10" lean-forward="false" selection-start-line="16" selection-start-column="4" selection-end-line="16" selection-end-column="10" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Alarm.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="105">
          <caret line="10" column="39" lean-forward="false" selection-start-line="10" selection-start-column="39" selection-end-line="10" selection-end-column="39" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/routes/api.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="420">
          <caret line="20" column="26" lean-forward="false" selection-start-line="20" selection-start-column="19" selection-end-line="20" selection-end-column="26" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/routes/web.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Alarm.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="105">
          <caret line="10" column="39" lean-forward="false" selection-start-line="10" selection-start-column="39" selection-end-line="10" selection-end-column="39" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/User.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="231">
          <caret line="13" column="36" lean-forward="false" selection-start-line="13" selection-start-column="24" selection-end-line="13" selection-end-column="36" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Site.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="168">
          <caret line="13" column="24" lean-forward="false" selection-start-line="13" selection-start-column="24" selection-end-line="13" selection-end-column="24" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/routes/web.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Log.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="357">
          <caret line="22" column="63" lean-forward="false" selection-start-line="22" selection-start-column="59" selection-end-line="22" selection-end-column="63" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Historique.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="273">
          <caret line="18" column="58" lean-forward="false" selection-start-line="18" selection-start-column="58" selection-end-line="18" selection-end-column="58" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Http/Controllers/UserController.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="273">
          <caret line="18" column="55" lean-forward="false" selection-start-line="18" selection-start-column="55" selection-end-line="18" selection-end-column="55" />
          <folding>
            <element signature="e#123#136#0#PHP" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/routes/api.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="504">
          <caret line="24" column="27" lean-forward="false" selection-start-line="24" selection-start-column="18" selection-end-line="24" selection-end-column="27" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/app/Http/Controllers/LogController.php">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="483">
          <caret line="23" column="5" lean-forward="true" selection-start-line="23" selection-start-column="5" selection-end-line="23" selection-end-column="5" />
          <folding>
            <element signature="e#40#59#0#PHP" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
  </component>
  <component name="masterDetails">
    <states>
      <state key="ScopeChooserConfigurable.UI">
        <settings>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
    </states>
  </component>
</project>