import java.net.*;
import java.io.*;
import java.util.regex.*;
import java.sql.*;
import java.util.Date;
import java.text.SimpleDateFormat;
import java.io.File;
import java.io.IOException;
import java.io.FileInputStream;
import java.util.Properties;

public class GprsServer {
    private ServerSocket serverSocket;

    public static String crc16(String s){
        int[] table = {
            0x0000, 0xC0C1, 0xC181, 0x0140, 0xC301, 0x03C0, 0x0280, 0xC241,
            0xC601, 0x06C0, 0x0780, 0xC741, 0x0500, 0xC5C1, 0xC481, 0x0440,
            0xCC01, 0x0CC0, 0x0D80, 0xCD41, 0x0F00, 0xCFC1, 0xCE81, 0x0E40,
            0x0A00, 0xCAC1, 0xCB81, 0x0B40, 0xC901, 0x09C0, 0x0880, 0xC841,
            0xD801, 0x18C0, 0x1980, 0xD941, 0x1B00, 0xDBC1, 0xDA81, 0x1A40,
            0x1E00, 0xDEC1, 0xDF81, 0x1F40, 0xDD01, 0x1DC0, 0x1C80, 0xDC41,
            0x1400, 0xD4C1, 0xD581, 0x1540, 0xD701, 0x17C0, 0x1680, 0xD641,
            0xD201, 0x12C0, 0x1380, 0xD341, 0x1100, 0xD1C1, 0xD081, 0x1040,
            0xF001, 0x30C0, 0x3180, 0xF141, 0x3300, 0xF3C1, 0xF281, 0x3240,
            0x3600, 0xF6C1, 0xF781, 0x3740, 0xF501, 0x35C0, 0x3480, 0xF441,
            0x3C00, 0xFCC1, 0xFD81, 0x3D40, 0xFF01, 0x3FC0, 0x3E80, 0xFE41,
            0xFA01, 0x3AC0, 0x3B80, 0xFB41, 0x3900, 0xF9C1, 0xF881, 0x3840,
            0x2800, 0xE8C1, 0xE981, 0x2940, 0xEB01, 0x2BC0, 0x2A80, 0xEA41,
            0xEE01, 0x2EC0, 0x2F80, 0xEF41, 0x2D00, 0xEDC1, 0xEC81, 0x2C40,
            0xE401, 0x24C0, 0x2580, 0xE541, 0x2700, 0xE7C1, 0xE681, 0x2640,
            0x2200, 0xE2C1, 0xE381, 0x2340, 0xE101, 0x21C0, 0x2080, 0xE041,
            0xA001, 0x60C0, 0x6180, 0xA141, 0x6300, 0xA3C1, 0xA281, 0x6240,
            0x6600, 0xA6C1, 0xA781, 0x6740, 0xA501, 0x65C0, 0x6480, 0xA441,
            0x6C00, 0xACC1, 0xAD81, 0x6D40, 0xAF01, 0x6FC0, 0x6E80, 0xAE41,
            0xAA01, 0x6AC0, 0x6B80, 0xAB41, 0x6900, 0xA9C1, 0xA881, 0x6840,
            0x7800, 0xB8C1, 0xB981, 0x7940, 0xBB01, 0x7BC0, 0x7A80, 0xBA41,
            0xBE01, 0x7EC0, 0x7F80, 0xBF41, 0x7D00, 0xBDC1, 0xBC81, 0x7C40,
            0xB401, 0x74C0, 0x7580, 0xB541, 0x7700, 0xB7C1, 0xB681, 0x7640,
            0x7200, 0xB2C1, 0xB381, 0x7340, 0xB101, 0x71C0, 0x7080, 0xB041,
            0x5000, 0x90C1, 0x9181, 0x5140, 0x9301, 0x53C0, 0x5280, 0x9241,
            0x9601, 0x56C0, 0x5780, 0x9741, 0x5500, 0x95C1, 0x9481, 0x5440,
            0x9C01, 0x5CC0, 0x5D80, 0x9D41, 0x5F00, 0x9FC1, 0x9E81, 0x5E40,
            0x5A00, 0x9AC1, 0x9B81, 0x5B40, 0x9901, 0x59C0, 0x5880, 0x9841,
            0x8801, 0x48C0, 0x4980, 0x8941, 0x4B00, 0x8BC1, 0x8A81, 0x4A40,
            0x4E00, 0x8EC1, 0x8F81, 0x4F40, 0x8D01, 0x4DC0, 0x4C80, 0x8C41,
            0x4400, 0x84C1, 0x8581, 0x4540, 0x8701, 0x47C0, 0x4680, 0x8641,
            0x8201, 0x42C0, 0x4380, 0x8341, 0x4100, 0x81C1, 0x8081, 0x4040,
        };
        byte[] bytes = s.getBytes();
        int crc = 0x0000;
        for (byte b : bytes) {
            crc = (crc >>> 8) ^ table[(crc ^ b) & 0xff];
        }
        String hex = Integer.toHexString(crc).toUpperCase();
        return ("000" + hex).substring(("000" + hex).length() - 4, ("000" + hex).length()) ;
    }

    public void start(int port) {
        try{
            Class.forName("com.mysql.jdbc.Driver"); 
            serverSocket = new ServerSocket(port);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            String fullpath = "logs/gprs/"+ Integer.toString(port) + "/" + dateFormat.format(new Date()) + ".log";
            File logFile = new File(fullpath);
            logFile.createNewFile();
            Properties config = new Properties();
            config.load(new FileInputStream("config.properties"));
            while (true)
                new EchoClientHandler(serverSocket.accept(), port, logFile, config).start();
        }
        catch (IOException e) {
            System.out.println("An error occurred on log file.");
            e.printStackTrace();
        }
        catch(Exception e){System.out.println(e);} 
    }
 
    public void stop() {
        try{
            serverSocket.close();
        }
        catch(Exception e){System.out.println(e);} 
    }

    public static boolean checkCRC(String msg){
        Pattern pattern = Pattern.compile("([A-Z0-9]{8})(\".+\"\\d+R0L0#\\d+\\[.*\\])");
        Matcher matcher = pattern.matcher(msg);
        if(matcher.matches()){
            String crcHead = matcher.group(1);
            String stringData = matcher.group(2);
            String lenHex = Integer.toHexString(stringData.length()).toUpperCase();
            String len = ("000" + lenHex).substring(("000" + lenHex).length() - 4, ("000" + lenHex).length());
            String crc = crc16(stringData);
            if(crcHead.equals(crc + len))
                return true;
        }
        return false;
    }

    public static String prepareAck(String seqId, String prom){
        String ackString = "\"ACK\"" + seqId + "R0L0#" + prom + "[]";
        String len = Integer.toHexString(ackString.length()).toUpperCase();
        String crc = crc16(ackString);
        String lenHex = ("000" + len).substring(("000" + len).length() - 4, ("000" + len).length());
        String res = "\n" + crc + lenHex + ackString + "\r";
	    return res;
    }

    public static String prepareNack(String seqId, String prom){
        String ackString = "\"NACK\"" + seqId + "R0L0#" + prom + "[]";
        String len = Integer.toHexString(ackString.length()).toUpperCase();
        String crc = crc16(ackString);
        String lenHex = ("000" + len).substring(("000" + len).length() - 4, ("000" + len).length());
        String res = "\n" + crc + lenHex + ackString + "\r";
	    return res;
    }
	
    private static class EchoClientHandler extends Thread {
        private Socket clientSocket;
        private PrintWriter out;
        private BufferedReader in;
        private int port;
        private File logFile;
		private Date lastDate;
        private Properties config;
 
        public EchoClientHandler(Socket socket, int port, File file, Properties config) {
            this.clientSocket = socket;
            this.port = port;
            this.logFile = file;
            this.config = config;
			this.lastDate = new Date();
        }

        public void tryToWrite(String line){
            try {
				FileWriter myWriter = new FileWriter("recovery/gprs.csv", true);
                myWriter.write(line+ "\n");
                myWriter.close();
                System.out.println("Successfully wrote to the file.");
            } catch (IOException e) {
                System.out.println("An error occurred.");
                e.printStackTrace();
                try{
                    Thread.sleep(100);
                    tryToWrite(line + "\n");
                }
                catch(InterruptedException ie){
                    ie.printStackTrace();
                }
            }
        }    

        public void run() {
			String sql = "INSERT INTO ademcomemlog" +
				"(prom, messageType, eventQualify, codeevent, ademcomemlog.partition, zones, " +
				"istraite, dtarrived, transmitter, port)" + 
				"VALUES (?, '18', ?, ?, ?, ?, 1, ?, '"+ config.getProperty("public_ip") +"', ?)";
			
			try {
				out = new PrintWriter(clientSocket.getOutputStream(), true);
				in = new BufferedReader(new InputStreamReader(clientSocket.getInputStream()));
			}
			catch(IOException ioe){
				ioe.printStackTrace();
			}

			String inputLine;
			boolean foundMessage = false;
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
			String dtarrived = dateFormat.format(new Date());
			String prom = new String();
			String eventQualify = new String();
			String codeevent = new String();
			String zones = new String();
			String partition = new String();
			
			try {
                while ((inputLine = in.readLine()) != null) {
                    try {
                        lastDate = new Date();
                        String data = inputLine.replace("\n", "").replace("\r", "");
                        System.out.println(data);
                        Pattern nullPattern = Pattern.compile("[A-Z0-9]+\"NULL\"(\\d+)R0L0#(\\d+)\\[\\]");
                        Matcher nullMatcher = nullPattern.matcher(data);
                        Pattern pattern = Pattern.compile("([A-Z0-9]+)\"ADM-CID\"(\\d+)R0L0#(\\d+)\\[#\\d+\\|(\\d{1})(\\d+) (\\d+) (\\d+)\\]");
                        Matcher matcher = pattern.matcher(data);
                        Pattern testPattern = Pattern.compile("communications test");
                        Matcher testMatcher = testPattern.matcher(data);
                        
                        String datelog = dateFormat.format(new Date());
                        if(port == 2512){
                            FileWriter logWriter = new FileWriter(logFile, true);
                            logWriter.write(datelog +" -> " + data + "\n");
                            logWriter.close();
                        }
                        if(nullMatcher.matches()){
                            System.out.println("nullmatcher");
                            String seqId = nullMatcher.group(1);
                            prom = nullMatcher.group(2);
                            if(checkCRC(data)){
                                String ackMessage = prepareAck(seqId, prom);
                                out.println(ackMessage);
                            }
                            else{
                                String nackMessage = prepareNack(seqId, prom);
                                out.println(nackMessage);
                            }

                        }
                        else if(matcher.matches()){
                            foundMessage = true;
                            System.out.println("-> " + datelog);
                            if(port != 2512){	
                                FileWriter logWriter = new FileWriter(logFile, true);
                                logWriter.write(datelog +" -> " + data + "\n");
                                logWriter.close();
                            }
                            
                            Date currentDate = new Date();
                            dtarrived = dateFormat.format(currentDate);
                            String crcHead = matcher.group(1);
                            String seqId = matcher.group(2);
                            prom = matcher.group(3);
                            eventQualify = matcher.group(4);
                            codeevent = matcher.group(5);
                            partition = matcher.group(6);
                            zones = matcher.group(7);
                        
                            if(checkCRC(data)){
                                String ackMessage = prepareAck(seqId, prom);
                                out.println(ackMessage);
                                if(!codeevent.equals("321")){
                                    try{
                                        Connection connection = DriverManager.getConnection("************************************************************************","tls","Srv$$OvH@tls2023");
                                        PreparedStatement stmt = connection.prepareStatement(sql);
                                        stmt.setString(1, prom);
                                        stmt.setString(2, eventQualify);
                                        stmt.setString(3, codeevent);
                                        stmt.setString(4, partition);
                                        stmt.setString(5, zones);
                                        stmt.setString(6, dtarrived);
                                        stmt.setInt(7, port);
                                        int rows = stmt.executeUpdate();
                                        System.out.println(prom + ",18," + eventQualify + "," + codeevent + "," + partition + "," + zones + ",1," + dtarrived + "," + config.getProperty("public_ip") +"," + Integer.toString(port));
                                        System.out.println("Rows inserted : " + rows );
                                        connection.close();
                                    }
                                    catch(SQLException e){
                                        e.printStackTrace();
                                        System.out.println("SQL ERROR X[");
                                        tryToWrite(prom + ",18," + eventQualify + "," + codeevent + "," + partition + "," + zones + ",1," + dtarrived + "," + config.getProperty("public_ip") +"," + Integer.toString(port));
                                    }
                                    catch(Exception e){
                                        e.printStackTrace();
                                        try{
                                            in.close();
                                            out.close();
                                            clientSocket.close();
                                        }
                                        catch(Exception ex){
                                            ex.printStackTrace();
                                        }
                                    }
                                }
                            }
                            else{
                                String nackMessage = prepareNack(seqId, prom);
                                out.println(nackMessage);
                            }
                        }
                        else if(testMatcher.matches()){
                            System.out.println("test transmission :)");
                            try{
                                Connection connection = DriverManager.getConnection("************************************************************************","tls","Srv$$OvH@tls2023");
                                PreparedStatement stmt = connection.prepareStatement(sql);    
                                Date currentDate = new Date();
                                dtarrived = dateFormat.format(currentDate);
                                stmt.setString(1, "0001");
                                stmt.setString(2, "1");
                                stmt.setString(3, "602");
                                stmt.setString(4, "0");
                                stmt.setString(5, "0");
                                stmt.setString(6, dtarrived);
                                stmt.setInt(7, port);
                                int rows = stmt.executeUpdate();
                                System.out.println("Rows inserted : " + rows );
                                connection.close();
                            }
                            catch(SQLException e){
                                e.printStackTrace();
                                System.out.println("SQL ERROR X[");
                                tryToWrite(data);
                            }
                            catch(Exception e){
                                e.printStackTrace();
                                try{
                                    in.close();
                                    out.close();
                                    clientSocket.close();
                                }
                                catch(Exception ex){
                                    ex.printStackTrace();
                                }
                            }
                        }
                        else if(data.length() > 0){
                            System.out.println("another response...................");
                        }
                        Thread.sleep(10);
                    }
                    catch(SocketException se){
                        System.out.println("ECONNRESET");
                        break;
                    }
                    catch (SocketTimeoutException ex) {
                        /* Test if this action has been cancelled */
                        System.out.println("CONNECTION TIMEOUT");
                    }
                    catch(Exception e){
                        try{
                            e.printStackTrace();
                            dtarrived = dateFormat.format(new Date());	
                            FileWriter logWriter = new FileWriter(logFile, true);
                            logWriter.write(dtarrived + " -> EXCEPTION ERROR\n");
                            logWriter.close();
                        }
                        catch(Exception ex){
                            ex.printStackTrace();
                        }
                    }
                }
			}
			catch(IOException ioe){
				System.out.println("ECONNRESET");
			}
        }
    }
    
    public static void main(String[] args) {
        try{
            GprsServer server=new GprsServer();
            int port = 2511;
            if(args.length > 0 && args[0] != null && args[0].trim() != "")
                port = Integer.parseInt(args[0].trim());
            server.start(port);
        }
        catch(Exception e){
            System.out.println(e);
        }
    }
}
