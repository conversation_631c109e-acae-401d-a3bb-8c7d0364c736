<?php

/**
 * Created by PhpStorm.
 * User: Dirsit
 * Date: 15/05/2020
 * Time: 11:04
 */

namespace App;


use Illuminate\Database\Eloquent\Model;

class Agent extends Model
{
    public  $timestamps = false;

    protected $attributes = [
        'nom',
        'ignore_name',
        'real_site_id',
        'fonction_id',
        'empreinte',
        'digit1',
        'digit2',
        'empreinte_optic',
        'digit3',
        'digit4',
        'pointeuse_id',
        'user_id',
        'last_date_pointage',
        'numero_stagiaire',
        'numero_employe',
        'num_emp_soit',
        'num_emp_saoi',
        'date_embauche',
        'date_confirmation',
        'date_conf_soit',
        'date_conf_saoi',
        'soft_delete',
        'observation',
        'date_sortie',
        'sal_forfait',
        'sal_base',
        'nb_heure_contrat',
        'nb_heure_convenu',
        'idm_depl',
        'perdiem',
        'part_variable',
        'prime_anc',
        'cin',
        'cv',
        'photo',
        'residence',
        'plan_reperage',
        'bulletin_n3',
        'bonne_conduite',
        'created_at',
        'last_update',
        'admin_updated_at',
        'synchronized_at',
    ];
    public function site()
    {
        return $this->belongsTo('App\Site', 'site_id');
    }
    public function societe()
    {
        return $this->belongsTo('App\Societe', 'societe_id');
    }
    public function agence()
    {
        return $this->belongsTo('App\Agence', 'agence_id');
    }
}
