<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SplitIntoNumeros extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'split:numeros';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Copy the value of the phones column inside of contacts into table numeros';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info("Starting to split phone numbers...");

        // Fetch all records from old_table
        DB::table('contacts')->get()->each(function ($record) {

            $normalizedData = str_replace('.', ',', $record->phone);

            // Split the phone numbers by comma
            $phoneNumbers = explode(',', $normalizedData);

            foreach ($phoneNumbers as $phone) {
                $trimmedPhone = trim($phone); // Remove spaces
                if (!empty($trimmedPhone) && preg_match('/^\d{7,15}$/', $trimmedPhone)) {
                    // Insert into new_table with reference to old_table_id
                    DB::table('numeros')->insert([
                        'id_contact' => $record->idContact,
                        'numero' => trim($phone) // Remove spaces
                    ]);
                }
            }
        });

        $this->info("Phone numbers split and inserted successfully!");
    }
}
