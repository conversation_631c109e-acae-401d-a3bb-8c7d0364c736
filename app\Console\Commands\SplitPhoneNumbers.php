<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SplitPhoneNumbers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'split:phone_agents';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Splits phone numbers from sites.phone_agent and inserts them into numeros';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info("Starting to split phone numbers...");

        // Fetch all records from old_table
        DB::table('sites')->get()->each(function ($record) {
            // Normalize data: Replace "." with ","
            $normalizedData = str_replace('.', ',', $record->phone_agent);

            // Split the phone numbers by ","
            $phoneNumbers = explode(',', $normalizedData);

            foreach ($phoneNumbers as $phone) {
                $trimmedPhone = trim($phone); // Remove spaces

                // Validate: Only insert if it's not empty and looks like a phone number
                if (!empty($trimmedPhone) && preg_match('/^\d{7,15}$/', $trimmedPhone)) {
                    DB::table('numeros')->insert([
                        'id_site' => $record->idsite,
                        'numero' => $trimmedPhone
                    ]);
                }
            }
        });

        $this->info("Phone numbers split and inserted successfully!");
    }
}
