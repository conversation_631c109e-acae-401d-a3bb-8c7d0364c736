<?php

namespace App;

use Illuminate\Database\Eloquent\Model;


class Contact extends Model
{
    protected $primaryKey = 'idContact';
    protected $table = 'contacts';
    protected $visible = ['idContact', 'nom', 'prenom', 'phone', 'adresse', 'phones', 'lastupdate'];
    protected $with = ['phones'];
    public  $timestamps = false;

    public function phones()
    {
        return $this->hasMany('App\Numero', 'id_contact', 'idContact');
    }
}
