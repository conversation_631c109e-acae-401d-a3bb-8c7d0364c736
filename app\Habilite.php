<?php

namespace App;

use Illuminate\Database\Eloquent\Model;


class Habilite extends Model
{
    protected $primaryKey = 'idhabilite';
    protected $visible = ['idhabilite', 'password', 'quality', 'idcontact', 'contact', 'idordre', 'lastupdate', 'soft_delete'];
    protected $with = ['contact'];
    public  $timestamps = false;

    public function contact()
    {
        return $this->belongsTo('App\Contact', 'idcontact', 'idContact');
    }
}
