<?php

namespace App;

use Illuminate\Database\Eloquent\Model;


class Historique extends Model
{
    protected $table = 'ademcolog';
    protected $primaryKey = 'idademco';
    public $timestamps = false;
    protected $fillable = [
        'prom', 'messageType', 'eventQualify', 'codeevent', 'partition', 'zones', 'istraite', 'dtarrived', 'dttraite',
        'transmitter', 'codeTevent', 'IdUser', 'lastUpdate', 'Lat', 'Lon', 'gps', 
        'agent_id', 'pointeuse_user_id', 'pointeuse_id', 'site_id', 'port'
    ];
    public function alarm(){
        return $this->belongsTo('App\Alarm', 'codeTevent', 'code');
    }
    public function site(){
        return $this->belongsTo('App\Site', 'prom', 'prom');
    }
}