<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
// use Illuminate\Database\Eloquent\SoftDeletes;

class HistoriquePointage extends Model
{
    protected $fillable = [
        'pointage_id',
        'user_id',
        'objet',
        'detail'
    ];

    public function pointage()
    {
        return $this->belongsTo('App\Pointage', 'pointage_id');
    }

    // use SoftDeletes;
    public function user()
    {
        return $this->belongsTo('App\User', 'user_id');
    }
}
