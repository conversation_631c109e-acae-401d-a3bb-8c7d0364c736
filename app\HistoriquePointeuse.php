<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
// use Illuminate\Database\Eloquent\SoftDeletes;

class HistoriquePointeuse extends Model
{
    protected $fillable = [
        'pointeuse_id',
        'user_id',
        'objet',
        'detail'
    ];
    public function pointeuse()
    {
        return $this->belongsTo('App\Pointeuse', 'pointeuse_id');
    }
    // use SoftDeletes;
    public function user()
    {
        return $this->belongsTo('App\User', 'user_id');
    }
}
