<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Rapport;
use App\TypeAction;
use App\Action;
use Validator;

class ActionController extends Controller {
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
    }

    public function show_by_rapport_id($rapport_id){
        return response()->json(DB::select("SELECT a.id, t.nom as 'action', a.created_at FROM actions a 
        LEFT JOIN type_actions t ON t.id = a.type_action_id
        WHERE (a.soft_delete is null or a.soft_delete = 0) and a.rapport_id = ?
        ORDER BY a.created_at DESC", [$rapport_id]));
    }

    public function store(Request $request){
        $rapport = Rapport::find($request->rapport_id);
        if($rapport != null && $request->authId == $rapport->user_id){
            $action = new Action();
            $action->rapport_id = $request->rapport_id;
            $action->type_action_id = $request->type_action_id;
            $action->created_at = new \DateTime;
            $action->updated_at = new \DateTime;
            return response()->json($action->save());
        }
        return response()->json(true);
    }

    public function delete($id, Request $request){
        $action = Action::find($id);
        $rapport = Rapport::find($action->rapport_id);
        if($request->authId == $rapport->user_id){
            $action->soft_delete = 1;
            return response()->json($action->save());
        }
        return response()->json(true);
    }
}