<?php

namespace App\Http\Controllers;

use App\Agent;
use App\HistoriqueAgent;
use App\Fonction;
use App\Agence;
use App\Pointage;
use Illuminate\Http\Request;
// use Validator;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Http\Util\PaieUtil;

class AgentController extends Controller
{
    private $attributeNames = array(
        'nom' => 'Nom',
        'societe_id' => 'Immatriculation',
        'fonction_id' => 'Fonction',
        'agence_id' => 'Agence',
        'date_embauche' => "Date d'embauche",
        'date_confirmation' => "Date de confirmation DGM",
        'date_conf_soit' => "Date de confirmation SOIT",
        'numero_employe' => "Num. employé DGM",
        'num_emp_soit' => "Num. employé SOIT",
        'numero_stagiaire' => "Num. stagiaire",
        'nb_heure_contrat' => "Nb d'heure contrat",
        'sal_base' => "Salaire de base",
        'idm_depl' => "Indemnité de déplacement",
        'part_variable' => "part_variable",
        'perdiem' => "perdiem",
    );

    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
    }
    public function getDayOrNightDate()
    {
        if (
            new \DateTime >= (new \DateTime)->setTime(06, 50, 0) &&
            new \DateTime < (new \DateTime)->setTime(17, 50, 0)
        )
            return (new \DateTime)->setTime(07, 0, 0)->format('Y-m-d H:i:s');
        else if (new \DateTime < (new \DateTime)->setTime(06, 50, 0))
            return (new \DateTime)->setTime(18, 0, 0)->sub(new \DateInterval('P1D'))->format('Y-m-d H:i:s');
        return (new \DateTime)->setTime(18, 0, 0)->format('Y-m-d H:i:s');
    }
    public function get_site($agent_id, Request $request)
    {
        $site = DB::select("SELECT s.idsite, s.nom FROM agents a
            LEFT JOIN sites s ON s.idsite = a.real_site_id
            where id = ?", [$agent_id])[0];
        $interval = PaieUtil::getIntervalByAgent($agent_id, 0, 0);
        if ($request->is_after_confirmable) {
            if ($request->reclamation)
                $begin = $interval['begin']->sub(new \DateInterval('P1M'));
            else
                $begin = $interval['begin'];
        } else {
            if ($request->reclamation)
                $begin = $interval['begin']->sub(new \DateInterval('P2M'));
            else
                $begin = $interval['begin']->sub(new \DateInterval('P1M'));
        }
        $default_date = $begin->format('Y-m-d');
        return response()->json(compact('site', 'default_date'));
    }

    public function index(Request $request)
    {
        if ($request->authRole != 'client') {
            $agents = DB::select("SELECT a.id, a.nom, a.numero_stagiaire, a.numero_employe, a.societe_id, s.nom as 'site',
                a.date_embauche, a.last_date_pointage, a.date_confirmation, a.date_conf_soit, a.num_emp_soit, a.created_at
                FROM agents a
                LEFT JOIN sites s on s.idsite = a.real_site_id
                WHERE (a.soft_delete is null or a.soft_delete = 0) and
                 (a.nom like '%" . $request->search . "%' or a.numero_stagiaire like '%" . $request->search . "%' or a.numero_employe like '%"
                . $request->search . "%' or a.num_emp_soit like '%" . $request->search . "%' )" .
                " ORDER BY a.last_update DESC limit " . $request->offset . ", 50");
            $fonctions = Fonction::select('id', 'libelle')->orderBy('libelle')->get();
            $agences = Agence::select('id', 'libelle')->orderBy('libelle')->get();
            return response()->json(compact('fonctions', 'agences', 'agents'));
        }
        return response()->json(false);
    }
    public function index_modal(Request $request)
    {
        $agents = DB::select("SELECT a.id, a.nom, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.societe_id,
            s.nom as 'site', y.id as 'pointage_id', a.empreinte, a.empreinte_optic, f.libelle as fonction
            FROM agents a
            LEFT JOIN sites s on s.idsite = a.site_id
            LEFT JOIN fonctions f on f.id = a.fonction_id
            LEFT JOIN (
                select p.id, p.agent_id from pointages p
                where date_pointage= ?
                group by p.agent_id
            ) y ON y.agent_id = a.id
            WHERE (a.nom like '%" . $request->search . "%' or a.numero_stagiaire like '%" . $request->search . "%' or a.numero_employe like '%"
            . $request->search . "%' or a.num_emp_soit like '%" . $request->search . "%' )" .
            " ORDER BY a.last_update DESC limit " . $request->offset . ", 50", [$this->getDayOrNightDate()]);
        $fonctions = Fonction::select('id', 'libelle')->orderBy('libelle')->get();
        $agences = Agence::select('id', 'libelle')->orderBy('libelle')->get();
        return response()->json(compact('fonctions', 'agences', 'agents'));
    }

    public function index_modal_sd(Request $request)
    {
        $agents = DB::select("SELECT a.id, a.nom, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.societe_id, a.soft_delete,
            s.nom as 'site', y.id as 'pointage_id', a.empreinte, a.empreinte_optic, f.libelle as fonction
            FROM agents a
            LEFT JOIN sites s on s.idsite = a.site_id
            LEFT JOIN fonctions f on f.id = a.fonction_id
            LEFT JOIN (
                select p.id, p.agent_id from pointages p
                where date_pointage= ?
                group by p.agent_id
            ) y ON y.agent_id = a.id
            WHERE
                (a.nom like '%" . $request->search . "%' or a.numero_stagiaire like '%" . $request->search . "%' or a.numero_employe like '%"
            . $request->search . "%' or a.num_emp_soit like '%" . $request->search . "%' )" .
            " ORDER BY a.last_update DESC limit " . $request->offset . ", 50", [$this->getDayOrNightDate()]);
        $fonctions = Fonction::select('id', 'libelle')->orderBy('libelle')->get();
        $agences = Agence::select('id', 'libelle')->orderBy('libelle')->get();
        return response()->json(compact('fonctions', 'agences', 'agents'));
    }

    public function index_pointeuse(Request $request)
    {
        if ($request->without_empreinte) {
            if ($request->optic) {
                $agents = DB::select("SELECT a.id, a.nom, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.societe_id, s.nom as 'site',
                    (a.digit3 is null) as 'optic_gauche', (a.digit4 is null) as 'optic_droite'
                    FROM agents a
                    LEFT JOIN sites s on s.idsite = a.site_id
                    WHERE (a.soft_delete is null or a.soft_delete = 0)
                    and (a.nom like '%" . $request->search . "%' or a.numero_stagiaire like '%" . $request->search . "%' or a.numero_employe like '%"
                    . $request->search . "%' or a.num_emp_soit like '%" . $request->search . "%' )" .
                    " ORDER BY a.last_update DESC limit " . $request->offset . ", 50");
            } else {
                $agents = DB::select("SELECT a.id, a.nom, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.societe_id, s.nom as 'site',
                    (a.digit1 is null) as 'capacitif_gauche', (a.digit2 is null) as 'capacitif_droite'
                    FROM agents a
                    LEFT JOIN sites s on s.idsite = a.site_id
                    WHERE (a.soft_delete is null or a.soft_delete = 0)
                    and (a.nom like '%" . $request->search . "%' or a.numero_stagiaire like '%" . $request->search . "%' or a.numero_employe like '%"
                    . $request->search . "%' or a.num_emp_soit like '%" . $request->search . "%' )" .
                    " ORDER BY a.last_update DESC limit " . $request->offset . ", 50");
            }
        } else {
            if ($request->optic) {
                $agents = DB::select("SELECT a.id, a.nom, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.societe_id, s.nom as 'site',
                    (a.digit3 is not null) as 'optic_gauche', (a.digit4 is not null) as 'optic_droite'
                    FROM agents a
                    LEFT JOIN sites s on s.idsite = a.site_id
                    WHERE (a.soft_delete is null or a.soft_delete = 0)
                    and a.empreinte_optic = 1
                    and (a.nom like '%" . $request->search . "%' or a.numero_stagiaire like '%" . $request->search . "%' or a.numero_employe like '%"
                    . $request->search . "%' or a.num_emp_soit like '%" . $request->search . "%' )" .
                    " ORDER BY a.last_update DESC limit " . $request->offset . ", 50");
            } else {
                $agents = DB::select("SELECT a.id, a.nom, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.societe_id, s.nom as 'site',
                    (a.digit1 is not null) as 'capacitif_gauche', (a.digit2 is not null) as 'capacitif_droite'
                    FROM agents a
                    LEFT JOIN sites s on s.idsite = a.site_id
                    WHERE (a.soft_delete is null or a.soft_delete = 0)
                    and a.empreinte = 1
                    and (a.nom like '%" . $request->search . "%' or a.numero_stagiaire like '%" . $request->search . "%' or a.numero_employe like '%"
                    . $request->search . "%' or a.num_emp_soit like '%" . $request->search . "%' )" .
                    " ORDER BY a.last_update DESC limit " . $request->offset . ", 50");
            }
        }
        return response()->json(compact('agents'));
    }

    public function list_enroll_agents()
    {
        $agents = DB::select("SELECT a.id, a.nom, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, s.nom as 'site', a.societe_id, a.empreinte, a.empreinte_optic
            FROM agents a
            LEFT JOIN sites s on s.idsite = a.site_id
            WHERE a.soft_delete is null or a.soft_delete = 0
            ORDER BY a.last_update DESC");
        $fonctions = Fonction::select('id', 'libelle')->get();
        return response()->json(compact('fonctions', 'agents'));
    }

    public function archive(Request $request)
    {
        if ($request->authRole != 'client') {
            $agents = DB::select("SELECT a.id, a.nom, a.numero_stagiaire, a.numero_employe, a.societe_id, a.observation,
                so.prefixe, a.date_embauche, a.last_date_pointage, a.date_confirmation, a.date_conf_soit, a.num_emp_soit
                FROM agents a
                LEFT JOIN societes so on so.id = a.societe_id
                WHERE a.soft_delete = 1 and
                 (a.nom like '%" . $request->search . "%' or a.numero_stagiaire like '%" . $request->search . "%' or a.numero_employe like '%"
                . $request->search . "%' or a.num_emp_soit like '%" . $request->search . "%' )" .
                " ORDER BY a.last_update DESC limit " . $request->offset . ", 50");
            return response()->json(compact('agents'));
        }
        return response()->json(false);
    }
    public function get_empreinte($id, Request $request)
    {
        if ($request->digit && $request->pointeuse_id) {
            $keys = DB::select(
                "SELECT id FROM agent_pointeuses where agent_id = ? and pointeuse_id = ? and digit = ?",
                [$id, $request->pointeuse_id, $request->digit]
            );
        }
        if (count($keys) == 0) {
            $agent = Agent::select("digit1", "digit2", "digit3", "digit4")->find($id);
            if ($agent)
                return response()->json($agent);
        }
        return response()->json(false);
    }
    public function show($id, Request $request)
    {
        if (in_array($request->authRole, ["op", "rh", "root"]))
            $agents = DB::select("SELECT a.id, a.nom, a.numero_stagiaire, a.numero_employe, a.societe_id, a.fonction_id, a.real_site_id,
                    a.observation, a.nb_heure_contrat, a.nb_heure_convenu, s.nom as 'site', COALESCE(GROUP_CONCAT(DISTINCT n.numero SEPARATOR ', '), '') AS phone_agent, a.empreinte, a.empreinte_optic,
                    so.prefixe, so.nom as 'societe', f.libelle as fonction,
                    a.date_embauche, a.date_confirmation, a.date_sortie, a.last_date_pointage, a.created_at,
                    a.date_conf_soit, a.num_emp_soit, a.agence_id, ac.libelle as 'agence', a.ignore_name,
                    a.cin, a.cv, a.photo, a.residence, a.plan_reperage, a.bulletin_n3, a.bonne_conduite,
                    a.sal_forfait, a.sal_base, a.idm_depl, a.perdiem, a.part_variable, a.prime_anc
                FROM agents a
                LEFT JOIN agences ac on ac.id = a.agence_id
                LEFT JOIN sites s on s.idsite = a.real_site_id
                LEFT JOIN fonctions f on f.id = a.fonction_id
                LEFT JOIN societes so on so.id = a.societe_id
                LEFT JOIN numeros n on s.idsite = n.id_site
                WHERE a.id = ? GROUP BY a.id", [$id]);
        else if ($request->authRole == 'room')
            $agents = DB::select("SELECT a.id, a.nom, a.numero_stagiaire, a.numero_employe, a.societe_id, a.fonction_id, a.real_site_id,
                    a.observation, a.nb_heure_contrat, a.nb_heure_convenu, s.nom as 'site', COALESCE(GROUP_CONCAT(DISTINCT n.numero SEPARATOR ', '), '') AS phone_agent, a.empreinte, a.empreinte_optic,
                    so.prefixe, so.nom as 'societe', f.libelle as fonction,
                    a.date_embauche, a.date_confirmation, a.date_sortie, a.last_date_pointage,
                    a.date_conf_soit, a.num_emp_soit, a.agence_id, ac.libelle as 'agence'
                FROM agents a
                LEFT JOIN agences ac on ac.id = a.agence_id
                LEFT JOIN sites s on s.idsite = a.real_site_id
                LEFT JOIN fonctions f on f.id = a.fonction_id
                LEFT JOIN societes so on so.id = a.societe_id
                LEFT JOIN numeros n on s.idsite = n.id_site
                WHERE a.id = ? GROUP BY a.id", [$id]);
        if ($agents != null) {
            return response()->json(['agent' => $agents[0]]);
        }
        return response()->json(false);
    }

    public function pointage($id, Request $request)
    {
        $interval = PaieUtil::getIntervalByAgent($id, $request->year, $request->month);
        if (!$interval)
            return response()->json(['message' => 'set_site']);

        $begin = $interval['begin'];
        $end = $interval['end'];

        $month = $end->format('n');
        $year = $end->format('Y');
        $begin = $begin->format("Y-m-d");
        $end = $end->format("Y-m-d");
        $pointages = DB::select("SELECT p.id, s.nom as 'site', p.date_pointage FROM pointages p
            LEFT JOIN sites s ON s.idsite = p.site_id
            WHERE (p.soft_delete is null or p.soft_delete = 0) and p.agent_id = ? and p.date_pointage > ? and p.date_pointage < ?
            ORDER BY p.date_pointage DESC", [$id, $begin . ' 00:00:00', $end . ' 23:59:00']);

        return response()->json(compact('month', 'year', 'begin', 'end', 'pointages'));
    }

    public function reset_digit($id, Request $request)
    {
        if (in_array($request->type_capteur, ["capacitif", "optic"])) {
            $agent = Agent::find($id);
            if ($request->type_capteur == "capacitif") {
                $agent->empreinte = null;
                $agent->digit1 = null;
                $agent->digit2 = null;
            } else if ($request->type_capteur == "optic") {
                $agent->empreinte_optic = null;
                $agent->digit3 = null;
                $agent->digit4 = null;
            }
            $agent->last_update = now();
            return response()->json($agent->save());
        }
    }

    public function store(Request $request)
    {
        if (in_array($request->authRole, ['root', 'rh'])) {
            $request->merge([
                'nom' => trim(preg_replace('!\s+!', ' ', $request->nom)),
            ]);
            $agent = new Agent();
            $validator = Validator::make($request->all(), [
                'nom' => ['required', Rule::unique('agents')->where(function ($query) use ($request) {
                    return $query->where('nom', 'like', $request->nom . '%');
                })],
                'societe_id' => ['required'],
                'agence_id' => ['required'],
                'fonction_id' => ['required'],
                'nb_heure_contrat' => ['required', 'integer'],
                'sal_base' => ['required', 'integer']
            ])->setAttributeNames($this->attributeNames);

            if ($validator->fails())
                return \response()->json(['error' => $validator->errors()]);
            else {
                if ($request->societe_id == 1) {
                    $validator = Validator::make($request->all(), [
                        'numero_employe' => ['required', 'integer', Rule::unique('agents')->where(function ($query) use ($request) {
                            return $query->where('societe_id', $request->societe_id)->where('numero_employe', $request->numero_employe);
                        })],
                        'numero_stagiaire' => ['integer'],
                        'num_emp_soit' => ['integer'],
                        'date_confirmation' => ['required'],
                    ])->setAttributeNames($this->attributeNames);
                } else if ($request->societe_id == 2) {
                    $validator = Validator::make($request->all(), [
                        'num_emp_soit' => ['required', 'integer', Rule::unique('agents')->where(function ($query) use ($request) {
                            return $query->where('societe_id', $request->societe_id)->where('num_emp_soit', $request->num_emp_soit);
                        })],
                        'numero_stagiaire' => ['integer'],
                        'numero_employe' => ['integer'],
                        'date_conf_soit' => ['required'],
                    ])->setAttributeNames($this->attributeNames);
                } else if ($request->societe_id == 3) {
                    $request->numero_employe = null;
                    $request->num_emp_soit = null;
                    $request->date_confirmation = null;
                    $request->date_conf_soit = null;
                    $validator = Validator::make($request->all(), [
                        'numero_stagiaire' => ['required', 'integer', Rule::unique('agents')->where(function ($query) use ($request) {
                            return $query->where('societe_id', $request->societe_id)->where('numero_stagiaire', $request->numero_stagiaire);
                        })],
                        'date_embauche' => ['required'],
                    ])->setAttributeNames($this->attributeNames);
                } else if ($request->societe_id == 4) {
                    $request->numero_employe = null;
                    $request->numero_stagiaire = null;
                    $request->num_emp_soit = null;
                    $request->date_embauche = null;
                    $request->date_confirmation = null;
                    $request->date_conf_soit = null;
                }

                if ($validator->fails()) {
                    return \response()->json(['error' => $validator->errors()]);
                } else {
                    $details = '';
                    $details = $details . 'Nom: ' . $request->nom . '\n';

                    $new_societe = $request->societe_id == 3 ? 'Stagiaire' : ($request->societe_id == 2 ? 'SOIT' : ($request->societe_id == 1 ? 'Dirickx Guard' : ($agent->societe_id == 4 ? 'Sans matricule' : '')));
                    $details = $details . 'Société: ' . $new_societe . '\n';

                    if ($request->numero_stagiaire != null && $request->numero_stagiaire != '')
                        $details = $details . 'Num. stagiaire: ' . $request->numero_stagiaire . '\n';
                    if ($request->num_emp_soit != null && $request->num_emp_soit != '')
                        $details = $details . 'Num. employe SOIT: ' . $request->num_emp_soit . '\n';
                    if ($request->numero_employe != null && $request->numero_employe != '')
                        $details = $details . 'Num. employe DGM: ' . $request->numero_employe . '\n';

                    $new_agence = Agence::find($request->agence_id);
                    if ($new_agence != null)
                        $details = $details . 'Agence: ' . $new_agence->libelle . '\n';

                    if ($request->date_embauche != null && $request->date_embauche != '')
                        $details = $details . 'Date embauche: ' . $request->date_embauche . '\n';
                    if ($request->sal_forfait != null && $request->sal_forfait != '')
                        $details = $details . 'Salaire forfaitisé: ' . ($request->sal_forfait == 1 ? 'Oui' : 'Non') . '\n';

                    $new_fonction = Fonction::find($request->fonction_id);
                    if ($new_fonction != null)
                        $details = $details . 'Fonction: ' . $new_fonction->libelle . '\n';

                    $details = $details . 'Salaire de base: ' . $request->sal_base . '\n';
                    $details = $details . 'Nb. heure contrat: ' . $request->nb_heure_contrat . '\n';
                    if ($request->nb_heure_convenu != null && $request->nb_heure_convenu != '')
                        $details = $details . 'Nb. heure convenu: ' . $request->nb_heure_convenu . '\n';
                    if ($request->part_variable != null && $request->part_variable != '')
                        $details = $details . 'Part variable: ' . $request->part_variable . '\n';
                    if ($request->perdiem != null && $request->perdiem != '')
                        $details = $details . 'Perdiem: ' . $request->perdiem . '\n';
                    if ($request->idm_depl != null && $request->idm_depl != '')
                        $details = $details . 'Indemnité de déplacement: ' . $request->idm_depl . '\n';
                    if ($request->prime_anc != null && $request->prime_anc != '')
                        $details = $details . 'Prime d\'ancienneté: ' . $request->prime_anc . '\n';
                    if ($request->cv == 1)
                        $details = $details . 'CV: Oui\n';
                    if ($request->cin == 1)
                        $details = $details . 'CIN: Oui\n';
                    if ($request->residence == 1)
                        $details = $details . 'Certificat de résidence: Oui\n';
                    if ($request->plan_reperage == 1)
                        $details = $details . 'Plan de repérage: Oui\n';
                    if ($request->bulletin_n3 == 1)
                        $details = $details . 'Bulletin n°3: Oui\n';
                    if ($request->bonne_conduite == 1)
                        $details = $details . 'Certificat de bonne conduite: Oui\n';

                    $agent->user_id = $request->authId;
                    $agent->nom = $request->nom;
                    $agent->fonction_id = $request->fonction_id;
                    $agent->agence_id = $request->agence_id;
                    $agent->sal_forfait = $request->sal_forfait;
                    $agent->sal_base = $request->sal_base;
                    $agent->nb_heure_contrat = $request->nb_heure_contrat;
                    $agent->nb_heure_convenu = $request->nb_heure_convenu;
                    $agent->societe_id = $request->societe_id;
                    $agent->numero_employe = $request->numero_employe;
                    $agent->num_emp_soit = $request->num_emp_soit;
                    $agent->numero_stagiaire = $request->numero_stagiaire;
                    if ($request->societe_id == 1) {
                        $agent->date_embauche = $request->date_confirmation;
                        $agent->date_confirmation = $request->date_confirmation;
                    } else if ($request->societe_id == 2) {
                        $agent->date_embauche = $request->date_conf_soit;
                        $agent->date_conf_soit = $request->date_conf_soit;
                    } else if ($request->societe_id == 3) {
                        $agent->date_embauche = $request->date_embauche;
                    }
                    $agent->cin = $request->cin;
                    $agent->cv = $request->cv;
                    $agent->photo = $request->photo;
                    $agent->residence = $request->residence;
                    $agent->plan_reperage = $request->plan_reperage;
                    $agent->bulletin_n3 = $request->bulletin_n3;
                    $agent->bonne_conduite = $request->bonne_conduite;
                    $agent->last_update = now();
                    $agent->created_at = now();
                    $agent->idm_depl = $request->idm_depl;
                    $agent->perdiem = $request->perdiem;
                    $agent->part_variable = $request->part_variable;
                    $agent->prime_anc = $request->prime_anc;
                    $agent->save();

                    if ($details != '') {
                        $historique_agent = new HistoriqueAgent();
                        $historique_agent->objet = "Nouveau agent";
                        $historique_agent->detail = $details;
                        $historique_agent->agent_id = $agent->id;
                        $historique_agent->user_id = $request->authId;
                        $historique_agent->created_at = now();
                        $historique_agent->updated_at = now();
                        $historique_agent->save();
                    }
                }
            }
            return response()->json($agent);
        }
    }

    public function update($id, Request $request)
    {
        if (in_array($request->authRole, ['root', 'rh'])) {
            $request->merge([
                'nom' => trim(preg_replace('!\s+!', ' ', $request->nom)),
            ]);
            $agent = Agent::find($id);

            if (!in_array($request->societe_id, [1, 2, 3, 4]))
                $request->societe_id = null;
            if ($agent->ignore_name)
                $validator = Validator::make($request->all(), [
                    'societe_id' => ['required'],
                    'agence_id' => ['required'],
                    'fonction_id' => ['required'],
                    'nb_heure_contrat' => ['required', 'integer'],
                    'sal_base' => ['required', 'integer'],
                ])->setAttributeNames($this->attributeNames);
            else
                $validator = Validator::make($request->all(), [
                    'nom' => ['required', Rule::unique('agents')->where(function ($query) use ($request, $id) {
                        return $query
                            ->where('nom', 'like', $request->nom . '%')
                            ->where('id', '<>', $id);
                    })],
                    'societe_id' => ['required'],
                    'agence_id' => ['required'],
                    'fonction_id' => ['required'],
                    'nb_heure_contrat' => ['required', 'integer'],
                    'sal_base' => ['required', 'integer'],
                ])->setAttributeNames($this->attributeNames);

            if ($validator->fails())
                return \response()->json(['error' => $validator->errors()]);
            else {
                if ($request->societe_id == 1) {
                    $validator = Validator::make($request->all(), [
                        'numero_employe' => ['required', 'integer', Rule::unique('agents')->where(function ($query) use ($request, $id) {
                            return $query->where('societe_id', $request->societe_id)->where('numero_employe', $request->numero_employe)->where('id', '<>', $id);
                        })],
                        'numero_stagiaire' => ['integer'],
                        'num_emp_soit' => ['integer'],
                        'date_confirmation' => ['required'],
                    ])->setAttributeNames($this->attributeNames);
                } else if ($request->societe_id == 2) {
                    $validator = Validator::make($request->all(), [
                        'num_emp_soit' => ['required', 'integer', Rule::unique('agents')->where(function ($query) use ($request, $id) {
                            return $query->where('societe_id', $request->societe_id)->where('num_emp_soit', $request->num_emp_soit)->where('id', '<>', $id);
                        })],
                        'numero_stagiaire' => ['integer'],
                        'numero_employe' => ['integer'],
                        'date_conf_soit' => ['required'],
                    ])->setAttributeNames($this->attributeNames);
                } else if ($request->societe_id == 3) {
                    $request->numero_employe = null;
                    $request->num_emp_soit = null;
                    $request->date_confirmation = null;
                    $request->date_conf_soit = null;
                    $validator = Validator::make($request->all(), [
                        'numero_stagiaire' => ['required', 'integer', Rule::unique('agents')->where(function ($query) use ($request, $id) {
                            return $query->where('societe_id', $request->societe_id)->where('numero_stagiaire', $request->numero_stagiaire)->where('id', '<>', $id);
                        })],
                        'date_embauche' => ['required'],
                    ])->setAttributeNames($this->attributeNames);
                } else if ($request->societe_id == 4) {
                    $request->numero_employe = null;
                    $request->numero_stagiaire = null;
                    $request->num_emp_soit = null;
                    $request->date_embauche = null;
                    $request->date_confirmation = null;
                    $request->date_conf_soit = null;
                }

                if ($validator->fails()) {
                    return \response()->json(['error' => $validator->errors()]);
                } else {
                    $details = '';
                    if ($agent->nom != $request->nom)
                        $details = $details . 'Nom: ' . $agent->nom . ' -> ' . $request->nom . '\n';
                    if ($agent->societe_id != $request->societe_id) {
                        $old_societe = $agent->societe_id == 3 ? 'Stagiaire' : ($agent->societe_id == 2 ? 'SOIT' : ($agent->societe_id == 1 ? 'Dirickx Guard' : ($agent->societe_id == 4 ? 'Sans matricule' : '')));
                        $new_societe = $request->societe_id == 3 ? 'Stagiaire' : ($request->societe_id == 2 ? 'SOIT' : ($request->societe_id == 1 ? 'Dirickx Guard' : ($request->societe_id == 4 ? 'Sans matricule' : '')));
                        $details = $details . 'Société: ' . $old_societe . ' -> ' . $new_societe . '\n';
                    }
                    if ($agent->numero_stagiaire != $request->numero_stagiaire)
                        $details = $details . 'Num. stagiaire: ' . $agent->numero_stagiaire . ' -> ' . $request->numero_stagiaire . '\n';
                    if ($agent->numero_employe != $request->numero_employe)
                        $details = $details . 'Num. employe DGM: ' . $agent->numero_employe . ' -> ' . $request->numero_employe . '\n';
                    if ($agent->num_emp_soit != $request->num_emp_soit)
                        $details = $details . 'Num. employe SOIT: ' . $agent->num_emp_soit . ' -> ' . $request->num_emp_soit . '\n';
                    if ($agent->agence_id != $request->agence_id) {
                        $old_agence = Agence::find($agent->agence_id);
                        $new_agence = Agence::find($request->agence_id);
                        if ($old_agence != null && $new_agence != null)
                            $details = $details . 'Agence: ' . $old_agence->libelle . ' -> ' . $new_agence->libelle . '\n';
                    }
                    if ($agent->date_embauche != $request->date_embauche)
                        $details = $details . 'Date embauche: ' . $agent->date_embauche . ' -> ' . $request->date_embauche . '\n';
                    if ($agent->sal_forfait != $request->sal_forfait)
                        $details = $details . 'Salaire forfaitisé: ' . ($agent->sal_forfait == 1 ? 'Oui' : 'Non') . ' -> ' . ($request->sal_forfait == 1 ? 'Oui' : 'Non') . '\n';
                    if ($agent->fonction_id != $request->fonction_id) {
                        $old_fonction = Fonction::find($agent->fonction_id);
                        $new_fonction = Fonction::find($request->fonction_id);
                        if ($old_fonction != null && $new_fonction != null)
                            $details = $details . 'Fonction: ' . $old_fonction->libelle . ' -> ' . $new_fonction->libelle . '\n';
                    }
                    if ($agent->sal_base != $request->sal_base)
                        $details = $details . 'Salaire de base: ' . $agent->sal_base . ' -> ' . $request->sal_base . '\n';
                    if ($agent->nb_heure_contrat != $request->nb_heure_contrat)
                        $details = $details . 'Nb. heure contrat: ' . $agent->nb_heure_contrat . ' -> ' . $request->nb_heure_contrat . '\n';
                    if ($agent->nb_heure_convenu != $request->nb_heure_convenu)
                        $details = $details . 'Nb. heure contrat: ' . $agent->nb_heure_convenu . ' -> ' . $request->nb_heure_convenu . '\n';
                    if ($agent->part_variable != $request->part_variable)
                        $details = $details . 'Part variable: ' . $agent->part_variable . ' -> ' . $request->part_variable . '\n';
                    if ($agent->perdiem != $request->perdiem)
                        $details = $details . 'Perdiem: ' . $agent->perdiem . ' -> ' . $request->perdiem . '\n';
                    if ($agent->idm_depl != $request->idm_depl)
                        $details = $details . 'Indemnité de déplacement: ' . $agent->idm_depl . ' -> ' . $request->idm_depl . '\n';
                    if ($agent->prime_anc != $request->prime_anc)
                        $details = $details . 'Prime d\'ancienneté: ' . $agent->prime_anc . ' -> ' . $request->prime_anc . '\n';
                    if ($agent->cv != $request->cv)
                        $details = $details . 'CV: ' . ($request->cv == 1 ? 'Oui' : 'Non') . '\n';
                    if ($agent->cin != $request->cin)
                        $details = $details . 'CIN: ' . ($request->cin == 1 ? 'Oui' : 'Non') . '\n';
                    if ($agent->residence != $request->residence)
                        $details = $details . 'Certificat de résidence: ' . ($request->residence == 1 ? 'Oui' : 'Non') . '\n';
                    if ($agent->plan_reperage != $request->plan_reperage)
                        $details = $details . 'Plan de repérage: ' . ($request->plan_reperage == 1 ? 'Oui' : 'Non') . '\n';
                    if ($agent->bulletin_n3 != $request->bulletin_n3)
                        $details = $details . 'Bulletin n°3: ' . ($request->bulletin_n3 == 1 ? 'Oui' : 'Non') . '\n';
                    if ($agent->bonne_conduite != $request->bonne_conduite)
                        $details = $details . 'Certificat de bonne conduite: ' . ($request->bonne_conduite == 1 ? 'Oui' : 'Non') . '\n';

                    if ($details != '') {
                        $historique_agent = new HistoriqueAgent();
                        $historique_agent->objet = "Modification de l'agent";
                        $historique_agent->detail = $details;
                        $historique_agent->agent_id = $id;
                        $historique_agent->user_id = $request->authId;
                        $historique_agent->created_at = now();
                        $historique_agent->updated_at = now();
                        $historique_agent->save();
                    }

                    if (!$agent->ignore_name)
                        $agent->nom = $request->nom;
                    $agent->fonction_id = $request->fonction_id;
                    $agent->societe_id = $request->societe_id;
                    $agent->agence_id = $request->agence_id;
                    $agent->sal_forfait = $request->sal_forfait;
                    $agent->sal_base = $request->sal_base;
                    $agent->nb_heure_contrat = $request->nb_heure_contrat;
                    $agent->nb_heure_convenu = $request->nb_heure_convenu;
                    $agent->numero_employe = $request->numero_employe;
                    $agent->num_emp_soit = $request->num_emp_soit;
                    $agent->numero_stagiaire = $request->numero_stagiaire;
                    $agent->date_embauche = $request->date_embauche;
                    $agent->date_confirmation = $request->date_confirmation;
                    $agent->date_conf_soit = $request->date_conf_soit;
                    $agent->cin = $request->cin;
                    $agent->cv = $request->cv;
                    $agent->photo = $request->photo;
                    $agent->residence = $request->residence;
                    $agent->plan_reperage = $request->plan_reperage;
                    $agent->bulletin_n3 = $request->bulletin_n3;
                    $agent->bonne_conduite = $request->bonne_conduite;
                    $agent->last_update = now();
                    $agent->idm_depl = $request->idm_depl;
                    $agent->perdiem = $request->perdiem;
                    $agent->part_variable = $request->part_variable;
                    $agent->prime_anc = $request->prime_anc;
                    $agent->save();
                }
            }
        }
        return response()->json($agent);
    }
    public function change_real_site($agent_id, Request $request)
    {
        if (in_array($request->authId, [1, 7, 39]))
            return response()->json(Agent::where('id', $agent_id)->update(['real_site_id' => $request->site_id]));
        return response()->json(false);
    }
    public function change_site(Request $request)
    {
        $agent_ids = array_column(
            DB::select("SELECT a.id FROM agents a
                LEFT JOIN (
                    select p.id, p.agent_id from pointages p
                    where date_pointage = ? group by p.agent_id
                ) y ON y.agent_id = a.id
                WHERE y.id is null and a.id in (" . implode(', ', $request->agent_ids) . ")", [$this->getDayOrNightDate()]),
            'id'
        );
        $updateOk = Agent::whereIn('id', $agent_ids)
            ->update(['site_id' => $request->site_id]);
        return response()->json($updateOk);
    }
    public function soft_delete($id, Request $request)
    {
        if (in_array($request->authRole, ['root', 'rh'])) {
            $agent = Agent::find($id);
            $agent->soft_delete = 1;
            $agent->observation = $request->observation;
            $agent->date_sortie = $request->date_sortie;
            $agent->last_update = now();

            $historique_agent = new HistoriqueAgent();
            $historique_agent->objet = "Mise en archive de l'agent";
            $historique_agent->detail = $request->observation;
            $historique_agent->agent_id = $id;
            $historique_agent->user_id = $request->authId;
            $historique_agent->created_at = now();
            $historique_agent->updated_at = now();
            $historique_agent->save();

            return response()->json($agent->save());
        }
    }

    public function delete($id)
    {
        if (in_array($request->authRole, ['root', 'rh'])) {
            $agent = Agent::find($id);
            HistoriqueAgent::where('agent_id', $id)->delete();
            return response()->json($agent->delete());
        }
    }
    public function digit_to_new_agent(Request $request)
    {
        if ($request->nom && $request->digit1 && $request->digit2) {
            $agent = new Agent();
            $agent->nom = $request->nom;
            $agent->numero_employe = $request->numero_employe;
            $agent->numero_stagiaire = $request->numero_stagiaire;
            //$agent->site_id = $request->site_id;
            $agent->empreinte = true;
            $agent->digit1 = $agent->digit1;
            $agent->digit2 = $agent->digit2;
            //$agent->last_agent_id = $request->agent_id;
            $agent->last_update = now();
            return response()->json($agent->save());
        }
        return response()->json(false);
    }
    public function digit_to_existing_agent($id, Request $request)
    {
        if ($request->digit1 && $request->digit2) {
            $agent = Agent::find($id);
            $agent->empreinte = true;
            $agent->digit1 = $request->digit1;
            $agent->digit2 = $request->digit2;
            $agent->last_update = now();
            return response()->json($agent->save());
        }
        return response()->json(false);
    }
    public function digit_to_optic_agent($id, Request $request)
    {
        if ($request->digit3 && $request->digit4) {
            $agent = Agent::find($id);
            $agent->empreinte_optic = true;
            $agent->digit3 = $request->digit3;
            $agent->digit4 = $request->digit4;
            $agent->last_update = now();
            return response()->json($agent->save());
        }
        return response()->json(false);
    }
    public function operation($agent_id, $begin, $end)
    {
        $historiques = HistoriqueAgent::with('user')
            ->where('agent_id', $agent_id)
            ->where('created_at', '>', $begin . ' 00:00:00')
            ->where('created_at', '<', $end . ' 23:00:00')
            ->orderBy('created_at', 'desc')
            ->get();
        return response()->json($historiques);
    }
    public function notification()
    {
        $agents = DB::select("SELECT id, nom, last_date_pointage,
            (date_embauche is not null and TIMESTAMPDIFF(MONTH, date_embauche, now()) > 24) as confirmation,
            (last_date_pointage is null or TIMESTAMPDIFF(DAY, last_date_pointage, now()) > 30) as inactif
            FROM agents WHERE
            (soft_delete is null or soft_delete = 0) and
            (created_at is null or TIMESTAMPDIFF(DAY, created_at, now()) > 30) and
            (
                (
                    societe_id is not null and
                    date_embauche is not null and
                    numero_employe is null and
                    num_emp_soit is null and
                    TIMESTAMPDIFF(MONTH, date_embauche, now()) > 24
                )
                or
                (
                    last_date_pointage is null or
                    TIMESTAMPDIFF(DAY, last_date_pointage, now()) > 30
                )
            )
            LIMIT 10");
        return response()->json(['now' => (new \DateTime())->format('Y-m-d'), 'agents' => $agents]);
    }
    public function new_num_stg()
    {
        $agent = DB::select("SELECT max(numero_stagiaire) as new from agents where societe_id = 3");
        return response()->json($agent[0]->new + 1);
    }
    public function new_num_emp_soit()
    {
        $agent_soit = DB::select("SELECT max(num_emp_soit) as new from agents where societe_id = 2");
        return response()->json($agent_soit[0]->new + 1);
    }
    public function new_num_emp_dgm()
    {
        $agent_dgm = DB::select("SELECT max(numero_employe) as new from agents where societe_id = 1");
        return response()->json($agent_dgm[0]->new + 1);
    }
    public function restore($id, Request $request)
    {
        $updateOk = Agent::where('id', $id)->update([
            'soft_delete' => null,
            'last_update' => now()
        ]);
        $historique = new HistoriqueAgent();
        $historique->agent_id = $id;
        $historique->user_id = $request->authId;
        $historique->objet = "Restauration de l'agent";
        $historique->save();
        return response()->json($updateOk);
    }
}
