<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AppelleController extends Controller
{
    public function __construct()
    {
        ini_set('max_execution_time', 300);
        date_default_timezone_set("Indian/Antananarivo");
    }
    public function getDayOrNightDate()
    {
        if (
            new \DateTime >= (new \DateTime)->setTime(06, 50, 0) &&
            new \DateTime < (new \DateTime)->setTime(17, 50, 0)
        )
            return (new \DateTime)->setTime(07, 0, 0)->format('Y-m-d H:i:s');
        else if (new \DateTime < (new \DateTime)->setTime(06, 50, 0))
            return (new \DateTime)->setTime(18, 0, 0)->sub(new \DateInterval('P1D'))->format('Y-m-d H:i:s');
        return (new \DateTime)->setTime(18, 0, 0)->format('Y-m-d H:i:s');
    }

    public function getAppelleCompact($uniqueids){
        $cdr = DB::connection("mysql2")->select("SELECT CONCAT(c.uniqueid, '') as 'uniqueid', c.datetime, c.src, c.dst, c.duration, c.calltype, c.disposition
                FROM cdr c WHERE c.uniqueid in (". implode(',' , array_column($uniqueids, "id")) .") ORDER BY c.datetime DESC");
            $appelles = [];
            $puces = [];
            foreach ($cdr as $c) {
                if(!isset($appelles[$c->uniqueid])){
                    $appelles[$c->uniqueid] = [];
                    $appelles[$c->uniqueid] = [
                        "uniqueid" => $c->uniqueid,
                        "calltype" => $c->calltype,
                        "cdr" => []
                    ];
                    if($c->calltype == "Inbound"){
                        if(preg_match('/^\+261\d{9}$/', $c->src)){
                            $appelles[$c->uniqueid]["numero"] = "0" . substr($c->src, 4, 9);
                            $puces[] = $appelles[$c->uniqueid]["numero"];
                        }
                        else if(preg_match('/^\d{9}$/', $c->src)){
                            $appelles[$c->uniqueid]["numero"] = "0" . $c->src;
                            $puces[] = $appelles[$c->uniqueid]["numero"];
                        }
                        else if(preg_match('/^\d{10}$/', $c->src)){
                            $appelles[$c->uniqueid]["numero"] = $c->src;
                            $puces[] = $appelles[$c->uniqueid]["numero"];
                        }
                        else
                            $appelles[$c->uniqueid]["numero"] = $c->src;
                    }
                    else if($c->calltype == "Outbound"){
                        $appelles[$c->uniqueid]["numero"] = $c->dst;
                        $puces[] = $appelles[$c->uniqueid]["numero"];
                    }
                    else 
                        $appelles[$c->uniqueid]["numero"] = $c->dst . " -> " . $c->src;
                }
                $appelles[$c->uniqueid]["cdr"][] = [
                    "uniqueid" => $c->uniqueid,
                    "datetime" => $c->datetime,
                    "src" => $c->src,
                    "dst" => $c->dst,
                    "disposition" => $c->disposition
                ];
            }

            foreach ($appelles as $key => $a) {
                $dispositions = array_column($appelles[$key]['cdr'], 'disposition');
                if(in_array('ANSWERED', $dispositions))
                    $appelles[$key]['disposition'] = "ANSWERED";
                else if(in_array('NO ANSWER', $dispositions))
                    $appelles[$key]['disposition'] = "NO ANSWER";
                else if(in_array('BUSY', $dispositions))
                    $appelles[$key]['disposition'] = "BUSY";
                else
                    $appelles[$key]['disposition'] = $dispositions[0];
            }
            
            $admin_users = DB::select("SELECT `name`, email, flotte FROM admin_users WHERE flotte in (". implode(',' , $puces) . ")");

            $numeros = DB::select("SELECT n.numero, n.id_site, n.id_contact FROM numeros n
                WHERE numero in (". implode(',' , $puces) .")");
            $contact_ids = [];
            $site_ids = [];
            foreach ($numeros as $n) {
                if($n->id_contact)
                    $contact_ids[] = $n->id_contact;
                else if($n->id_site)
                    $site_ids[] = $n->id_site;
            }
            $habilites = [];
            $sites = [];
            if(count($contact_ids) > 0)
                $habilites = DB::select("SELECT c.idcontact, c.nom, c.prenom, s.nom as 'site' FROM contacts c
                    LEFT JOIN habilites h ON c.idContact = h.idcontact
                    LEFT JOIN sites s ON s.idsite = h.idsite
                    WHERE c.idcontact in (" . implode(',' , $contact_ids) . ")");
            if(count($site_ids) > 0)
                $sites = DB::select("SELECT s.idsite, s.nom FROM sites s WHERE s.idsite in (". implode(',' , $site_ids) .")");
            
            foreach ($appelles as $key => $a) {
                if ((isset($a['numero']))) {
                    foreach ($numeros as $n) {
                        $contactSet = false;
                        foreach ($habilites as $h) {
                            if($h->idcontact == $n->id_contact && 
                                $a['numero'] == $n->numero){
                                $appelles[$key]['client'] = $h->nom . " " . $h->prenom;
                                $appelles[$key]['site'] = $h->site;
                                $appelles[$key]['contacttype'] = "client";
                                $contactSet = true;
                                break;
                            }
                        }
                        if(!$contactSet)
                            foreach ($admin_users as $u) {
                                if($a['numero'] == $u->flotte){
                                    $appelles[$key]['name'] = $u->name;
                                    $appelles[$key]['email'] = $u->email;
                                    $appelles[$key]['contacttype'] = "user";
                                    $contactSet = true;
                                    break;
                                }
                            }
                        if(!$contactSet)
                            foreach ($sites as $s) {
                                if($s->idsite == $n->id_site && $a['numero'] == $n->numero){
                                    $appelles[$key]['site'] = $s->nom;
                                    $appelles[$key]['contacttype'] = "agent";
                                    $contactSet = true;
                                    break;
                                }
                            }
                            
                        if ($contactSet) {
                            break;
                        }
                    }
                }
            }

            $appelleObj = $appelles;
            $appelles = [];
            foreach ($appelleObj as $key => $value) {
                $appelles[] = $value;
            }
            return compact('appelleObj', 'appelles', 'habilites', 'sites', 'admin_users');
    }

    public function index(Request $request)
    {
        $uniqueids = DB::connection("mysql2")->select("SELECT CAST(c.uniqueid AS CHAR) as 'id' FROM cdr c GROUP BY CAST(c.uniqueid AS CHAR) ORDER BY c.datetime DESC limit " . $request->offset . ", 50");
        if(count($uniqueids) > 0) {
            $appelles = $this->getAppelleCompact($uniqueids)['appelles'];
            return response()->json(compact('appelles'));
        }
        return response()->json(['appelles' => []]);
    }

    function getBeginService(){
        //return "2025-03-31 05:30:00";
        if((new \DateTime >= (new \DateTime)->setTime(5, 50, 0) &&
                new \DateTime < (new \DateTime)->setTime(17, 50, 0))){
            return (new \DateTime)->setTime(5, 30, 0)->format('Y-m-d H:i:s');
        }
        else if(new \DateTime < (new \DateTime)->setTime(5, 50, 0))
            return (new \DateTime)->setTime(17, 30, 0)->sub(new \DateInterval('P1D'))->format('Y-m-d H:i:s');
        return (new \DateTime)->setTime(17, 30, 0)->format('Y-m-d H:i:s');
    }

    // public function a_rappeler(Request $request)
    // {
    //     $cdr = DB::connection("mysql2")->select("SELECT CAST(c.uniqueid AS CHAR) as 'id', c.dst, c.src, c.calltype FROM cdr c 
    //         WHERE c.datetime > ?
    //         GROUP BY CAST(c.uniqueid AS CHAR) ORDER BY c.datetime DESC", [$this->getBeginService()]);
    //     $uniqueids = [];
    //     foreach ($cdr as $c) {
    //         if($c->calltype == "Inbound"){
    //             if(preg_match('/^\+261\d{9}$/', $c->src))
    //                 $c->numero = "0" . substr($c->src, 4, 9);
    //             else if(preg_match('/^\d{9}$/', $c->src))
    //                 $c->numero = "0" . $c->src;
    //             else if(preg_match('/^\d{10}$/', $c->src))
    //                 $c->numero = $c->src;
    //         }
    //         else if($c->calltype == "Outbound"){
    //             if(preg_match('/^\d{10}$/', $c->dst))
    //                 $c->numero = $c->dst;
    //         }
    //         if(($c->calltype == "Inbound" || $c->calltype == "Outbound") 
    //             && isset($c->numero) && !in_array($c->numero, array_column($uniqueids, 'numero'))
    //         )
    //             $uniqueids[] = $c;
    //     }
    //     if(count($uniqueids) > 0) {
    //         $appelleCompact = $this->getAppelleCompact($uniqueids);
    //         $sites = $appelleCompact['sites'];
    //         $habilites = $appelleCompact['habilites'];
    //         $admin_users = $appelleCompact['admin_users'];
    //         $appelleObj = $appelleCompact['appelleObj'];
    //         $arrayList = $appelleCompact['appelles'];
    //         $appelles = [];
    //         foreach ($arrayList as $a) {
    //             if(!isset($a['disposition'])){
    //                 dd($a);
    //             }
    //             if(($a['disposition'] == "NO ANSWER" || $a['disposition'] == "BUSY"))
    //                 $appelles[] = $a;
    //         }
    //         return response()->json(compact('appelleObj', 'appelles', 'sites', 'habilites', 'admin_users'));
    //     }

    //     return response()->json(['appelles' => []]);
    // }


    public function get_a_rappler(Request $request){
        $a_rappeler = DB::select("SELECT * FROM call_reminders order by uniqueid desc");
        if(count($a_rappeler) > 0) {
            $puces = [];
            foreach($a_rappeler as $a){
                $puces[] = $a->numero;
            }
            $numeros = DB::select("SELECT n.numero, n.id_site, n.id_contact FROM numeros n
                WHERE numero in (". implode(',' , $puces) .")");
            $admin_users = DB::select("SELECT `name`, email, flotte FROM admin_users WHERE flotte in (". implode(',' , $puces) . ")");
            $contact_ids = [];
            $site_ids = [];
            foreach ($numeros as $n) {
                if($n->id_contact)
                    $contact_ids[] = $n->id_contact;
                else if($n->id_site)
                    $site_ids[] = $n->id_site;
            }
            $habilites = [];
            $sites = [];
            if(count($contact_ids) > 0)
                $habilites = DB::select("SELECT c.idcontact, c.nom, c.prenom, s.nom as 'site' FROM contacts c
                    LEFT JOIN habilites h ON c.idContact = h.idcontact
                    LEFT JOIN sites s ON s.idsite = h.idsite
                    WHERE c.idcontact in (" . implode(',' , $contact_ids) . ")");
            if(count($site_ids) > 0)
                $sites = DB::select("SELECT s.idsite, s.nom FROM sites s WHERE s.idsite in (". implode(',' , $site_ids) .")");

            foreach ($a_rappeler as $a) {
                if ((isset($a->numero))) {
                    foreach ($numeros as $n) {
                        $contactSet = false;
                        foreach ($habilites as $h) {
                            if($h->idcontact == $n->id_contact && $a->numero == $n->numero){
                                $a->client = $h->nom . " " . $h->prenom;
                                $a->site = $h->site;
                                $a->contacttype = "client";
                                $contactSet = true;
                                break;
                            }
                        }
                        if(!$contactSet)
                            foreach ($admin_users as $u) {
                                if($a->numero == $u->flotte){
                                    $a->name = $u->name;
                                    $a->email = $u->email;
                                    $a->contacttype = "user";
                                    $contactSet = true;
                                    break;
                                }
                            }
                        if(!$contactSet)
                            foreach ($sites as $s) {
                                if($s->idsite == $n->id_site && $a->numero == $n->numero){
                                    $a->site = $s->nom;
                                    $a->contacttype = "agent";
                                    $contactSet = true;
                                    break;
                                }
                            }
                            
                        if ($contactSet) {
                            break;
                        }
                    }
                }
            }
            $appelles = [];
            foreach ($a_rappeler as $a) {
                if(!isset($a->disposition)){
                    dd($a);
                }
                if(($a->disposition == "NO ANSWER" || $a->disposition == "BUSY"))
                    $appelles[] = $a;
            }
            return response()->json(compact('appelles', 'sites', 'habilites', 'admin_users', 'puces'));
        }
        return response()->json(['appelles' => []]);
    }
}
