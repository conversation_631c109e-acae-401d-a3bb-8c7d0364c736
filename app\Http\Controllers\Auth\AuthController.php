<?php

namespace App\Http\Controllers\Auth;

use App\ClientUser;
use App\UserSite;
use App\Site;
use Carbon\Carbon;
use Validator;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;

class AuthController extends Controller {
    public function login(Request $request) {
        $validator = Validator::make($request->all(), [
            'email' => 'required|string|email',
            'password' => 'required|string'
        ])->setAttributeNames([
            'email' => 'E-mail',
            'password' => 'Mot de passe'
        ]);
        if($validator->fails())
           return response()->json(['error' => $validator->errors()->first()]);
        $credentials = request(['email', 'password']);
        if(!Auth::attempt($credentials))
            return response()->json([
                'message' => 'Unauthorized'
            ],401);
        $user = $request->user();
        $tokenResult = $user->createToken('Personal Access Token');
        $token = $tokenResult->token;
        if ($request->remember_me)
            $token->expires_at = Carbon::now()->addWeeks(1);
            $token->save();
            $client_sites = UserSite::where('user_id', $user->id)->get();
            if(count($client_sites) > 0){
                if($user->site_id) {
                    $site = Site::select("idsite", "nom", "adresse", "vigilance", "soft_delete")->find($user->site_id);
                    if(
                        $site == null || $site->soft_delete == 1 
                        || UserSite::where('user_id', $user->id)->where('site_id', $user->site_id)->first() == null
                    ) {
                        $site_id = $client_sites[0]->site_id;
                        ClientUser::where('id', $user->id)->update(['site_id' => $site_id]);
                        $site = Site::select("idsite", "nom", "adresse", "vigilance", "soft_delete")->find($site_id);
                    }
                }
                else {
                    $site_id = $client_sites[0]->site_id;
                    ClientUser::where('id', $user->id)->update(['site_id' => $site_id]);
                    $site = Site::select("idsite", "nom", "adresse", "vigilance", "soft_delete")->find($site_id);
                }
            }
            if($site != null){
                $site->nb_site = count($client_sites);
                if($site->nb_site > 0)
                    return response()->json([
                        'client' => [
                            'id' => $user->id,
                            'name' => $user->name,
                            'email' => $user->email,
                        ],
                        'site' => $site,
                        'access_token' => $tokenResult->accessToken,
                        'token_type' => 'Bearer',
                        'expires_at' => Carbon::parse(
                            $tokenResult->token->expires_at
                        )->toDateTimeString()
                    ]);
            }
            return response()->json(["error" => "Aucun site n'est rattaché à votre compte"]);
    }

    public function register(Request $request) {
        $request->validate([
                'name' => 'required|string',
                'email' => 'required|string|email|unique:client_users',
                'password' => 'required|string'
        ]);
        $user = new ClientUser;
        $user->name = $request->name;
        $user->email = $request->email;
        $user->password = bcrypt($request->password);
        $user->save();
        return response()->json([
            'message' => 'Successfully created user!'
        ], 201);
    }

    public function logout(Request $request) {
        $request->user()->token()->revoke();
        return response()->json([
            'message' => 'Successfully logged out'
        ], 201);
    }

    public function user(Request $request) {
        return response()->json($request->user());
    }
}