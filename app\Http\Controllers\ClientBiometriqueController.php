<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\HistoriquePointeuse;
use App\AgentPointeuse;
use App\Agent;
use App\Pointeuse;
// use Validator;
use Illuminate\Support\Facades\Validator;
// use Illuminate\Validation\Rule;

class ClientBiometriqueController extends Controller
{
    private $attributeNames = array(
        'empreinte_id' => 'Empreinte',
        'agent_id' => 'Agent',
        'digit' => 'DigitN',
    );

    public function __construct()
    {
        ini_set('max_execution_time', 300);
        date_default_timezone_set("Indian/Antananarivo");
    }

    public function list_id($id, Request $request)
    {
        $details = null;

        exec(env("CLIENTJS") . ' listId' . substr('000' . $id, -4), $details);

        $historique_pointeuse = new HistoriquePointeuse();
        $historique_pointeuse->pointeuse_id = $id;
        $historique_pointeuse->user_id = $request->authId;
        $historique_pointeuse->objet = "Mise à jour de la liste d'empreintes enclenché.";
        $historique_pointeuse->detail = null;
        $historique_pointeuse->created_at = now();
        $historique_pointeuse->updated_at = now();
        $historique_pointeuse->save();

        // return response([env("CLIENTJS")]);
        return response()->json($details);
    }

    public function update_sim($id, Request $request)
    {
        $pointeuse = Pointeuse::find($id);

        $historique_pointeuse = new HistoriquePointeuse();
        $historique_pointeuse->pointeuse_id = $id;
        $historique_pointeuse->user_id = $request->authId;
        $historique_pointeuse->objet = "Mise à jour de la Carte Sim enclenché.";
        $historique_pointeuse->detail = "ancien sim : " . $pointeuse->sim;
        $historique_pointeuse->created_at = now();
        $historique_pointeuse->updated_at = now();
        $historique_pointeuse->save();

        $details = null;
        exec(env("CLIENTJS") . ' ussd'
            . substr('000' . $id, -4) . $request->ussdCode, $details);
        // . "user" . $request->authId, $details);

        return response()->json($details);
    }

    public function add_empreinte($pointeuse_id, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'agent_id' => ['required'],
            'digit' => ['required'],
        ])->setAttributeNames($this->attributeNames);

        if ($validator->fails())
            return response()->json(['error' => $validator->errors()]);
        $empreinte = AgentPointeuse::where('pointeuse_id', $pointeuse_id)
            ->where('agent_id', $request->agent_id)
            ->where('digit', $request->digit)
            ->first();
        if ($empreinte != null)
            return response()->json(['error' => ['empreinte_id' => ['L\'empreinte est déjà enrolé sur cette pointeuse.']]]);

        $details = null;

        $agent = Agent::where('id', $request->agent_id)->first();

        $matricule = ($agent->societe_id == 1 ? ('DGM-' . $agent->numero_employe) : ($agent->societe_id == 2 ? ('SOIT-' . $agent->num_emp_soit) : ($agent->societe_id == 3 ? ('ST-' . $agent->numero_stagiaire) : 'Ndf ')));

        $historique_pointeuse = new HistoriquePointeuse();
        $historique_pointeuse->pointeuse_id = $pointeuse_id;
        $historique_pointeuse->user_id = $request->authId;
        $historique_pointeuse->objet = "Enrolement d'empreinte enclenché.";
        $historique_pointeuse->detail = "agent : " . $matricule . " " . $agent->nom;
        $historique_pointeuse->created_at = now();
        $historique_pointeuse->updated_at = now();
        $historique_pointeuse->save();

        exec(
            env("CLIENTJS") . ' enroll'
                . substr('000' . $pointeuse_id, -4)
                . substr('0000' . $request->agent_id, -5)
                . $request->digit
                . "user" . $request->authId,
            $details
        );
        return response()->json($details);
    }
    public function remove_empreinte($pointeuse_id, $empreinte_id, Request $request)
    {
        $empreinte = AgentPointeuse::where('pointeuse_id', $pointeuse_id)
            ->where('empreinte_id', $empreinte_id)
            ->first();
        if ($empreinte != null) {

            if ($empreinte->agent_id)
                $agent = Agent::where('id', $empreinte->agent_id)->first();
            else
                $agent = null;

            if ($agent)
                $matricule = ($agent->societe_id == 1 ? ('DGM-' . $agent->numero_employe) : ($agent->societe_id == 2 ? ('SOIT-' . $agent->num_emp_soit) : ($agent->societe_id == 3 ? ('ST-' . $agent->numero_stagiaire) : 'Ndf ')));

            $historique_pointeuse = new HistoriquePointeuse();
            $historique_pointeuse->pointeuse_id = $pointeuse_id;
            $historique_pointeuse->user_id = $request->authId;
            $historique_pointeuse->objet = "Suppression d'empreinte enclenché.";
            if ($agent)
                $historique_pointeuse->detail = "agent : " . $matricule . " nom:  " . $agent->nom;
            else
                $historique_pointeuse->detail = "aucun agent";
            $historique_pointeuse->created_at = now();
            $historique_pointeuse->updated_at = now();
            $historique_pointeuse->save();

            $details = null;
            exec(
                env("CLIENTJS") . ' delete'
                    . substr('000' . $pointeuse_id, -4)
                    . substr('00' . $empreinte_id, -3)
                    . "user" . $request->authId,
                $details
            );
            return response()->json($details);
        } else
            return response()->json([
                'error' => ['empreinte_id' => ['Cette empreinte n\'existe pas.']]
            ]);
    }
    public function register($pointeuse_id, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'agent_id' => ['required'],
            'digit' => ['required'],
        ])->setAttributeNames($this->attributeNames);
        if ($validator->fails())
            return response()->json(['error' => $validator->errors()]);
        if ($validator->fails())
            return response()->json(['error' => $validator->errors()]);
        $empreinte = AgentPointeuse::where('pointeuse_id', $pointeuse_id)
            ->where('agent_id', $request->agent_id)
            ->where('digit', $request->digit)
            ->first();
        if ($empreinte != null)
            return response()->json(['error' => ['empreinte_id' => ['L\'empreinte est déjà enrolé sur cette pointeuse.']]]);
        /*
        $agent = Agent::select('digit_'. $request->digit)->find($request->agent_id);
        if($agent && !$agent['digit_'. $request->digit])
            return response()->json(['error' => ['agent_id' => ['L\'agent est déjà enregistré.']]]);
        */

        $agent = Agent::where('id', $request->agent_id)->first();
        $matricule = ($agent->societe_id == 1 ? ('DGM-' . $agent->numero_employe) : ($agent->societe_id == 2 ? ('SOIT-' . $agent->num_emp_soit) : ($agent->societe_id == 3 ? ('ST-' . $agent->numero_stagiaire) : 'Ndf ')));

        $historique_pointeuse = new HistoriquePointeuse();
        $historique_pointeuse->pointeuse_id = $pointeuse_id;
        $historique_pointeuse->user_id = $request->authId;
        $historique_pointeuse->objet = "Enregistrement d'empreinte enclenché.";
        $historique_pointeuse->detail = "agent : " . $matricule . " " . $agent->nom;
        $historique_pointeuse->created_at = now();
        $historique_pointeuse->updated_at = now();
        $historique_pointeuse->save();

        $details = null;
        exec(
            env("CLIENTJS") . ' enrDir'
                . substr('000' . $pointeuse_id, -4)
                . substr('' . $request->digit, -1)
                . substr('0000' . $request->agent_id, -5)
                . "user" . $request->authId,
            $details
        );
        return response()->json($details);
    }

    public function cancel_register($id, Request $request)
    {
        $details = null;

        // $agent = Agent::where('id', $request->agent_id)->first();
        // $matricule = ($agent->societe_id == 1 ? ('DGM-' . $agent->numero_employe) : ($agent->societe_id == 2 ? ('SOIT-' . $agent->num_emp_soit) : ($agent->societe_id == 3 ? ('ST-' . $agent->numero_stagiaire) : 'Ndf ')));

        $historique_pointeuse = new HistoriquePointeuse();
        $historique_pointeuse->pointeuse_id = $id;
        $historique_pointeuse->user_id = $request->authId;
        $historique_pointeuse->objet = "Annulation enregistrement d'empreinte enclenché.";
        $historique_pointeuse->detail = '';
        $historique_pointeuse->created_at = now();
        $historique_pointeuse->updated_at = now();
        $historique_pointeuse->save();

        exec(env("CLIENTJS") . ' enrDirCancel'
            . substr('000' . $id, -4)
            . "user" . $request->authId, $details);
        return response()->json($details);
    }

    public function get_template($id, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'empreinte_id' => ['required'],
            'agent_id' => ['required'],
            'digit' => ['required'],
        ])->setAttributeNames($this->attributeNames);
        if ($validator->fails())
            return response()->json(['error' => $validator->errors()]);
        $details = null;

        $agent = Agent::where('id', $request->agent_id)->first();
        $matricule = ($agent->societe_id == 1 ? ('DGM-' . $agent->numero_employe) : ($agent->societe_id == 2 ? ('SOIT-' . $agent->num_emp_soit) : ($agent->societe_id == 3 ? ('ST-' . $agent->numero_stagiaire) : 'Ndf ')));

        $historique_pointeuse = new HistoriquePointeuse();
        $historique_pointeuse->pointeuse_id = $id;
        $historique_pointeuse->user_id = $request->authId;
        $historique_pointeuse->objet = "Enregistrement du template d'empreinte enclenché.";
        $historique_pointeuse->detail = "agent : " . $matricule . " " . $agent->nom;
        $historique_pointeuse->created_at = now();
        $historique_pointeuse->updated_at = now();
        $historique_pointeuse->save();

        exec(
            env("CLIENTJS") . ' getTmp'
                . substr('000' . $id, -4)
                . substr('00' . $request->empreinte_id, -3)
                . substr('' . $request->digit, -1)
                . substr('0000' . $request->agent_id, -5)
                . "user" . $request->authId,
            $details
        );
        return response()->json($details);
    }
}
