<?php

namespace App\Http\Controllers;

use App\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ClientController extends Controller
{
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
    }

    public function index(Request $request){
        $clients = DB::select("SELECT idClient, Societe, Adresse FROM clients
            WHERE Societe like '%" . $request->search . "%' or Adresse like '%" . $request->search . "%'
            ORDER BY lastupdate desc limit " . $request->offset . ", 50");
        return response()->json($clients);
    }
    public function store(Request $request){
        $client = new Client();
        $client->Societe = $request->nom;
        $client->Adresse = $request->adresse;
        $client->TypeClient = $request->type;
        $client->lastupdate = now();
        $client->save();
        return response()->json($client);
    }
    public function update($id, Request $request){
        $client = Client::find($id);
        $client->Societe = $request->nom;
        $client->Adresse = $request->adresse;
        $client->TypeClient = $request->type;
        $client->lastupdate = now();
        $client->save();
        return response()->json($client);
    }
    public function delete($id){
        $client = Client::find($id);
        return response()->json($client->delete());
    }
}