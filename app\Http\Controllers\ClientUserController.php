<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\ClientUser;
use App\UserSite;
use App\Site;
use Validator;
use Illuminate\Validation\Rule;

class ClientUserController extends Controller
{
    private $attributeNames = array(
        'name' => 'Nom',
        'email' => 'Email',
        'site_id' => 'Site'
    );
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
    }
    public function index(){
        $clients = DB::select("SELECT c.id, c.name, c.email, s.nom as 'site' FROM client_users c
            LEFT JOIN sites s ON s.idsite = c.site_id
            ORDER BY c.updated_at desc");
        $sites = DB::select("SELECT s.nom from sites s where (soft_delete is null or soft_delete = 0)");
        return response()->json(compact('clients', 'sites'));
    }

    public function show($id){
        $client = DB::select("SELECT c.id, c.name, c.email, c.site_id, s.nom as 'site' FROM client_users c
            LEFT JOIN sites s ON s.idsite = c.site_id
            WHERE c.id = ?", [$id])[0];
        $sites = DB::select("SELECT us.id, us.site_id, us.user_id, s.nom FROM user_sites us 
            LEFT JOIN sites s ON s.idsite = us.site_id
            WHERE us.user_id = ?", [$id]);
        if($client->site_id) {
            $site = Site::select("idsite", "nom", "vigilance", "soft_delete")->find($client->site_id);
            if(
                $site == null || $site->soft_delete == 1 
                || UserSite::where('user_id', $client->id)->where('site_id', $client->site_id)->first() == null
            ) {
                if(count($sites) > 0){
                    $site_id = $sites[0]->site_id;
                    ClientUser::where('id', $client->id)->update(['site_id' => $site_id]);
                    $site = Site::select("idsite", "nom", "adresse", "vigilance", "soft_delete")->find($site_id);
                }
                else 
                    ClientUser::where('id', $client->id)->update(['site_id' => null]);
            }
        }
        else if(count($sites) > 0){
            $site_id = $sites[0]->site_id;
            ClientUser::where('id', $client->id)->update(['site_id' => $site_id]);
            $site = Site::select("idsite", "nom", "adresse", "vigilance", "soft_delete")->find($site_id);
        }
        $client->sites = $sites;
        return response()->json(compact('client'));
    }

    public function site($site_id){
        $current_site = Site::find($site_id);
        $clients = ClientUser::select('id', 'nom')->where('id', $current_site->group_client_id)->get();
        $sites = Site::select('idsite', 'nom')->where('group_client_id', $current_site->group_client_id)->get();
        return response()->json(compact('clients', 'sites'));
    }

    public function add_site($id, Request $request){
        $client = ClientUser::find($id);
        if(UserSite::where('site_id', $client->site_id)->where('user_id', $client->user_id)->first() != null)
            return response()->json(false);
        $user_site = new UserSite();
        $user_site->user_id = $id;
        $user_site->site_id = $request->site_id;
        return response()->json($user_site->save());
    }
    public function remove_site($id, Request $request){
        $user_site = UserSite::find($id);
        return response()->json($user_site->delete());
    }

    public function store(Request $request){
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|unique:client_users,email',
            'name' => 'required',
        ])->setAttributeNames($this->attributeNames);
        if($validator->fails())
            return response()->json(['error' => $validator->errors()]);
        $client = new ClientUser();
        $client->name = $request->name;
        $client->email = $request->email;
        $client->created_at = now();
        $client->updated_at = now();
        $client->save();
        return response()->json($client->id);
    }

    public function update($id, Request $request){
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'email' => 'required|unique:client_users,email,'. $id,
        ])->setAttributeNames($this->attributeNames);
        if($validator->fails())
            return response()->json(['error' => $validator->errors()]);
        $client = ClientUser::find($id);
        $client->name = $request->name;
        $client->email = $request->email;
        $client->site_id = $request->site_id;
        $client->updated_at = now();
        $client->save();
        return response()->json($client->id);
    }

    public function delete($id){
        $client = ClientUser::find($id);
        if($client != null){
            UserSite::where('user_id', $id)->delete();
            return response()->json($client->delete());
        }
        return false;
    }
}