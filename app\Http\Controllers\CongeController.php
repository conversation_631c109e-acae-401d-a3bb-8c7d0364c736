<?php

namespace App\Http\Controllers;

use App\Conge;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class CongeController extends Controller
{
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
    }

    public function index($agent_id){
        $current_date = (new \DateTime())->format("Y-m-d");
        $date_embauche = null;
        $date_dgm = null;
        $date_soit = null;
        $agents = DB::select("SELECT a.date_embauche, a.date_confirmation, a.date_conf_soit FROM agents a
            WHERE a.id = ?", [$agent_id]);
        if($agents != null){
            $date_embauche = $agents[0]->date_embauche;
            $date_dgm = $agents[0]->date_confirmation;
            $date_soit = $agents[0]->date_conf_soit;
        }
        $conges = Conge::where('agent_id', $agent_id)
            ->orderBy('begin_date', 'desc')
            ->get();
        return response()->json(compact('conges', 'current_date', 'date_embauche', 'date_dgm', 'date_soit'));
    }
    public function store(Request $request){
        if($request->authRole == 'root' || $request->authRole == 'rh'){
            $date_limit = (new \DateTime())->sub("P5D");
            $date_request = (\DateTime::createFromFormat("Y-n-j H:i:s", $request->begin_date . " 00:00:00"));
            if($date_request > $date_limit){
                $conge = new Conge();
                $conge->agent_id = $request->agent_id;
                $conge->begin_date = $request->begin_date;
                $conge->nb_day = $request->nb_day;
                $conge->created_at = now();
                $conge->updated_at = now();
                $conge->save();
                return response()->json($conge);
            }
        }
        return false;
    }
    public function update($id, Request $request){
        if($request->authRole == 'root' || $request->authRole == 'rh'){
            $date_limit = (new \DateTime())->sub("P5D");
            $date_request = (\DateTime::createFromFormat("Y-n-j H:i:s", $request->begin_date . " 00:00:00"));
            if($date_request > $date_limit){
                $conge = Conge::find($id);
                $conge->begin_date = $request->begin_date;
                $conge->nb_day = $request->nb_day;
                $conge->updated_at = now();
                $conge->save();
                return response()->json($conge);
            }
        }
        return false;
    }
    public function delete($id){
        if($request->authRole == 'root' || $request->authRole == 'rh'){
            $date_limit = (new \DateTime())->sub("P5D");
            $date_request = (\DateTime::createFromFormat("Y-n-j H:i:s", $request->begin_date . " 00:00:00"));
            if($date_request > $date_limit){
                $conge = Conge::find($id);
                return response()->json($conge->delete());
            }
        }
        return false;
    }
}