<?php

namespace App\Http\Controllers;

use App\Contact;
use App\Numero;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ContactController extends Controller
{
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
    }

    private static $attributeNames = array(
        'nom' => 'Nom',
        'phone' => 'Téléphone'
    );

    public function index(Request $request)
    {
        $showArchived = $request->has('archived') && $request->archived == '1';
        $search = '%' . $request->search . '%';

        $contacts = DB::select("SELECT c.idContact, s.idsite, c.nom, c.prenom, c.adresse, c.lastupdate, s.nom as 'site'
            FROM contacts c
            LEFT JOIN habilites h ON c.idContact = h.idcontact
            LEFT JOIN sites s ON s.idsite = h.idsite
            LEFT JOIN numeros n ON c.idContact = n.id_contact
            WHERE (c.nom LIKE ? OR c.prenom LIKE ? OR s.nom LIKE ? OR n.numero LIKE ?)
            AND c.soft_delete = ?
            GROUP BY c.idContact
            ORDER BY c.lastupdate DESC
            LIMIT ?, 50", [
            $search,
            $search,
            $search,
            $search,
            $showArchived ? 1 : 0,
            $request->offset
        ]);

        $contactIds = array_map(function ($contact) {
            return $contact->idContact;
        }, $contacts);

        $phones = DB::table('numeros')
            ->select('id_contact', 'numero')
            ->whereIn('id_contact', $contactIds)
            ->where('soft_delete', 0)
            ->get();

        $contactsWithPhones = array_map(function ($contact) use ($phones) {
            $contact->phones = array_values(array_filter($phones->toArray(), function ($phone) use ($contact) {
                return $phone->id_contact == $contact->idContact;
            }));
            return $contact;
        }, $contacts);

        return response()->json($contactsWithPhones);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'nom' => 'required',
            'phones' => 'array',
            'phones.*' => 'regex:/^\d{10}$/'
        ])->setAttributeNames(self::$attributeNames);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()->first()]);
        }

        $phones = $request->phones;

        $existingMatches = [];

        foreach ($phones as $phone) {
            $existingNumeros = Numero::where('numero', 'LIKE BINARY', $phone)
                ->where('soft_delete', 0)
                ->wherenotnull('id_contact')
                ->where('id_contact', '!=', 0)
                ->get();

            if ($existingNumeros->isNotEmpty()) {
                foreach ($existingNumeros as $existingNumero) {
                    $existingContact = Contact::where('idContact', $existingNumero->id_contact)->first();

                    if ($existingContact) {
                        $existingMatches[] = [
                            'contact' => $existingContact,
                            'phone_number' => $phone,
                        ];
                    }
                }
            }
        }

        if (!empty($existingMatches)) {
            return response()->json([
                'error' => 'Ces numéros de téléphone sont déjà associés à des contacts.',
                'existingMatches' => $existingMatches,
            ]);
        }

        $contact = new Contact();
        $contact->nom = $request->nom;
        $contact->prenom = $request->prenom;
        $contact->adresse = $request->adresse;
        $contact->lastupdate = now();
        $contact->save();

        foreach ($phones as $phone) {
            $numero = Numero::where('numero', 'LIKE BINARY', $phone)
                ->where('id_contact', $contact->idContact)
                ->first();

            if (is_null($numero)) {
                DB::table('numeros')->insert([
                    'id_contact' => $contact->idContact,
                    'numero' => $phone,
                ]);
            } else {
                $numero->soft_delete = 0;
                $numero->lastupdate = now();
                $numero->save();
            }
        }

        return response()->json($contact);
    }

    public function update($id, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'nom' => 'required',
            'phones' => 'array',
            'phones.*' => 'regex:/^\d{10}$/'
        ])->setAttributeNames(self::$attributeNames);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()->first()]);
        }

        $phones = $request->phones;

        $existingMatches = [];

        foreach ($phones as $phone) {
            $existingNumeros = Numero::where('numero', 'LIKE BINARY', $phone)
                ->where('soft_delete', 0)
                ->wherenotnull('id_contact')
                ->where('id_contact', '!=', $id)
                ->get();

            if ($existingNumeros->isNotEmpty()) {
                foreach ($existingNumeros as $existingNumero) {
                    $existingContact = Contact::where('idContact', $existingNumero->id_contact)->first();

                    if ($existingContact) {
                        $existingMatches[] = [
                            'contact' => $existingContact,
                            'phone_number' => $phone,
                        ];
                    }
                }
            }
        }

        if (!empty($existingMatches)) {
            return response()->json([
                'error' => 'Ces numéros de téléphone sont déjà associés à des contacts.',
                'existingMatches' => $existingMatches,
            ]);
        }

        $contact = Contact::find($id);
        $contact->nom = $request->nom;
        $contact->prenom = $request->prenom;
        $contact->adresse = $request->adresse;
        $contact->lastupdate = now();
        $contact->save();

        $existingNumbers = [];

        $results = DB::table('numeros')
            ->where('id_contact', $id)
            ->get();

        if ($results) {
            foreach ($results as $item) {
                $existingNumbers[] = isset($item->numero) ? $item->numero : null;
            }
        }

        $numbersToDelete = array_diff($existingNumbers, $request->phones);
        if (!empty($numbersToDelete)) {
            DB::table('numeros')
                ->where('id_contact', $id)
                ->whereIn('numero', $numbersToDelete)
                ->update(['soft_delete' => true, 'lastupdate' => now()]);
        }

        foreach ($request->phones as $phone) {
            $existingNumber = DB::table('numeros')
                ->where('id_contact', $id)
                ->where('numero', $phone)
                ->first();

            if ($existingNumber) {
                if ($existingNumber->soft_delete) {
                    DB::table('numeros')
                        ->where('id', $existingNumber->id)
                        ->update(['soft_delete' => false, 'lastupdate' => now()]);
                }
            } else {
                DB::table('numeros')->insert([
                    'id_contact' => $id,
                    'numero' => $phone,
                    'soft_delete' => false,
                    'lastupdate' => now(),
                ]);
            }
        }

        return response()->json($contact);
    }

    public function delete($id)
    {
        $contact = Contact::find($id);
        foreach (
            Numero::where('id_contact', $id)->get() as $phone
        ) {
            $phone->soft_delete = 1;
            $phone->lastupdate = now();
            $phone->save();
        }

        $contact->soft_delete = 1;
        $contact->lastupdate = now();

        return response()->json($contact->save());
    }
    public function restore($id)
    {
        $contact = Contact::find($id);

        // Restore the contact
        $contact->soft_delete = 0;
        $contact->lastupdate = now();
        $contact->save();

        // Restore associated phone numbers
        foreach (Numero::where('id_contact', $id)->get() as $phone) {
            $phone->soft_delete = 0;
            $phone->lastupdate = now();
            $phone->save();
        }

        return response()->json(true);
    }
}
