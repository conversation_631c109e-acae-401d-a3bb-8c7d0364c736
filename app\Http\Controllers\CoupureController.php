<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Coupure;

class CoupureController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
    }
    public function get_current_date(){
        return response()->json((new \DateTime)->format('Y-m-d H:i:s'));
    }
    public function getDayOrNightDate(){
        if(new \DateTime >= (new \DateTime)->setTime(5, 50, 0) &&
                new \DateTime < (new \DateTime)->setTime(17, 50, 0))
            return (new \DateTime)->setTime(06, 0, 0)->format('Y-m-d H:i:s');
        else if(new \DateTime < (new \DateTime)->setTime(5, 50, 0))
            return (new \DateTime)->setTime(18, 0, 0)->sub(new \DateInterval('P1D'))->format('Y-m-d H:i:s');
        return (new \DateTime)->setTime(18, 00, 0)->format('Y-m-d H:i:s');
    }
    public function getNightDate(){
        if(new \DateTime < (new \DateTime)->setTime(5, 50, 0))
            return (new \DateTime)->setTime(18, 0, 0)->sub(new \DateInterval('P1D'))->format('Y-m-d H:i:s');
        return (new \DateTime)->setTime(18, 0, 0)->format('Y-m-d H:i:s');
    }
    public function index(){
        $date_limit = $this->getDayOrNightDate();
        $coupures = Coupure::where('vigilance', '>=', $date_limit)->get();
        return response()->json($coupures);
    }
    public function store(Request $request){
        $vigilance = (new \DateTime($request->vigilance));
        $date_limit = (new \DateTime($this->getDayOrNightDate()))->sub(new \DateInterval('PT1H'));
        if($request->transmitter && $request->vigilance &&  $vigilance >= $date_limit && $vigilance < (new \DateTime)->add(new \DateInterval('PT10M'))){
            $coupure = new Coupure();
            $coupure->transmitter = $request->transmitter;
            $coupure->vigilance = $request->vigilance;
            $coupure->updated_at = now();
            $coupure->created_at = now();
            return response()->json($coupure->save());
        }
        return response()->json(["error" => "EACCES"]);
    }
}
