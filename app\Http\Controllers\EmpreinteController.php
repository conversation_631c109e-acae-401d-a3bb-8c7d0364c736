<?php

namespace App\Http\Controllers;

use App\AgentPointeuse;
use App\Pointage;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class EmpreinteController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
    }

    function intervalHoraire(){
        if(new \DateTime >= (new \DateTime)->setTime(5, 50, 0) &&
                new \DateTime < (new \DateTime)->setTime(17, 50, 0)){
            return [
                'date_pointage' => (new \DateTime)->setTime(7, 0, 0)->format('Y-m-d H:i:s'),
                'begin' => (new \DateTime)->setTime(05, 45, 0)->format('Y-m-d H:i:s'),
                'end' => (new \DateTime)->setTime(06, 10, 0)->format('Y-m-d H:i:s')
            ];
        }
        else if(new \DateTime < (new \DateTime)->setTime(5, 50, 0))
            return [
                'date_pointage' => (new \DateTime)->sub(new \DateInterval('P1D'))->setTime(18, 0, 0)->format('Y-m-d H:i:s'),
                'begin' => (new \DateTime)->setTime(17, 45, 0)->sub(new \DateInterval('P1D'))->format('Y-m-d H:i:s'),
                'end' => (new \DateTime)->setTime(18, 10, 0)->sub(new \DateInterval('P1D'))->format('Y-m-d H:i:s')
            ];
            return [
                'date_pointage' => (new \DateTime)->setTime(18, 0, 0)->format('Y-m-d H:i:s'),
                'begin' => (new \DateTime)->setTime(17, 45, 0)->format('Y-m-d H:i:s'),
                'end' => (new \DateTime)->setTime(18, 10, 0)->format('Y-m-d H:i:s')
            ];
    }

    public function define($id, Request $request){
        $ap = AgentPointeuse::find($id);
        if($ap && $request->digit && $request->agent_id){
            $ap->digit = $request->digit;
            $ap->agent_id = $request->agent_id;
            $interval = $this->intervalHoraire();
            DB::table('ademcotemp')
                ->where('pointeuse_user_id', $ap->empreinte_id)
                ->where('pointeuse_id', $ap->pointeuse_id)
                ->whereNull('agent_id')
                ->update(['agent_id' => $request->agent_id]);
            DB::table('ademcolog')
                ->where('pointeuse_user_id', $ap->empreinte_id)
                ->where('pointeuse_id', $ap->pointeuse_id)
                ->whereNull('agent_id')
                ->update(['agent_id' => $request->agent_id]);

            $logs = DB::select("SELECT dtarrived, site_id FROM ademcotemp where agent_id = ? and pointeuse_id = ? and dtarrived >= ? and dtarrived < ? order by dtarrived limit 1"
                , [$ap->agent_id, $ap->pointeuse_id, $interval['begin'], $interval['end']]);
            $pointages = DB::select("SELECT id FROM pointages where agent_id = ? and date_pointage = ?", [$request->agent_id, $interval['date_pointage']]);
            if($logs != null){
                $firstLog = $logs[0];
                if($pointages != null)
                    Pointage::where('agent_id', $request->agent_id)
                        ->where('date_pointage', $interval['date_pointage'])
                        ->update(['dtarrived' => $firstLog->dtarrived, 'pointeuse_id' => $ap->pointeuse_id,
                            'last_update' => now(), 'site_id' => $firstLog->site_id, 'type_pointage_id' => null]);
                else
                    Pointage::create([
                        'agent_id' => $request->agent_id, 
                        'pointeuse_id' => $ap->pointeuse_id, 
                        'site_id' => $firstLog->site_id,
                        'dtarrived' => $firstLog->dtarrived, 
                        'date_pointage' => $interval['date_pointage'],
                        'last_update' => now(),
                    ]);
            }
            return response()->json($ap->save());
        }
    }
}
