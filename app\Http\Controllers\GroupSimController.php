<?php

namespace App\Http\Controllers;


use Illuminate\Support\Facades\DB;
use App\GroupSim;
use App\Gateway;
use Illuminate\Http\Request;

class GroupSimController extends Controller
{
    public function index(){
        $group_sims = GroupSim::orderBy('nom')->get();
        $gateways = Gateway::all();
        return response()->json(compact('group_sims', 'gateways'));
    }
    public function store(Request $request){
        if(in_array($request->authRole, ['root'])){
            $group = new GroupSim();
            if($request->nom){
                $group->nom = $request->nom;
                $group->save();
                return response()->json($group);
            }
        }
        return response()->json(false);
    }
    public function update($id, Request $request){
        if(in_array($request->authRole, ['root'])){
            $group = GroupSim::find($id);
            if($group != null){
                $group->nom = $request->nom;
                $group->save();
                return response()->json($group);
            }
        }
        return response()->json(false);
    }
    public function delete($id, Request $request){
        if(in_array($request->authRole, ['root'])){
            $group = GroupSim::find($id);
            if($group != null){
                return response()->json($group->delete());
            }
        }
        return response()->json(false);
    }
}