<?php

namespace App\Http\Controllers;


use Illuminate\Support\Facades\DB;
use App\GroupSite;
use App\JourFerie;
use Illuminate\Http\Request;

class GroupSiteController extends Controller
{
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
    }

    public function getCurrentVigilanceDate(){
        $current_date = new \DateTime;
        $date_vigilance = new \DateTime;
        $date_vigilance->setTime(0, 0, 0);
        while (
            (!(new \DateTime >= (new \DateTime)->setTime(05, 50, 0) && new \DateTime <= (new \DateTime)->setTime(17, 50, 0)) && 
            ((clone $date_vigilance)->sub(new \DateInterval('PT10M')) < (new \DateTime) && ((new \DateTime) >= (clone $date_vigilance)->add(new \DateInterval('PT20M')))))
            || ((new \DateTime >= (new \DateTime)->setTime(05, 50, 0) && new \DateTime <= (new \DateTime)->setTime(17, 50, 0)) && 
            ((clone $date_vigilance)->sub(new \DateInterval('PT10M')) < (new \DateTime) && ((new \DateTime) >= (clone $date_vigilance)->add(new \DateInterval('PT50M')))))
        ) {
            if(new \DateTime >= (new \DateTime)->setTime(05, 50, 0) &&
                    new \DateTime <= (new \DateTime)->setTime(17, 50, 0))
                $date_vigilance->add(new \DateInterval('PT1H'));
            else $date_vigilance->add(new \DateInterval('PT30M'));
        }
        return $date_vigilance;
    }

    public function index(Request $request){
        $groups = GroupSite::orderBy('nom')->get();
        $date_vigilance = $this->getCurrentVigilanceDate();
        $horaire = '';
        $current_date = new \DateTime();
        if(new \DateTime >= (new \DateTime)->setTime(05, 50, 0) &&
                new \DateTime < (new \DateTime)->setTime(17, 50, 0))
            $horaire = 'day';
        else {
            if(new \DateTime < (new \DateTime)->setTime(05, 50,0))
                $current_date = (new \DateTime)->sub(new \DateInterval('P1D'));
            $horaire = 'night';
        }
        $field = $horaire . '_' . $current_date->format('w');
        
        $groupSite = [];
        if($request->biometrique){
            if(JourFerie::where('date', $current_date->format('Y-m-d'))->first() == null){
                $pointeuses = DB::select("SELECT s.group_id, coalesce(count(p.id), 0) as 'nb_site' FROM pointeuses p
                    LEFT JOIN sites s ON s.idsite = p.site_id
                    LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id
                    WHERE s.pointeuse = 1 and (p.soft_delete is null or p.soft_delete = 0) 
                    and (s.soft_delete is null or s.soft_delete = 0)
                    and (h.id is null or (h.". $field ." is not null and h.". $field ." = 1)) " .
                    "GROUP BY s.group_id");
            }
            else {
                $pointeuses = DB::select("SELECT s.group_id, coalesce(count(p.id), 0) as 'nb_site' FROM pointeuses p
                    LEFT JOIN sites s ON s.idsite = p.site_id
                    LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id
                    WHERE s.pointeuse = 1 and (p.soft_delete is null or p.soft_delete = 0)
                    and (s.soft_delete is null or s.soft_delete = 0)
                    and (h.id is null or (h.". $field ." is not null and h.". $field ." = 1) or (h.". $horaire ."_ferie is not null and h.". $horaire ."_ferie = 1)) " .
                    "GROUP BY s.group_id");
            }
            foreach ($groups as $g) {
                foreach ($pointeuses as $p) {
                    if($p->group_id == $g->id && $p->nb_site > 0){
                        $g->nb_site = $p->nb_site;
                        $groupSite[] = $g;
                    }
                }
            }
        }
        else {
            if(JourFerie::where('date', $current_date->format('Y-m-d'))->first() == null)
                $vigilances = DB::select("SELECT s.group_id, coalesce(count(s.idsite), 0) as 'nb_site' FROM sites s
                    LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id
                    WHERE (s.vigilance = 1 or s.do_checkphone = 1) and (s.soft_delete is null or s.soft_delete = 0)
                    and (h.id is null or (h.". $field ." is not null and h.". $field ." = 1)) " .
                    "GROUP BY s.group_id", []);
            else
                $vigilances = DB::select("SELECT s.group_id, coalesce(count(s.idsite), 0) as 'nb_site' FROM sites s
                    LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id
                    WHERE (s.vigilance = 1 or s.do_checkphone = 1) and (s.soft_delete is null or s.soft_delete = 0)
                    and (h.id is null or (h.". $field ." is not null and h.". $field ." = 1) or (h.". $horaire ."_ferie is not null and h.". $horaire ."_ferie = 1)) " .
                    "GROUP BY s.group_id", []);
            
            foreach ($groups as $g) {
                foreach ($vigilances as $p) {
                    if($p->group_id == $g->id && $p->nb_site > 0){
                        $g->nb_site = $p->nb_site;
                        $groupSite[] = $g;
                    }
                }
            }
        }
        return response()->json([
            'date_vigilance' =>$date_vigilance->format('H:i'), 
            'current_datetime' => (new \DateTime)->format('Y-m-d H:i:s'), 
            'current_date' => (new \DateTime)->format('H:i'),
            'groups' => $groupSite, 
        ]);
    }

    public function vigilance(Request $request){
        if(in_array($request->authRole, ['root', 'room']))
            $groupSite = DB::select("SELECT nom, vigilance_group_id as id FROM group_sites group by vigilance_group_id order by nom");
        else if($request->authRole == 'client' && $request->authGroupId)
            $groupSite = DB::select("SELECT nom, vigilance_group_id as id FROM group_sites where vigilance_group_id = ?", [$request->authGroupId]);
        return response()->json($groupSite);
    }
    public function store(Request $request){
        if(in_array($request->authRole, ['root'])){
            if($request->nom && $request->sim_group_id){
                $group = new GroupSite();
                $group->nom = $request->nom;
                $group->sim_group_id = $request->sim_group_id;
                if($request->vigilance_group_id){
                    $group->vigilance_group_id = $request->vigilance_group_id;
                    $group->save();
                }
                else{
                    $group->save();
                    $group->vigilance_group_id = $group->id;
                    $group->save();
                }
                return response()->json($group);
            }
        }
        return response()->json(false);
    }
    public function update($id, Request $request){
        if(in_array($request->authRole, ['root'])){
            if($request->nom && $request->sim_group_id && $request->vigilance_group_id){
                $group = GroupSite::find($id);
                if($group != null){
                    $group->nom = $request->nom;
                    $group->sim_group_id = $request->sim_group_id;
                    $group->vigilance_group_id = $request->vigilance_group_id;
                    $group->save();
                    return response()->json($group);
                }
            }
        }
        return response()->json(false);
    }
    public function delete($id, Request $request){
        if(in_array($request->authRole, ['root'])){
            $group = GroupSite::find($id);
            if($group != null){
                return response()->json($group->delete());
            }
        }
        return response()->json(false);
    }
}