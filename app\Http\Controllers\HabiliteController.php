<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Habilite;

class HabiliteController extends Controller
{
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
    }

    public function index($site_id)
    {
        $habilites = DB::select("SELECT h.idhabilite, h.quality, h.password, c.nom, c.prenom, c.phone, h.idordre
            FROM habilites h
            LEFT JOIN contacts c on c.idContact = h.idcontact
            WHERE h.idsite = ? AND h.soft_delete = 0
            ORDER BY h.idordre ASC", [$site_id]);
        return response()->json($habilites);
    }

    public function client($site_id)
    {
        $habilites = DB::select("SELECT h.idhabilite, h.quality, c.nom, c.prenom, c.phone, h.idordre
            FROM habilites h
            LEFT JOIN contacts c on c.idContact = h.idcontact
            WHERE h.idsite = ? AND h.soft_delete = 0
            ORDER BY h.idordre ASC", [$site_id]);
        return response()->json($habilites);
    }

    public function store(Request $request)
    {
        $habilite = new Habilite();
        $habilite->idcontact = $request->idcontact;
        $habilite->quality = $request->quality;
        $habilite->password = $request->password;
        $habilite->idordre = $request->idordre;
        $habilite->idsite = $request->idsite;
        $habilite->soft_delete = 0; // Ensure new records are not soft deleted
        $habilite->lastupdate = now(); // Set the lastupdate to current time
        return response()->json($habilite->save());
    }

    public function update(Request $request, $id)
    {
        $habilite = Habilite::find($id);
        $habilite->idcontact = $request->idcontact;
        $habilite->quality = $request->quality;
        $habilite->idordre = $request->idordre;
        $habilite->password = $request->password;
        $habilite->lastupdate = now(); // Update the lastupdate to current time
        return response()->json($habilite->save());
    }

    public function delete($id)
    {
        $habilite = Habilite::find($id);
        if ($habilite != null) {
            $habilite->soft_delete = 1; // Perform soft delete
            $habilite->lastupdate = now(); // Update the lastupdate to current time
            return response()->json($habilite->save());
        }
        return false;
    }
}
