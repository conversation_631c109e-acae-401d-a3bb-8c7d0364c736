<?php

namespace App\Http\Controllers;

use App\Historique;
use App\UserSite;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class HistoriqueController extends Controller
{
    public function site($site_id, $begin, $end){
        $date_begin = date_create_from_format('Y-m-d', $begin);
        if($date_begin >= ((new \DateTime)->setTime(0, 0, 0))->sub(new \DateInterval('P1D')))
			$historiques =  DB::select("SELECT a.idademco, a.prom, a.dtarrived as dateArrived, a.codeTevent, a.eventQualify, 
                ev.Description as alarm, a.zones as numZone, z.nomZone, a.sip, a.panel_user, a.panel_area
                FROM ademcotemp a
                LEFT JOIN sites st on st.idsite = a.site_id
                LEFT JOIN eventcode ev on ev.code = a.codeTevent
                LEFT JOIN zonesites z on z.idsite = st.idsite and z.numZone = a.zones
                WHERE a.pointeuse_id is null and a.site_id = '". $site_id ."' and a.dtarrived between '". $begin ." 00:00:00' and '". $end ." 23:59:59'
                ORDER BY a.dtarrived desc, idademco desc");
        else
            $historiques =  DB::select("SELECT a.idademco, a.prom, a.dtarrived as dateArrived, a.codeTevent, a.eventQualify, 
                ev.Description as alarm, a.zones as numZone, z.nomZone, a.sip, a.panel_user, a.panel_area
                FROM ademcolog a
                LEFT JOIN sites st on st.idsite = a.site_id
                LEFT JOIN eventcode ev on ev.code = a.codeTevent
                LEFT JOIN zonesites z on z.idsite = st.idsite and z.numZone = a.zones
                WHERE a.pointeuse_id is null and a.site_id = '". $site_id ."' and a.dtarrived between '". $begin ." 00:00:00' and '". $end ." 23:59:59'
                ORDER BY a.dtarrived desc, idademco desc");
        return response()->json($historiques);
    }
    public function site_client($site_id, $date, Request $request){
        if(UserSite::where("user_id", $request->user()->id)->where("site_id", $site_id)->first() == null)
            return response()->json([
                'message' => 'Unauthorized'
            ],401);
        else {
            $date_begin = date_create_from_format('Y-m-d', $date);
            if($date_begin >= ((new \DateTime)->setTime(0, 0, 0))->sub(new \DateInterval('P1D')))
                $historiques =  DB::select("SELECT a.idademco, a.prom, a.dtarrived as dateArrived, a.codeTevent, a.eventQualify, ev.Description as alarm, a.zones as numZone, z.nomZone
                    FROM ademcotemp a
                    LEFT JOIN sites st on st.idsite = a.site_id
                    LEFT JOIN eventcode ev on ev.code = a.codeTevent
                    LEFT JOIN zonesites z on z.idsite = st.idsite and z.numZone = a.zones
                    WHERE a.pointeuse_id is null and a.site_id = '". $site_id ."' and a.dtarrived between '". $date ." 00:00:00' and '". $date ." 23:59:59'
                    AND a.codeTevent != 1000
                    ORDER BY a.dtarrived desc, idademco desc");
            else
                $historiques =  DB::select("SELECT a.idademco, a.prom, a.dtarrived as dateArrived, a.codeTevent, a.eventQualify, ev.Description as alarm, a.zones as numZone, z.nomZone
                    FROM ademcolog a
                    LEFT JOIN sites st on st.idsite = a.site_id
                    LEFT JOIN eventcode ev on ev.code = a.codeTevent
                    LEFT JOIN zonesites z on z.idsite = st.idsite and z.numZone = a.zones
                    WHERE a.pointeuse_id is null and a.site_id = '". $site_id ."' and a.dtarrived between '". $date ." 00:00:00' and '". $date ." 23:59:59'
                    AND a.codeTevent != 1000
                    ORDER BY a.dtarrived desc, idademco desc");
            return response()->json($historiques);
        }
    }
    public function pointeuse($pointeuse_id, $begin, $end, Request $request){
        $date_begin = date_create_from_format('Y-m-d', $begin);
        if($date_begin >= ((new \DateTime)->setTime(0, 0, 0))->sub(new \DateInterval('P1D')))
			$historiques =  DB::select("SELECT a.idademco, a.prom, a.dtarrived as dateArrived, a.codeTevent, a.eventQualify, ev.Description as alarm,
                ag.id as agent_id, ag.nom as nom_agent, ag.societe_id, ag.numero_employe, ag.num_emp_soit, ag.numero_stagiaire
                FROM ademcotemp a
                LEFT JOIN sites st on st.idsite = a.site_id
                LEFT JOIN eventcode ev on ev.code = a.codeTevent
                LEFT JOIN agents ag on ag.id = a.agent_id
                WHERE a.pointeuse_id = '". $pointeuse_id ."' and a.dtarrived between '". $begin ." 00:00:00' and '". $end ." 23:59:59'
                ORDER BY a.dtarrived desc, idademco desc");
        else
            $historiques =  DB::select("SELECT a.idademco, a.prom, a.dtarrived as dateArrived, a.codeTevent, a.eventQualify, ev.Description as alarm,
                ag.id as agent_id, ag.nom as nom_agent, ag.societe_id, ag.numero_employe, ag.num_emp_soit, ag.numero_stagiaire
                FROM ademcolog a
                LEFT JOIN sites st on st.idsite = a.site_id
                LEFT JOIN eventcode ev on ev.code = a.codeTevent
                LEFT JOIN agents ag on ag.id = a.agent_id
                WHERE a.pointeuse_id = '". $pointeuse_id ."' and a.dtarrived between '". $begin ." 00:00:00' and '". $end ." 23:59:59'
                ORDER BY a.dtarrived desc, idademco desc");
        return response()->json($historiques);
    }
}