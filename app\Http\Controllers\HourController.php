<?php

namespace App\Http\Controllers;

use App\Http\Util\PaieUtil;
use App\Agent;
use App\Societe;
use App\Paie;
use App\JourFerie;
use App\Pointage;
use App\Fonction;
use App\Agence;
use App\HistoriquePointage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class HourController extends Controller
{
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
    }

    public function index(Request $request)
    {
        $query_paie = "SELECT p.id, p.agent_id, p.site_id, p.month, p.year, p.group_id, p.confirm_hour, p.confirm, p.societe_id,
                p.sal_forfait, p.heure_trav, p.heure_dim, p.heure_nuit, p.heure_ferie, p.heure_reclam, s.nom as 'site'
            from paies p
            left join sites s on s.idsite = p.site_id
            WHERE p.confirm_hour is not null and p.confirm_hour = 1 and
            p.month = ? and p.year = ? and p.group_id = ?";
        $query_agent = "SELECT a.id, a.nom, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.sal_forfait,
                a.nb_heure_contrat, a.date_embauche, a.date_confirmation, a.date_conf_soit,
                a.societe_id, s.idsite as 'site_id', s.nom as 'site', f.libelle as 'fonction', agc.libelle as 'agence'
            FROM agents a
            LEFT JOIN agences agc ON agc.id = a.agence_id
            LEFT JOIN sites s on s.idsite = a.site_id
            LEFT JOIN fonctions f on f.id = a.fonction_id
            WHERE a.id in ";
        $query_agent_without_paie = "SELECT a.id, a.nom, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.sal_forfait,
                a.nb_heure_contrat, a.date_embauche, a.date_confirmation, a.date_conf_soit,
                a.societe_id, s.idsite as 'site_id', s.nom as 'site', f.libelle as 'fonction', agc.libelle as 'agence'
            FROM agents a
            LEFT JOIN agences agc ON agc.id = a.agence_id
            LEFT JOIN sites s on s.idsite = a.site_id
            LEFT JOIN fonctions f on f.id = a.fonction_id
            WHERE (a.date_embauche < ? or a.date_confirmation < ? or a.date_conf_soit < ?)
            and (a.soft_delete is null or a.soft_delete = 0)
            and s.group_pointage_id = ? ";
        $response = PaieUtil::allRequest('hour', $request, $query_paie, $query_agent, $query_agent_without_paie);
        if (!$response)
            return response()->json(false);
        return response()->json($response);
    }

    public function show($agent_id, $year, $month, Request $request)
    {
        if (in_array($request->authRole, ['root', 'op', 'rh']))
            return response()->json(PaieUtil::showRequest('hour', $agent_id, $year, $month, $request));
        return response()->json(false);
    }

    function confirm($agent_id, $year, $month, Request $request)
    {
        if (in_array($request->authRole, ['root', 'op'])) {
            $paie = Paie::where('agent_id', $agent_id)
                ->where('year', $year)
                ->where('month', $month)
                ->first();
            $array_agent = DB::select("SELECT a.id, g.id as 'group_id', g.day as 'group_day', s.idsite as 'site_id', a.sal_forfait
                FROM agents a
                LEFT JOIN sites s ON s.idsite = a.site_id
                LEFT JOIN group_pointage_sites g ON g.id = s.group_pointage_id
                WHERE a.id = ?", [$agent_id]);
            if ($array_agent != null && $array_agent[0] != null) {
                $agent = $array_agent[0];
                if ($agent->group_day == 20) {
                    $begin_date = (\DateTime::createFromFormat("Y-n-j H:i:s", $year . "-" . $month . "-" . $agent->group_day . " 00:00:00"))
                        ->sub(new \DateInterval('P1M'));
                    $end_date = (\DateTime::createFromFormat("Y-n-j H:i:s", $year . "-" . $month . "-" . $agent->group_day . " 23:00:00"))
                        ->sub(new \DateInterval('P1D'));
                } else {
                    $begin_date = (\DateTime::createFromFormat("Y-n-j H:i:s", $year . "-" . $month . "-" . $agent->group_day . " 00:00:00"));
                    $end_date = (\DateTime::createFromFormat("Y-n-j H:i:s", $year . "-" . $month . "-" . $agent->group_day . " 23:00:00"))
                        ->add(new \DateInterval('P1M'))->sub(new \DateInterval('P1D'));
                }

                if (PaieUtil::isConfirmable($agent->group_day, $month, $year)) {
                    $pointage_query = Pointage::where('agent_id', $agent_id)
                        ->where('date_pointage', '>', $begin_date)
                        ->where('date_pointage', '<', $end_date)
                        ->whereNull('paie_id')
                        ->whereNull('reclamation_id');
                    $pointages = $pointage_query->select('date_pointage')->get();
                    $jour_feries = JourFerie::select('date')
                        ->where('date', '>=', $begin_date)
                        ->where('date', '<=', $end_date)
                        ->get();
                    $heure_trav = count($pointages) * 12;
                    $heure_ferie = 0;
                    $heure_nuit = 0;
                    $heure_dim = 0;
                    if (!$agent->sal_forfait) {
                        foreach ($pointages as $ptg) {
                            $pointage = \DateTime::createFromFormat("Y-m-d H:i:s", $ptg->date_pointage);
                            if ($pointage->format('H:i:s') == "18:00:00")
                                $heure_nuit += 12;
                            if (date('N', strtotime($ptg->date_pointage)) == 7)
                                $heure_dim += 12;
                            foreach ($jour_feries as $j) {
                                if ($j->date == $pointage->format('Y-m-d'))
                                    $heure_ferie += 12;
                            }
                        }
                    }
                    $reclamation_query = Pointage::where('agent_id', $agent_id)
                        ->where('date_pointage', '>', (clone $begin_date)->sub(new \DateInterval('P1M'))->format('Y-m-d H:i:s'))
                        ->where('date_pointage', '<', $begin_date)
                        ->whereNull('paie_id')
                        ->whereNull('reclamation_id');
                    $reclamations = $reclamation_query->select('id')->get();
                    $heure_reclam = count($reclamations) * 12;

                    if ($paie == null || ($paie != null && !$paie->confirm_hour && !$paie->confirm)) {
                        if ($paie == null) {
                            $paie = new Paie();
                            $paie->month = $month;
                            $paie->year = $year;
                            $paie->agent_id = $agent->id;
                        }
                        $paie->sal_forfait = $agent->sal_forfait;
                        $paie->day = $agent->group_day;
                        $paie->group_id = $agent->group_id;
                        $paie->site_id = $agent->site_id;
                        $paie->confirm_hour = 1;
                        $paie->heure_trav = $heure_trav;
                        $paie->heure_dim = $heure_dim;
                        $paie->heure_nuit = $heure_nuit;
                        $paie->heure_ferie = $heure_ferie;
                        $paie->heure_reclam = $heure_reclam;
                        $saveOk = $paie->save();
                        if ($saveOk) {
                            $pointage_query->update(['paie_id' => $paie->id]);
                            $reclamation_query->update(['reclamation_id' => $paie->id]);
                        }
                        return response()->json($saveOk);
                    } else {
                        $message = 'already_confirm';
                        return response()->json(compact('message', 'paie'));
                    }
                }
            }
        }
        return response()->json(false);
    }

    function cancel_confirmation($paie_id, Request $request)
    {
        if (in_array($request->authRole, ['root', 'op'])) {
            $paie = Paie::find($paie_id);
            if ($paie->confirm_hour && !$paie->confirm && PaieUtil::isConfirmable($paie->day, $paie->month, $paie->year)) {
                $updateOk = Pointage::where('paie_id', $paie->id)->orWhere('reclamation_id', $paie->id)
                    ->update(['paie_id' => null, 'reclamation_id' => null]);
                if ($updateOk) {
                    $paie->confirm_hour = 0;
                    $updateOk = $paie->save();
                }
                return response()->json($updateOk);
            }
        }
        return response()->json(false);
    }

    function update($agent_id, $year, $month, Request $request)
    {
        if (in_array($request->authRole, ['root', 'op'])) {
            $agent = DB::select("SELECT a.id, s.group_pointage_id as 'group_id' FROM agents a
                left join sites s on s.idsite = a.site_id
                left join group_pointage_sites g ON g.id = s.group_pointage_id
                where a.id = ?", [$agent_id])[0];
            $paie = Paie::where('agent_id', $agent_id)->where('year', $year)->where('month', $month)->first();
            if ($paie == null) {
                $paie = new Paie();
                $paie->agent_id = $agent_id;
                $paie->year = $year;
                $paie->month = $month;
                $paie->group_id = $agent->group_id;
            }
            $paie->edited = true;
            $paie->perdiem = $request->perdiem;
            $paie->part_variable = $request->part_variable;
            $paie->idm_depl = $request->idm_depl;
            $paie->save();
            return response()->json($paie);
        } else return response()->json(false);
    }

    function getCorrectInterval($request)
    {
        $interval = PaieUtil::getIntervalByAgent($request->agent_id, 0, 0);
        $month = (clone $interval['end'])->sub(new \DateInterval("P1M"))->format('n');
        $year = (clone $interval['end'])->sub(new \DateInterval("P1M"))->format('Y');
        $paie = Paie::where('confirm_hour', 1)
            ->where('agent_id', $request->agent_id)
            ->where('month', $month)
            ->where('year', $year)
            ->first();
        if ($request->is_after_confirmable) {
            if ($request->reclamation) {
                if ($paie == null)
                    return false;
                $begin = $interval['begin']->sub(new \DateInterval("P1M"));
                $end = $interval['begin']->sub(new \DateInterval("P1D"));
            } else {
                $begin = $interval['begin'];
                $end = $interval['end'];
            }
        } else {
            if ($paie != null)
                return false;
            if ($request->reclamation) {
                $begin = $interval['begin']->sub(new \DateInterval("P2M"));
                $end = $interval['end']->sub(new \DateInterval("P2M"));
            } else {
                $begin = $interval['begin']->sub(new \DateInterval("P1M"));
                $end = $interval['end']->sub(new \DateInterval("P1M"));
            }
        }
        return compact('begin', 'end');
    }

    function add_pointage(Request $request)
    {
        if (in_array($request->authRole, ["root", "op"])) {
            $correct_interval = $this->getCorrectInterval($request);
            if (!$correct_interval)
                return response()->json(false);
            $begin = $correct_interval['begin'];
            $end = $correct_interval['end'];
            $month = $end->format('n');
            $year = $end->format('Y');

            $paie = Paie::select('confirm_hour')
                ->where('agent_id', $request->agent_id)
                ->where('month', $month)
                ->where('year', $year)
                ->first();
            if (($paie == null || ($paie != null && $paie->confirm_hour == 0))
                && (in_array($request->horaire, ["j", "n", "jn"]))
            ) {
                if (in_array($request->horaire, ["j", "n"])) {
                    if ($request->horaire == "j")
                        $horaire = "07:00:00";
                    else $horaire = "18:00:00";
                    $date_pointage_d = $request->date_pointage . ' ' . $horaire;
                    $pointage = Pointage::where('agent_id', $request->agent_id)
                        ->where('date_pointage', $date_pointage_d)
                        ->first();
                    $date_pointage = \DateTime::createFromFormat("Y-m-d H:i:s", $date_pointage_d);
                    if ($pointage != null)
                        return response()->json(["message" => "already_exist"]);
                    else if ($date_pointage < $begin || $date_pointage > $end || $date_pointage > (new \DateTime())->setTime(0, 0))
                        return response()->json(["message" => "out_of_interval"]);
                    else {
                        $pointage = new Pointage();
                        $pointage->agent_id = $request->agent_id;
                        $pointage->site_id = $request->site_id;
                        $pointage->date_pointage = $date_pointage_d;
                        $pointage->editable = 1;
                        $pointage->user_id = $request->authId;
                        $pointage->last_update = now();
                        return response()->json($pointage->save());
                    }
                } else {
                    $horaire = "07:00:00";
                    $date_pointage_j = $request->date_pointage . ' ' . $horaire;
                    $pointage = Pointage::where('agent_id', $request->agent_id)
                        ->where('date_pointage', $date_pointage_j)
                        ->first();
                    $date_pointage = \DateTime::createFromFormat("Y-m-d H:i:s", $date_pointage_j);
                    if ($pointage != null)
                        return response()->json(["message" => "already_exist"]);
                    else if ($date_pointage < $begin || $date_pointage > $end || $date_pointage > (new \DateTime())->setTime(0, 0))
                        return response()->json(["message" => "out_of_interval"]);

                    $horaire = "18:00:00";
                    $date_pointage_n = $request->date_pointage . ' ' . $horaire;
                    $pointage = Pointage::where('agent_id', $request->agent_id)
                        ->where('date_pointage', $date_pointage_n)
                        ->first();
                    $date_pointage = \DateTime::createFromFormat("Y-m-d H:i:s", $date_pointage_n);
                    if ($pointage != null)
                        return response()->json(["message" => "already_exist"]);
                    else if ($date_pointage < $begin || $date_pointage > $end || $date_pointage > (new \DateTime())->setTime(0, 0))
                        return response()->json(["message" => "out_of_interval"]);

                    $pointage = new Pointage();
                    $pointage->agent_id = $request->agent_id;
                    $pointage->site_id = $request->site_id;
                    $pointage->date_pointage = $date_pointage_j;
                    $pointage->editable = 1;
                    $pointage->last_update = now();
                    $pointage->save();
                    $pointage = new Pointage();
                    $pointage->agent_id = $request->agent_id;
                    $pointage->site_id = $request->site_id;
                    $pointage->date_pointage = $date_pointage_n;
                    $pointage->editable = 1;
                    $pointage->last_update = now();
                    return response()->json($pointage->save());
                }
            }
        }
        return response()->json(false);
    }

    function update_pointage($pointage_id, Request $request)
    {
        if (in_array($request->authRole, ['root', 'op'])) {
            $correct_interval = $this->getCorrectInterval($request);
            if (!$correct_interval)
                return response()->json(false);
            $begin = $correct_interval['begin'];
            $end = $correct_interval['end'];
            $month = $end->format('n');
            $year = $end->format('Y');

            $paie = Paie::select('confirm_hour')
                ->where('agent_id', $request->agent_id)
                ->where('month', $month)
                ->where('year', $year)
                ->first();
            if (($paie == null || ($paie != null && $paie->confirm_hour == 0))
                && (in_array($request->horaire, ["j", "n"]))
            ) {
                if ($request->horaire == "") $horaire = "07:00:00";
                else $horaire = "18:00:00";
                $request->date_pointage = $request->date_pointage . ' ' . $horaire;
                $pointage = Pointage::where('id', '<>', $pointage_id)
                    ->where('agent_id', $request->agent_id)
                    ->where('date_pointage', $request->date_pointage)
                    ->first();
                $date_pointage = \DateTime::createFromFormat("Y-m-d H:i:s", $request->date_pointage);
                if ($pointage != null)
                    return response()->json(["message" => "already_exist"]);
                else if ($date_pointage < $begin || $date_pointage > $end || $date_pointage > (new \DateTime())->setTime(0, 0))
                    return response()->json(["message" => "out_of_interval"]);
                else {
                    $pointage = Pointage::find($pointage_id);
                    $pointage->agent_id = $request->agent_id;
                    $pointage->site_id = $request->site_id;
                    $pointage->date_pointage = $request->date_pointage;
                    $pointage->user_id = $request->authId;
                    $pointage->last_update = now();
                    return response()->json($pointage->save());
                }
            }
        }
        return response()->json(false);
    }

    function delete_pointage($pointage_id, Request $request)
    {
        if (in_array($request->authRole, ['root', 'resp'])) {
            $pointage = Pointage::find($pointage_id);
            $pointage->soft_delete = true;

            $historique = new HistoriquePointage();
            $historique->pointage_id = $pointage_id;
            $historique->user_id = $request->authId;
            $historique->objet = "Mise en archive du pointage.";
            $historique->detail = null;
            $historique->created_at = now();
            $historique->updated_at = now();
            $historique->save();

            return response()->json($pointage->save());
        }
        return response()->json(false);
    }
    function restore_pointage($pointage_id, Request $request)
    {
        if (in_array($request->authRole, ['root', 'resp'])) {
            $pointage = Pointage::find($pointage_id);
            $pointage->soft_delete = false;

            $historique = new HistoriquePointage();
            $historique->pointage_id = $pointage_id;
            $historique->user_id = $request->authId;
            $historique->objet = "pointage restauré";
            $historique->detail = null;
            $historique->created_at = now();
            $historique->updated_at = now();
            $historique->save();

            return response()->json($pointage->save());
        }
        return response()->json(false);
    }
}
