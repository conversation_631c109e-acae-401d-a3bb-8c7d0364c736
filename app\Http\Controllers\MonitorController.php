<?php

namespace App\Http\Controllers;

use App\GroupSim;
use App\GroupSite;
use App\Site;
use App\SimGateway;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class MonitorController extends Controller
{
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
    }

    public function repartition(Request $request){
        $transmitters = DB::select("SELECT s.transmitter, count(s.prom) as nb_site, s.port,
            sg.sim_group_id, g.nom as 'group', sg.gateway_id, gt.ip as 'gateway'
            FROM sites s
            LEFT JOIN sim_gateways sg ON sg.numero = s.transmitter
            LEFT JOIN group_sims g ON g.id = sg.sim_group_id
            LEFT JOIN gateways gt ON gt.id = sg.gateway_id
            WHERE (soft_delete is null or soft_delete = 0)
            and (without_system is null or without_system = 0)
            and (prom is not null and trim(prom) != '')
            GROUP BY s.transmitter
            ORDER BY s.transmitter ASC");
        return response()->json($transmitters);
    }
    public function show(Request $request, $transmitter){
        $sites = DB::select("SELECT s.idsite, s.prom, s.nom, s.date_last_signal, s.idcentrale,
            s.sms, s.vigilance, h.nom as 'horaire', s.transmitter, s.correct_transmitter, c.nom as 'centrale', 
            g.sim_group_id as 'g_id', s1.sim_group_id as 't_id', s2.sim_group_id as 'ct_id'
            FROM sites s
            LEFT JOIN centrales c ON c.idcentrale = s.idcentrale
            LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id
            LEFT JOIN group_sites g ON g.id = s.group_id
            LEFT JOIN sim_gateways s1 on s1.numero = s.transmitter
            LEFT JOIN sim_gateways s2 on s2.numero = s.correct_transmitter
            WHERE prom is not null and
            (soft_delete is null or soft_delete = 0) and
            (without_system is null or without_system = 0) and
            transmitter = ?", [$transmitter]);
        /*if($transmitter == 'null')
            $sites = $query->whereNull('transmitter')->get();
        else $sites = $query->where('transmitter', $transmitter)->get();*/
        return response()->json($sites);
    }
    public function configuration(){
        $group_sims = GroupSim::orderBy('nom')->get();
        $group_sites = DB::select("SELECT g1.id, g1.nom, g1.vigilance_group_id, g2.nom as 'group_2',
            g1.sim_group_id, gs.nom as 'group_sim'
            FROM group_sites g1
            LEFT join group_sites g2 on g1.vigilance_group_id = g2.id
            LEFT JOIN group_sims gs ON gs.id = g1.sim_group_id
            ORDER BY g1.nom");
        return response()->json(compact('group_sims', 'group_sites'));
    }
}