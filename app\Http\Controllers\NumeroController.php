<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class NumeroController extends Controller
{
    public function index(Request $request)
    {
        $search = '%' . $request->search . '%';

        $contacts = DB::select("SELECT numero, c.nom, c.prenom, s.nom as site FROM numeros
        LEFT JOIN contacts c ON numeros.id_contact = c.idContact
        LEFT JOIN sites s ON numeros.id_site = s.idsite
        WHERE numero LIKE ?
        LIMIT ?, 50
        GROUP BY numeros.id
        ", [
            $search,
            $request->offset
        ]);

        return response()->json($contacts);
    }
}
