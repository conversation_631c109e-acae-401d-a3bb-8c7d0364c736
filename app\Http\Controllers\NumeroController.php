<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class NumeroController extends Controller
{
    public function index(Request $request)
    {
        $search = '%' . $request->search . '%';

        $contacts = DB::select("SELECT numero, c.nom, c.prenom, s.nom as site FROM numeros
        LEFT JOIN contacts c ON numeros.id_contact = c.idContact
        LEFT JOIN sites s ON numeros.id_site = s.idsite
        WHERE numero LIKE ?
        LIMIT ?, 50
        GROUP BY numeros.id
        ", [
            $search,
            $request->offset
        ]);

        return response()->json($contacts);
    }

    public function comprehensive(Request $request)
    {
        $search = '%' . ($request->search ?? '') . '%';
        $offset = $request->offset ?? 0;

        // Get numero data with comprehensive related information
        $numeros = DB::select("
            SELECT
                n.id,
                n.numero,
                n.id_contact,
                n.id_site,
                c.nom as contact_nom,
                c.prenom as contact_prenom,
                c.adress<PERSON> as contact_adresse,
                s.nom as site_nom,
                s.prom as site_prom,
                s.adresse as site_adresse,
                CONCAT(COALESCE(c.nom, ''), ' ', COALESCE(c.prenom, '')) as info_description
            FROM numeros n
            LEFT JOIN contacts c ON n.id_contact = c.idContact
            LEFT JOIN sites s ON n.id_site = s.idsite
            WHERE (n.numero LIKE ? OR c.nom LIKE ? OR c.prenom LIKE ? OR s.nom LIKE ?)
            AND (n.soft_delete IS NULL OR n.soft_delete = 0)
            AND (c.soft_delete IS NULL OR c.soft_delete = 0)
            AND (s.soft_delete IS NULL OR s.soft_delete = 0)
            ORDER BY n.numero ASC
            LIMIT ?, 50
        ", [
            $search,
            $search,
            $search,
            $search,
            $offset
        ]);

        // Get contact and site IDs for fetching related data
        $contactIds = array_unique(array_filter(array_map(function ($numero) {
            return $numero->id_contact;
        }, $numeros)));

        $siteIds = array_unique(array_filter(array_map(function ($numero) {
            return $numero->id_site;
        }, $numeros)));

        // Get habilites data for the contacts and sites
        $habilites = [];
        if (!empty($contactIds) && !empty($siteIds)) {
            $habilites = DB::select("
                SELECT
                    h.idhabilite,
                    h.idcontact,
                    h.idsite,
                    h.quality,
                    h.password,
                    h.idordre,
                    c.nom as contact_nom,
                    c.prenom as contact_prenom,
                    s.nom as site_nom
                FROM habilites h
                LEFT JOIN contacts c ON h.idcontact = c.idContact
                LEFT JOIN sites s ON h.idsite = s.idsite
                WHERE h.idcontact IN (" . implode(',', array_fill(0, count($contactIds), '?')) . ")
                AND h.idsite IN (" . implode(',', array_fill(0, count($siteIds), '?')) . ")
                AND (h.soft_delete IS NULL OR h.soft_delete = 0)
                ORDER BY h.idordre ASC
            ", array_merge($contactIds, $siteIds));
        }

        // Organize the data
        $result = array_map(function ($numero) use ($habilites) {
            $numero->habilites = array_filter($habilites, function ($habilite) use ($numero) {
                return $habilite->idcontact == $numero->id_contact && $habilite->idsite == $numero->id_site;
            });
            return $numero;
        }, $numeros);

        return response()->json($result);
    }
}
