<?php

namespace App\Http\Controllers;


use App\Http\Util\PaieUtil;
use App\Paie;
use App\Prime;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PaieController extends Controller
{
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
    }

    public function index(Request $request){
        $query_paie = "SELECT p.id, p.agent_id, p.month, p.year, p.group_id, 
                p.societe_id, p.site_id, p.fonction_id, p.sal_forfait, p.sal_base, p.nb_heure_contrat, p.nb_heure_convenu,
                p.confirm_hour, p.confirm, 
                p.prime_ex, p.prime_div, p.prime_ass, p.prime_resp, p.prime_entr, p.prime_anc, 
                p.heure_trav, p.heure_dim, p.heure_nuit, p.heure_ferie, p.heure_reclam,
                s.nom as 'site', f.libelle as 'fonction', p.perdiem, p.part_variable, p.idm_depl, p.edited
            from paies p 
            LEFT JOIN sites s ON s.idsite = p.site_id
            LEFT JOIN fonctions f ON f.id = p.fonction_id
            WHERE p.confirm_hour is not null and p.confirm = 1 and
            p.month = ? and p.year = ? and p.group_id = ?";
        $query_agent = "SELECT a.id, a.nom, a.numero_stagiaire, a.numero_employe, a.num_emp_soit,
                a.societe_id, a.nb_heure_contrat, a.nb_heure_convenu, a.sal_forfait, a.sal_base, a.date_embauche, a.date_confirmation, a.date_conf_soit,
                s.nom as 'site', f.libelle as 'fonction', agc.libelle as 'agence', a.perdiem, a.part_variable, a.idm_depl, a.prime_anc
            FROM agents a
            LEFT JOIN agences agc ON agc.id = a.agence_id
            LEFT JOIN sites s on s.idsite = a.site_id
            LEFT JOIN fonctions f on f.id = a.fonction_id
            WHERE a.id in ";
        $query_agent_without_paie = "SELECT a.id, a.nom, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, 
                a.societe_id, a.nb_heure_contrat, a.nb_heure_convenu, a.sal_forfait, a.sal_base, a.date_embauche, a.date_confirmation, a.date_conf_soit,
                s.nom as 'site', f.libelle as 'fonction', agc.libelle as 'agence', a.perdiem, a.part_variable, a.idm_depl, a.prime_anc
            FROM agents a
            LEFT JOIN agences agc ON agc.id = a.agence_id
            LEFT JOIN sites s on s.idsite = a.site_id
            LEFT JOIN fonctions f on f.id = a.fonction_id
            WHERE (a.date_embauche < ? or a.date_confirmation < ? or a.date_conf_soit < ?) 
            and (a.soft_delete is null or a.soft_delete = 0) 
            and s.group_pointage_id = ? ";
        $response = PaieUtil::allRequest('paie', $request, $query_paie, $query_agent, $query_agent_without_paie);
        if(!$response)
            return response()->json(false);
        return response()->json($response);
    }
    
    public function show($agent_id, $year, $month, Request $request){
        if($request->authRole == 'rh' || $request->authRole == 'op' || $request->authRole == 'root')
            return response()->json(PaieUtil::showRequest('paie', $agent_id, $year, $month, $request));
        return response()->json(false);
    }

    function confirm($agent_id, $year, $month, Request $request){
        if($request->authRole == 'root' || $request->authRole == 'rh'){
            $paie = Paie::where('agent_id', $agent_id)
                ->where('year', $year)
                ->where('month', $month)
                ->first();
            if($paie != null && $paie->confirm_hour && !$paie->confirm){
                $array_agent = DB::select("SELECT a.id, a.site_id, a.fonction_id, a.nb_heure_contrat, a.nb_heure_convenu, a.societe_id,
                    a.sal_forfait, a.sal_base, a.perdiem, a.part_variable, a.idm_depl, a.prime_anc
                    FROM agents a
                    WHERE a.id = ?", [$agent_id]);
                if($array_agent != null && $array_agent[0] != null){
                    $agent = $array_agent[0];
                    if($paie->day == 20){
                        $begin_date = (\DateTime::createFromFormat("Y-n-j H:i:s", $year ."-". $month ."-". $paie->day . " 00:00:00"))
                            ->sub(new \DateInterval('P1M'));
                        $end_date = (\DateTime::createFromFormat("Y-n-j H:i:s", $year ."-". $month ."-". $paie->day . " 23:00:00"))
                            ->sub(new \DateInterval('P1D'));
                    }
                    else {
                        $begin_date = (\DateTime::createFromFormat("Y-n-j H:i:s", $year ."-". $month ."-". $paie->day . " 00:00:00"));
                        $end_date = (\DateTime::createFromFormat("Y-n-j H:i:s", $year ."-". $month ."-". $paie->day . " 23:00:00"))
                            ->add(new \DateInterval('P1M'))->sub(new \DateInterval('P1D'));
                    }

                    if(PaieUtil::isConfirmable($paie->day, $month, $year)){
                        $paie->fonction_id = $agent->fonction_id;
                        $paie->site_id = $agent->site_id;
                        $paie->societe_id = $agent->societe_id;
                        $paie->sal_base = $agent->sal_base;
                        $paie->nb_heure_contrat = $agent->nb_heure_contrat;
                        $paie->nb_heure_convenu = $agent->nb_heure_convenu;
                        $paie->confirm = 1;
                        if(!$paie->edited){
                            $paie->part_variable = $agent->part_variable;
                            $paie->perdiem = $agent->perdiem;
                            $paie->idm_depl = $agent->idm_depl;
                            $paie->prime_anc = $agent->prime_anc;
                        }
                        $primes = DB::select("SELECT p.montant, t.code FROM primes p 
                            LEFT JOIN type_primes t ON t.id = p.type_id
                            WHERE p.agent_id = ? and p.month = ? and p.year = ?", [$agent_id, $month, $year]);
                        $prime_ex = 0;
                        $prime_div = 0;
                        $prime_ass = 0;
                        $prime_resp = 0;
                        $prime_entr = 0;
                        foreach($primes as $p){
                            if($p->code == 'prime_ex')
                                $prime_ex += $p->montant;
                            else if($p->code == 'prime_div')
                                $prime_div += $p->montant;
                            else if($p->code == 'prime_ass')
                                $prime_ass += $p->montant;
                            else if($p->code == 'prime_resp')
                                $prime_resp += $p->montant;
                            else if($p->code == 'prime_entr')
                                $prime_entr += $p->montant;
                        }
                        $paie->prime_ex = $prime_ex;
                        $paie->prime_div = $prime_div;
                        $paie->prime_ass = $prime_ass;
                        $paie->prime_resp = $prime_resp;
                        $paie->prime_entr = $prime_entr;
                        $saveOk = $paie->save();
                        if($saveOk){
                            Prime::where('agent_id', $agent_id)
                                ->where('year', $year)
                                ->where('month', $month)
                                ->update(["paie_id" => $paie->id]);
                        }
                        return response()->json($saveOk);
                    }
                    else{
                        $message = 'already_confirm';
                        return response()->json(compact('message', 'paie'));
                    }
                }
            }
        }
        return response()->json(false);
    }

    function update($agent_id, $year, $month, Request $request){
        if($request->authRole == 'root' || $request->authRole == 'rh'){
            $agent = DB::select("SELECT a.id, s.group_pointage_id as 'group_id' FROM agents a 
                left join sites s on s.idsite = a.site_id
                left join group_pointage_sites g ON g.id = s.group_pointage_id
                where a.id = ?", [$agent_id])[0];
            $paie = Paie::where('agent_id', $agent_id)->where('year', $year)->where('month', $month)->first();
            if($paie == null){
                $paie = new Paie();
                $paie->agent_id = $agent_id;
                $paie->year = $year;
                $paie->month = $month;
                $paie->group_id = $agent->group_id;
            }
            $paie->edited = true;
            $paie->perdiem = $request->perdiem;
            $paie->part_variable = $request->part_variable;
            $paie->idm_depl = $request->idm_depl;
            $paie->prime_anc = $request->prime_anc;
            $paie->save();
            return response()->json($paie);
        }
        else return response()->json(false);
    }
}