<?php

namespace App\Http\Controllers;

use App\Log;
use App\User;
use App\Habilite;
use App\Verouillage;
use App\Historique;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\JourFerie;

class PanicController extends Controller
{
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
    }

    public function getCurrentVigilanceDate()
    {
        $current_date = new \DateTime;
        $date_vigilance = new \DateTime;
        $date_vigilance->setTime(0, 0, 0);
        while (
            (!(new \DateTime >= (new \DateTime)->setTime(06, 0, 0) && new \DateTime <= (new \DateTime)->setTime(18, 0, 0)) &&
                ((clone $date_vigilance)->sub(new \DateInterval('PT10M')) < (new \DateTime) && ((new \DateTime) >= (clone $date_vigilance)->add(new \DateInterval('PT20M')))))
            || ((new \DateTime >= (new \DateTime)->setTime(06, 0, 0) && new \DateTime <= (new \DateTime)->setTime(18, 0, 0)) &&
                ((clone $date_vigilance)->sub(new \DateInterval('PT10M')) < (new \DateTime) && ((new \DateTime) >= (clone $date_vigilance)->add(new \DateInterval('PT50M')))))
        ) {
            if (
                new \DateTime >= (new \DateTime)->setTime(06, 50, 0) &&
                new \DateTime <= (new \DateTime)->setTime(17, 50, 0)
            )
                $date_vigilance->add(new \DateInterval('PT1H'));
            else $date_vigilance->add(new \DateInterval('PT30M'));
        }
        return $date_vigilance;
    }

    public function findPanic(Request $request)
    {   
        $user = User::find($request->user_id);
        $userCtrl = new UserController();
        $response = [
            'datetime' => (new \DateTime())->format("Y-m-d H:i:s"),
            'alarm' => $userCtrl->getNbAlarm()
        ];


        if($user && $user->last_find_panic == null || new \DateTime($user->last_find_panic) < (new \DateTime)->sub(new \DateInterval('PT5S'))){
            $user->last_find_panic = (new \DateTime);
            $user->save();
            
            $panics = DB::select("SELECT min(a.idademco) as idademco, a.prom, a.site_id, a.eventQualify, ev.Description as alarm_name, st.nom as site_name,
                    min(a.dtarrived) as dtarrived, count(a.site_id) as nb_alarm , a.codeTevent as code, a.zones, z.nomZone, st.idsite, a.transmitter, intervention_id, resp_sup_id, superviseur_id
                FROM ademcomemlog a
                LEFT JOIN sites st on st.idsite = a.site_id
                LEFT JOIN eventcode ev on ev.code = a.codeTevent
                LEFT JOIN zonesites z on z.idsite = st.idsite and z.numZone = a.zones
                LEFT JOIN clients cl on cl.idclient = st.idClient
                WHERE a.IdUser is null
                    and st.idsite is not null
                    and (
                        (a.codeTevent = 301 and st.notify_outage = 1)
                        or (a.codeTevent in (" . env('ARM_CODES', '400,401,402,403,404,405,406,407,408,409,441,442,454,456') . ") and st.notify_arm = 1)
                        or a.codeTevent in (" . env('PANIC_CODES', '100,101,120,151,110,111,130,131,132,133,134,137,140') . ", 455)
                    )
                GROUP BY a.site_id, a.codeTevent, a.zones, a.eventQualify
                ORDER BY a.dtarrived ASC");
            if (count($panics) > 0) {
                if ($request->lastPanicId) {
                    foreach ($panics as $panic) {
                        if ($panic->idademco == $request->lastPanicId) {
                            $intervention = DB::select("SELECT s.idsite as id, s.nom, GROUP_CONCAT(DISTINCT n.numero SEPARATOR ', ') as 'phone_agent' FROM sites s
                                LEFT JOIN group_intervention_sites g ON g.id = s.group_intervention_id
                                LEFT JOIN numeros n ON n.id_site = s.idsite
                                WHERE s.intervention is not null and s.intervention = 1
                                AND s.idsite = ?", [$panic->intervention_id]);
                            $superviseur = DB::select("SELECT name, COALESCE(flotte, '') as flotte FROM admin_users WHERE id = ?", [$panic->superviseur_id]);
                            $manager = DB::select("SELECT name, COALESCE(flotte, '') as flotte FROM admin_users WHERE id = ?", [$panic->resp_sup_id]);
                            $habilites = DB::select("SELECT h.idhabilite,h.quality,h.password,c.nom,c.prenom,GROUP_CONCAT(COALESCE(n.numero, '') SEPARATOR ', ') AS phone,c.idContact
                                FROM habilites h
                                LEFT JOIN contacts c ON c.idContact = h.idcontact
                                LEFT JOIN numeros n ON n.id_contact = h.idcontact
                                WHERE idsite = ? AND h.soft_delete = 0
                                GROUP BY h.idhabilite, h.quality, h.password, c.nom, c.prenom, c.idContact
                                ORDER BY h.idordre ASC", [$panic->idsite]);
                            $panic->habilites = $habilites;
                            $panic->manager = $manager;
                            $panic->superviseur = $superviseur;
                            $panic->intervention = $intervention;
                            $response['current_panic'] = $panic; 
                            $response['panics'] = $panics;
                            break;
                        }
                    }
                }
                else {
                    $panic = $panics[0];
                    $intervention = DB::select("SELECT s.idsite as id, s.nom, GROUP_CONCAT(DISTINCT n.numero SEPARATOR ', ') as 'phone_agent' FROM sites s
                                    LEFT JOIN group_intervention_sites g ON g.id = s.group_intervention_id
                                    LEFT JOIN numeros n ON n.id_site = s.idsite
                                    WHERE s.intervention is not null and s.intervention = 1
                                    AND s.idsite = ?", [$panic->intervention_id]);
                    $superviseur = DB::select("SELECT name, COALESCE(flotte, '') as flotte FROM admin_users WHERE id = ?", [$panic->superviseur_id]);
                    $manager = DB::select("SELECT name, COALESCE(flotte, '') as flotte FROM admin_users WHERE id = ?", [$panic->resp_sup_id]);
                    $habilites = DB::select("SELECT h.idhabilite,h.quality,h.password,c.nom,c.prenom,GROUP_CONCAT(COALESCE(n.numero, '') SEPARATOR ', ') AS phone,c.idContact
                        FROM habilites h
                        LEFT JOIN contacts c ON c.idContact = h.idcontact
                        LEFT JOIN numeros n ON n.id_contact = h.idcontact
                        WHERE idsite = ? AND h.soft_delete = 0
                        GROUP BY h.idhabilite, h.quality, h.password, c.nom, c.prenom, c.idContact
                        ORDER BY h.idordre ASC", [$panic->idsite]);
                    $panic->habilites = $habilites;
                    $panic->manager = $manager;
                    $panic->superviseur = $superviseur;
                    $panic->intervention = $intervention;
                    $response['current_panic'] = $panic; 
                    $response['panics'] = $panics; 
                }
    
            } else if (!env('ESCAPE_DIAG')) {
                $sites = DB::select("SELECT idsite, nom, last_vigilance, date_last_signal,
                (TIMESTAMPDIFF(HOUR, date_last_signal, now()) > interval_test + 1) as 'manque',
                (TIMESTAMPDIFF(HOUR, last_vigilance, now()) >= 50) as 'bouton_panne'
                from sites
                where (soft_delete is null or soft_delete = 0)
                and (without_system is null or without_system = 0)
                and (centrale_drx is null or centrale_drx = 0)
                and (date_report_diag is null or TIMESTAMPDIFF(HOUR, date_report_diag, now()) >= 1)
                and (
                    TIMESTAMPDIFF(HOUR, date_last_signal, now()) > interval_test + 1
                    or (
                        (vigilance is not null and vigilance = 1) and
                        TIMESTAMPDIFF(HOUR, last_vigilance, now()) >= 50
                    )
                )");
                if ($sites != null) {
                    $panne = $sites[0];
                    $panne->count = count($sites);
                    $response['panne'] = $panne;
                }
            } else {
                if (new \DateTime > (new \DateTime)->setTime(6, 5, 0) && new \DateTime < (new \DateTime)->setTime(6, 20, 0)) {
                    $end = (new \DateTime)->setTime(6, 0, 0)->format('Y-m-d H:i:s');
                    $alarms = DB::select(
                        "SELECT a.idademco from ademcomemlog a where a.received_at <= ? and
                        a.codeTevent in (" . env('PANIC_CODES', '100,101,120,151,110,111,130,131,132,133,134,137,140') . ")",
                        [$end]
                    );
                    if (count($alarms) > 0)
                        $response['end_service'] = [
                                'date' => $end,
                                'nb_alarm' => count($alarms)
                            ];
                } else if (new \DateTime > (new \DateTime)->setTime(18, 5, 0) && new \DateTime < (new \DateTime)->setTime(18, 20, 0)) {
                    $end = (new \DateTime)->setTime(18, 0, 0)->format('Y-m-d H:i:s');
                    $alarms = DB::select(
                        "SELECT a.idademco from ademcomemlog a where a.received_at <= ? and
                        a.codeTevent in (" . env('PANIC_CODES', '100,101,120,151,110,130,131,132,133,134,137,140') . ")",
                        [$end]
                    );
                    if (count($alarms) > 0)
                        $response['end_service'] = [
                                'date' => $end,
                                'nb_alarm' => count($alarms)
                            ];
                }
            }
        }
        else {
            $response['waiting'] = true;
        }
        return response()->json($response);
    }

    public function traitePanic(Request $request, $id)
    {
        $log = Log::find($id);
        if ($log != null) {
            $historique = new Historique();
            $historique->prom = $log->prom;
            $historique->messageType = $log->messageType;
            $historique->eventQualify = $log->eventQualify;
            $historique->codeevent = $log->codeevent;
            $historique->partition = $log->partition;
            $historique->zones = $log->zones;
            $historique->istraite = 2;
            $historique->dtarrived = $log->dtarrived;
            $historique->dttraite = $log->dttraite;
            $historique->transmitter = $log->transmitter;
            $historique->codeTevent = $log->codeevent;
            $historique->IdUser = $log->IdUser;
            $historique->lastUpdate = $log->lastUpdate;
            $historique->Lat = $log->Lat;
            $historique->Lon = $log->Lon;
            $historique->IdUser = $request->user_id;
            $historique->dttraite = now();
            $historique->save();
            $log->delete();
            $habilites = json_decode($request->habilites);
            foreach ($habilites as $h) {
                $hab = Habilite::find($h->id);
                if ($hab) {
                    $hab->password = $h->password;
                    $hab->phone = $h->phone;
                    $hab->save();
                }
            }
            return response()->json(true);
        }
        return response()->json(false);
    }
    public function verouillerPanic(Request $request)
    {
        $prom = $request->prom;
        $code = $request->code;
        $user = $request->user;
        $verouillage = Verouillage::where('prom', $prom)->where('code', $code)->first();
        if ($verouillage == null) {
            Verouillage::where('user_id', $user)->delete();
            $new_verou = new Verouillage();
            $new_verou->prom = $prom;
            $new_verou->code = $code;
            $new_verou->user_id = $user;
            $new_verou->seen_at = now();
            $new_verou->save();
        }
        return response()->json(false);
    }
    public function show($id)
    {
        $panics = DB::select("SELECT a.idademco, a.prom, a.eventQualify, ev.Description as alarm_name, st.nom as site_name, st.commentaire,
            a.dtarrived, a.codeTevent as code, a.zones, z.nomZone, st.idsite, intervention_id, resp_sup_id, superviseur_id
            FROM ademcomemlog a
            LEFT JOIN sites st on st.idsite = a.site_id
            LEFT JOIN eventcode ev on ev.code = a.codeTevent
            LEFT JOIN zonesites z on z.idsite = st.idsite and z.numZone = a.zones
            LEFT JOIN clients cl on cl.idclient = st.idClient
            WHERE a.idademco = " . $id);
        if ($panics != null) {
            $panic = $panics[0];
            $nb_alarm = Log::select('idademco')
                ->where('prom', $panic->prom)
                ->where('zones', $panic->zones)
                ->where('codeTevent', $panic->code)
                ->where('eventQualify', $panic->eventQualify)
                ->count();
            $panic->nb_alarm = $nb_alarm;
            $intervention = DB::select("SELECT s.idsite as id, s.nom, GROUP_CONCAT(DISTINCT n.numero SEPARATOR ', ') as 'phone_agent' FROM sites s
                            LEFT JOIN group_intervention_sites g ON g.id = s.group_intervention_id
                            LEFT JOIN numeros n ON n.id_site = s.idsite
                            WHERE s.intervention is not null and s.intervention = 1
                            AND s.idsite = ?", [$panic->intervention_id]);
            $superviseur = DB::select("SELECT name, COALESCE(flotte, '') as flotte FROM admin_users WHERE id = ?", [$panic->superviseur_id]);
            $manager = DB::select("SELECT name, COALESCE(flotte, '') as flotte FROM admin_users WHERE id = ?", [$panic->resp_sup_id]);
            $habilites = DB::select("SELECT h.idhabilite,h.quality,h.password,c.nom,c.prenom,GROUP_CONCAT(COALESCE(n.numero, '') SEPARATOR ', ') AS phone,c.idContact
                FROM habilites h
                LEFT JOIN contacts c ON c.idContact = h.idcontact
                LEFT JOIN numeros n ON n.id_contact = h.idcontact
                WHERE idsite = ? AND h.soft_delete = 0
                GROUP BY h.idhabilite, h.quality, h.password, c.nom, c.prenom, c.idContact
                ORDER BY h.idordre ASC", [$panic->idsite]);
            $panic->habilites = $habilites;
            $panic->manager = $manager;
            $panic->superviseur = $superviseur;
            $panic->intervention = $intervention;
            return  response()->json($panic);
        }
        return response()->json(false);
    }
}
