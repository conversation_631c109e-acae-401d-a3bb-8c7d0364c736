<?php

namespace App\Http\Controllers;

use App\Pointage;
use App\Site;
use App\JourFerie;
use App\Agent;
use App\CommentPointage;
use App\Reclamation;
use App\HistoriquePointage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class PointageController extends Controller
{
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
    }
    public function get_current_date()
    {
        return response()->json((new \DateTime)->format('Y-m-d H:i:s'));
    }
    public function getDayOrNightDate()
    {
        if (
            new \DateTime >= (new \DateTime)->setTime(5, 50, 0) &&
            new \DateTime < (new \DateTime)->setTime(17, 50, 0)
        )
            return (new \DateTime)->setTime(07, 0, 0)->format('Y-m-d H:i:s');
        else if (new \DateTime < (new \DateTime)->setTime(5, 50, 0))
            return (new \DateTime)->setTime(18, 0, 0)->sub(new \DateInterval('P1D'))->format('Y-m-d H:i:s');
        return (new \DateTime)->setTime(18, 00, 0)->format('Y-m-d H:i:s');
    }
    public function getNightDate()
    {
        if (new \DateTime < (new \DateTime)->setTime(5, 50, 0))
            return (new \DateTime)->setTime(18, 0, 0)->sub(new \DateInterval('P1D'))->format('Y-m-d H:i:s');
        return (new \DateTime)->setTime(18, 0, 0)->format('Y-m-d H:i:s');
    }
    public function isDayService()
    {
        if (
            new \DateTime >= (new \DateTime)->setTime(5, 50, 0) &&
            new \DateTime < (new \DateTime)->setTime(17, 50, 0)
        )
            return true;
        return false;
    }
    public function show($id)
    {
        $pointage = DB::select("SELECT p.id, s.nom as 'site', p.soft_delete, p.date_pointage, p.agent_id,
            a.nom, a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.num_emp_saoi
            FROM pointages p
            LEFT JOIN sites s ON s.idsite = p.site_id
            LEFT JOIN agents a ON a.id = p.agent_id
            where p.id = ?", [$id])[0];
        return response()->json($pointage);
    }
    public function get_pointage($site_id)
    {
        $horaire = '';
        $current_date = new \DateTime();
        if (
            new \DateTime >= (new \DateTime)->setTime(05, 50, 0) &&
            new \DateTime < (new \DateTime)->setTime(17, 50, 0)
        )
            $horaire = 'day';
        else {
            if (new \DateTime < (new \DateTime)->setTime(05, 50, 0))
                $current_date = (new \DateTime)->sub(new \DateInterval('P1D'));
            $horaire = 'night';
        }
        $field = $horaire . '_' . $current_date->format('w');
        $jour_ferie = JourFerie::where('date', $current_date->format('Y-m-d'))->first();
        $site = DB::select("SELECT s.idsite, s.nom, s.group_pointage_id, nb_agent_day, nb_agent_night, s.pointage_biometrique, h.{$field}, h.{$horaire}_ferie
            FROM sites s
            LEFT JOIN horaire_effectifs h ON h.site_id = s.idsite
            WHERE idsite = ?", [$site_id])[0];
        if ($jour_ferie == null)
            $site->nb_agent = $site->{$field};
        else
            $site->nb_agent = $site->{$horaire."_ferie"};
        
        $agents = DB::select("SELECT a.id, a.site_id, a.nom, a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.empreinte, a.empreinte_optic
            FROM agents a
            WHERE (a.soft_delete is null or a.soft_delete = 0) and a.site_id = ?
            ORDER BY a.nom asc", [$site->idsite]);
        $site->agents = $agents;

        $pointages = DB::select("SELECT p.id, p.site_id, p.agent_id, p.vigilance, p.type_pointage_id, tp.nom as 'type_pointage', p.motif, p.dtarrived, p.soft_delete,
            p.pointeuse_id, b.nom as 'pointeuse', b.optic, ap.id as 'agent_pointeuse_id'
            FROM pointages p
            LEFT JOIN pointeuses b ON b.id = p.pointeuse_id
            LEFT JOIN type_pointages tp ON tp.id = p.type_pointage_id
            LEFT JOIN agent_pointeuses ap ON ap.agent_id = p.agent_id and ap.pointeuse_id = p.pointeuse_id
            WHERE p.date_pointage = ? and p.site_id = ?
            group by p.id
            ORDER BY b.id asc, p.id asc", [$this->getDayOrNightDate(), $site_id]);
        $reclamations = DB::select(
            "SELECT r.id as 'reclamation_id', r.agent_not_registered, ag.nom, ag.societe_id,
            ag.numero_stagiaire, ag.num_emp_soit, ag.numero_employe , r.site_id, r.agent_id
            FROM reclamations r
            LEFT JOIN agents ag ON ag.id = r.agent_id
            LEFT JOIN pointages p ON p.date_pointage = ? AND p.agent_id = r.agent_id
            WHERE r.site_id = ? and r.date_pointage = ? AND p.id is null",
            [$this->getDayOrNightDate(), $site_id, $this->getDayOrNightDate()]
        );

        return [
            'site' => $site,
            'pointages' => $pointages,
            'reclamations' => $reclamations,
        ];

        // return compact('site', 'pointages', 'reclamations');
    }
    public function store(Request $request)
    {
        $site = Site::find($request->site_id);
        $date_pointage = $this->getDayOrNightDate();
        $verify_reclamation = Reclamation::where('agent_id', $request->agent_id)->where('date_pointage', $date_pointage)->first();
        if ($verify_reclamation)
            return response()->json(['error' => "Veuillez d'abord supprimer la réclamation."]);
        if (!$site->pointage_biometrique || ($site->pointage_biometrique && $request->type_pointage_id && $request->motif)) {
            foreach ($request->agents as $agent_id) {
                $agent = DB::select("SELECT a.id, a.nom, COALESCE(a.sal_forfait, 0) as 'sal_forfait', a.soft_delete FROM agents a
                    WHERE a.id = ? limit 1", [$agent_id]);
                if ($agent)
                    $sal_forfait = $agent[0]->sal_forfait;
                $mis_a_pied = DB::select("SELECT COALESCE(a.id, 0) FROM absences a
                    WHERE a.type_absence = 'mis_a_pied'
                    and a.employe_id = ? and a.status = 'done'
                    and a.depart < now() and a.retour > now()
                    limit 1", [$agent_id]);
                $service_24 = DB::select("SELECT COALESCE(s.id, 0) FROM service24s s
                    WHERE s.status in ('validation', 'done') and  s.employe_id = ?
                    and (s.date_pointage = ? OR DATE_ADD(s.date_pointage, INTERVAL 1 HOUR) = ?)
                    limit 1", [$agent_id, $date_pointage, $date_pointage]);
                $last_pointage = DB::select("SELECT max(date_pointage) as 'date_pointage' FROM pointages p
                    WHERE p.agent_id = ?
                    and (p.soft_delete is null or p.soft_delete = 0)", [$agent_id]);
                $last_date_pointage = null;
                if (count($last_pointage) > 0)
                    $last_date_pointage = strtotime($last_pointage[0]->date_pointage);
                if($agent[0]->soft_delete == 1)
                    return response()->json(["error" => "L'employé " . $agent[0]->nom . " est en archive."]);
                if ($sal_forfait != 0)
                    return response()->json(["error" => "Le salaire de l'employé " . $agent[0]->nom . " est forfaitaire."]);
                if ($mis_a_pied != null)
                    return response()->json(["error" => "L'employé " . $agent[0]->nom . " est mis à pied.", "mis_a_pied" => 1]);
                if (count($service_24) == 0 && $last_date_pointage != null && (strtotime($date_pointage) - $last_date_pointage) < 72000)
                    return response()->json(["error" => "L'employé " . $agent[0]->nom . " n'est pas autorisé à faire du service 24.", "service24" => 1]);
            }
            foreach ($request->agents as $agent_id) {
                $pointage = Pointage::where('date_pointage', $date_pointage)
                    ->where('agent_id', $agent_id)->first();
                if ($pointage == null) {
                    $pointage = new Pointage();
                    $pointage->date_pointage = $date_pointage;
                    $pointage->site_id = $request->site_id;
                    $pointage->agent_id = $agent_id;
                    if ($site->pointage_biometrique) {
                        $pointage->pointeuse_id = $site->pointeuse_id;
                        $pointage->motif = $request->motif;
                        $pointage->type_pointage_id = $request->type_pointage_id;
                    } else
                        $pointage->vigilance = $request->vigilance;
                    $pointage->user_id = $request->authId;
                    $pointage->last_update = now();
                    $pointage->save();
                }
            }
        }
        return response()->json($this->get_pointage($request->site_id));
    }

    public function store_reclamation(Request $request)
    {
        $site = Site::find($request->site_id);
        $date_pointage = $this->getDayOrNightDate();
        if(!$request->superviseur_id){
            return response(["error" => "Champ superviseur requis"]);
        }
        if($request->agent_id){
            if(!$request->type)
                return response(["error" => "Type de réclamation requis"]);      
            $agent = DB::select("SELECT a.id, a.nom, COALESCE(a.sal_forfait, 0) as 'sal_forfait' FROM agents a
                    WHERE a.id = ? limit 1", [$request->agent_id]);
            if ($agent[0]->sal_forfait != 0)
                return response()->json(["error" => "Le salaire de l'employé " . $agent[0]->nom . " est forfaitaire."]);
            $verify_pointage = DB::select("SELECT s.nom as 'site', p.soft_delete FROM pointages p
                LEFT JOIN sites s ON s.idsite = p.site_id
                WHERE p.agent_id = ?
                and p.date_pointage = ?", [$request->agent_id, $date_pointage]);
            if (count($verify_pointage) > 0) {
                return response(["error" => "L'agent est déjà pointé"
                    . ($verify_pointage[0]->soft_delete == 1 ? " mais annulé" : "") . " sur le site " . $verify_pointage[0]->site]);
            }
            $verify_reclamation = DB::select("SELECT * FROM reclamations r
                WHERE r.agent_id = ?
                and r.date_pointage = ?", [$request->agent_id, $date_pointage]);
            if (count($verify_reclamation) > 0) {
                return response(["error" => "L'agent a déjà une réclamation "]);
            }
            if ($agent) {
                $agentData = Agent::where('id', $request->agent_id)->first();  
                $agentData->site_id = $request->site_id;
                if($agentData->save()){
                    $reclamation = new Reclamation();
                    $reclamation->agent_id = $request->agent_id;
                    $reclamation->user_id = $request->authId;
                    $reclamation->site_id = $request->site_id;
                    $reclamation->date_pointage = $date_pointage;
                    $reclamation->superviseur_id = $request->superviseur_id;
                    $reclamation->type = $request->type;
                    $reclamation->created_at = new \DateTime();
                    $reclamation->updated_at = new \DateTime();
                    if ($reclamation->save()) {
                        $agent = Agent::find($reclamation->agent_id);
                        return response(["success" => "Réclamation enregistré", "reclamation" => [
                            "reclamation_id" => $reclamation->id,
                            "nom" => $agent->nom,
                            "societe_id" => $agent->societe_id,
                            "numero_employe" => $agent->numero_employe,
                            "num_emp_soit" => $agent->num_emp_soit,
                            "numero_stagiaire" => $agent->numero_stagiaire,
                            "site_id" => $reclamation->site_id,
                            "agent_id" => $reclamation->agent_id,
                            "id" => $reclamation->agent_id,
                            ]]);
                    }
                }
                else{
                    return response(["error" => "Impossible de changer le site"]);
                }
            }
        } else if ($request->agent_name) {
            $reclamation = new Reclamation();
            $reclamation->user_id = $request->authId;
            $reclamation->site_id = $request->site_id;
            $reclamation->date_pointage = $date_pointage;
            $reclamation->superviseur_id = $request->superviseur_id;
            $reclamation->agent_not_registered = $request->agent_name;
            $reclamation->type = 'sm'; //sans matricule
            $reclamation->created_at = new \DateTime();
            $reclamation->updated_at = new \DateTime();
            if ($reclamation->save()) {
                return response(["success" => "Réclamation enregistré", "reclamation" => [
                    "reclamation_id" => $reclamation->id,
                    "nom" => $reclamation->agent_not_registered,
                    "societe_id" => null,
                    "numero_employe" => null,
                    "num_emp_soit" => null,
                    "numero_stagiaire" => null,
                    "site_id" => $reclamation->site_id,
                ]]);
            }
        }
    }

    public function reclamation_to_pointage(Request $request, $id){
        DB::beginTransaction();
        try{
            $reclamation = Reclamation::find($id);
            if($reclamation && $reclamation->agent_id){
                $agents = $request->input('agents', []);
                if (!in_array($reclamation->agent_id, $agents)) {
                    $agents[] = $reclamation->agent_id;
                    $request->merge(['agents' => $agents]);
                }
                $request->merge(['site_id' => $reclamation->site_id, 'agent_id' => $reclamation->agent_id]);
                $reclamation->delete();
                $controller = new PointageController();
                $result = $controller->store($request);
                $responseData = json_decode($result->getContent(), true);
                if (isset($responseData['error'])) {
                    throw new \Exception($responseData['error']);
                }
                DB::commit();
                if($request->simple_result){
                    return response()->json(['success' => "Reclamation deleted and Pointage created"]);
                }
                return $result;
            }
        }
        catch (\Exception $e) {
            DB::rollBack(); 
            return response()->json(['error' => $e->getMessage() ]);
        }
    }

    public function get_admin_user(Request $request)
    {
        $users = DB::select("SELECT * FROM admin_users
            where (name like '%" . $request->value . "%' or email like '%" . $request->value . "%')
            and (blocked is null or blocked=0)", []);
        // and (must_change_password is null or must_change_password = 0)
        return response()->json(compact('users'));
    }

    public function update($id, Request $request)
    {
        $pointage = Pointage::find($id);
        if ($pointage) {
            $old_site = Site::find($pointage->site_id);
            $new_site = Site::find($request->site_id);
            if (!$new_site->pointage_biometrique || ($new_site->pointage_biometrique && (($request->motif && $request->type_pointage_id) || $pointage->dtarrived))) {
                if ($new_site->pointage_biometrique) {
                    $pointage->site_id = $request->site_id;
                    $pointage->vigilance = null;
                    $pointage->pointeuse_id = $new_site->pointeuse_id;
                    if (!$pointage->dtarrived) {
                        $pointage->motif = $request->motif;
                        $pointage->type_pointage_id = $request->type_pointage_id;
                    }
                    $pointage->save();
                    Agent::where('id', $pointage->agent_id)
                        ->update(['site_id' => $request->site_id]);
                    return response()->json([$this->get_pointage($old_site->idsite), $this->get_pointage($new_site->idsite)]);
                } else {
                    $pointage->site_id = $request->site_id;
                    $pointage->vigilance = $request->vigilance;
                    $pointage->pointeuse_id = null;
                    $pointage->motif = null;
                    $pointage->type_pointage_id = null;
                    $pointage->user_id = $request->authId;
                    $pointage->save();
                    Agent::where('id', $pointage->agent_id)
                        ->update(['site_id' => $request->site_id]);
                    return response()->json([$this->get_pointage($old_site->idsite), $this->get_pointage($new_site->idsite)]);
                }
            }
        }
        return response()->json("pointage not found");
    }
    public function delete($id, Request $request)
    {
        $pointage = Pointage::find($id);
        $site_id = $pointage->site_id;
        if ($pointage->dtarrived == null || ($pointage->dtarrived != null && $request->motif != null)) {
            $pointage->soft_delete = 1;
            $pointage->user_id = $request->authId;
            $pointage->pointeuse_id = null;
            $pointage->motif = $request->motif;
            $pointage->save();
        }
        return response()->json($this->get_pointage($site_id));
    }
    public function cancel_delete($id, Request $request)
    {
        $pointage = Pointage::find($id);
        $site_id = $pointage->site_id;
        $site = Site::find($site_id);
        if ($pointage->soft_delete == 1) {
            if ($site->pointeuse) {
                $pointage->pointeuse_id = $site->pointeuse_id;
                $pointage->vigilance = $request->vigilance;
                $pointage->vigilance = null;
                if (!$pointage->dtarrived) {
                    $pointage->motif = $request->motif;
                    $pointage->type_pointage_id = $request->type_pointage_id;
                }
            } else {
                $pointage->motif = null;
            }
            $pointage->soft_delete = null;
            $pointage->user_id = $request->authId;
            $pointage->save();
        }
        return response()->json($this->get_pointage($site_id));
    }
    public function change_motif($id, Request $request)
    {
        $pointage = Pointage::find($id);
        $site_id = $pointage->site_id;
        if (!$pointage->dtarrived && $request->motif) {
            $pointage->user_id = $request->authId;
            $pointage->motif = $request->motif;
            $pointage->save();
        } else if ($pointage->dtarrived) {
            $pointage->motif = $request->motif;
            $pointage->save();
        }
        return response()->json($this->get_pointage($site_id));
    }
    public function index(Request $request)
    {
        $sites = [];
        $pointages = [];
        $reclamations = [];
        $horaire = '';
        $current_date = new \DateTime();
        if (
            new \DateTime >= (new \DateTime)->setTime(05, 50, 0) &&
            new \DateTime < (new \DateTime)->setTime(17, 50, 0)
        )
            $horaire = 'day';
        else {
            if (new \DateTime < (new \DateTime)->setTime(05, 50, 0))
                $current_date = (new \DateTime)->sub(new \DateInterval('P1D'));
            $horaire = 'night';
        }
        $field = $horaire . '_' . $current_date->format('w');
        $jour_ferie = JourFerie::where('date', $current_date->format('Y-m-d'))->first();

        if ($request->pointage_error) {
            $pointage_query = "SELECT p.id, p.site_id, p.agent_id, p.vigilance, p.type_pointage_id, tp.nom as 'type_pointage', p.motif, p.dtarrived, p.soft_delete,
                    a.nom, a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.empreinte, a.empreinte_optic,
                    p.pointeuse_id, b.nom as 'pointeuse', b.optic, ap.id as 'agent_pointeuse_id', cmp.comment, cmp.id as 'comment_id'
                    FROM pointages p
                    LEFT JOIN agents a ON a.id = p.agent_id
                    LEFT JOIN sites s ON s.idsite = p.site_id
                    LEFT JOIN horaire_effectifs h1 ON h1.site_id = s.idsite
                    LEFT JOIN horaire_effectifs h2 ON h2.site_id = s.group_planning_id
                    LEFT JOIN pointeuses b ON b.id = p.pointeuse_id
                    LEFT JOIN type_pointages tp ON tp.id = p.type_pointage_id
                    LEFT JOIN agent_pointeuses ap ON ap.agent_id = p.agent_id and ap.pointeuse_id = p.pointeuse_id
                    LEFT JOIN comment_pointages cmp ON cmp.site_id = s.idsite and cmp.date_pointage = ?
                    WHERE p.date_pointage = ? and s.group_pointage_id = ? ";
            if (JourFerie::where('date', $current_date->format('Y-m-d'))->first() == null) {
                $pointage_query = $pointage_query . " AND ((s.group_planning_id is null and h1.{$field} = 0) or (s.group_planning_id is not null and h2.{$field} = 0)) GROUP BY p.agent_id";
            } else {
                $pointage_query = $pointage_query . " AND ((s.group_planning_id is null and h1.{$horaire}_ferie = 0) or (s.group_planning_id is not null and h2.{$horaire}_ferie = 0)) GROUP BY p.agent_id";
            }
            $pointages = DB::select($pointage_query,
                [
                    $this->getDayOrNightDate(), 
                    $this->getDayOrNightDate(), 
                    $request->group_pointage_id]
            );
        }
        else {
            $site_id_query = "SELECT s.idsite
                FROM horaire_effectifs h
                LEFT JOIN sites s ON s.idsite = h.site_id
                LEFT JOIN (
                    SELECT p.site_id, COUNT(p.id) as nb_agent
                    FROM pointages p
                    WHERE p.date_pointage = ? and (soft_delete is null or soft_delete = 0)
                    GROUP BY p.site_id
                ) y ON y.site_id = s.idsite
                WHERE (s.idsite = s.group_planning_id or s.group_planning_id is null) 
                    AND (s.soft_delete IS NULL OR s.soft_delete = 0)
                    AND s.pointage = 1
                    AND s.group_pointage_id = ?";
                
            if ($request->no_agent) {
                if ($jour_ferie == null) {
                    $site_id_query = $site_id_query . " AND y.nb_agent IS NULL AND h.{$field} > 0";
                } else {
                    $site_id_query = $site_id_query . " AND y.nb_agent IS NULL AND h.{$horaire}_ferie > 0";
                }
            } else if ($request->manque_surplus) {
                if ($jour_ferie == null) {
                    $site_id_query = $site_id_query . " AND y.nb_agent IS NOT NULL AND (h.{$field} - y.nb_agent) != 0";
                } else {
                    $site_id_query = $site_id_query . " AND y.nb_agent IS NOT NULL AND (h.{$horaire}_ferie - y.nb_agent) != 0";
                }
            } else {
                if ($jour_ferie == null) {
                    $site_id_query = $site_id_query . " AND h.{$field} > 0 ";
                } else {
                    $site_id_query = $site_id_query . " AND h.{$horaire}_ferie > 0";
                }
            }
            $site_ids = DB::select($site_id_query . " AND s.nom LIKE ? GROUP BY s.idsite ORDER BY s.nom ASC LIMIT ?, 15",
                [
                    $this->getDayOrNightDate(),
                    $request->group_pointage_id,
                    "%{$request->search}%",
                    (int) $request->offset
                ]
            );
        }

        if ($request->pointage_error) {
            $sites = [];
            if (count($pointages) > 0) {
                $ids = [];
                foreach ($pointages as $ptg) {
                    if (!in_array($ptg->site_id, $ids))
                        $ids[] = $ptg->site_id;
                }
                $sites = DB::select("SELECT s.idsite, s.nom, nb_agent_day, nb_agent_night, h.{$horaire}_ferie, h.{$field},
                        COALESCE(GROUP_CONCAT(DISTINCT n.numero SEPARATOR ', '), '') AS phone_agent,
                        group_pointage_id, pointage_biometrique, s.pointeuse, s.pointeuse_id,
                        cmp.comment, cmp.id AS comment_id
                    FROM sites s
                    LEFT JOIN numeros n ON n.id_site = s.idsite
                    LEFT JOIN horaires h ON h.id = s.horaire_pointage_id
                    LEFT JOIN comment_pointages cmp
                        ON cmp.site_id = s.idsite
                        AND cmp.date_pointage = ?
                    WHERE s.idsite IN (" . implode(", ", array_fill(0, count($ids), "?")) . ")
                    GROUP BY s.idsite
                ", array_merge([$this->getDayOrNightDate()], $ids));

                for ($i = 0; $i < count($sites); $i++) {
                    $sites[$i]->agents = [];
                    for ($j = 0; $j < count($pointages); $j++) {
                        if ($sites[$i]->idsite == $pointages[$j]->site_id) {
                            $agent = clone $pointages[$j];
                            $agent->id = $pointages[$j]->agent_id;
                            $sites[$i]->agents[] = $agent;
                        }
                    }
                }
            }
        } else {
            $ids = array_column($site_ids, 'idsite');
            if (count($ids) > 0) {
                $site_query = "SELECT s.idsite, s.nom, nb_agent_day, nb_agent_night, h.{$field}, h.{$horaire}_ferie,
                    COALESCE(GROUP_CONCAT(DISTINCT n.numero SEPARATOR ', '), '') AS phone_agent,
                    group_pointage_id, pointage_biometrique, s.pointeuse, s.pointeuse_id, cmp.comment, cmp.id as comment_id
                        FROM horaire_effectifs h
                        LEFT JOIN sites s ON s.idsite = h.site_id
                        LEFT JOIN numeros n ON n.id_site = s.idsite
                        LEFT JOIN (
                            SELECT p.site_id, COUNT(p.id) as nb_agent
                            FROM pointages p
                            WHERE p.date_pointage = ? and (soft_delete is null or soft_delete = 0)
                            GROUP BY p.site_id
                        ) y ON y.site_id = s.idsite
                        LEFT JOIN comment_pointages cmp ON cmp.site_id = s.idsite and cmp.date_pointage = ?";
                        
                $sites = DB::select($site_query . " WHERE s.idsite in (" . implode(", ", array_column($site_ids, "idsite")) . ") 
                    OR s.group_planning_id in (" . implode(", ", $ids) . ") group by s.idsite", 
                    [
                        $this->getDayOrNightDate(),
                        $this->getDayOrNightDate(),
                        $request->group_pointage_id,
                    ]
                );
                for ($i = 0; $i < count($sites); $i++) {
                    $sites[$i]->agents = [];
                    if ($jour_ferie == null)
                        $sites[$i]->nb_agent = $sites[$i]->{$field};
                    else
                        $sites[$i]->nb_agent = $sites[$i]->{$horaire."_ferie"};
                }
                $ids = array_column($sites, 'idsite');
                $agents = DB::select("SELECT a.id, a.site_id, a.nom, a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.empreinte, a.empreinte_optic
                    FROM agents a
                    WHERE (a.soft_delete is null or a.soft_delete = 0) and a.site_id in (" . implode(', ', $ids) . ")
                    ORDER BY a.nom asc");
                for ($i = 0; $i < count($sites); $i++) {
                    for ($j = 0; $j < count($agents); $j++) {
                        if ($sites[$i]->idsite == $agents[$j]->site_id) {
                            $sites[$i]->agents[] = $agents[$j];
                        }
                    }
                }
                $pointages = DB::select(
                    "SELECT p.id, p.site_id, p.agent_id, p.vigilance, p.type_pointage_id, tp.nom as 'type_pointage', p.motif, p.dtarrived, p.soft_delete,
                    p.pointeuse_id, b.nom as 'pointeuse', b.optic, ap.id as 'agent_pointeuse_id'
                    FROM pointages p
                    LEFT JOIN pointeuses b ON b.id = p.pointeuse_id
                    LEFT JOIN type_pointages tp ON tp.id = p.type_pointage_id
                    LEFT JOIN agent_pointeuses ap ON ap.agent_id = p.agent_id and ap.pointeuse_id = p.pointeuse_id
                    WHERE p.date_pointage= ? and p.site_id in (" . implode(', ', $ids) . ")
                    group by p.id
                    ORDER BY b.id asc, p.id asc",
                    [$this->getDayOrNightDate()]
                );

                $reclamations = DB::select(
                    "SELECT r.id, r.site_id, r.agent_id, r.user_id, r.date_pointage, r.superviseur_id, r.type, r.created_at, r.updated_at, r.agent_not_registered,
                    a.nom, a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.empreinte, a.empreinte_optic
                    FROM reclamations r
                    LEFT JOIN agents a ON a.id = r.agent_id
                    LEFT JOIN pointages p ON p.date_pointage = ? and p.agent_id = r.agent_id
                    WHERE p.id is null and r.date_pointage = ? and r.site_id in (" . implode(', ', $ids) . ")",
                    [$this->getDayOrNightDate(), $this->getDayOrNightDate()]
                );
            }
        }

        return response()->json(compact('sites', 'pointages', 'reclamations'));
    }
    public function select_agents($site_id)
    {
        $agents = DB::select(
            "SELECT a.id, a.nom, a.numero_employe, a.numero_stagiaire, a.num_emp_soit, a.societe_id FROM agents a
            WHERE (a.soft_delete = 0 or a.soft_delete is null) and a.site_id= ? and a.id not in (select p.agent_id from pointages p where p.date_pointage = ? and p.site_id = ?)",
            [$site_id, $this->getDayOrNightDate(), $site_id]
        );
        $isDayService = $this->isDayService();
        return response()->json(compact('agents', 'isDayService'));
    }
    public function pointage_par_site(Request $request)
    {
        $pointages = Pointage::where('date_pointage', $request->date_pointage)
            ->where('site_id', $request->site_id)
            ->with('agent')
            ->orderBy('date_pointage')
            ->get();
        return response()->json($pointages);
    }

    public function add_comment(Request $request)
    {
        $comment_pointage = CommentPointage::where('site_id', $request->site_id)->where('date_pointage', $request->date_pointage)->first();
        $old_comment = $comment_pointage ? clone $comment_pointage : null;
        if (!$comment_pointage) {
            $comment_pointage = new CommentPointage();
        }
        $comment_pointage->comment = $request->comment;
        $comment_pointage->date_pointage = $request->date_pointage;
        $comment_pointage->site_id = $request->site_id;
        if (!$old_comment && !$request->comment)
            return response()->json(null);
        else {
            $comment_pointage->user_id = $request->authId;
            $comment_pointage->save();
        }
        return response()->json(compact('comment_pointage'));
    }

    public function getDatePointageLimit($date_pointage)
    {
        if (
            new \DateTime >= (new \DateTime)->setTime(5, 50, 0) &&
            new \DateTime < (new \DateTime)->setTime(17, 50, 0)
        )
            $end = (new \DateTime)->setTime(07, 0, 0);
        else if (new \DateTime < (new \DateTime)->setTime(5, 50, 0))
            $end = (new \DateTime)->setTime(18, 0, 0)->sub(new \DateInterval('P1D'));
        else
            $end = (new \DateTime)->setTime(18, 00, 0);

        $day = $end->format("d");
        if ($day > 20) {
            $month = $end->format("m");
            $year = $end->format("Y");
            $begin = (new \DateTime)->setDate($year, $month, 20)->setTime(7, 0, 0);
        } else {
            $date_service = clone $end;
            $month = $date_service->sub(new \DateInterval('P1M'))->format("m");
            $year = $date_service->sub(new \DateInterval('P1M'))->format("Y");
            $begin = (new \DateTime)->setDate($year, $month, 20)->setTime(7, 0, 0);
        }

        $date_pointage = \DateTime::createFromFormat("Y-m-d H:i:s", $date_pointage);
        /*return $date_pointage->format("Y-m-d H:i:s") . ", " .
            $begin->format("Y-m-d H:i:s") . ", " .
            $end->format("Y-m-d H:i:s");*/
        return ($date_pointage < $begin || $date_pointage >= $end);
    }

    public function store_late(Request $request)
    {
        $validator = Validator::make(array_filter($request->all(), function ($a) {
            return ($a !== "" && $a !== 0);
        }), [
            'site_id' => 'required',
            'agent_id' => 'required',
            'date_pointages' => 'required',
        ]);
        if ($validator->fails())
            return response()->json(['error' => $validator->errors()->first()]);
        foreach ($request->date_pointages as $date_pointage) {
            if ($this->getDatePointageLimit($date_pointage))
                return response()->json(['error' => "Date pointage ". $date_pointage ."  invalide."]);
            if (
                Pointage::where('agent_id', $request->agent_id)
                ->where(function ($query) {
                    return $query->where('soft_delete', '0')
                        ->orWhereNull('soft_delete');
                })
                ->where('date_pointage', $date_pointage)
                ->first() != null
            )
                return response()->json(['error' => "Le pointage de l'agent à la date ". $date_pointage ." existe déjà."]);
        }
        $pointages = [];
        foreach ($request->date_pointages as $date_pointage) {
            $soft_delete_pointage = Pointage::where('agent_id', $request->agent_id)
                ->where('soft_delete', '1')
                ->where('date_pointage', $date_pointage)
                ->first();
            if ($soft_delete_pointage) {
                $pointage = $soft_delete_pointage;
                $pointage->soft_delete = null;
            } else {
                $pointage = new Pointage();
                $pointage->agent_id = $request->agent_id;
                $pointage->date_pointage = $date_pointage;
            }
            $pointage->site_id = $request->site_id;
            $pointage->late = 1;
            $pointage->last_update = now();
            $pointage->save();
        }
        return response()->json(true);
    }

    public function index_late(Request $request)
    {
        $pointages = DB::select("SELECT p.id, s.nom as 'site', p.soft_delete, p.date_pointage,
            a.nom, a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.num_emp_saoi
            FROM pointages p
            LEFT JOIN sites s ON s.idsite = p.site_id
            LEFT JOIN agents a ON a.id = p.agent_id
            where p.late = 1 order by p.last_update desc limit " . $request->offset . ", 50");
        return response()->json($pointages);
    }

    public function operation($pointage_id, $begin, $end)
    {
        $historiques = HistoriquePointage::with('user')
            ->where('pointage_id', $pointage_id)
            ->where('created_at', '>', $begin . ' 00:00:00')
            ->where('created_at', '<', $end . ' 23:00:00')
            ->orderBy('created_at', 'desc')
            ->get();
        return response()->json($historiques);
    }
}
