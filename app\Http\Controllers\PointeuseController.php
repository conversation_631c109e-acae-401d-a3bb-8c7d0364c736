<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Pointeuse;
use App\Site;
use App\HistoriquePointeuse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class PointeuseController extends Controller
{
    private $attributeNames = array(
        'id' => 'ID',
        'site_id' => 'Site',
        'nom' => 'Description'
    );
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
    }
    public function index(Request $request)
    {
        $pointeuses = DB::select("SELECT p.id, s.nom as 'site', p.sim, p.nom, p.soft_delete, p.without_sensor, p.check_panic FROM pointeuses p
            LEFT JOIN sites s ON s.idsite = p.site_id
            where (p.nom like '%" . $request->search . "%' or s.nom like '%" . $request->search . "%' or LPAD(p.id, 4, '0') like '%" . $request->search . "%') " .
            " order by p.updated_at desc limit " . $request->offset . ", 50");
        return response()->json($pointeuses);
    }

    public function show($id)
    {
        $pointeuse = DB::select("SELECT p.id, p.nom, p.sim, p.site_id, s.nom as 'site', p.soft_delete, p.optic, p.register_mode,
            p.group_diag_id, g.nom as 'group_diag_site', p.without_sensor
            FROM pointeuses p
            LEFT JOIN sites s ON s.idsite = p.site_id
            LEFT JOIN group_diag_sites g ON g.id = p.group_diag_id
            WHERE p.id = ?", [$id]);

        if (!empty($pointeuse)) {
            $pointeuse = $pointeuse[0];
            $agents = DB::select("SELECT ap.id, a.nom, a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.soft_delete,
            ap.agent_id, ap.digit, ap.get_digit, ap.pointeuse_id, ap.empreinte_id, ap.created_at
            FROM agent_pointeuses ap
            LEFT JOIN agents a ON a.id = ap.agent_id
            WHERE ap.pointeuse_id = ?
            ORDER BY ap.created_at desc", [$id]);
            $pointeuse->agents = $agents;
            $ids = array_column($agents, 'id');
            //$last_pointages;
            return response()->json(compact('pointeuse', 'agents'));
        }
        $pointeuse = null;

        $agents = DB::select("SELECT ap.id, a.nom, a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.soft_delete,
            ap.agent_id, ap.digit, ap.get_digit, ap.pointeuse_id, ap.empreinte_id, ap.created_at
            FROM agent_pointeuses ap
            LEFT JOIN agents a ON a.id = ap.agent_id
            WHERE ap.pointeuse_id = ?
            ORDER BY ap.created_at desc", [$id]);
        // $pointeuse->agents = $agents;
        $ids = array_column($agents, 'id');
        //$last_pointages;
        return response()->json(compact('pointeuse', 'agents'));
    }

    public function new_id()
    {
        $data = DB::select("SELECT max(id) as lastId FROM pointeuses", []);
        return response()->json($data[0]);
    }

    public function site($site_id)
    {
        $current_site = Site::find($site_id);
        $pointeuses = Pointeuse::select('id', 'nom')->where('id', $current_site->pointeuse_id)->get();
        $sites = Site::select('idsite', 'nom')->where('pointeuse_id', $current_site->pointeuse_id)->get();
        return response()->json(compact('pointeuses', 'sites'));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|unique:pointeuses,id',
            'nom' => 'required',
            'sim' => ['regex:/^03(2|3|4|8)\d{7}$/i', Rule::unique('pointeuses')->where(function ($query) use ($request) {
                return $query->where('sim', $request->sim);
            })]
        ])->setAttributeNames($this->attributeNames);

        if ($validator->fails())
            return response()->json(['error' => $validator->errors()->first()]);
        $pointeuse = new Pointeuse();
        $pointeuse->id = $request->id;
        $pointeuse->nom = $request->nom;
        $pointeuse->sim = $request->sim;
        $pointeuse->site_id = $request->site_id;
        $pointeuse->group_diag_id = $request->group_diag_id;
        if ($request->without_sensor)
            $request->optic = null;
        $pointeuse->without_sensor = $request->without_sensor;
        $pointeuse->optic = $request->optic;
        $pointeuse->created_at = now();
        $pointeuse->updated_at = now();
        $pointeuse->save();

        $historique_pointeuse = new HistoriquePointeuse();
        $historique_pointeuse->pointeuse_id = $pointeuse->id;
        $historique_pointeuse->user_id = $request->authId;
        $historique_pointeuse->objet = "Création d'une nouvelle pointeuse";
        $historique_pointeuse->detail = "";
        $historique_pointeuse->created_at = now();
        $historique_pointeuse->updated_at = now();
        $historique_pointeuse->save();

        return response()->json($pointeuse->id);
    }

    public function toggle_activation($id)
    {
        $pointeuse = Pointeuse::find($id);
        if ($pointeuse->soft_delete)
            $pointeuse->soft_delete = null;
        else
            $pointeuse->soft_delete = 1;
        $pointeuse->updated_at = now();
        $pointeuse->save();
        return response()->json($pointeuse->id);
    }

    public function update($id, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'nom' => 'required',
            'sim' => ['regex:/^03(2|3|4|8)\d{7}$/i', Rule::unique('pointeuses')->where(function ($query) use ($request, $id) {
                return $query->where('sim', $request->sim)->where('id', '<>', $id);
            })]
        ])->setAttributeNames($this->attributeNames);
        if ($validator->fails())
            return response()->json(['error' => $validator->errors()->first()]);

        $historique_pointeuse = new HistoriquePointeuse();
        $pointeuse = Pointeuse::find($id);
        $site = Site::find($pointeuse->site_id);

        $detail = "Nom : " . $site->nom . " -> " . Site::find($request->site_id)->nom .
            "\nDescription : " . $pointeuse->nom . " -> " . $request->nom .
            "\nSIM : " . $pointeuse->sim . " -> " . $request->sim .
            "\nSans capteur : " . ($pointeuse->without_sensor ? "oui" : "non") . " -> " . ($request->without_sensor ? "oui" : "non") .
            "\nCapteur d'empreinte optique : " . ($pointeuse->optic ? "oui" : "non") . " -> " . ($request->optic ? "oui" : "non");

        $pointeuse->nom = $request->nom;
        $pointeuse->sim = $request->sim;
        $pointeuse->site_id = $request->site_id;

        $pointeuse->group_diag_id = $request->group_diag_id;
        if ($request->without_sensor)
            $request->optic = null;
        $pointeuse->without_sensor = $request->without_sensor;
        $pointeuse->optic = $request->optic;
        $pointeuse->updated_at = now();
        $pointeuse->save();

        $historique_pointeuse->objet = "Modification de la pointeuse";
        $historique_pointeuse->detail = $detail;
        $historique_pointeuse->created_at = now();
        $historique_pointeuse->updated_at = now();
        $historique_pointeuse->pointeuse_id = $pointeuse->id;
        $historique_pointeuse->user_id = $request->authId;
        $historique_pointeuse->save();
        return response()->json($pointeuse->id);
    }

    public function delete($id)
    {
        $pointeuse = Pointeuse::find($id);
        $pointeuse->sim = null;
        if ($pointeuse != null) {
            $pointeuse->site_id = null;
            return response()->json($pointeuse->save());
        }
        return false;
    }

    public function set_site($id, Request $request)
    {
        $pointeuse = Pointeuse::find($id);
        if ($pointeuse != null) {
            $pointeuse->site_id = $request->site_id;
            $pointeuse->save();
            return response()->json(DB::select("SELECT s.idsite, s.nom, p.site_id FROM sites s
                left join pointeuses p on p.site_id = s.idsite
                where s.pointeuse_id = ?", [$id]));
        }
        return false;
    }

    public function operation($pointeuse_id, $begin, $end)
    {
        $historiques = HistoriquePointeuse::with('user')
            ->where('pointeuse_id', $pointeuse_id)
            ->where('created_at', '>', $begin . ' 00:00:00')
            ->where('created_at', '<', $end . ' 23:00:00')
            ->orderBy('created_at', 'desc')
            ->get();
        return response()->json($historiques);
    }
}
