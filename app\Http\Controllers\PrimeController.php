<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Paie;
use App\Prime;
use App\TypePrime;
use App\Http\Util\PaieUtil;
use Validator;

class PrimeController extends Controller
{
    private $attributeNames = array(
        'montant' => 'Montant',
        'type_id' => 'Type',
        'month' => 'Mois',
        'agent_id' => 'Agent'
    );
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
    }

    public function all($agent_id){
        $primes = DB::select("SELECT p.id, p.month, p.year, p.montant, p.type_id, t.libelle as 'type' FROM primes p
            LEFT JOIN type_primes t ON t.id = p.type_id
            WHERE p.agent_id = ? and p.paie_id is not null
            ORDER BY p.year DESC, p.month DESC, p.id DESC", [$agent_id]);
        return response()->json(compact('primes'));
    }
    public function index($agent_id, Request $request){
        if($request->month != 0 && $request->year != 0)
            $primes = DB::select("SELECT p.id, p.month, p.year, p.montant, p.type_id, t.libelle as 'type' FROM primes p
                LEFT JOIN type_primes t ON t.id = p.type_id
                WHERE p.agent_id = ? and p.month = ? and p.year = ?
                ORDER BY p.year DESC, p.month DESC, p.id DESC", [$agent_id, $request->month, $request->year]);
        else
            $primes = DB::select("SELECT p.id, p.month, p.year, p.montant, p.type_id, t.libelle as 'type' FROM primes p
                LEFT JOIN type_primes t ON t.id = p.type_id
                WHERE agent_id = ? and paie_id is null
                ORDER BY p.year DESC, p.month DESC, p.id DESC", [$agent_id]);
        $type_primes = TypePrime::all();
        return response()->json(compact('primes', 'type_primes'));
    }
    public function month($agent_id){
        $interval = PaieUtil::getIntervalByAgent($agent_id, 0, 0);
        $month = $interval['end']->format('m');
        $year = $interval['end']->format('Y');
        return response()->json(compact('month', 'year'));
    }
    public function store(Request $request){
        if($request->authRole == 'root' || $request->authRole == 'rh'){
            $validator = Validator::make($request->all(), [
                'agent_id' => ['required'],
                'montant' => ['integer', 'required'],
                'type_id' => ['required'],
                'month' => ['required'],
            ])->setAttributeNames($this->attributeNames);
            if($validator->fails())
                return \response()->json(['error' => $validator->errors()]);
            
            $isValidMonth = false;
            $day = null;
            $agents = DB::select("SELECT a.id, g.day FROM agents a
                LEFT JOIN sites s ON s.idsite = a.site_id 
                LEFT JOIN group_pointage_sites g ON g.id = s.group_pointage_id 
                WHERE a.id = ?", [$request->agent_id]);
            if($agents)
                $day = $agents[0]->day;
            $paie = Paie::select('day', 'confirm', 'confirm_hour')
                ->where('agent_id', $request->agent_id)
                ->where('month', $request->month)
                ->where('year', $request->year)
                ->first();
            if($paie && $paie->confirm_hour == 1)
                $day = $paie->day;
            if($day && PaieUtil::isConfirmable($day, $request->month, $request->year) && ($paie == null || $paie->confirm == 0))
                $isValidMonth = true;
            else if($day && PaieUtil::isAfterConfirmable($day, $request->month, $request->year))
                $isValidMonth = true;
            
            if($isValidMonth){
                $prime = new Prime();
                $prime->agent_id = $request->agent_id;
                $prime->montant = $request->montant;
                $prime->month = $request->month;
                $prime->year = $request->year;
                $prime->type_id = $request->type_id;
                $prime->created_at = now();
                $prime->updated_at = now();
                return response()->json($prime->save());
            }
            else{
                return response()->json(['error' => ['not_allowed' => ["Ce mois ne peut plus être sélectionné"]]]);
            }
        }
        return response()->json(false);
    }

    public function update(Request $request, $id){
        if($request->authRole == 'root' || $request->authRole == 'rh'){
            $validator = Validator::make($request->all(), [
                'montant' => ['integer', 'required'],
                'type_id' => ['required'],
                'month' => ['required'],
            ])->setAttributeNames($this->attributeNames);
            if($validator->fails())
                return response()->json(['error' => $validator->errors()]);
            $prime = Prime::where('id', $id)->whereNull('paie_id')->first();
            if($prime != null){
                $isValidMonth = false;
                $day = null;
                $agents = DB::select("SELECT a.id, g.day FROM agents a
                    LEFT JOIN sites s ON s.idsite = a.site_id 
                    LEFT JOIN group_pointage_sites g ON g.id = s.group_pointage_id 
                    WHERE a.id = ?", [$id]);
                if($agents)
                    $day = $agents[0]->day;
                $paie = Paie::select('day', 'confirm', 'confirm_hour')
                    ->where('agent_id', $id)
                    ->where('month', $request->month)
                    ->where('year', $request->year)
                    ->first();
                if($paie && $paie->confirm_hour == 1)
                    $day = $paie->day;
                if($day && PaieUtil::isConfirmable($day, $request->month, $request->year) && ($paie == null || $paie->confirm == 0))
                    $isValidMonth = true;
                else if($day && PaieUtil::isAfterConfirmable($day, $request->month, $request->year))
                    $isValidMonth = true;
                
                if($isValidMonth){
                    $prime->montant = $request->montant;
                    $prime->month = $request->month;
                    $prime->year = $request->year;
                    $prime->type_id = $request->type_id;
                    $prime->updated_at = now();
                    return response()->json($prime->save());
                }
                else{
                    return response()->json(['error' => ['not_allowed' => ["Ce mois ne peut plus être sélectionné"]]]);
                }
            }
        }
        return response()->json(false);
    }
    public function delete($id, Request $request){
        if($request->authRole == 'root' || $request->authRole == 'rh'){
            $prime = Prime::where('id', $id)->whereNull('paie_id')->first();
            if($prime != null)
                return response()->json($prime->delete());
        }
        return false;
    }
}