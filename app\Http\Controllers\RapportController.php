<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Log;
use App\Site;
use App\Action;
use App\Rapport;
use App\TypeRapport;
use App\TypeAction;
use App\Habilite;
use Validator;

class RapportController extends Controller
{
    private $attributeNames = array(
        'site_id' => "Site",
        'type_rapport_id' => "Type de rapport",
        'commentaire' => 'Commentaire',
        'depart' => 'Départ',
        'arrivee' => 'Arrivée',
        'debut' => 'Début',
        'fin' => 'Fin',
        'tache' => 'Tâche effectué',
        'intervention_id' => 'Intervention',
    );

    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
    }

    public function field_selection()
    {
        $type_rapports = TypeRapport::all();
        $type_actions = TypeAction::all();
        $interventions = Site::select("idsite as id", "nom as libelle")->where("intervention", "1")->get();
        return response()->json(compact('type_rapports', 'type_actions', 'interventions'));
    }

    public function client($site_id, $year, $month)
    {
        $date_begin = date_create_from_format("Y-m-d", $year . "-" . $month . "-01");
        $date_end = (clone $date_begin)->add(new \DateInterval('P1M'));
        $rapports = DB::select(
            "SELECT r.id, r.created_at, r.type_rapport_id, t.nom as 'type' FROM rapports r
            LEFT JOIN type_rapports t ON t.id = r.type_rapport_id
            WHERE (soft_delete = 0 or soft_delete is null)
            and t.id is not null and site_id = ? and created_at >= ? and created_at < ?
            ORDER BY created_at desc",
            [$site_id, $date_begin, $date_end]
        );
        return response()->json($rapports);
    }

    public function index()
    {
        $current_date = new \DateTime;
        if ($current_date >= (clone $current_date)->setTime(6, 0, 0) && $current_date < (clone $current_date)->setTime(18, 0, 0)) {
            $date_begin = (clone $current_date)->setTime(6, 0, 0)->format('Y-m-d H:i:s');
        } else {
            if ($current_date < (clone $current_date)->setTime(6, 0, 0)) {
                $date_begin = (clone $current_date)->sub(new \DateInterval('P1D'))->setTime(18, 00, 0)->format('Y-m-d H:i:s');
            } else {
                $date_begin = (clone $current_date)->setTime(18, 00, 0)->format('Y-m-d H:i:s');
            }
        }
        $rapports = DB::select("SELECT r.id, r.dtarrived, r.created_at, r.type_rapport_id, r.soft_delete, u.email as 'operateur', t.nom as 'type', s.nom as 'site'
            from rapports r
            left join users u on u.id = r.user_id
            left join type_rapports t on t.id = r.type_rapport_id
            left join sites s on s.idsite = r.site_id
            where (r.soft_delete is null or r.soft_delete = 0)
            and ((r.type_rapport_id not in (1, 3) and r.created_at >= ? ) or (r.type_rapport_id in (1, 3) and r.fin >= ?))
            order by r.created_at desc", [$date_begin, $date_begin]);
        foreach ($rapports as $r)
            $r->nb_alarm = 0;

        if (count($rapports)) {
            $ids = array_column($rapports, 'id');
            $alarms = DB::select("SELECT a.idademco, a.rapport_id FROM ademcomemlog a
                where a.rapport_id in (" . implode(", ", $ids) . ")");
            $logs = DB::select("SELECT a.idademco, a.rapport_id FROM ademcotemp a
                where a.rapport_id in (" . implode(", ", $ids) . ")");
            foreach ($rapports as $r) {
                foreach ($alarms as $a)
                    if ($a->rapport_id == $r->id)
                        $r->nb_alarm++;
                foreach ($logs as $l)
                    if ($l->rapport_id == $r->id)
                        $r->nb_alarm++;
            }
        }
        return response()->json($rapports);
    }

    public function show_by_site($site_id)
    {
        $current_date = new \DateTime;
        if ($current_date >= (clone $current_date)->setTime(6, 0, 0) && $current_date < (clone $current_date)->setTime(18, 0, 0)) {
            if ($current_date <= (clone $current_date)->setTime(6, 20, 0))
                $date_begin = (clone $current_date)->setTime(5, 30, 0);
            else
                $date_begin = (clone $current_date)->setTime(6, 0, 0);
            $date_end = (clone $current_date)->setTime(18, 0, 0);
        } else {
            if ($current_date < (clone $current_date)->setTime(6, 0, 0)) {
                $date_begin = (clone $current_date)->sub(new \DateInterval('P1D'))->setTime(17, 30, 0);
                $date_end = (clone $current_date)->setTime(6, 0, 0);
            } else {
                if ($current_date <= (clone $current_date)->setTime(18, 20, 0))
                    $date_begin = (clone $current_date)->setTime(17, 30, 0);
                else
                    $date_begin = (clone $current_date)->setTime(18, 0, 0);
                $date_end = (clone $current_date)->add(new \DateInterval('P1D'))->setTime(6, 0, 0);
            }
        }
        $rapports = DB::select("SELECT r.id, r.dtarrived, r.created_at, r.type_rapport_id, u.email as 'operateur', t.nom as 'type'
            from rapports r
            left join users u on u.id = r.user_id
            left join type_rapports t on t.id = r.type_rapport_id
            where (r.soft_delete is null or r.soft_delete = 0) and r.site_id = ?
            and ((r.type_rapport_id not in (1, 3) and r.created_at >= ? ) or (r.type_rapport_id in (1, 3) and r.fin >= ?))
            order by r.created_at desc", [$site_id, $date_begin, $date_begin]);
        foreach ($rapports as $r)
            $r->nb_alarm = 0;

        if (count($rapports)) {
            $ids = array_column($rapports, 'id');
            $alarms = DB::select("SELECT a.idademco, a.rapport_id FROM ademcomemlog a
                where a.rapport_id in (" . implode(", ", $ids) . ")");
            $logs = DB::select("SELECT a.idademco, a.rapport_id FROM ademcotemp a
                where a.rapport_id in (" . implode(", ", $ids) . ")");
            foreach ($rapports as $r) {
                foreach ($alarms as $a)
                    if ($a->rapport_id == $r->id)
                        $r->nb_alarm++;
                foreach ($logs as $l)
                    if ($l->rapport_id == $r->id)
                        $r->nb_alarm++;
            }
        }
        return response()->json($rapports);
    }

    public function show($id)
    {
        $rapports = DB::select("SELECT r.id, r.site_id, r.type_rapport_id, r.commentaire, r.dtarrived, r.created_at, r.user_id, r.debut, r.fin,
                r.technicien, r.tache, r.depart, r.arrivee, itv.intervention_id, itv.nom as 'intervention',
                t.nom as 'type', u.email as 'operateur', s.nom as 'site', g.nom as 'group'
            from rapports r
            left join type_rapports t on t.id = r.type_rapport_id
            left join users u on u.id = r.user_id
            left join sites s on s.idsite = r.site_id
            left join sites itv on itv.idsite = r.intervention_id
            left join group_diag_sites g on g.id = s.group_diag_id
            WHERE r.id = ?", [$id]);
        if ($rapports == null)
            return response()->json("empty_value");
        $actions = DB::select("SELECT a.id, t.nom as 'action', a.created_at FROM actions a
            LEFT JOIN type_actions t ON t.id = a.type_action_id
            WHERE a.rapport_id = ?
            ORDER BY a.created_at DESC", [$id]);

        $alarms = DB::select("SELECT a.idademco, a.dtarrived, a.zones, a.codeTevent, ev.Description as 'alarm'
            FROM ademcomemlog a
            LEFT JOIN eventcode ev on ev.code = a.codeTevent
            WHERE rapport_id = ?
            order by dtarrived desc", [$id]);
        $logs = DB::select("SELECT a.idademco, a.dtarrived, a.zones, a.codeTevent, ev.Description as 'alarm'
            FROM ademcotemp a
            LEFT JOIN eventcode ev on ev.code = a.codeTevent
            WHERE rapport_id = ?", [$id]);
        $habilites = Habilite::where('idsite', $rapports[0]->site_id)->where('soft_delete', 0)->get();
        foreach ($logs as $l) {
            $alarms[] = $l;
        }
        $rapports[0]->habilites = $habilites;
        $rapports[0]->alarms = $alarms;
        $rapports[0]->actions = $actions;

        $current_date = (new \DateTime)->format('Y-m-d H:i:s');
        return response()->json(['current_date' => $current_date, 'rapport' => $rapports[0]]);
    }

    public function client_show($id)
    {
        $rapports = DB::select("SELECT r.id, r.site_id, r.type_rapport_id, r.commentaire, r.dtarrived, r.created_at, r.user_id, r.debut, r.fin,
                r.technicien, r.tache, r.depart, r.arrivee,
                t.nom as 'type', u.email as 'operateur', s.nom as 'site', g.nom as 'group'
            from rapports r
            left join type_rapports t on t.id = r.type_rapport_id
            left join users u on u.id = r.user_id
            left join sites s on s.idsite = r.site_id
            left join group_diag_sites g on g.id = s.group_diag_id
            WHERE r.id = ?", [$id]);
        if ($rapports == null)
            return response()->json("empty_value");
        $actions = DB::select("SELECT a.id, t.nom as 'action', a.created_at FROM actions a
            LEFT JOIN type_actions t ON t.id = a.type_action_id
            WHERE a.rapport_id = ?
            ORDER BY a.created_at asc", [$id]);

        $alarms = DB::select("SELECT a.idademco, a.dtarrived, a.zones as numZone, z.nomZone, a.codeTevent, ev.Description as 'alarm'
            FROM ademcomemlog a
            LEFT JOIN sites st on st.idsite = a.site_id
            LEFT JOIN eventcode ev on ev.code = a.codeTevent
            LEFT JOIN zonesites z on z.idsite = st.idsite and z.numZone = a.zones
            WHERE rapport_id = ?
            order by dtarrived asc", [$id]);
        $alarmlog = DB::select("SELECT a.idademco, a.dtarrived, a.zones as numZone, z.nomZone, a.codeTevent, ev.Description as 'alarm'
            FROM ademcolog a
            LEFT JOIN sites st on st.idsite = a.site_id
            LEFT JOIN eventcode ev on ev.code = a.codeTevent
            LEFT JOIN zonesites z on z.idsite = st.idsite and z.numZone = a.zones
            WHERE rapport_id = ?
            order by dtarrived asc", [$id]);
        foreach ($alarmlog as $log) {
            array_push($alarms, $log);
        }
        $rapports[0]->alarms = $alarms;
        $rapports[0]->actions = $actions;

        $current_date = (new \DateTime)->format('Y-m-d H:i:s');
        return response()->json(['current_date' => $current_date, 'rapport' => $rapports[0]]);
    }

    public function store(Request $request)
    {
        if (in_array($request->eventcode, [100, 101, 120, 151, 110, 111, 130, 131, 132, 133, 134, 137, 140])) {
            $rapport = new Rapport();
            $rapport->site_id = $request->site_id;
            $rapport->eventcode = $request->eventcode;
            $rapport->zone = $request->zone;
            $rapport->dtarrived = $request->dtarrived;
            $rapport->idademco = $request->idademco;
            $rapport->created_at = new \DateTime;
            $rapport->updated_at = new \DateTime;
            $rapport->user_id = $request->authId;
            $rapport->save();
            return $this->show($rapport->id);
        } else if ($request->type_rapport_id == 3) {
            $validator = Validator::make(array_filter($request->all(), function ($a) {
                return $a !== "";
            }), [
                'site_id' => 'required',
                'technicien' => 'required',
                'tache' => 'required',
                'debut' => 'required',
                'fin' => 'required',
            ])->setAttributeNames($this->attributeNames);
            if ($validator->fails())
                return response()->json(['error' => $validator->errors()]);
            $rapport = new Rapport();
            $rapport->type_rapport_id = 3;
            $rapport->site_id = $request->site_id;
            $rapport->technicien = $request->technicien;
            $rapport->tache = $request->tache;
            $rapport->debut = $request->debut;
            $rapport->fin = $request->fin;
            $rapport->created_at = new \DateTime;
            $rapport->updated_at = new \DateTime;
            $rapport->user_id = $request->authId;
            $rapport->save();
            return $this->show($rapport->id);
        } else if ($request->type_rapport_id == 6) {
            $validator = Validator::make(array_filter($request->all(), function ($a) {
                return ($a !== "" && $a !== 0);
            }), [
                'site_id' => 'required',
                'intervention_id' => 'required',
                'commentaire' => 'required',
                'depart' => 'required',
                'arrivee' => 'required',
                'fin' => 'required',
            ])->setAttributeNames($this->attributeNames);
            if ($validator->fails())
                return response()->json(['error' => $validator->errors()]);
            $rapport = new Rapport();
            $rapport->type_rapport_id = 6;
            $rapport->site_id = $request->site_id;
            $rapport->intervention_id = $request->intervention_id;
            $rapport->commentaire = $request->commentaire;
            $rapport->depart = $request->depart;
            $rapport->arrivee = $request->arrivee;
            $rapport->fin = $request->fin;
            $rapport->created_at = new \DateTime;
            $rapport->updated_at = new \DateTime;
            $rapport->user_id = $request->authId;
            $rapport->save();
            return $this->show($rapport->id);
        }
        return response()->json(false);
    }

    public function update($id, Request $request)
    {
        $rapport = Rapport::find($id);
        if ($request->authId == $rapport->user_id) {
            $validator = Validator::make(array_filter($request->all(), function ($a) {
                return $a !== "";
            }), [
                'type_rapport_id' => 'required',
            ])->setAttributeNames($this->attributeNames);
            if ($validator->fails())
                return response()->json(['error' => $validator->errors()]);

            $current_date = new \DateTime;
            if ($current_date >= (clone $current_date)->setTime(6, 0, 0) && $current_date < (clone $current_date)->setTime(18, 0, 0)) {
                $date_begin = (clone $current_date)->setTime(6, 0, 0);
                $date_end = (clone $current_date)->setTime(18, 0, 0);
            } else {
                if ($current_date < (clone $current_date)->setTime(6, 0, 0)) {
                    $date_begin = (clone $current_date)->sub(new \DateInterval('P1D'))->setTime(18, 00, 0);
                    $date_end = (clone $current_date)->setTime(06, 00, 00);
                } else {
                    $date_begin = (clone $current_date)->setTime(18, 00, 0);
                    $date_end = (clone $current_date)->add(new \DateInterval('P1D'))->setTime(06, 00, 00);
                }
            }
            if ($request->type_rapport_id == 1 || $request->type_rapport_id == 6) {
                $validator = Validator::make(array_filter($request->all(), function ($a) {
                    return ($a !== "" && $a !== 0);
                }), [
                    'intervention_id' => 'required',
                    'commentaire' => 'required',
                    'depart' => 'required',
                    'arrivee' => 'required',
                    'fin' => 'required',
                ])->setAttributeNames($this->attributeNames);
                if ($validator->fails())
                    return response()->json(['error' => $validator->errors()]);

                $depart = \DateTime::createFromFormat("Y-m-d H:i:s", $request->depart);
                $arrivee = \DateTime::createFromFormat("Y-m-d H:i:s", $request->arrivee);
                $fin = \DateTime::createFromFormat("Y-m-d H:i:s", $request->fin);
                $diff = $depart->diff($arrivee);
                $hours = $diff->h;
                $hours = $hours + ($diff->days * 24);
                $diff_fin = $arrivee->diff($fin);
                $hour_fin = $diff_fin->h;
                $hour_fin = $hour_fin + ($diff_fin->days * 24);
                if (
                    !$depart || !$arrivee || $depart >= $arrivee || $hours > 6 || $hour_fin > 6
                    || (($depart < $date_begin || $depart > $date_end) && ($arrivee < $date_begin || $arrivee > $date_end))
                    || $arrivee > $fin
                )
                    return response()->json(['error' => ['depart' => ["L'interval de date est incorrecte."]]]);
                else if ($fin <= $date_begin)
                    return response()->json(['error' => ['fin' => ["La date fin ne doit pas être inférieur à " . $date_begin->format("H:i:s") . "."]]]);
            }
            if ($request->type_rapport_id == 3) {
                $validator = Validator::make(array_filter($request->all(), function ($a) {
                    return $a !== "";
                }), [
                    'technicien' => 'required',
                    'tache' => 'required',
                    'debut' => 'required',
                    'fin' => 'required',
                ])->setAttributeNames($this->attributeNames);
                if ($validator->fails())
                    return response()->json(['error' => $validator->errors()]);

                $debut = \DateTime::createFromFormat("Y-m-d H:i:s", $request->debut);
                $fin = \DateTime::createFromFormat("Y-m-d H:i:s", $request->fin);
                $diff = $debut->diff($fin);
                $hours = $diff->h;
                $hours = $hours + ($diff->days * 24);
                if (
                    !$debut || !$fin || $debut >= $fin || $hours > 6
                    || (($debut < $date_begin || $debut > $date_end) && ($fin < $date_begin || $fin > $date_end))
                )
                    return response()->json(['error' => ['debut' => ["L'interval de date est incorrecte."]]]);
                else if ($fin <= $date_begin)
                    return response()->json(['error' => ['fin' => ["La date fin ne doit pas être inférieur à " . $date_begin->format("H:i:s") . "."]]]);
            }

            if ($request->type_rapport_id != 3 && $request->type_rapport_id != 6 && $rapport->dtarrived == null) {
                return response()->json(['error' => ['type_rapport_id' => ["Action non permise."]]]);
            }
            if ($request->authId == $rapport->user_id) {
                $rapport->type_rapport_id = $request->type_rapport_id;
                $rapport->commentaire = $request->commentaire;
                $rapport->updated_at = new \DateTime;
                if ($request->type_rapport_id == 1 || $request->type_rapport_id == 6) {
                    $rapport->intervention_id = $request->intervention_id;
                }
                if ($request->type_rapport_id == 1 || $request->type_rapport_id == 6) {
                    $rapport->depart = $request->depart;
                    $rapport->arrivee = $request->arrivee;
                    $rapport->fin = $request->fin;
                } else if ($request->type_rapport_id == 3) {
                    $rapport->technicien = $request->technicien;
                    $rapport->tache = $request->tache;
                    $rapport->commentaire = null;
                    $rapport->debut = $request->debut;
                    $rapport->fin = $request->fin;
                }
                if ($rapport->idademco) {
                    $log = Log::find($rapport->idademco);
                    if ($log) {
                        $log->rapport_id = $rapport->id;
                        $log->save();
                    }
                }
                $rapport->save();
                return response()->json(true);
            }
        }
    }

    public function soft_delete($id, Request $request)
    {
        $rapport = Rapport::find($id);
        if ($rapport != null && $request->authId == $rapport->user_id) {
            if (
                count(DB::select("SELECT idademco from ademcomemlog where rapport_id = ?", [$rapport->id])) > 0
                || count(DB::select("SELECT idademco from ademcotemp where rapport_id = ?", [$rapport->id])) > 0
            )
                return response()->json(["error" => "Un ou plusieurs alarmes sont rattachés sur ce rapport"]);
            $rapport->soft_delete = true;
            return response()->json($rapport->save());
        }
        return response()->json(true);
    }

    public function restaure($id, Request $request)
    {
        $rapport = Rapport::find($id);
        if ($rapport != null && $request->authId == $rapport->user_id && $rapport->soft_delete == true) {
            $rapport->soft_delete = false;
            return response()->json($rapport->save());
        }
        return response()->json(true);
    }

    public function cancel($id, Request $request)
    {
        $rapport = Rapport::find($id);
        if ($rapport != null && $request->authId == $rapport->user_id && $rapport->type_rapport_id == null) {
            Action::where('rapport_id', $id)->delete();
            return response()->json($rapport->delete());
        }
        return response()->json(true);
    }

    public function attach_alarm($id, Request $request)
    {
        $rapport = Rapport::find($id);
        $alarm = Log::find($request->idademco);

        $current_date = new \DateTime;
        if ($current_date >= (clone $current_date)->setTime(6, 0, 0) && $current_date < (clone $current_date)->setTime(18, 0, 0)) {
            if ($current_date <= (clone $current_date)->setTime(6, 20, 0))
                $date_begin = (clone $current_date)->setTime(5, 30, 0);
            else
                $date_begin = (clone $current_date)->setTime(6, 0, 0);
            $date_end = (clone $current_date)->setTime(18, 0, 0);
        } else {
            if ($current_date < (clone $current_date)->setTime(6, 0, 0)) {
                $date_begin = (clone $current_date)->sub(new \DateInterval('P1D'))->setTime(17, 30, 0);
                $date_end = (clone $current_date)->setTime(6, 0, 0);
            } else {
                if ($current_date <= (clone $current_date)->setTime(18, 20, 0))
                    $date_begin = (clone $current_date)->setTime(17, 30, 0);
                else
                    $date_begin = (clone $current_date)->setTime(18, 0, 0);
                $date_end = (clone $current_date)->add(new \DateInterval('P1D'))->setTime(6, 0, 0);
            }
        }
        $date_begin = $date_begin->getTimestamp();
        $date_end = $date_end->getTimestamp();
        $created_at = strtotime($rapport->created_at);
        $fin = strtotime($rapport->fin);
        if (
            $alarm != null && $rapport != null && $rapport->type_rapport_id != 6 && $rapport->site_id == $alarm->site_id
            && in_array($alarm->codeTevent, [100, 101, 120, 151, 110, 111, 130, 131, 132, 133, 134, 137, 140])
        ) {
            $alarm->rapport_id = $rapport->id;
            return response()->json($alarm->save());
        }
        return response()->json(false);
    }
}
