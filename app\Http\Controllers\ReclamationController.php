<?php

namespace App\Http\Controllers;

use App\Reclamation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ReclamationController extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
    }
    public function get_current_date(){
        return response()->json((new \DateTime)->format('Y-m-d H:i:s'));
    }
    public function getDayOrNightDate(){
        if(new \DateTime >= (new \DateTime)->setTime(5, 50, 0) &&
                new \DateTime < (new \DateTime)->setTime(17, 50, 0))
            return (new \DateTime)->setTime(07, 0, 0)->format('Y-m-d H:i:s');
        else if(new \DateTime < (new \DateTime)->setTime(5, 50, 0))
            return (new \DateTime)->setTime(18, 0, 0)->sub(new \DateInterval('P1D'))->format('Y-m-d H:i:s');
        return (new \DateTime)->setTime(18, 00, 0)->format('Y-m-d H:i:s');
    }

    public function getDayOrNightDateLastService(){
        if(new \DateTime >= (new \DateTime)->setTime(5, 50, 0) && new \DateTime < (new \DateTime)->setTime(17, 50, 0))
            return (new \DateTime)->sub(new \DateInterval('P1D'))->setTime(18, 0, 0)->format('Y-m-d H:i:s');
        else if(new \DateTime < (new \DateTime)->setTime(5, 50, 0))
            return (new \DateTime)->setTime(07, 0, 0)->sub(new \DateInterval('P1D'))->format('Y-m-d H:i:s');    
        return (new \DateTime)->setTime(07, 00, 0)->format('Y-m-d H:i:s');
    }
    
    public function index(Request $request){
        $offset = $request->offset;
        $value = $request->value;
        $condition = "";
        if($value || $value != ""){
            $condition = "AND (ag.nom LIKE '%$value%' 
            OR ag.numero_employe LIKE '%$value%' 
            OR ag.num_emp_soit LIKE '%$value%' 
            OR ag.numero_stagiaire LIKE '%$value%'
            OR s.nom LIKE '%$value%'
            OR r.agent_not_registered LIKE '%$value%') ";
        }
        $reclamations = DB::select("SELECT r.id, r.date_pointage, r.agent_id, r.agent_not_registered, r.type, ag.nom, ag.societe_id, ag.numero_employe, r.type,
            ag.num_emp_soit, ag.numero_stagiaire, s.nom as 'site', au.name as 'superviseur', au.email as 'superviseur_email'
            FROM reclamations r
            LEFT JOIN agents ag on ag.id = r.agent_id 
            LEFT JOIN sites s on s.idsite = r.site_id
            LEFT JOIN admin_users au on au.id = r.superviseur_id
            WHERE r.date_pointage = ?
            ". $condition ."
            ORDER BY r.id desc LIMIT ?, 50 ", [ReclamationController::getDayOrNightDate(), $offset]);
        $ids = [];
        foreach (array_column($reclamations, 'agent_id') as $id) {
            if(!empty($id))
                $ids[] = $id;
        }
        if(count($ids) > 0){
            $service24s = DB::select("SELECT id as 'service24_id', employe_id FROM service24s 
                WHERE status != 'draft' and (date_pointage = ? or DATE_ADD(date_pointage, INTERVAL 1 HOUR) = ?) 
                and employe_id in (" . implode(', ', $ids) . ")", [ReclamationController::getDayOrNightDate(), ReclamationController::getDayOrNightDate()]);
            $pointages = DB::select("SELECT id as 'pointage_id', agent_id FROM pointages WHERE (soft_delete is null or soft_delete = 0) and date_pointage = ?
                and agent_id in (" . implode(', ', $ids) . ")", [ReclamationController::getDayOrNightDate()]);
            foreach ($reclamations as $r) {
                foreach ($service24s as $s24) {
                    if($s24->employe_id == $r->agent_id)
                        $r->service24_id = $s24->service24_id;
                }
                foreach ($pointages as $p) {
                    if($p->agent_id == $r->agent_id)
                        $r->pointage_id = $p->pointage_id;
                }
            }
        }
        return response()->json(compact('reclamations'));
    }

    public function show(Request $request, $id){
        $reclamation = DB::select("SELECT r.id, r.id as 'reclamation_id', r.date_pointage, r.agent_id, r.agent_not_registered, ag.nom, ag.societe_id, ag.numero_employe, 
            ag.num_emp_soit, ag.numero_stagiaire, s.nom as 'site', s.phone_agent, au.name as 'superviseur', au.email as 'superviseur_email',
            ser.id as 'service24_id', p.id as 'ptg_id', last_ptg.id as 'last_service_ptg_id', r.site_id
            FROM reclamations r
            LEFT JOIN service24s ser ON r.agent_id = ser.employe_id and (ser.date_pointage = r.date_pointage  OR  DATE_ADD(ser.date_pointage, INTERVAL 1 HOUR) = r.date_pointage)
            LEFT JOIN pointages p on p.date_pointage = ? AND p.agent_id = r.agent_id
            LEFT JOIN pointages last_ptg on last_ptg.date_pointage = ? AND last_ptg.agent_id = r.agent_id
            LEFT JOIN agents ag on ag.id = r.agent_id 
            LEFT JOIN sites s on s.idsite = r.site_id
            LEFT JOIN admin_users au on au.id = r.superviseur_id
            WHERE r.id = ?", [ReclamationController::getDayOrNightDate(), ReclamationController::getDayOrNightDateLastService(), $id]);
        $site = DB::selectOne("SELECT s.idsite, s.nom, nb_agent_day, nb_agent_night,
            group_pointage_id, pointage_biometrique, s.pointeuse, s.pointeuse_id
            FROM sites s WHERE idsite = ?", [$reclamation[0]->site_id]);
        $reclamation[0]->site_detail = $site;
        return response()->json(compact('reclamation'));
    }

    public function delete(Request $request, $id){
        $reclamation = Reclamation::find($id);
        if($reclamation){
            $reclamation->delete();
            
            if($request->input('simple_delete') == 1){
                return response()->json(['succes' => 'Suppression réussie', "id" => $id]);
            }
            $pointageController = new PointageController();
            return response()->json($pointageController->get_pointage($reclamation->site_id));
        }
    }
}
