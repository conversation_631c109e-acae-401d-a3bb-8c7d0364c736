<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\RepartitionTache;
use App\ConfirmationTache;

class RepartitionTacheController extends Controller
{
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
    }
    
    public function getDayOrNightDate(){
        if(new \DateTime >= (new \DateTime)->setTime(07, 0, 0) &&
                new \DateTime < (new \DateTime)->setTime(18, 0, 0))
            return (new \DateTime)->setTime(07, 0, 0)->format('Y-m-d H:i:s');
        else if(new \DateTime < (new \DateTime)->setTime(07, 0, 0))
            return (new \DateTime)->setTime(18, 0, 0)->sub(new \DateInterval('P1D'))->format('Y-m-d H:i:s');
        return (new \DateTime)->setTime(18, 0, 0)->format('Y-m-d H:i:s');
    }

    public function assign_task(Request $request){
        $horaire = $this->getDayOrNightDate();
        if(ConfirmationTache::where('horaire', $horaire)->first() == null){
            $repartition = RepartitionTache::where('horaire', $horaire)
                ->where('vigilance_group_id', $request->group_id)
                ->first();
            if($repartition == null){
                $repartition = new RepartitionTache();
                $repartition->horaire = $horaire;
                $repartition->user_id = $request->authId;
                $repartition->vigilance_group_id = $request->group_id;
                return response()->json($repartition->save());
            }
        }
        return response()->json(false);
    }

    public function cancel_task(Request $request){
        $horaire = $this->getDayOrNightDate();
        if(ConfirmationTache::where('horaire', $horaire)->first() == null){
            $repartition = RepartitionTache::where('horaire', $horaire)
                ->where('user_id', $request->authId)
                ->where('vigilance_group_id', $request->group_id)
                ->first();
            if($repartition != null){
                return response()->json($repartition->delete());
            }
        }
        return response()->json(false);
    }
    public function lock_task(Request $request){
        $horaire = $this->getDayOrNightDate();
        $emptyGroup = DB::select("SELECT g.id, g.nom, y.username  FROM group_sites g
            LEFT JOIN (
                SELECT r.vigilance_group_id, u.email as username FROM repartition_taches r 
                LEFT JOIN users u ON u.id = r.user_id WHERE r.horaire = ?
            ) y ON y.vigilance_group_id = g.id
            WHERE y.username is null
            order by nom", [$horaire]);
        if($emptyGroup == null){
            $confirmation = new ConfirmationTache();
            $confirmation->horaire = $horaire;
            return response()->json($confirmation->save());
        }
        return response()->json(false);
    }
}