<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Pointage;
use App\Site;
use App\GroupSite;
use App\ConfirmationTache;
use App\User;
use App\IndiceTache;

class ReportController extends Controller
{
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
    }

    public function getDayOrNightDate()
    {
        if (
            new \DateTime >= (new \DateTime)->setTime(07, 0, 0) &&
            new \DateTime < (new \DateTime)->setTime(18, 0, 0)
        )
            return (new \DateTime)->setTime(07, 0, 0)->format('Y-m-d H:i:s');
        else if (new \DateTime < (new \DateTime)->setTime(07, 0, 0))
            return (new \DateTime)->setTime(18, 0, 0)->sub(new \DateInterval('P1D'))->format('Y-m-d H:i:s');
        return (new \DateTime)->setTime(18, 0, 0)->format('Y-m-d H:i:s');
    }

    public function index(Request $request)
    {
        if (in_array($request->authRole, ['rh', 'root', 'room', 'op']))
            $day = DB::select("SELECT s.idsite, s.prom, s.nom, g.vigilance_group_id as group_id FROM sites s
                LEFT JOIN group_sites g ON g.id = s.group_id
                WHERE s.vigilance = 1  and (s.night is null or s.night = 0) and (s.soft_delete = 0 or s.soft_delete is null)
                ORDER BY s.nom ASC");
        else if ($request->authRole == 'client' && $request->authGroupId)
            $day = DB::select("SELECT s.idsite, s.prom, s.nom, g.vigilance_group_id as group_id FROM sites s
                LEFT JOIN group_sites g ON g.id = s.group_id
                WHERE s.vigilance = 1  and (s.night is null or s.night = 0) and (s.soft_delete = 0 or s.soft_delete is null)
                and s.group_id = ?
                ORDER BY s.nom ASC", [$request->authGroupId]);
        if (in_array($request->authRole, ['rh', 'root', 'room', 'op']))
            $night = DB::select("SELECT s.idsite, s.prom, s.nom, g.vigilance_group_id as group_id FROM sites s
                LEFT JOIN group_sites g ON g.id = s.group_id
                WHERE s.vigilance = 1  and (s.day is null or s.day = 0) and (s.soft_delete = 0 or s.soft_delete is null)
                ORDER BY s.nom ASC");
        else if ($request->authRole == 'client' && $request->authGroupId)
            $night = DB::select("SELECT s.idsite, s.prom, s.nom, g.vigilance_group_id as group_id FROM sites s
                LEFT JOIN group_sites g ON g.id = s.group_id
                WHERE s.vigilance = 1  and (s.day is null or s.day = 0) and (s.soft_delete = 0 or s.soft_delete is null)
                and s.group_id = ?
                ORDER BY s.nom ASC", [$request->authGroupId]);

        $pointage_sites = Site::select('idsite', 'prom', 'nom', 'group_pointage_id as group_id')
            ->where(function ($query) {
                return $query->where('soft_delete', '0')
                    ->orWhereNull('soft_delete');
            })
            ->where('pointage', '1')
            ->orderBy('nom')
            ->get();
        $group_sites = DB::select("SELECT g.id, g.nom, y.username  FROM group_sites g
            LEFT JOIN (
                SELECT r.vigilance_group_id, u.email as username FROM repartition_taches r
                LEFT JOIN users u ON u.id = r.user_id WHERE r.horaire = ?
            ) y ON y.vigilance_group_id = g.id
            order by nom", [$this->getDayOrNightDate()]);
        $current_date = (new \DateTime)->format('Y-m-d H:i:s');
        $pointage_date = $this->getDayOrNightDate();
        $locked = (ConfirmationTache::where('horaire', $this->getDayOrNightDate())->first() != null);
        $users = User::select('id', 'email')->where('role', 'room')->where('id', '!=', 7)->get();
        return response()->json(compact('day', 'night', 'current_date', 'pointage_date', 'pointage_sites', 'group_sites', 'locked', 'users'));
    }

    public function pointage_agent(Request $request)
    {
        $pointages = DB::select("SELECT a.prenom, a.nom, a.numero_employe, a.numero_stagiaire, p.agent_id, p.site_id
            FROM pointages p
            LEFT JOIN agents a ON a.id = p.agent_id
            WHERE p.date_pointage = ?", [$request->datetime]);
        return response()->json($pointages);
    }

    public function pointage_mensuel(Request $request)
    {
        $agents = DB::select("SELECT a.id, a.nom, a.numero_employe, a.numero_stagiaire, a.num_emp_soit,
                    a.societe_id, a.site_id AS site_id, s.nom AS site,
                    COALESCE(GROUP_CONCAT(DISTINCT n.numero SEPARATOR ', '), '') AS phone_agent
            FROM agents a
            LEFT JOIN sites s ON s.idsite = a.site_id
            LEFT JOIN numeros n ON n.id_site = s.idsite
            WHERE s.group_pointage_id = ?
            GROUP BY a.id, s.nom
            ORDER BY s.nom, a.numero_employe, a.numero_stagiaire, a.nom
            ", [$request->group_id]);

        $agent_ids = [];
        foreach ($agents as $a) {
            array_push($agent_ids, $a->id);
        }
        $pointages = DB::select("SELECT p.agent_id, p.site_id, p.date_pointage, s.nom as 'site' FROM pointages p
            LEFT JOIN sites s ON s.idsite = p.site_id
            WHERE (p.soft_delete is null or p.soft_delete = 0) and p.date_pointage > ? and p.date_pointage < ?
            and p.agent_id in (" . implode(',', $agent_ids) . " )
            ORDER BY p.date_pointage", [$request->begin, $request->end]);
        return response()->json(compact('agents', 'pointages'));
    }

    public function confirm_repartition_tache(Request $request)
    {
        return response()->json(ConfirmationTache::where('horaire', $this->getDayOrNightDate())->first() == null);
    }

    public function indice_tache(Request $request)
    {
        return response()->json(
            IndiceTache::where('horaire', '>', $request->begin)
                ->where('horaire', '<', $request->end)
                ->get()
        );
    }
}
