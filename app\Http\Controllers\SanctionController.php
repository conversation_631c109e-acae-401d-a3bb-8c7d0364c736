<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Paie;
use App\Sanction;
use App\Http\Util\PaieUtil;
use Validator;

class SanctionController extends Controller
{
    private $attributeNames = array(
        'montant' => 'Montant',
        'motif' => 'Motif',
        'month' => 'Mois',
        'agent_id' => 'Agent'
    );
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
    }

    public function all($agent_id){
        $sanctions = DB::select("SELECT s.id, s.month, s.year, s.montant, s.motif FROM sanctions s
            WHERE s.agent_id = ? and s.paie_id is not null
            ORDER BY s.year DESC, s.month DESC, s.id DESC", [$agent_id]);
        return response()->json(compact('sanctions'));
    }

    public function index($agent_id, Request $request){
        if($request->month != 0 && $request->year != 0)
            $sanctions = DB::select("SELECT s.id, s.month, s.year, s.montant, s.motif FROM sanctions s
                WHERE s.agent_id = ? and s.month = ? and s.year = ?
                ORDER BY s.year DESC, s.month DESC, s.id DESC", [$agent_id, $request->month, $request->year]);
        else
            $sanctions = DB::select("SELECT s.id, s.month, s.year, s.montant, s.motif FROM sanctions s
                WHERE agent_id = ? and paie_id is null
                ORDER BY s.year DESC, s.month DESC, s.id DESC", [$agent_id]);
        return response()->json(compact('sanctions'));
    }
    public function month($agent_id){
        $interval = PaieUtil::getIntervalByAgent($agent_id, 0, 0);
        $month = $interval['end']->format('m');
        $year = $interval['end']->format('Y');
        return response()->json(compact('month', 'year'));
    }
    public function store(Request $request){
        if($request->authRole == 'root' || $request->authRole == 'rh'){
            $validator = Validator::make($request->all(), [
                'agent_id' => ['required'],
                'montant' => ['integer', 'required'],
                'motif' => ['required'],
                'month' => ['required'],
            ])->setAttributeNames($this->attributeNames);
            if($validator->fails())
                return \response()->json(['error' => $validator->errors()]);
            
            $isValidMonth = false;
            $day = null;
            $agents = DB::select("SELECT a.id, g.day FROM agents a
                LEFT JOIN sites s ON s.idsite = a.site_id 
                LEFT JOIN group_pointage_sites g ON g.id = s.group_pointage_id 
                WHERE a.id = ?", [$request->agent_id]);
            if($agents)
                $day = $agents[0]->day;
            $paie = Paie::select('day', 'confirm', 'confirm_hour')
                ->where('agent_id', $request->agent_id)
                ->where('month', $request->month)
                ->where('year', $request->year)
                ->first();
            if($paie && $paie->confirm_hour == 1)
                $day = $paie->day;
            if($day && PaieUtil::isConfirmable($day, $request->month, $request->year) && ($paie == null || $paie->confirm == 0))
                $isValidMonth = true;
            else if($day && PaieUtil::isAfterConfirmable($day, $request->month, $request->year))
                $isValidMonth = true;
            
            if($isValidMonth){
                $sanction = new Sanction();
                $sanction->agent_id = $request->agent_id;
                $sanction->montant = $request->montant;
                $sanction->month = $request->month;
                $sanction->year = $request->year;
                $sanction->motif = $request->motif;
                $sanction->created_at = now();
                $sanction->updated_at = now();
                return response()->json($sanction->save());
            }
            else{
                return response()->json(['error' => ['not_allowed' => ["Ce mois ne peut plus être sélectionné"]]]);
            }
        }
        return response()->json(false);
    }

    public function update(Request $request, $id){
        if($request->authRole == 'root' || $request->authRole == 'rh'){
            $validator = Validator::make($request->all(), [
                'montant' => ['integer', 'required'],
                'motif' => ['required'],
                'month' => ['required'],
            ])->setAttributeNames($this->attributeNames);
            if($validator->fails())
                return response()->json(['error' => $validator->errors()]);
            $sanction = Sanction::where('id', $id)->whereNull('paie_id')->first();
            if($sanction != null){
                $isValidMonth = false;
                $day = null;
                $agents = DB::select("SELECT a.id, g.day FROM agents a
                    LEFT JOIN sites s ON s.idsite = a.site_id 
                    LEFT JOIN group_pointage_sites g ON g.id = s.group_pointage_id 
                    WHERE a.id = ?", [$id]);
                if($agents)
                    $day = $agents[0]->day;
                $paie = Paie::select('day', 'confirm', 'confirm_hour')
                    ->where('agent_id', $id)
                    ->where('month', $request->month)
                    ->where('year', $request->year)
                    ->first();
                if($paie && $paie->confirm_hour == 1)
                    $day = $paie->day;
                if($day && PaieUtil::isConfirmable($day, $request->month, $request->year) && ($paie == null || $paie->confirm == 0))
                    $isValidMonth = true;
                else if($day && PaieUtil::isAfterConfirmable($day, $request->month, $request->year))
                    $isValidMonth = true;
                
                if($isValidMonth){
                    $sanction->montant = $request->montant;
                    $sanction->month = $request->month;
                    $sanction->year = $request->year;
                    $sanction->motif = $request->motif;
                    $sanction->updated_at = now();
                    return response()->json($sanction->save());
                }
                else{
                    return response()->json(['error' => ['not_allowed' => ["Ce mois ne peut plus être sélectionné"]]]);
                }
            }
        }
        return response()->json(false);
    }
    public function delete($id, Request $request){
        if($request->authRole == 'root' || $request->authRole == 'rh'){
            $sanction = Sanction::where('id', $id)->whereNull('paie_id')->first();
            if($sanction != null)
                return response()->json($sanction->delete());
        }
        return false;
    }
}