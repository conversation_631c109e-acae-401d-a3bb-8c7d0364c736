<?php

namespace App\Http\Controllers;

use App\Service24;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class Service24Controller extends Controller
{
    public function __construct(){
        date_default_timezone_set("Indian/Antananarivo");
    }

    public function getDayOrNightDate(){
        if(new \DateTime >= (new \DateTime)->setTime(5, 50, 0) &&
                new \DateTime < (new \DateTime)->setTime(17, 50, 0))
            return (new \DateTime)->setTime(07, 0, 0)->format('Y-m-d H:i:s');
        else if(new \DateTime < (new \DateTime)->setTime(5, 50, 0))
            return (new \DateTime)->setTime(18, 0, 0)->sub(new \DateInterval('P1D'))->format('Y-m-d H:i:s');
        return (new \DateTime)->setTime(18, 00, 0)->format('Y-m-d H:i:s');
    }

    public function index(Request $request){
        $condition = "";
        $value = $request->value;
        if($value || $value != ""){
            $condition = "AND (ag.nom LIKE '%$value%' 
            OR ag.numero_employe LIKE '%$value%' 
            OR ag.num_emp_soit LIKE '%$value%' 
            OR ag.numero_stagiaire LIKE '%$value%') ";
        }
        $services = DB::select("SELECT srv.id, srv.employe_id, srv.status, srv.motif, srv.date_pointage,
            ag.nom, ag.societe_id, ag.numero_stagiaire, ag.num_emp_soit, ag.num_emp_saoi, ag.numero_employe,
            ptg.id as 'pointage_id'
            FROM service24s srv
            LEFT JOIN agents ag ON ag.id = srv.employe_id
            LEFT JOIN pointages ptg ON ptg.agent_id = srv.employe_id AND ptg.date_pointage = ?
            WHERE srv.status != 'draft' and (srv.date_pointage = ? OR DATE_ADD(srv.date_pointage, INTERVAL 1 HOUR) = ?)
            ". $condition . "
            LIMIT ?, 50", [$this->getDayOrNightDate(), $this->getDayOrNightDate(), $this->getDayOrNightDate(), $request->offset]);
        return response()->json(compact('services'));
    }

    public function show(Request $request, $id){
        $service = DB::select("SELECT s.id, s.begin_pointage, s.end_pointage, s.employe_id, s.status, s.motif, s.date_pointage,
            ag.nom, ag.societe_id, ag.numero_stagiaire, ag.num_emp_soit, ag.num_emp_saoi, ag.numero_employe,
            ptg.id as 'pointage_id'
            FROM service24s s
            LEFT JOIN agents ag ON ag.id = s.employe_id
            LEFT JOIN pointages ptg ON ptg.agent_id = s.employe_id AND ptg.date_pointage = ?
            WHERE s.id = ?", [$this->getDayOrNightDate(), $id]);
        return response()->json(compact('service'));
    }
}
