<?php

namespace App\Http\Controllers;

use App\Signale;
use Illuminate\Http\Request;

class SignaleController extends Controller
{
    public function site($site_id){
        $signales = Signale::where('site_id', $site_id)->get();
        foreach ($signales as $sign) {
            $sign->received;
            $sign->emit;
        }
        return response()->json($signales);
    }
    public function store(Request $request){
        $signale = new Signale();
        $signale->user_id = $request->user_id;
        $signale->site_id = $request->site_id;
        $signale->zone = $request->zone;
        $signale->received_code = $request->received_code;
        $signale->emit_code = $request->emit_code;
        return response()->json($signale->save());
    }
    public function update($id, Request $request){
        $signale = Signale::find($id);
        $signale->user_id = $request->user_id;
        $signale->site_id = $request->site_id;
        $signale->zone = $request->zone;
        $signale->received_code = $request->received_code;
        $signale->emit_code = $request->emit_code;
        return response()->json($signale->save());
    }
    public function delete($id){
        $signale = Signale::find($id);
        return response()->json($signale->delete());
    }
}