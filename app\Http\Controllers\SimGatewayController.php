<?php

namespace App\Http\Controllers;


use Illuminate\Support\Facades\DB;
use App\SimGateway;
use Illuminate\Http\Request;

class SimGatewayController extends Controller
{
    public function update($transmitter, Request $request){
        if(in_array($request->authRole, ['root'])){
            $sim = SimGateway::where('numero', $transmitter)->first();
            if($sim == null)
                $sim = new SimGateway();
            $sim->numero = $transmitter;
            $sim->sim_group_id = $request->sim_group_id;
            $sim->gateway_id = $request->gateway_id;
            return response()->json($sim->save());
        }
        return response()->json(false);
    }
}