<?php

namespace App\Http\Controllers;

use App\Pointeuse;
use App\Site;
use App\Centrale;
use App\Client;
use App\Numero;
use App\GroupSite;
use App\GroupPointageSite;
use App\GroupDiagSite;
use App\HistoriqueSite;
use App\Horaire;
use App\JourFerie;
use App\Secteur;
use App\GroupInterventionSite;
use Illuminate\Http\Request;
use Validator;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;

class SiteController extends Controller
{
    private $attributeNames = array(
        'nom' => 'Nom',
        'centrale' => 'Centrale',
        'prom' => 'Prom',
        'puce' => 'Numéro du puce',
        'secteur_id' => 'Secteur',
        'group_diag_id' => 'Groupe diagnostique',
        'interval_test' => 'Interval de diagnostique',
        'professionnel' => 'Type diagnostique',
        'pointeuse_id' => 'Pointeuse',
    );
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
    }
    public function new_prom()
    {
        $new_prom = DB::select("SELECT max(prom) as last_prom from sites WHERE cast(prom as DECIMAL) < 9999");
        if ($new_prom != null) {
            $new_prom = $new_prom[0]->last_prom + 1;
        }
        return response()->json($new_prom);
    }
    public function index(Request $request)
    {
        return response()->json(
            DB::table('sites as s')
                ->selectRaw("s.idsite, s.nom, s.prom, s.adresse, s.numeropuces, s.arm,
                    (
                        (s.without_system IS NULL OR s.without_system = 0)
                        AND (s.centrale_drx IS NULL OR s.centrale_drx = 0)
                        AND (s.created_at IS NULL OR TIMESTAMPDIFF(DAY, s.created_at, NOW()) >= 1)
                        AND (s.date_last_signal IS NULL OR TIMESTAMPDIFF(HOUR, s.date_last_signal, NOW()) >= 25)
                    ) AS manque")
                ->where(function ($query) use ($request) {
                    $query->whereNull('s.soft_delete')
                        ->orWhere('s.soft_delete', 0);
                })
                ->where(function ($query) use ($request) {
                    $search = "%{$request->search}%";
                    $query->where('s.nom', 'LIKE', $search)
                        ->orWhere('s.prom', 'LIKE', $search)
                        ->orWhere('s.numeropuces', 'LIKE', $search)
                        ->orWhere('s.adresse', 'LIKE', $search);
                })
                ->orderBy('s.lastupdate', 'desc')
                ->offset((int) $request->offset)
                ->limit(50)
                ->get()
        );
    }
    public function archive(Request $request)
    {
        return response()->json(
            DB::select("SELECT idsite, nom, prom, adresse, numeropuces FROM sites WHERE soft_delete=1 and
                 (nom like '%" . $request->search . "%' or prom like '%" . $request->search . "%' or adresse like '%" . $request->search . "%' )" .
                " ORDER BY lastupdate desc limit " . $request->offset . ", 50")
        );
    }
    public function client()
    {
        return response()->json(
            Site::select('idsite', 'nom')
                ->whereNull('soft_delete')
                ->orWhere('soft_delete', '0')
                ->orderBy('lastupdate', 'desc')
                ->get()
        );
    }
    public function selection(Request $request)
    {
        return response()->json(
            DB::select("SELECT s.idsite, s.nom, g.nom as 'group' from sites s
                LEFT JOIN group_pointage_sites g on g.id = group_pointage_id
                WHERE s.pointage = 1 and (soft_delete is null or soft_delete = 0)
                    and s.nom like '%" . $request->search . "%'
                    order by  s.lastupdate desc limit " . $request->offset . ", 50", [])
        );
    }
    public function select_diag()
    {
        return response()->json(
            DB::select("SELECT s.idsite, s.nom, g.nom as 'group' from sites s
                LEFT JOIN group_diag_sites g on g.id = group_diag_id
                WHERE soft_delete is null or soft_delete = 0
                order by idsite")
        );
    }

    public function selectable($id, Request $request)
    {
        $horaire = '';
        $current_date = new \DateTime();
        if (
            new \DateTime >= (new \DateTime)->setTime(06, 50, 0) &&
            new \DateTime < (new \DateTime)->setTime(17, 50, 0)
        )
            $horaire = 'day';
        else {
            if (new \DateTime < (new \DateTime)->setTime(06, 50, 0))
                $current_date = (new \DateTime)->sub(new \DateInterval('P1D'));
            $horaire = 'night';
        }
        $field = $horaire . '_' . $current_date->format('w');

        if (JourFerie::where('date', $current_date->format('Y-m-d'))->first() == null) {
            return response()->json(
                DB::select("SELECT s.idsite, s.nom, s.pointeuse, s.pointage_biometrique from sites s
                    LEFT JOIN horaires h ON h.id = s.horaire_pointage_id
                    WHERE idsite != ? and s.pointage = 1
                    and (soft_delete is null or soft_delete = 0)
                    and (h.id is null or (h." . $field . " is not null and h." . $field . " = 1))
                    and s.nom like '%" . $request->search . "%'
                    order by  s.lastupdate desc limit " . $request->offset . ", 50", [$id])
            );
        } else {
            return response()->json(
                DB::select("SELECT s.idsite, s.nom, s.pointeuse, s.pointage_biometrique from sites s
                    LEFT JOIN horaires h ON h.id = s.horaire_pointage_id
                    WHERE idsite != ? and s.pointage = 1
                    and (soft_delete is null or soft_delete = 0)
                    and (h.id is null or (h." . $horaire . "_ferie is not null and h." . $horaire . "_ferie = 1) or (h." . $field . " is not null and h." . $field . " = 1))
                    and s.nom like '%" . $request->search . "%'
                    order by  s.lastupdate desc limit " . $request->offset . ", 50", [$id])
            );
        }
    }

    public function all_site(Request $request)
    {
        return response()->json(
            DB::select("SELECT s.idsite, s.nom, s.pointeuse, s.pointage_biometrique from sites s
                WHERE s.nom like '%" . $request->search . "%'
                order by  s.lastupdate desc limit " . $request->offset . ", 50", [])
        );
    }


    public function notification()
    {
        $sites = DB::select("SELECT idsite, nom, date_report_diag,
            TIMESTAMPDIFF(HOUR, date_report_diag, now()) as 'diff_report_diag',
            (date_last_signal is null or TIMESTAMPDIFF(HOUR, date_last_signal, now()) > interval_test) as 'manque',
            (last_vigilance is null or TIMESTAMPDIFF(HOUR, last_vigilance, now()) >= 48) as 'bouton_panne'
            from sites
            where (soft_delete is null or soft_delete = 0)
            and (date_report_diag is null or date_report_diag < now())
            and (without_system is null or without_system = 0)
            and (created_at is null or TIMESTAMPDIFF(DAY, created_at, now()) >= 1)
            and (
                (date_last_signal is null or TIMESTAMPDIFF(HOUR, date_last_signal, now()) > interval_test)
                or (
                    (vigilance is not null and vigilance = 1) and
                    (last_vigilance is null or TIMESTAMPDIFF(HOUR, last_vigilance, now()) >= 48)
                )
            )");
        return response()->json(['now' => (new \DateTime())->format('Y-m-d'), 'sites' => $sites]);
    }
    public function show($id)
    {
        // Fetch the site with related data, ensuring habilites are not soft deleted
        $site = Site::with(['centrale', 'secteur', 'pointeuse_o', 'client', 'habilites' => function ($query) {
            $query->where('soft_delete', 0); // Only fetch habilites where soft_delete is 0
        }])->find($id);

        // Fetch additional site data with soft_delete condition
        $sites = DB::select("SELECT
        (
            (without_system is null or without_system = 0)
            and (centrale_drx is null or centrale_drx = 0)
            and (
                created_at is null or
                TIMESTAMPDIFF(DAY, created_at, now()) > 1
            )
            and (
                date_last_signal is null or
                TIMESTAMPDIFF(HOUR, date_last_signal, now()) > interval_test + 1
            )
        ) as 'manque'
        FROM sites
        WHERE (soft_delete = 0 or soft_delete is null)
        and idsite = ?", [$id]);

        // Add the 'manque' field to the site object if the query returned results
        if ($sites != null) {
            $site->manque = $sites[0]->manque;
        }

        return response()->json($site);
    }
    public function store(Request $request)
    {
        if (in_array($request->authRole, ['root', 'room'])) {
            $transmission = '';
            if ($request->without_system) {
                $validator = Validator::make($request->all(), [
                    'nom' => ['required', Rule::unique('sites')->where(function ($query) use ($request) {
                        return $query->where('nom', $request->nom);
                    })],
                ]);
                $request->pointeuse_id = null;
                $request->gprs = null;
                $request->sms = null;
                $request->prom = '';
                $request->puce = null;
                $request->centrale = null;
                $request->group_diag_id = null;
                $request->interval_test = null;
                $request->commentaire = null;
            } else if ($request->centrale_drx) {
                $validator = Validator::make($request->all(), [
                    'nom' => ['required', Rule::unique('sites')->where(function ($query) use ($request) {
                        return $query->where('nom', $request->nom);
                    })],
                    'pointeuse_id' => 'required',
                ]);
                $request->prom = null;
                $request->gprs = null;
                $request->sms = null;
                $request->puce = null;
                $request->centrale = null;
                $request->group_diag_id = null;
                $request->interval_test = null;
                $request->commentaire = null;
            } else {
                $request->pointeuse_id = null;
                $request['numeropuces'] = $request->puce;
                $validator = Validator::make($request->all(), [
                    'nom' => ['required', Rule::unique('sites')->where(function ($query) use ($request) {
                        return $query->where('nom', $request->nom);
                    })],
                    'centrale' => 'required',
                    'prom' => ['required', Rule::unique('sites')->where(function ($query) use ($request) {
                        return $query->where('prom', $request->prom);
                    })],
                    'numeropuces' => ['required', Rule::unique('sites')->where(function ($query) use ($request) {
                        return $query->where('numeropuces', $request->numeropuces);
                    })],
                ])->setAttributeNames($this->attributeNames);
                if ($request->prom) {
                    $transmission = self::getTransmissionType($request->prom);
                    if (!$transmission)
                        return response()->json(['error' => ["prom" => ["Transmission incorrect, vérifier le prom"]]]);
                    if (!preg_match("/^03(2|3|4|7|8)\d{7}$/i", $request->puce))
                        return response()->json(['error' => ['puce' => ["Le numero puce est incorrect"]]]);
                    if ($transmission == 'sms' && $request->prom != $request->puce)
                        return response()->json(['error' => ['prom' => ["Le prom et le puce doit être le même."]]]);
                }
            }
            $validator_vigilance = null;
            $validator_pointage = null;
            $validator_checkphone = null;
            if ($request->pointeuse || $request->vigilance) {
                $request->pointage = 1;
                if ($request->pointeuse) {
                    $validator_vigilance = Validator::make($request->all(), [
                        'group_id' => 'required',
                        'horaire_vigilance_id' => ['required', 'integer'],
                    ])->setAttributeNames(['group_id' => 'Groupe vigilance', 'horaire_vigilance_id' => 'Service vigilance']);
                } else {
                    $validator_vigilance = Validator::make($request->all(), [
                        'group_id' => 'required',
                        'horaire_vigilance_id' => ['required', 'integer'],
                    ])->setAttributeNames(['group_id' => 'Groupe vigilance', 'horaire_vigilance_id' => 'Service vigilance']);
                }
            }
            if ($request->pointage == 1) {
                $validator_pointage = Validator::make($request->all(), [
                    'group_pointage_id' => 'required',
                    'horaire_pointage_id' => ['required', 'integer']
                ])->setAttributeNames(['group_pointage_id' => 'Groupe pointage', 'horaire_pointage_id' => 'Service pointage']);
            }
            if ($request->pointage == 1 && $request->do_checkphone == 1) {
                $validator_checkphone = Validator::make($request->all(), [
                    'checkphone' => ['required', 'regex:/^03(2|3|4|7|8)\d{7}$/i', Rule::unique('sites')->where(function ($query) use ($request) {
                        return $query->where('checkphone', $request->checkphone);
                    })]
                ])->setAttributeNames(['group_pointage_id' => 'Groupe pointage', 'horaire_pointage_id' => 'Service pointage']);
            } else
                $request->checkphone = null;
            if ($request->intervention == 1) {
                $validator_intervention = Validator::make($request->all(), [
                    'group_intervention_id' => 'required',
                ])->setAttributeNames(["group_intervention_id" => "Groupe d'intervention"]);
                $request->intervention_id = null;
            } else {
                $validator_intervention = Validator::make($request->all(), [
                    'intervention_id' => 'required',
                ])->setAttributeNames(["intervention_id" => "Equipe d'intervention"]);
                $request->group_intervention_id = null;
            }

            if ($validator->fails()) {
                return response()->json(['error' => $validator->errors()]);
            } else if (!$request->without_system && !$request->centrale_drx && !$transmission) {
                return response()->json([
                    'error' => ['prom' => ['Le champ Prom est incorrect']]
                ]);
            } else if ($validator_vigilance != null && $validator_vigilance->fails()) {
                return response()->json(['error' => $validator_vigilance->errors()]);
            } else if ($validator_pointage != null && $validator_pointage->fails()) {
                return response()->json(['error' => $validator_pointage->errors()]);
            } else if ($validator_checkphone != null && $validator_checkphone->fails()) {
                $site = Site::select('nom')->where('checkphone', $request->checkphone)->first();
                return response()->json(['error' => ['checkphone' => ["Le numéro checkphone existe déjà : " . $site->nom]]]);
            } else if ($validator_intervention != null && $validator_intervention->fails()) {
                return response()->json(['error' => $validator_intervention->errors()]);
            } else {
                $site = new Site();
                $site->prom = $request->prom;
                $site->nom = $request->nom;
                $site->adresse = $request->adresse;
                $site->group_id = $request->group_id;
                $site->group_pointage_id = $request->group_pointage_id;
                $site->notify_arm = $request->notify_arm;
                $site->notify_outage = $request->notify_outage;
                $site->pointeuse = $request->pointeuse;
                $site->vigilance = $request->vigilance;
                $site->numeropuces = $request->puce;
                $site->idcentrale = $request->centrale;
                $site->idClient = $request->client;
                if ($transmission == 'sms') {
                    $site->gprs = null;
                    $site->sms = 1;
                } else if ($transmission == 'gprs') {
                    $site->gprs = 1;
                    $site->sms = null;
                }
                if ($transmission)
                    $site->correct_transmitter = self::getCorrectTransmitter($request);
                $site->pointage_day = $request->pointage_day;
                $site->pointage_night = $request->pointage_night;
                $site->nb_agent_day = $request->nb_agent_day;
                $site->nb_agent_night = $request->nb_agent_night;
                $site->pointeuse_id = $request->pointeuse_id;
                if ($request->pointage)
                    $site->do_checkphone = $request->do_checkphone;
                else
                    $site->do_checkphone = null;
                if ($site->do_checkphone)
                    $site->checkphone = $request->checkphone;
                else
                    $site->checkphone = null;
                $site->pointage = $request->pointage;
                $site->without_system = $request->without_system;
                $site->centrale_drx = $request->centrale_drx;
                $site->pointage_biometrique = $request->pointage_biometrique;
                $site->professionnel = $request->professionnel;
                $site->horaire_vigilance_id = $request->horaire_vigilance_id;
                $site->horaire_pointage_id = $request->horaire_pointage_id;
                $site->group_diag_id = $request->group_diag_id;
                $site->interval_test = $request->interval_test;
                $site->commentaire = $request->commentaire;
                $site->total_hour = $request->total_hour;
                $site->secteur_id = $request->secteur_id;
                $site->intervention = $request->intervention;
                if ($request->intervention)
                    $site->intervention_id = null;
                else $site->intervention_id = $request->intervention_id;
                $site->created_at = now();
                $site->lastupdate = now();
                $site->save();

                foreach ($request->phone_agent as $phone) {
                    DB::table('numeros')->insert([
                        'id_site' => $site->idsite,
                        'numero' => $phone
                    ]);
                }

                if ($request->pointeuse) {
                    $pointeuse = Pointeuse::find($request->pointeuse_id);
                    if ($pointeuse && !$pointeuse->site_id) {
                        $pointeuse->site_id = $site->idsite;
                        $pointeuse->save();
                    }
                }

                if ($site->idsite)
                    $result = $site->idsite;
                else
                    $result = false;

                $details = '';
                $details = $details . 'Nom: ' . $request->nom . '\n';
                if ($request->adresse) $details = $details . 'Adresse: ' . $request->adresse . '\n';
                $client = Client::find($request->client);
                if ($client)
                    $details = $details . 'Client: ' . $client->Societe . '\n';
                if ($request->without_system) $details = $details . 'Site sans système: Oui\n';
                else {
                    $details = $details . ($request->professionnel ? 'Site professionnel' : 'Site particulier') . '\n';
                    $details = $details . 'Group diagnostique: ' . ($request->group_diag_id == 1 ? 'Tana' : ($request->group_diag_id == 2 ? 'Tamatave' : 'Province')) . '\n';
                    $details = $details . 'Interval test cyclique: ' . $request->interval_test . ' heures\n';
                    if ($request->commentaire) $details = $details . 'Commentaire: ' . $request->commentaire . '\n';
                    if ($transmission) $details = $details . 'Transmission: ' . strtoupper($transmission) . '\n';
                    if ($request->prom) $details = $details . 'Prom: ' . $request->prom . '\n';
                    if ($request->puce) $details = $details . 'Puce: ' . $request->puce . '\n';
                    $centrale = Centrale::find($request->centrale);
                    if ($centrale)
                        $details = $details . 'Centrale: ' . $centrale->nom . '\n';
                    if ($request->vigilance) $details = $details . 'Vigilance: Activé\n';
                    $group_v = GroupSite::find($request->group_id);
                    if ($group_v)
                        $details = $details . 'Groupe vigilance: ' . $group_v->nom . '\n';
                    if ($request->horaire_vigilance_id) {
                        $horaire = Horaire::find($request->horaire_vigilance_id)->nom;
                        $details = $details . 'Service vigilance: ' . $horaire . '\n';
                    }
                }
                if ($request->pointage) $details = $details . 'Pointage: Activé\n';
                $group_p = GroupPointageSite::find($request->group_pointage_id);
                if ($group_p)
                    $details = $details . 'Groupe pointage: ' . $group_p->nom . '\n';
                if ($request->horaire_pointage_id) {
                    $horaire = Horaire::find($request->horaire_pointage_id)->nom;
                    $details = $details . 'Service pointage: ' . $horaire . '\n';
                }
                if ($request->pointeuse) $details = $details . 'Pointeuse biométrique: Oui\n';
                if ($request->nb_agent_day) $details = $details . 'Nb agent jour: ' . $request->nb_agent_day . '\n';
                if ($request->nb_agent_night) $details = $details . 'Nb agent nuit: ' . $request->nb_agent_night . '\n';
                if (!empty($request->phone_agent))
                    $details .= 'Contact agent: ' . implode(', ', $request->phone_agent) . "\n";
                if ($request->checkphone) $details = $details . 'Contact agent: ' . $request->checkphone . '\n';
                if ($request->total_hour) $details = $details . 'Total d\'heure: ' . $request->total_hour . ' heures/mois\n';
                if ($request->secteur_id) {
                    $secteur = Secteur::find($request->secteur_id);
                    $details = $details . 'Secteur: ' . $secteur->nom . '\n';
                }
                if ($request->intervention)
                    $details = $details . 'Intervention: Oui\n';
                if ($request->notify_arm) $details = 'Notifier quand il y a armement/désarmement: Oui\n';
                if ($request->notify_outage) $details = 'Notifier quand il y a coupure/rétablissement de courant: Oui\n';

                $historique_site = new HistoriqueSite();
                $historique_site->objet = "Nouveau site";
                $historique_site->detail = $details;
                $historique_site->site_id = $site->idsite;
                $historique_site->user_id = $request->authId;
                $historique_site->created_at = now();
                $historique_site->updated_at = now();
                $historique_site->save();

                return response()->json($result);
            }
        }
    }
    public function diag($id, Request $request)
    {
        if (in_array($request->authRole, ["room", "root"])) {
            $validator = Validator::make($request->all(), [
                'diagnostique_id' => 'required',
                'joignable' => 'required',
            ])->setAttributeNames(['diagnostique_id' => 'Diagnostique', 'joignable' => "Tentative d'appel"]);
            if ($validator->fails())
                return response()->json(['error' => $validator->errors()]);
            $site = Site::find($id);
            if ($site != null) {
                $diagnostique = $request->diagnostique_id == 3 ? 'Manque de transmission' : ($request->diagnostique_id == 2 ? 'Bouton de ronde en panne' : 'Système fonctionnel');
                $details = 'Diagnostique: ' . $diagnostique . '\n' .
                    'Tentative d\'appelle: ' . ($request->joignable ? 'Joignable' : 'Injoignable') . '\n';
                if ($site->commentaire != $request->commentaire)
                    $details = $details . 'Commentaire: ' . $site->commentaire . ' -> ' . $request->commentaire;
                $historique_site = new HistoriqueSite();
                $historique_site->objet = "Diagnostique du site";
                $historique_site->detail = $details;
                $historique_site->site_id = $site->idsite;
                $historique_site->user_id = $request->authId;
                $historique_site->created_at = now();
                $historique_site->updated_at = now();
                $historique_site->save();

                $site->diagnostique_id = $request->diagnostique_id;
                $site->joignable = $request->joignable;
                $site->commentaire = $request->commentaire;
                if ($request->diagnostique_id == 3)
                    $site->date_report_diag = (new \DateTime())->add(new \DateInterval("P7D"))->format('Y-m-d H:i:s');
                else if ($request->diagnostique_id == 2)
                    $site->date_report_diag = (new \DateTime())->add(new \DateInterval("P5D"))->format('Y-m-d H:i:s');
                else
                    $site->date_report_diag = (new \DateTime())->add(new \DateInterval("P2D"))->format('Y-m-d H:i:s');
                $site->lastupdate = now();
                $site->save();
                return $site->idsite;
            }
        }
        return false;
    }
    private function getOperateur($numero)
    {
        $pattern_sms_telma = "/^03(4|8)\d{7}$/i";
        $pattern_sms_orange = "/^03(2|7)\d{7}$/i";
        $pattern_sms_airtel = "/^033\d{7}$/i";
        if (preg_match($pattern_sms_orange, $numero))
            return "orange";
        if (preg_match($pattern_sms_telma, $numero))
            return "telma";
        if (preg_match($pattern_sms_airtel, $numero))
            return "airtel";
        return "";
    }
    private function getTransmissionType($prom)
    {
        $pattern_sms = "/^03(2|3|4|7|8)\d{7}$/i";
        $pattern_gprs = "/^\d{4}$/i";
        $pattern_gprs0000 = "/^0000\d{4}$/i";
        if (preg_match($pattern_sms, $prom))
            return 'sms';
        else if (preg_match($pattern_gprs, $prom) || preg_match($pattern_gprs0000, $prom))
            return 'gprs';
        else
            return false;
    }
    private function getCorrectTransmitter($request)
    {
        $transmission = self::getTransmissionType($request->prom);
        if ($transmission == "gprs") {
            if (self::getOperateur($request->puce) == "telma")
                return '0340554622';
            else if (self::getOperateur($request->puce) == "airtel")
                return '0335011323';
            else
                return '0321134413';
        } else {
            if (self::getOperateur($request->prom) == "telma")
                return '0340554622';
            else if (self::getOperateur($request->prom) == "airtel")
                return '0335011323';
            else if ($request->vigilance == 1) {
                if ($request->group_diag_id == 1)
                    return '0321134413';
                else
                    return '0321154685';
            } else {
                $array_sims = [
                    '0321130739',
                    '0321130742',
                    '0321130743',
                    '0321130744',
                    '0321130745'
                ];
                $sites = DB::select("SELECT s.transmitter_sms, count(s.prom) as nb_site
                    FROM sites s
                    WHERE (soft_delete is null or soft_delete = 0)
                    and (without_system is null or without_system = 0)
                    and (prom is not null)
                    and transmitter_sms in (" . implode(',', $array_sims) . ")
                    GROUP BY s.transmitter_sms
                    ORDER BY nb_site ASC;");
                if (count($sites) > 0) {
                    foreach ($array_sims as $num) {
                        if (!in_array($num, array_column($sites, 'transmitter_sms')))
                            return $num;
                    }
                    return $sites[0]->transmitter_sms;
                } else {
                    return $array_sims[0];
                }
            }
        }
        return false;
    }

    public function update($id, Request $request)
    {
        // dd($request->phone_agent);
        if (in_array($request->authRole, ['root', 'room'])) {
            $site = Site::find($id);
            $site->phone_agent = Numero::where('id_site', $id)->get();
            if ($site) {
                $historique_site = new HistoriqueSite();
                if ($request->get_out_archive) {
                    $site->soft_delete = 0;
                    $numeros = Numero::where('id_site', $id)->get();
                    foreach ($numeros as  $numero) {
                        $numero->soft_delete = 0;
                        $numero->save();
                    }
                    $historique_site->objet = "Restauration du site";
                } else
                    $historique_site->objet = "Modification du site";

                $transmission = '';

                if ($request->without_system) {
                    $validator = Validator::make($request->all(), [
                        'nom' => ['required', Rule::unique('sites')->where(function ($query) use ($request, $id) {
                            return $query->where('nom', $request->nom)->where('idsite', '<>', $id);
                        })],
                    ]);
                    $request->pointeuse_id = null;
                    $request->gprs = null;
                    $request->sms = null;
                    $request->prom = null;
                    $request->puce = null;
                    $request->centrale = null;
                    $request->group_diag_id = null;
                    $request->interval_test = null;
                    $request->commentaire = null;
                } else if ($request->centrale_drx) {
                    $validator = Validator::make($request->all(), [
                        'nom' => ['required', Rule::unique('sites')->where(function ($query) use ($request, $id) {
                            return $query->where('nom', $request->nom)->where('idsite', '<>', $id);
                        })],
                        'pointeuse_id' => "required",
                    ]);
                    $request->gprs = null;
                    $request->sms = null;
                    $request->prom = null;
                    $request->puce = null;
                    $request->centrale = null;
                    $request->group_diag_id = null;
                    $request->interval_test = null;
                    $request->commentaire = null;
                } else {
                    $request->pointeuse_id = null;
                    $request['numeropuces'] = $request->puce;
                    $validator = Validator::make($request->all(), [
                        'nom' => ['required', Rule::unique('sites')->where(function ($query) use ($request, $id) {
                            return $query->where('nom', $request->nom)->where('idsite', '<>', $id);
                        })],
                        'centrale' => 'required',
                        'prom' => ['required', Rule::unique('sites')->where(function ($query) use ($request, $id) {
                            return $query->where('prom', $request->prom)->where('idsite', '<>', $id);
                        })],
                        'numeropuces' => ['required', Rule::unique('sites')->where(function ($query) use ($request, $id) {
                            return $query->where('numeropuces', $request->numeropuces)->where('idsite', '<>', $id);
                        })],
                        'group_diag_id' => 'required',
                        'professionnel' => 'required',
                        'interval_test' => 'required',
                    ])->setAttributeNames($this->attributeNames);
                    if ($request->prom) {
                        $transmission = self::getTransmissionType($request->prom);
                        if (!$transmission)
                            return response()->json(['error' => ["prom" => ["Transmission incorrect, vérifier le prom"]]]);
                        if (!preg_match("/^03(2|3|4|7|8)\d{7}$/i", $request->puce))
                            return response()->json(['error' => ['puce' => ["Le numero puce est incorrect"]]]);
                        if ($transmission == 'sms' && $request->prom != $request->puce)
                            return response()->json(['error' => ['prom' => ["Le prom et le puce doit être le même."]]]);
                    }
                }
                $validator_vigilance = null;
                $validator_pointage = null;
                $validator_checkphone = null;
                if ($request->pointeuse || $request->vigilance) {
                    $request->pointage = 1;
                    if ($request->pointeuse) {
                        $validator_vigilance = Validator::make($request->all(), [
                            'group_id' => 'required',
                            'horaire_vigilance_id' => ['required', 'integer'],
                        ])->setAttributeNames(['group_id' => 'Groupe vigilance', 'horaire_vigilance_id' => 'Service vigilance']);
                    } else {
                        $validator_vigilance = Validator::make($request->all(), [
                            'group_id' => 'required',
                            'horaire_vigilance_id' => ['required', 'integer'],
                        ])->setAttributeNames(['group_id' => 'Groupe vigilance', 'horaire_vigilance_id' => 'Service vigilance']);
                    }
                }
                if ($request->pointage == 1) {
                    $validator_pointage = Validator::make($request->all(), [
                        // 'phone_agent' => ['required', 'regex:/^(\d{10})(,\d{10})*$/u'],
                        'group_pointage_id' => 'required',
                        'horaire_pointage_id' => ['required', 'integer']
                    ])->setAttributeNames(['group_pointage_id' => 'Groupe pointage', 'horaire_pointage_id' => 'Service pointage']);
                }
                if ($request->pointage == 1 && $request->do_checkphone == 1) {
                    $validator_checkphone = Validator::make($request->all(), [
                        'checkphone' => ['required', 'regex:/^03(2|3|4|7|8)\d{7}$/i', Rule::unique('sites')->where(function ($query) use ($request, $id) {
                            return $query->where('checkphone', $request->checkphone)->where('idsite', '<>', $id);
                        })]
                    ])->setAttributeNames(['group_pointage_id' => 'Groupe pointage', 'horaire_pointage_id' => 'Service pointage']);
                } else
                    $request->checkphone = null;
                if ($request->intervention == 1) {
                    $validator_intervention = Validator::make($request->all(), [
                        'group_intervention_id' => 'required',
                    ])->setAttributeNames(["group_intervention_id" => "Groupe d'intervention"]);
                    $request->intervention_id = null;
                } else {
                    $validator_intervention = Validator::make($request->all(), [
                        'intervention_id' => 'required',
                    ])->setAttributeNames(["intervention_id" => "Equipe d'intervention"]);
                    $request->group_intervention_id = null;
                }
                $old_transmission = self::getTransmissionType($site->prom);
                $new_transmission = self::getTransmissionType($request->prom);
                $old_operateur = self::getOperateur($site->prom);
                $new_operateur = self::getOperateur($request->prom);
                $old_operateur_puce = self::getOperateur($site->numeropuces);
                $new_operateur_puce = self::getOperateur($request->puce);

                if ($validator->fails()) {
                    return response()->json(['error' => $validator->errors()]);
                } else if (!$request->without_system && !$request->centrale_drx && !$transmission) {
                    return response()->json([
                        'error' => ['prom' => ['Le champ Prom est incorrect']]
                    ]);
                } else if ($validator_vigilance != null && $validator_vigilance->fails()) {
                    return response()->json(['error' => $validator_vigilance->errors()]);
                } else if ($validator_pointage != null && $validator_pointage->fails()) {
                    return response()->json(['error' => $validator_pointage->errors()]);
                } else if ($validator_checkphone != null && $validator_checkphone->fails()) {
                    $site = Site::select('nom')->where('checkphone', $request->checkphone)->first();
                    return response()->json(['error' => ['checkphone' => ["Le numéro checkphone existe déjà : " . $site->nom]]]);
                } else if ($validator_intervention != null && $validator_intervention->fails()) {
                    return response()->json(['error' => $validator_intervention->errors()]);
                } else {
                    $details = '';
                    if ($site->nom != $request->nom) $details = $details . 'Nom: ' . $site->nom . ' -> ' . $request->nom . '\n';
                    if ($site->adresse != $request->adresse) $details = $details . 'Adresse: ' . $site->adresse . ' -> ' . $request->adresse . '\n';

                    if ($site->idClient != $request->client) {
                        $new_client = Client::find($request->client);
                        $old_client = Client::find($site->idClient);
                        $details = $details . 'Client: ' . ($old_client ? $old_client->Societe : '') . ' -> ' . ($new_client ? $new_client->Societe : '') . '\n';
                    }
                    if ($site->without_system != $request->without_system) {
                        $details = $details . 'Site sans système: ' . ($site->without_system ? 'Oui' : 'Non') . ' -> ' . ($request->without_system ? 'Oui' : 'Non') . '\n';
                    }
                    if ($site->professionnel != $request->professionnel)
                        $details = $details . ($request->professionnel ? 'Site professionnel' : 'Site particulier') . '\n';
                    if ($site->group_diag_id != $request->group_diag_id)
                        $details = $details . 'Group diagnostique: ' . ($request->group_diag_id == 1 ? 'Tana' : ($request->group_diag_id == 2 ? 'Tamatave' : 'Province')) .
                            ' -> ' . ($request->group_diag_id == 1 ? 'Tana' : ($request->group_diag_id == 2 ? 'Tamatave' : 'Province')) . '\n';
                    if ($site->interval_test != $request->interval_test)
                        $details = $details . 'Interval test cyclique: ' . $request->interval_test . ' heures\n';
                    if ($site->commentaire != $request->commentaire)
                        $details = $details . 'Commentaire: ' . $site->commentaire . ' -> ' . $request->commentaire . '\n';

                    if ($transmission != $old_transmission)
                        $details = $details . 'Transmission: ' . strtoupper($old_transmission) . ' -> ' . strtoupper($transmission) . '\n';
                    if ($site->prom != $request->prom)
                        $details = $details . 'Prom: ' . $site->prom . ' -> ' . $request->prom . '\n';

                    if ($site->numeropuces != $request->puce)
                        $details = $details . 'Puce: ' . $site->numeropuces . ' -> ' . $request->puce . '\n';

                    if ($site->idcentrale != $request->centrale) {
                        $new_centrale = Centrale::find($request->centrale);
                        $old_centrale = Centrale::find($site->idcentrale);
                        $details = $details . 'Centrale: ' . ($old_centrale ? $old_centrale->nom : '') . ' -> ' . ($new_centrale ? $new_centrale->nom : '') . '\n';
                    }
                    if ($site->vigilance != $request->vigilance)
                        $details = $details . 'Vigilance: ' .  ($request->vigilance ? 'Activé' : 'Désactivé') . '\n';
                    if ($site->group_id != $request->group_id) {
                        $new_group_v = GroupSite::find($request->group_id);
                        $old_group_v = GroupSite::find($site->group_id);
                        $details = $details . 'Groupe vigilance: ' . ($old_group_v ? $old_group_v->nom : '') . ' -> ' . ($new_group_v ? $new_group_v->nom : '') . '\n';
                    }
                    if ($site->horaire_vigilance_id != $request->horaire_vigilance_id) {
                        $new_horaire = "";
                        $old_horaire = "";
                        if ($site->horaire_vigilance_id)
                            $old_horaire = Horaire::find($site->horaire_vigilance_id)->nom;
                        if ($request->horaire_vigilance_id)
                            $new_horaire = Horaire::find($request->horaire_vigilance_id)->nom;
                        $details = $details . 'Horaire vigilance: ' . $old_horaire . ' -> ' . $new_horaire . '\n';
                    }
                    if ($site->horaire_pointage_id != $request->horaire_pointage_id) {
                        $new_horaire = "";
                        $old_horaire = "";
                        if ($site->horaire_pointage_id)
                            $old_horaire = Horaire::find($site->horaire_pointage_id)->nom;
                        if ($request->horaire_pointage_id)
                            $new_horaire = Horaire::find($request->horaire_pointage_id)->nom;
                        $details = $details . 'Horaire pointage : ' . $old_horaire . ' -> ' . $new_horaire . '\n';
                    }
                    if ($site->pointage != $request->pointage)
                        $details = $details . 'Pointage: ' . ($request->pointage ? 'Activé' : 'Désactivé') . '\n';
                    if ($site->group_pointage_id != $request->group_pointage_id) {
                        $new_group_p = GroupPointageSite::find($request->group_pointage_id);
                        $old_group_p = GroupPointageSite::find($site->group_pointage_id);
                        $details = $details . 'Groupe pointage: ' . ($old_group_p ? $old_group_p->nom : '') . ' -> ' . ($new_group_p ? $new_group_p->nom : '') . '\n';
                    }
                    if ($site->horaire_pointage_id != $request->horaire_pointage_id) {
                        $new_horaire = "";
                        $old_horaire = "";
                        if ($site->horaire_pointage_id)
                            $old_horaire = Horaire::find($site->horaire_pointage_id)->nom;
                        if ($request->horaire_pointage_id)
                            $new_horaire = Horaire::find($request->horaire_pointage_id)->nom;
                        $details = $details . 'Service pointage: ' . $old_horaire . ' -> ' . $new_horaire . '\n';
                    }
                    if ($site->pointeuse != $request->pointeuse)
                        $details = $details . 'Pointeuse biométrique: ' . ($request->pointeuse ? 'Oui' : 'Non') . '\n';
                    if ($site->nb_agent_day != $request->nb_agent_day)
                        $details = $details . 'Nb agent jour: ' . $site->nb_agent_day . ' -> ' . $request->nb_agent_day . '\n';
                    if ($site->nb_agent_night != $request->nb_agent_night)
                        $details = $details . 'Nb agent nuit: ' . $site->nb_agent_night . ' -> ' . $request->nb_agent_night . '\n';

                    $oldPhoneNumbers = [];
                    $newPhoneNumbers = [];

                    if ($site->phone_agent) {
                        foreach ($site->phone_agent as $item) {
                            $oldPhoneNumbers[] = isset($item['numero']) ? $item['numero'] : null;
                        }
                    }

                    if ($request->phone_agent) {
                        $newPhoneNumbers = $request->phone_agent;
                    }

                    if (array_diff($oldPhoneNumbers, $newPhoneNumbers) || array_diff($newPhoneNumbers, $oldPhoneNumbers)) {
                        $details .= 'Contact agent: ' . implode(', ', $oldPhoneNumbers) .
                            ' -> ' . implode(', ', $newPhoneNumbers) . "\n";
                    }

                    if ($site->checkphone != $request->checkphone)
                        $details = $details . 'Contact agent: ' . $site->checkphone . ' -> ' . $request->checkphone . '\n';
                    if ($site->total_hour != $request->total_hour)
                        $details = $details . 'Total d\'heure: ' . $site->total_hour . ' -> ' . $request->total_hour . ' heures/mois\n';
                    if ($site->secteur_id != $request->secteur_id) {
                        $new_secteur = "";
                        $old_secteur = "";
                        if ($site->secteur_id)
                            $old_secteur = Secteur::find($site->secteur_id)->nom;
                        if ($request->secteur_id)
                            $new_secteur = Secteur::find($request->secteur_id)->nom;
                        $details = $details . 'Secteur: ' . $old_secteur . ' -> ' . $new_secteur . '\n';
                    }
                    if ($site->intervention != $request->intervention)
                        $details = $details . 'Intervention: ' . ($request->intervention ? 'Oui' : 'Non') . '\n';
                    if ($site->notify_arm != $request->notify_arm)
                        $details = 'Notifier quand il y a armement/désarmement: ' . ($request->notify_arm ? 'Oui' : 'Non') . '\n';
                    if ($site->notify_outage != $request->notify_outage)
                        $details = 'Notifier quand il y a coupure/rétablissement: ' . ($request->notify_outage ? 'Oui' : 'Non') . '\n';

                    $site->prom = $request->prom;
                    $site->nom = $request->nom;
                    $site->adresse = $request->adresse;
                    $site->group_id = $request->group_id;
                    $site->group_pointage_id = $request->group_pointage_id;
                    $site->notify_arm = $request->notify_arm;
                    $site->notify_outage = $request->notify_outage;
                    $site->vigilance = $request->vigilance;
                    $site->pointeuse = $request->pointeuse;
                    $site->pointeuse_id = $request->pointeuse_id;
                    $site->numeropuces = $request->puce;
                    $site->idcentrale = $request->centrale;
                    $site->idClient = $request->client;
                    if ($transmission == 'sms') {
                        $site->gprs = null;
                        $site->sms = 1;
                    } else if ($transmission == 'gprs') {
                        $site->gprs = 1;
                        $site->sms = null;
                    }
                    if (
                        $new_transmission && (
                            $old_transmission != $new_transmission
                            || $old_operateur != $new_operateur
                            || ($new_transmission == 'gprs' && $old_operateur_puce != $new_operateur_puce)
                            || $request->vigilance != $site->vigilance
                            || ($request->vigilance == 1 && $request->group_diag_id != $site->group_diag_id)
                        )
                    ) {
                        $site->correct_transmitter = self::getCorrectTransmitter($request);
                    } else if (!$new_transmission)
                        $site->correct_transmitter = null;
                    $site->pointage_day = $request->pointage_day;
                    $site->pointage_night = $request->pointage_night;
                    $site->nb_agent_day = $request->nb_agent_day;
                    $site->nb_agent_night = $request->nb_agent_night;

                    // $site->phone_agent = $request->phone_agent;
                    if ($request->pointage == 1)
                        $site->do_checkphone = $request->do_checkphone;
                    else
                        $site->do_checkphone = null;
                    if ($site->do_checkphone)
                        $site->checkphone = $request->checkphone;
                    else
                        $site->checkphone = null;

                    $site->without_system = $request->without_system;
                    $site->centrale_drx = $request->centrale_drx;
                    $site->pointage_biometrique = $request->pointage_biometrique;
                    $site->pointage = $request->pointage;
                    $site->professionnel = $request->professionnel;
                    $site->group_diag_id = $request->group_diag_id;
                    $site->horaire_vigilance_id = $request->horaire_vigilance_id;
                    $site->horaire_pointage_id = $request->horaire_pointage_id;
                    $site->interval_test = $request->interval_test;
                    $site->commentaire = $request->commentaire;
                    $site->total_hour = $request->total_hour;
                    $site->secteur_id = $request->secteur_id;
                    $site->intervention = $request->intervention;
                    $site->intervention_id = $request->intervention_id;
                    $site->group_intervention_id = $request->group_intervention_id;
                    $site->lastupdate = now();

                    $site->save();

                    if ($request->phone_agent) {
                        DB::table('numeros')->where('id_site', $id)->delete();
                        foreach ($request->phone_agent as $phone) {
                            DB::table('numeros')->insert([
                                'id_site' => $id,
                                'numero' => $phone
                            ]);
                        }
                    }

                    if ($site->idsite)
                        $result = $site->idsite;
                    else
                        $result = false;
                    if ($details) {
                        $historique_site->detail = $details;
                        $historique_site->site_id = $site->idsite;
                        $historique_site->user_id = $request->authId;
                        $historique_site->created_at = now();
                        $historique_site->updated_at = now();
                        $historique_site->save();
                    }
                    return response()->json($result);
                }
            }
            return response()->json(['error' => "L'enregistrement n'existe pas"]);
        }
    }

    public function delete($id, Request $request)
    {
        if (in_array($request->authRole, ['root', 'room'])) {
            $site = Site::find($id);
            $site->soft_delete = 1;
            $site->lastupdate = now();
            if ($site->prom) {
                $site->prom = null;
                $site->numeropuces = null;
            }
            if ($site->centrale_drx)
                $site->pointeuse_id = null;
            $site->checkphone = null;

            $numeros = Numero::where('id_site', $id)->get();
            foreach ($numeros as  $numero) {
                $numero->soft_delete = 1;
                $numero->save();
            }

            $historique_site = new HistoriqueSite();
            $historique_site->objet = "Mis en archive du site";
            $historique_site->detail = "";
            $historique_site->site_id = $id;
            $historique_site->user_id = $request->authId;
            $historique_site->created_at = now();
            $historique_site->updated_at = now();
            $historique_site->save();

            return response()->json($site->save());
        }
    }

    public function select_fields()
    {
        $groups = GroupSite::all();
        $group_pointages = GroupPointageSite::all();
        $centrales = Centrale::all();
        $group_diags = GroupDiagSite::all();
        $horaire_services = Horaire::all();
        $group_interventions = GroupInterventionSite::all();
        $interventions = DB::select("SELECT s.idsite as 'id', s.nom, g.nom as 'group' FROM sites s
            LEFT JOIN group_intervention_sites g ON g.id = s.group_intervention_id
            WHERE s.intervention is not null and s.intervention = 1
            ORDER BY s.nom");
        return response()->json(compact('groups', 'centrales', 'group_pointages', 'group_diags', 'horaire_services', 'group_interventions', 'interventions'));
    }
    public function vigilance_sites()
    {
        $day = DB::select("SELECT s.prom, s.nom
            FROM sites s
            WHERE s.vigilance = 1 and (s.night is null or s.night = 0) and s.soft_delete != 1
            ORDER BY s.idsite ASC");
        $night = DB::select("SELECT s.prom, s.nom
            FROM sites s
            WHERE s.vigilance = 1 and (s.day is null or s.day = 0) and s.soft_delete != 1
            ORDER BY s.idsite ASC");
        return response()->json(compact('day', 'night'));
    }
    public function operation($site_id, $begin, $end)
    {
        $historiques = HistoriqueSite::with('user')
            ->where('site_id', $site_id)
            ->where('created_at', '>', $begin . ' 00:00:00')
            ->where('created_at', '<', $end . ' 23:00:00')
            ->orderBy('created_at', 'desc')
            ->get();
        return response()->json($historiques);
    }

    public function pointeuse()
    {
        return response()->json(
            DB::select("SELECT idsite, nom FROM sites where (soft_delete is null or soft_delete = 0) and pointeuse = 1"),
            200
        );
    }
}
