<?php

/**
 * Created by PhpStorm.
 * User: Dirsit
 * Date: 14/05/2020
 * Time: 17:51
 */

namespace App\Http\Controllers;

use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\JourFerie;

class UserController extends Controller
{
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
    }
    function generateRandomString($length = 43)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }

    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => ['required'],
            'password' => ['required'],
            'extension' => ['regex:/^\d{4}$/u'],
        ]); //->setAttributeNames($this->attributeNames);

        if ($validator->fails())
            return \response()->json(['error' => $validator->errors()]);

        $credentials = $request->only('email', 'password');
        $user = User::where("email", $request->email)->first();
        if (password_verify($request->password, $user->password)) {
            $secret = $this->generateRandomString();
            $user->secret = $secret;
            $user->extension = $request->extension;
            $user->save();
            $user->datetime = (new \DateTime())->format("Y-m-d H:i:s");
            $user->local = in_array($this->ip($request), ["localhost", "127.0.0.1", "************", "************", "*************", "************", "************", "************", "*************", "**************", "**************"]);
            return response()->json($user);
        }
        return response()->json(["error" => "Erreur d'authentification"]);
    }
    public function update_extension($id, Request $request)
    {
        $user = User::find($id);
        $user->extension = $request->extension;;
        return response()->json($user->save());
    }
    public function change_password(Request $request)
    {
        $credentials = $request->only('email', 'password');
        $user = User::where("email", $request->email)->first();
        if (password_verify($request->password, $user->password)) {
            $user->password = Hash::make($request->new_password);
            $user->secret = null;
            $user->save();
            return response()->json(true);
        }
        return response()->json(false);
    }

    public function logout(Request $request)
    {
        if ($request->secret != '' && $request->secret != null) {
            $user = User::where('email', $request->username)->where('secret', $request->secret)->first();
            if ($user != null) {
                $logs = DB::select("SELECT a.idademco FROM ademcomemlog a WHERE a.IdUser = ? ORDER BY a.dtarrived DESC", [$user->id]);
                if (count($logs) == 0) {
                    $user->secret = null;
                    return response()->json($user->save());
                }
                return response()->json(['error' => "Veuillez tout acquitter avant de se déconnecter."]);
            }
        }
        return response()->json(false);
    }

    public function ip($request)
    {
        $domain = $request->root();
        if (starts_with($domain, 'http://'))
            $domain = substr($domain, 7);
        $array = explode(":", $domain);
        if (count($array) == 2)
            $domain = $array[0];
        return $domain;
    }

    public function getDayOrNightDate()
    {
        if (
            new \DateTime >= (new \DateTime)->setTime(5, 50, 0) &&
            new \DateTime < (new \DateTime)->setTime(17, 50, 0)
        )
            return (new \DateTime)->setTime(07, 0, 0)->format('Y-m-d H:i:s');
        else if (new \DateTime < (new \DateTime)->setTime(5, 50, 0))
            return (new \DateTime)->setTime(18, 0, 0)->sub(new \DateInterval('P1D'))->format('Y-m-d H:i:s');
        return (new \DateTime)->setTime(18, 0, 0)->format('Y-m-d H:i:s');
    }

    public function getCurrentVigilanceDate()
    {
        $current_date = new \DateTime;
        $date_vigilance = new \DateTime;
        $date_vigilance->setTime(0, 0, 0);
        while (
            (!(new \DateTime >= (new \DateTime)->setTime(06, 0, 0) && new \DateTime <= (new \DateTime)->setTime(18, 0, 0)) &&
                ((clone $date_vigilance)->sub(new \DateInterval('PT10M')) < (new \DateTime) && ((new \DateTime) >= (clone $date_vigilance)->add(new \DateInterval('PT20M')))))
            || ((new \DateTime >= (new \DateTime)->setTime(06, 0, 0) && new \DateTime <= (new \DateTime)->setTime(18, 0, 0)) &&
                ((clone $date_vigilance)->sub(new \DateInterval('PT10M')) < (new \DateTime) && ((new \DateTime) >= (clone $date_vigilance)->add(new \DateInterval('PT50M')))))
        ) {
            if (
                new \DateTime >= (new \DateTime)->setTime(06, 50, 0) &&
                new \DateTime <= (new \DateTime)->setTime(17, 50, 0)
            )
                $date_vigilance->add(new \DateInterval('PT1H'));
            else $date_vigilance->add(new \DateInterval('PT30M'));
        }
        return $date_vigilance;
    }

    public function getNbAlarm()
    {
        $date_vigilance = $this->getCurrentVigilanceDate();
        $date_begin = (clone $date_vigilance)->sub(new \DateInterval('PT10M'));

        $horaire = '';
        $current_date = new \DateTime();
        if (
            new \DateTime >= (new \DateTime)->setTime(05, 50, 0) &&
            new \DateTime < (new \DateTime)->setTime(17, 50, 0)
        )
            $horaire = 'day';
        else {
            if (new \DateTime < (new \DateTime)->setTime(05, 50, 0))
                $current_date = (new \DateTime)->sub(new \DateInterval('P1D'));
            $horaire = 'night';
        }
        $field = $horaire . '_' . $current_date->format('w');

        $alarm_query = "SELECT count(a.idademco) as 'nb'
            FROM ademcomemlog a
            LEFT JOIN sites s ON s.idsite = a.site_id
            LEFT JOIN pointeuses p on p.id = a.pointeuse_id
            LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id
            WHERE IdUser is null and istraite != 3 and (
                a.codeTevent not in (600, 601, 602, 603, 604, 1000, 321)
                or (
                    a.codeTevent = 1000 and (
                        s.soft_delete = 1
                        or p.soft_delete = 1
                        or (
                            (s.vigilance is null or s.vigilance = 0)
                            and (s.pointeuse is null or s.pointeuse = 0)
                            and (s.checkphone is null or s.checkphone = 0)
                        )
                        OR (
                            (s.vigilance = 1 or s.pointeuse = 1 or s.checkphone = 1) ";

        if (JourFerie::where('date', $current_date->format('Y-m-d'))->first() == null) {
            $alarm_query = $alarm_query . " AND h.{$field} = 0 ))))";
        } else {
            $alarm_query = $alarm_query . " AND h.{$horaire}_ferie = 0 ))))";
        }

        $alarm_count = 0;
        $alarms = DB::select($alarm_query);
        if ($alarms != null)
            $alarm_count = $alarms[0]->nb;

        return $alarm_count;
    }

    /**
     * Get current horaire information (day/night and field name for database queries)
     */
    private function getCurrentHoraireInfo()
    {
        $current_date = new \DateTime();
        $horaire = '';

        if (
            new \DateTime >= (new \DateTime)->setTime(05, 50, 0) &&
            new \DateTime < (new \DateTime)->setTime(17, 50, 0)
        ) {
            $horaire = 'day';
        } else {
            if (new \DateTime < (new \DateTime)->setTime(05, 50, 0)) {
                $current_date = (new \DateTime)->sub(new \DateInterval('P1D'));
            }
            $horaire = 'night';
        }

        $field = $horaire . '_' . $current_date->format('w');
        $is_holiday = JourFerie::where('date', $current_date->format('Y-m-d'))->first() !== null;

        return [
            'horaire' => $horaire,
            'field' => $field,
            'current_date' => $current_date,
            'is_holiday' => $is_holiday
        ];
    }

    /**
     * Count call reminders that are linked to alarms with codeTevent = 1000
     * and where vigilance is outside of the defined horaire (schedule)
     */
    private function countAlarmLinkedCallReminders($call_reminder_numbers)
    {
        if (empty($call_reminder_numbers)) {
            return 0;
        }

        $horaire_info = $this->getCurrentHoraireInfo();
        $numeros_escaped = array_map(fn($n) => "'" . addslashes($n) . "'", $call_reminder_numbers);
        $numero_list = implode(',', $numeros_escaped);

        // Build the horaire condition similar to getNbAlarm() method
        $horaire_condition = '';
        if ($horaire_info['is_holiday']) {
            $horaire_condition = "AND h.{$horaire_info['horaire']}_ferie = 0";
        } else {
            $horaire_condition = "AND h.{$horaire_info['field']} = 0";
        }

        $alarm_query = "SELECT COUNT(DISTINCT n.numero) as nb
            FROM ademcomemlog a
            LEFT JOIN sites s ON s.idsite = a.site_id
            LEFT JOIN pointeuses p ON p.id = a.pointeuse_id
            LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id
            LEFT JOIN numeros n ON n.id_site = s.idsite
            WHERE
                a.IdUser IS NULL
                AND a.istraite != 3
                AND a.codeTevent = 1000
                AND n.numero IN ($numero_list)
                AND (
                    s.soft_delete = 1
                    OR p.soft_delete = 1
                    OR (
                        (s.vigilance IS NULL OR s.vigilance = 0)
                        AND (s.pointeuse IS NULL OR s.pointeuse = 0)
                        AND (s.checkphone IS NULL OR s.checkphone = 0)
                    )
                    OR (
                        (s.vigilance = 1 OR s.pointeuse = 1 OR s.checkphone = 1)
                        {$horaire_condition}
                    )
                )
        ";

        $result = DB::select($alarm_query);
        return ($result && isset($result[0])) ? $result[0]->nb : 0;
    }

    /**
     * Count call reminders that are not linked to any meaningful data
     * (no client, site, or name information)
     */
    private function countUnrelatedCallReminders($call_reminders)
    {
        $unrelated_count = 0;
        foreach ($call_reminders as $reminder) {
            if (
                !isset($reminder->client) &&
                !isset($reminder->site) &&
                !isset($reminder->name)
            ) {
                $unrelated_count++;
            }
        }
        return $unrelated_count;
    }

    public function getAuthUser(Request $request)
    {
        if ($request->secret != '' && $request->secret != null) {
            $site_count = 0;
            $sites = DB::select("SELECT count(idsite) as 'nb'
                from sites
                where (soft_delete is null or soft_delete = 0)
                and (date_report_diag is null or date_report_diag < now())
                and (without_system is null or without_system = 0)
                and (created_at is null or TIMESTAMPDIFF(DAY, created_at, now()) >= 1)
                and (
                    (date_last_signal is null or TIMESTAMPDIFF(HOUR, date_last_signal, now()) > interval_test)
                    or (
                        (vigilance is not null and vigilance = 1) and
                        (last_vigilance is null or TIMESTAMPDIFF(HOUR, last_vigilance, now()) >= 48)
                    )
                )");
            if ($sites != null)
                $site_count = $sites[0]->nb;

            $reclamation_count = 0;
            $reclamations = DB::select(
                "SELECT r.id, r.agent_id FROM reclamations r WHERE r.date_pointage= ? and r.agent_id is not null",
                [$this->getDayOrNightDate()]
            );
            $ids = array_column($reclamations, 'agent_id');
            if (count($ids) > 0) {
                $service24s = DB::select(
                    "SELECT s24.employe_id FROM service24s s24
                    WHERE `status` != 'draft' and (DATE_ADD(s24.date_pointage, INTERVAL 1 HOUR) = ? OR s24.date_pointage = ?)
                    and s24.employe_id in (" . implode(', ', $ids) . ")",
                    [$this->getDayOrNightDate(), $this->getDayOrNightDate()]
                );
                $pointages = DB::select("SELECT p.agent_id FROM pointages p WHERE (p.soft_delete is null or p.soft_delete = 0)
                    and p.date_pointage = ? and p.agent_id in (" . implode(', ', $ids) . ")", [$this->getDayOrNightDate()]);
                foreach ($reclamations as $r) {
                    $has_s24 = false;
                    $has_ptg = false;
                    foreach ($service24s as $s) {
                        if ($r->agent_id == $s->employe_id)
                            $has_s24 = true;
                    }
                    foreach ($pointages as $p) {
                        if ($r->agent_id == $p->agent_id)
                            $has_ptg = true;
                    }
                    if ($has_s24 && !$has_ptg)
                        $reclamation_count++;
                }
            }

            $horaire_info = $this->getCurrentHoraireInfo();
            $pointage_query = "SELECT count(p.id) as 'nb' FROM pointages p
                LEFT JOIN sites s ON s.idsite = p.site_id
                LEFT JOIN horaire_effectifs h1 ON h1.site_id = s.idsite
                LEFT JOIN horaire_effectifs h2 ON h2.site_id = s.group_planning_id
                WHERE p.date_pointage = ? and (p.soft_delete is null or p.soft_delete = 0)";

            if ($horaire_info['is_holiday']) {
                $pointage_query = $pointage_query . " AND ((s.group_planning_id is null and h1.{$horaire_info['horaire']}_ferie = 0) or (s.group_planning_id is not null and h2.{$horaire_info['horaire']}_ferie = 0)) GROUP BY p.agent_id";
            } else {
                $pointage_query = $pointage_query . " AND ((s.group_planning_id is null and h1.{$horaire_info['field']} = 0) or (s.group_planning_id is not null and h2.{$horaire_info['field']} = 0)) GROUP BY p.agent_id";
            }
            $pointage_error = 0;
            $pointages = DB::select($pointage_query, [$this->getDayOrNightDate()]);
            if ($pointages != null)
                $pointage_error = count($pointages);

            $call_reminders = $this->getEnrichedCallReminders();

            $call_reminder_numbers = array_map(fn($a) => $a->numero, $call_reminders);

            $alarm_linked_count = $this->countAlarmLinkedCallReminders($call_reminder_numbers);

            $unrelated_count = $this->countUnrelatedCallReminders($call_reminders);

            $user = User::where('email', $request->username)->where('secret', $request->secret)->whereNotNull('secret')->first();
            if ($user != null) {
                $user->alarm = $this->getNbAlarm();
                $user->site = $site_count;
                $user->pointage_error = $pointage_error;
                $user->reclamation = $reclamation_count;
                $user->call_reminders = $call_reminders;
                $user->call_reminders_count = $alarm_linked_count + $unrelated_count;
                $user->datetime = (new \DateTime())->format("Y-m-d H:i:s");
                $user->last_update_count = (new \DateTime())->format("Y-m-d H:i:s");
                $user->local = in_array($this->ip($request), ["localhost", "127.0.0.1", "************", "************", "*************", "************", "************", "************", "*************", "**************", "**************"]);
                return response()->json($user);
            }
        }
        return response()->json(false);
    }

    function get_a_rappler()
    {
        $appelles = $this->getEnrichedCallReminders();

        $a_rappeler = DB::select("SELECT * FROM call_reminders order by uniqueid desc");
        $sites = [];
        $habilites = [];
        $admin_users = [];
        $puces = [];

        if (count($a_rappeler) > 0) {
            foreach ($a_rappeler as $a) {
                $puces[] = $a->numero;
            }
            $numeros = DB::select("SELECT n.numero, n.id_site, n.id_contact FROM numeros n
                WHERE numero in (" . implode(',', $puces) . ")");
            $admin_users = DB::select("SELECT `name`, email, flotte FROM admin_users WHERE flotte in (" . implode(',', $puces) . ")");
            $contact_ids = [];
            $site_ids = [];
            foreach ($numeros as $n) {
                if ($n->id_contact)
                    $contact_ids[] = $n->id_contact;
                else if ($n->id_site)
                    $site_ids[] = $n->id_site;
            }
            if (count($contact_ids) > 0)
                $habilites = DB::select("SELECT c.idcontact, c.nom, c.prenom, s.nom as 'site' FROM contacts c
                    LEFT JOIN habilites h ON c.idContact = h.idcontact
                    LEFT JOIN sites s ON s.idsite = h.idsite
                    WHERE c.idcontact in (" . implode(',', $contact_ids) . ")");
            if (count($site_ids) > 0)
                $sites = DB::select("SELECT s.idsite, s.nom FROM sites s WHERE s.idsite in (" . implode(',', $site_ids) . ")");
        }

        return response()->json(compact('appelles', 'sites', 'habilites', 'admin_users', 'puces'));
    }

    private function getEnrichedCallReminders()
    {
        $a_rappeler = DB::select("SELECT * FROM call_reminders order by uniqueid desc");
        if (count($a_rappeler) > 0) {
            $puces = [];
            foreach ($a_rappeler as $a) {
                $puces[] = $a->numero;
            }
            $numeros = DB::select("SELECT n.numero, n.id_site, n.id_contact FROM numeros n
                WHERE numero in (" . implode(',', $puces) . ")");
            $admin_users = DB::select("SELECT `name`, email, flotte FROM admin_users WHERE flotte in (" . implode(',', $puces) . ")");
            $contact_ids = [];
            $site_ids = [];
            foreach ($numeros as $n) {
                if ($n->id_contact)
                    $contact_ids[] = $n->id_contact;
                else if ($n->id_site)
                    $site_ids[] = $n->id_site;
            }
            $habilites = [];
            $sites = [];
            if (count($contact_ids) > 0)
                $habilites = DB::select("SELECT c.idcontact, c.nom, c.prenom, s.nom as 'site' FROM contacts c
                    LEFT JOIN habilites h ON c.idContact = h.idcontact
                    LEFT JOIN sites s ON s.idsite = h.idsite
                    WHERE c.idcontact in (" . implode(',', $contact_ids) . ")");
            if (count($site_ids) > 0)
                $sites = DB::select("SELECT s.idsite, s.nom FROM sites s WHERE s.idsite in (" . implode(',', $site_ids) . ")");

            foreach ($a_rappeler as $a) {
                if ((isset($a->numero))) {
                    foreach ($numeros as $n) {
                        $contactSet = false;
                        foreach ($habilites as $h) {
                            if ($h->idcontact == $n->id_contact && $a->numero == $n->numero) {
                                $a->client = $h->nom . " " . $h->prenom;
                                $a->site = $h->site;
                                $a->contacttype = "client";
                                $contactSet = true;
                                break;
                            }
                        }
                        if (!$contactSet)
                            foreach ($admin_users as $u) {
                                if ($a->numero == $u->flotte) {
                                    $a->name = $u->name;
                                    $a->email = $u->email;
                                    $a->contacttype = "user";
                                    $contactSet = true;
                                    break;
                                }
                            }
                        if (!$contactSet)
                            foreach ($sites as $s) {
                                if ($s->idsite == $n->id_site && $a->numero == $n->numero) {
                                    $a->site = $s->nom;
                                    $a->contacttype = "agent";
                                    $contactSet = true;
                                    break;
                                }
                            }

                        if ($contactSet) {
                            break;
                        }
                    }
                }
            }
            $appelles = [];
            foreach ($a_rappeler as $a) {
                if (!isset($a->disposition)) {
                    // Skip records without disposition instead of throwing error
                    continue;
                }
                if (($a->disposition == "NO ANSWER" || $a->disposition == "BUSY"))
                    $appelles[] = $a;
            }
            return $appelles;
        }
        return [];
    }
}
