<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\VCommentaire;
use App\Site;
use App\Pointeuse;
use App\JourFerie;

class VigilanceController extends Controller
{
    public function __construct()
    {
        date_default_timezone_set("Indian/Antananarivo");
    }
    public function getDayOrNightDate()
    {
        if (
            new \DateTime >= (new \DateTime)->setTime(5, 50, 0) &&
            new \DateTime < (new \DateTime)->setTime(17, 50, 0)
        )
            return (new \DateTime)->setTime(07, 0, 0)->format('Y-m-d H:i:s');
        else if (new \DateTime < (new \DateTime)->setTime(5, 50, 0))
            return (new \DateTime)->setTime(18, 0, 0)->sub(new \DateInterval('P1D'))->format('Y-m-d H:i:s');
        return (new \DateTime)->setTime(18, 0, 0)->format('Y-m-d H:i:s');
    }

    public function getCurrentVigilanceDate()
    {
        $current_date = new \DateTime;
        $date_vigilance = new \DateTime;
        $date_vigilance->setTime(0, 0, 0);
        while (
            (!(new \DateTime >= (new \DateTime)->setTime(05, 50, 0) && new \DateTime <= (new \DateTime)->setTime(17, 50, 0)) &&
                ((clone $date_vigilance)->sub(new \DateInterval('PT10M')) < (new \DateTime) && ((new \DateTime) >= (clone $date_vigilance)->add(new \DateInterval('PT20M')))))
            || ((new \DateTime >= (new \DateTime)->setTime(05, 50, 0) && new \DateTime <= (new \DateTime)->setTime(17, 50, 0)) &&
                ((clone $date_vigilance)->sub(new \DateInterval('PT10M')) < (new \DateTime) && ((new \DateTime) >= (clone $date_vigilance)->add(new \DateInterval('PT50M')))))
        ) {
            if (
                new \DateTime >= (new \DateTime)->setTime(05, 50, 0) &&
                new \DateTime <= (new \DateTime)->setTime(17, 50, 0)
            )
                $date_vigilance->add(new \DateInterval('PT1H'));
            else $date_vigilance->add(new \DateInterval('PT30M'));
        }
        return $date_vigilance;
    }

    public function vigilance(Request $request)
    {
        $date_vigilance = $this->getCurrentVigilanceDate();
        $date_begin = (clone $date_vigilance)->sub(new \DateInterval('PT10M'));
        $horaire = '';
        $current_date = new \DateTime();
        if (
            new \DateTime >= (new \DateTime)->setTime(05, 50, 0) &&
            new \DateTime < (new \DateTime)->setTime(17, 50, 0)
        )
            $horaire = 'day';
        else {
            if (new \DateTime < (new \DateTime)->setTime(06, 50, 0))
                $current_date = (new \DateTime)->sub(new \DateInterval('P1D'));
            $horaire = 'night';
        }
        $field = $horaire . '_' . $current_date->format('w');
        $ferie = $horaire . "_ferie";

        if (in_array($request->authRole, ['root', 'room'])) {
            if (JourFerie::where('date', $current_date->format('Y-m-d'))->first() == null)
                $vigilances = DB::select("SELECT s.idsite, y.vigilance, s.nom, COALESCE(GROUP_CONCAT(DISTINCT n.numero SEPARATOR ', '), '') AS phone_agent, s.group_id, s.prom, y.nb_vigilance, y.sip, s.commentaire,
                    if(pointeuse_id is not null, p.last_vigilance, s.last_vigilance) as 'last_vigilance'
                    FROM sites s
                    LEFT JOIN numeros n ON n.id_site = s.idsite
                    LEFT JOIN pointeuses p ON p.id = s.pointeuse_id
                    LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id
                    LEFT JOIN (
                        select site_id, min(ifnull(sip,0)) as sip, min(dtarrived) as vigilance, count(idademco) as nb_vigilance
                        from ademcotemp
                        where dtarrived > ?
                        and codeTevent='1000'
                        and (agent_id is null or agent_id = 0)
                        group by site_id
                    ) y on y.site_id = s.idsite
                    WHERE (s.vigilance = 1 or s.do_checkphone = 1) and (s.soft_delete is null or s.soft_delete = 0) and s.group_id = ?
                    and (h.id is null or (h.$field is not null and h.$field = 1)) " .
                    "GROUP BY s.idsite ORDER BY s.group_pointage_id DESC, s.idsite", [$date_begin->format('Y-m-d H:i:s'), $request->group_id]);
            else
                $vigilances = DB::select("SELECT s.idsite, y.vigilance, s.nom, COALESCE(GROUP_CONCAT(DISTINCT n.numero SEPARATOR ', '), '') AS phone_agent, s.group_id, s.prom, y.nb_vigilance, y.sip, s.commentaire,
                    if(pointeuse_id is not null, p.last_vigilance, s.last_vigilance) as 'last_vigilance'
                    FROM sites s
                    LEFT JOIN numeros n ON n.id_site = s.idsite
                    LEFT JOIN pointeuses p ON p.id = s.pointeuse_id
                    LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id
                    LEFT JOIN (
                        select site_id, min(ifnull(sip,0)) as sip, min(dtarrived) as vigilance, count(idademco) as nb_vigilance
                        from ademcotemp
                        where dtarrived > ?
                        and codeTevent='1000'
                        and (agent_id is null or agent_id = 0)
                        group by site_id
                    ) y on y.site_id = s.idsite
                    WHERE (s.vigilance = 1 or s.do_checkphone = 1) and (s.soft_delete is null or s.soft_delete = 0) and s.group_id = ?
                    and (h.id is null or (h.$field is not null and h.$field = 1) or (h.$ferie is not null and h.$ferie = 1)) " .
                    "GROUP BY s.idsite ORDER BY s.group_pointage_id DESC, s.idsite", [$date_begin->format('Y-m-d H:i:s'), $request->group_id]);
            $vigilance_commentaires = VCommentaire::select('site_id', 'commentaire', 'objet')
                ->where('date_vigilance', (clone $date_vigilance)->format('Y-m-d H:i:s'))
                ->whereNotNull('site_id')
                ->whereNull('pointeuse_id')
                ->get();
            $pointages = DB::select("SELECT p.site_id, count(p.id) FROM pointages p
                WHERE p.date_pointage= ? and p.site_id in (select idsite from sites where do_checkphone = 1 or vigilance = 1) GROUP BY p.site_id", [$this->getDayOrNightDate()]);
            $current_vigilance = null;
            if ($request->site_id)
                $current_vigilance = $this->get_vigilance_story($request->site_id);
        }
        return response()->json([
            'commentaires' => $vigilance_commentaires,
            'datetime_vigilance' => $date_vigilance->format('Y-m-d H:i:s'),
            'date_vigilance' => $date_vigilance->format('H:i'),
            'vigilances' => $vigilances,
            'current_datetime' => (new \DateTime)->format('Y-m-d H:i:s'),
            'current_date' => (new \DateTime)->format('H:i'),
            'current_vigilance' => $current_vigilance,
            'pointages' => $pointages
        ]);
    }

    private function get_alarm_story($site_id)
    {
        $current_date = new \DateTime;
        $date_pointage = $this->getDayOrNightDate();
        if ($current_date >= (clone $current_date)->setTime(5, 50, 0) && $current_date < (clone $current_date)->setTime(17, 50, 0)) {
            $date_limit = (clone $current_date)->setTime(5, 30, 0);
        } else {
            $date_limit = (clone $current_date)->setTime(17, 30, 0);
            if ($current_date < (clone $current_date)->setTime(6, 50, 0))
                $date_limit = $date_limit->sub(new \DateInterval('P1D'));
        }
        $vigilances = DB::select("SELECT a.dtarrived, a.sip, a.prom, a.dtarrived, a.codeTevent, a.eventQualify, a.zones,
            ev.Description as 'alarm', z.numZone, z.nomZone, a.received_at, a.pointeuse_id, a.site_id, a.agent_id,
            ag.nom as 'nom_agent', ag.numero_stagiaire, ag.numero_employe, ag.num_emp_soit, ag.societe_id
            FROM ademcotemp a
            LEFT JOIN agents ag ON ag.id = a.agent_id
            LEFT JOIN sites st on st.idsite = a.site_id
            LEFT JOIN eventcode ev ON ev.code = a.codeTevent
            LEFT JOIN zonesites z on z.idsite = st.idsite and z.numZone = a.zones
            WHERE a.dtarrived > ? and a.site_id = ?
            ORDER BY a.dtarrived DESC", [$date_limit->sub(new \DateInterval('PT12H'))->format('Y-m-d H:i:s'), $site_id]);
        return ['alarms' => $vigilances];
    }

    public function show_alarm($site_id)
    {
        return response()->json($this->get_alarm_story($site_id));
    }

    private function get_vigilance_story($site_id)
    {
        $current_date = new \DateTime;
        $date_pointage = $this->getDayOrNightDate();
        if ($current_date >= (clone $current_date)->setTime(5, 50, 0) && $current_date < (clone $current_date)->setTime(17, 50, 0)) {
            $date_limit = (clone $current_date)->setTime(5, 50, 0);
        } else {
            $date_limit = (clone $current_date)->setTime(17, 50, 0);
            if ($current_date < (clone $current_date)->setTime(6, 50, 0))
                $date_limit = $date_limit->sub(new \DateInterval('P1D'));
        }
        $vigilances = DB::select("SELECT a.dtarrived, a.sip
            FROM ademcotemp a
            WHERE
                a.codeTevent = 1000
                and a.dtarrived > ?
                and a.agent_id is null
                and a.site_id = ?
                ORDER BY a.dtarrived ASC", [$date_limit->format('Y-m-d H:i:s'), $site_id]);
        $vigilance_commentaires = VCommentaire::select('date_vigilance', 'commentaire', 'objet')
            ->where('date_vigilance', '>=', $date_limit->format('Y-m-d H:i:s'))
            ->where('site_id', $site_id)
            ->whereNull('pointeuse_id')
            ->get();
        $pointages = DB::select("SELECT a.nom, a.numero_employe, a.numero_stagiaire, y.pointage_id, a.id as agent_id, s.idsite as site_id, a.soft_delete
            FROM agents a
            LEFT JOIN sites s ON a.site_id = s.idsite
            LEFT JOIN (select p.id as pointage_id, p.agent_id from pointages p where p.date_pointage = ?) y ON y.agent_id = a.id
            WHERE s.idsite = ?", [$date_pointage, $site_id]);
        $checked = [];
        $unchecked = [];
        foreach ($pointages as $ptg) {
            if ($ptg->soft_delete != 1) {
                if ($ptg->pointage_id)
                    array_push($checked, $ptg);
                else array_push($unchecked, $ptg);
            }
        }
        return [
            'vigilances' => $vigilances,
            'commentaires' => $vigilance_commentaires,
            'agents' => compact('checked', 'unchecked')
        ];
    }

    public function show($site_id)
    {
        return response()->json($this->get_vigilance_story($site_id));
    }

    public function save_commentaire(Request $request)
    {
        $date_vigilance = $request->date_vigilance;
        if ($request->site_id) {
            $site_id = $request->site_id;
            $comment = VCommentaire::where('date_vigilance', $date_vigilance)->where('site_id', $site_id)->first();
            if ($comment == null && $request->objet) {
                $comment = new VCommentaire();
                $comment->date_vigilance = $date_vigilance;
                $comment->site_id = $site_id;
            }
        } else if ($request->agent_id && $request->pointeuse_id) {
            $pointeuse_id = $request->pointeuse_id;
            $agent_id = $request->agent_id;
            $comment = VCommentaire::where('date_vigilance', $date_vigilance)
                ->where('pointeuse_id', $pointeuse_id)
                ->where('agent_id', $agent_id)
                ->first();
            if ($comment == null && $request->objet) {
                $comment = new VCommentaire();
                $comment->date_vigilance = $date_vigilance;
                $comment->pointeuse_id = $pointeuse_id;
                $comment->agent_id = $agent_id;
            }
        } else if ($request->pointeuse_id) {
            $pointeuse_id = $request->pointeuse_id;
            $comment = VCommentaire::where('date_vigilance', $date_vigilance)
                ->where('pointeuse_id', $pointeuse_id)->first();
            if ($comment == null && $request->objet) {
                $comment = new VCommentaire();
                $comment->date_vigilance = $date_vigilance;
                $comment->pointeuse_id = $pointeuse_id;
            }
        }
        if ($comment != null) {
            if (!$request->objet) {
                return response()->json($comment->delete());
            } else {
                $comment->objet = $request->objet;
                $comment->commentaire = $request->commentaire;
                if (!$request->site_id && $request->pointeuse_id) {
                    $pointeuse = Pointeuse::find($request->pointeuse_id);
                    if ($pointeuse != null) $comment->site_id = $pointeuse->site_id;
                }
                return response()->json($comment->save());
            }
        }
        return response()->json(false);
    }

    public function get_vigilance($prom, Request $request)
    {
        $current_date = new \DateTime;
        $date_pointage = $this->getDayOrNightDate();
        if ($request->datetime) {
            $current_date = date_create_from_format('Y-m-d H:i:s', $request->datetime);
            $date_pointage = $request->datetime;
        }
        if ($current_date >= (clone $current_date)->setTime(5, 50, 0) && $current_date < (clone $current_date)->setTime(17, 50, 0)) {
            $date_begin = (clone $current_date)->setTime(5, 50, 0)->format('Y-m-d H:i:s');
            $date_end = (clone $current_date)->setTime(17, 50, 0)->format('Y-m-d H:i:s');
        } else {
            $date_begin = (clone $current_date)->setTime(17, 50, 0)->format('Y-m-d H:i:s');
            $date_end = (clone $current_date)->add(new \DateInterval('P1D'))->setTime(05, 50, 00)->format('Y-m-d H:i:s');
        }
        if ($current_date > (new \DateTime)->setTime(0, 0, 0)->sub(new \DateInterval('P1D')))
            $vigilances = DB::select(
                "SELECT a.dtarrived
                FROM ademcotemp a
                WHERE
                    a.codeTevent = 1000
                    and a.dtarrived >= ?
                    and a.dtarrived < ?
                    and a.prom = ?
                    ORDER BY a.dtarrived ASC",
                [$date_begin, $date_end, $prom]
            );
        else
            $vigilances = DB::select(
                "SELECT a.dtarrived
                FROM ademcolog a
                WHERE
                    a.codeTevent = 1000
                    and a.dtarrived >= ?
                    and a.dtarrived < ?
                    and a.prom = ?
                    ORDER BY a.dtarrived ASC",
                [$date_begin, $date_end, $prom]
            );
        $vigilance_commentaires = VCommentaire::select('date_vigilance', 'commentaire', 'objet')
            ->where('date_vigilance', '>=', $date_begin)
            ->where('date_vigilance', '<', $date_end)
            ->where('prom', $prom)
            ->get();
        $pointages = DB::select("SELECT a.nom, a.numero_employe, a.numero_stagiaire, y.pointage_id, a.id as agent_id, s.idsite as site_id, a.soft_delete
            FROM agents a
            LEFT JOIN sites s ON a.site_id = s.idsite
            LEFT JOIN (select p.id as pointage_id, p.agent_id from pointages p where p.date_pointage = ?) y ON y.agent_id = a.id
            WHERE s.prom = ?", [$date_pointage, $prom]);
        $checked = [];
        $unchecked = [];
        foreach ($pointages as $ptg) {
            if ($ptg->soft_delete != 1) {
                if ($ptg->pointage_id)
                    array_push($checked, $ptg);
                else array_push($unchecked, $ptg);
            }
        }
        $site = Site::select('interval_vigilance_jour', 'interval_vigilance_nuit')->where('prom', $prom)->first();
        return response()->json([
            'site' => $site,
            'vigilances' => $vigilances,
            'commentaires' => $vigilance_commentaires,
            'agents' => compact('checked', 'unchecked')
        ]);
    }

    public function report_abusif(Request $request)
    {
        $current_date = new \DateTime;
        $date_pointage = $this->getDayOrNightDate();
        if ($request->datetime) {
            $current_date = date_create_from_format('Y-m-d H:i:s', $request->datetime);
            $date_pointage = $request->datetime;
        }
        if ($current_date >= (clone $current_date)->setTime(5, 50, 0) && $current_date < (clone $current_date)->setTime(17, 50, 0)) {
            $horaire = 'day';
            $date_begin = (clone $current_date)->setTime(5, 50, 0)->format('Y-m-d H:i:s');
            $date_end = (clone $current_date)->setTime(17, 50, 0)->format('Y-m-d H:i:s');
        } else {
            $horaire = 'night';
            $date_begin = (clone $current_date)->setTime(17, 50, 0)->format('Y-m-d H:i:s');
            $date_end = (clone $current_date)->add(new \DateInterval('P1D'))->setTime(05, 50, 00)->format('Y-m-d H:i:s');
        }

        if ($current_date > (new \DateTime)->setTime(0, 0, 0)->sub(new \DateInterval('P1D')))
            $vigilances = DB::select(
                "SELECT a.dtarrived, a.site_id
                FROM ademcotemp a
                LEFT JOIN sites s ON s.idsite = a.site_id
                WHERE
                    a.codeTevent = 1000
                    and a.dtarrived >= ?
                    and a.dtarrived < ?
                    and a.agent_id is null
                    ORDER BY a.dtarrived ASC",
                [$date_begin, $date_end]
            );
        else
            $vigilances = DB::select(
                "SELECT a.dtarrived, a.site_id
                FROM ademcolog a
                LEFT JOIN sites s ON s.idsite = a.site_id
                WHERE
                    a.codeTevent = 1000
                    and a.dtarrived >= ?
                    and a.dtarrived < ?
                    and a.agent_id is null
                ORDER BY a.dtarrived ASC",
                [$date_begin, $date_end]
            );

        $field = $horaire . '_' . $current_date->format('w');
        $sites = DB::select("SELECT idsite, prom, s.nom, interval_vigilance_jour, interval_vigilance_nuit from sites s
            LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id
            where (soft_delete is null or soft_delete = 0) and (vigilance is not null and vigilance = 1)
            and (h.id is null or (h.$field is not null and h.$field = 1))");
        $ids = array_column($sites, 'idsite');
        $vigilance_commentaires = VCommentaire::select('date_vigilance', 'commentaire', 'objet', 'site_id')
            ->where('date_vigilance', '>=', $date_begin)
            ->where('date_vigilance', '<', $date_end)
            ->whereIn('site_id', $ids)
            ->get();
        $agents = DB::select("SELECT a.nom, a.numero_employe, a.numero_stagiaire, p.agent_id, p.id, p.site_id FROM pointages p
            LEFT JOIN agents a ON a.id = p.agent_id
            LEFT JOIN sites s ON s.idsite = p.site_id
            WHERE p.vigilance = 1 and p.date_pointage = ? and s.idsite in (" . implode(',', $ids) . ")", [$date_pointage]);
        return response()->json([
            'sites' => $sites,
            'vigilances' => $vigilances,
            'commentaires' => $vigilance_commentaires,
            'agents' => $agents
        ]);
    }

    public function report_vigilance_all(Request $request)
    {
        $current_date = new \DateTime;
        $date_pointage = $this->getDayOrNightDate();
        if ($request->datetime) {
            $current_date = date_create_from_format('Y-m-d H:i:s', $request->datetime);
            $date_pointage = $request->datetime;
        }
        if ($current_date >= (clone $current_date)->setTime(5, 50, 0) && $current_date < (clone $current_date)->setTime(17, 50, 0)) {
            $horaire = 'day';
            $date_begin = (clone $current_date)->setTime(5, 50, 0)->format('Y-m-d H:i:s');
            $date_end = (clone $current_date)->setTime(17, 50, 0)->format('Y-m-d H:i:s');
        } else {
            $horaire = 'night';
            $date_begin = (clone $current_date)->setTime(17, 50, 0)->format('Y-m-d H:i:s');
            $date_end = (clone $current_date)->add(new \DateInterval('P1D'))->setTime(5, 50, 0)->format('Y-m-d H:i:s');
        }
        if ($current_date > (new \DateTime)->setTime(0, 0, 0)->sub(new \DateInterval('P1D')))
            $vigilances = DB::select(
                "SELECT a.dtarrived, a.site_id
                FROM ademcotemp a
                LEFT JOIN sites s ON s.idsite = a.site_id
                LEFT JOIN group_sites g ON g.id = s.group_id
                WHERE
                    a.codeTevent = 1000
                    and a.dtarrived >= ?
                    and a.dtarrived < ?
                    and a.agent_id is null
                    and g.vigilance_group_id = ?
                    ORDER BY a.dtarrived ASC",
                [$date_begin, $date_end, $request->group_id]
            );
        else
            $vigilances = DB::select(
                "SELECT a.dtarrived, a.site_id
                FROM ademcolog a
                LEFT JOIN sites s ON s.idsite = a.site_id
                LEFT JOIN group_sites g ON g.id = s.group_id
                WHERE
                    a.codeTevent = 1000
                    and a.dtarrived >= ?
                    and a.dtarrived < ?
                    and a.agent_id is null
                    and g.vigilance_group_id = ?
                ORDER BY a.dtarrived ASC",
                [$date_begin, $date_end, $request->group_id]
            );

        $field = $horaire . '_' . $current_date->format('w');
        $sites = DB::select("SELECT idsite, prom, s.nom, interval_vigilance_jour, interval_vigilance_nuit from sites s
            LEFT JOIN group_sites g ON g.id = s.group_id
            LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id
            where (soft_delete is null or soft_delete = 0) and (vigilance is not null and vigilance = 1)
            and g.vigilance_group_id = ?
            and (h.id is null or (h.$field is not null and h.$field = 1))", [$request->group_id]);
        $ids = array_column($sites, 'idsite');
        $vigilance_commentaires = VCommentaire::select('date_vigilance', 'commentaire', 'objet', 'site_id')
            ->where('date_vigilance', '>=', $date_begin)
            ->where('date_vigilance', '<', $date_end)
            ->whereIn('site_id', $ids)
            ->get();
        $agents = DB::select("SELECT a.nom, a.numero_employe, a.numero_stagiaire, p.agent_id, p.id, p.site_id FROM pointages p
            LEFT JOIN agents a ON a.id = p.agent_id
            LEFT JOIN sites s ON s.idsite = p.site_id
            WHERE p.vigilance = 1 and p.date_pointage = ? and s.idsite in (" . implode(',', $ids) . ")", [$date_pointage]);
        return response()->json([
            'sites' => $sites,
            'vigilances' => $vigilances,
            'commentaires' => $vigilance_commentaires,
            'agents' => $agents
        ]);
    }

    public function report_vigilance_horaire(Request $request)
    {
        $field = $request->horaire . '_' . date_create_from_format('Y-m-d H:i:s', $request->date . ' 00:00:00')->format('w');
        if (JourFerie::where('date', $request->date)->first() == null)
            $sites = DB::select("SELECT s.idsite, s.prom, s.nom, g.vigilance_group_id as group_id
                FROM sites s
                LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id
                LEFT JOIN group_sites g ON g.id = s.group_id
                WHERE s.vigilance = 1 and (s.soft_delete is null or s.soft_delete = 0)
                and (h.id is null or (h.$field is not null and h.$field = 1)) " .
                "ORDER BY s.group_pointage_id DESC, s.idsite");
        else
            $sites = DB::select("SELECT s.idsite, s.prom, s.nom, g.vigilance_group_id as group_id
                FROM sites s
                LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id
                LEFT JOIN group_sites g ON g.id = s.group_id
                WHERE s.vigilance = 1 and (s.soft_delete is null or s.soft_delete = 0)
                and (h.id is null or (h.$field is not null and h.$field = 1) or (h." . $request->horaire . "_ferie is not null and h." . $request->horaire . "_ferie = 1)) " .
                "ORDER BY s.group_pointage_id DESC, s.idsite");
        return response()->json($sites);
    }

    public function pointeuse(Request $request)
    {
        $date_vigilance = $this->getCurrentVigilanceDate();
        $date_begin = (clone $date_vigilance)->sub(new \DateInterval('PT10M'));

        $horaire = '';
        $current_date = new \DateTime();
        if (
            new \DateTime >= (new \DateTime)->setTime(05, 50, 0) &&
            new \DateTime < (new \DateTime)->setTime(17, 50, 0)
        )
            $horaire = 'day';
        else {
            if (new \DateTime < (new \DateTime)->setTime(05, 50, 0))
                $current_date = (new \DateTime)->sub(new \DateInterval('P1D'));
            $horaire = 'night';
        }
        $field = $horaire . '_' . $current_date->format('w');

        if (JourFerie::where('date', $current_date->format('Y-m-d'))->first() == null) {
            $sites = DB::select("SELECT s.idsite, s.nom, s.pointeuse_id, p.last_connection, p.last_vigilance, p.site_id, s.group_id,
            COALESCE(GROUP_CONCAT(DISTINCT n.numero SEPARATOR ', '), '') AS phone_agent
            FROM sites s
            LEFT JOIN pointeuses p ON p.id = s.pointeuse_id
            LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id
            LEFT JOIN numeros n ON n.id_site = s.idsite
            WHERE s.pointeuse = 1
            AND (p.soft_delete IS NULL OR p.soft_delete = 0)
            AND (s.soft_delete IS NULL OR s.soft_delete = 0)
            AND s.group_id = ?
            AND (h.id IS NULL OR (h.$field IS NOT NULL AND h.$field = 1))
            GROUP BY s.idsite
            ORDER BY s.group_pointage_id DESC, p.id", [$request->group_id]);
        } else {
            $ferie = $horaire . "_ferie";
            $sites = DB::select("SELECT s.idsite, s.nom, s.pointeuse_id, p.last_connection, p.last_vigilance, p.site_id, s.group_id,
            COALESCE(GROUP_CONCAT(DISTINCT n.numero SEPARATOR ', '), '') AS phone_agent
            FROM sites s
            LEFT JOIN pointeuses p ON p.id = s.pointeuse_id
            LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id
            LEFT JOIN numeros n ON n.id_site = s.idsite
            WHERE s.pointeuse = 1
            AND (p.soft_delete IS NULL OR p.soft_delete = 0)
            AND (s.soft_delete IS NULL OR s.soft_delete = 0)
            AND s.group_id = ?
            AND (h.id IS NULL OR (h.$field IS NOT NULL AND h.$field = 1) OR (h.$ferie IS NOT NULL AND h.$ferie = 1))
            GROUP BY s.idsite
            ORDER BY s.group_pointage_id DESC, p.id", [$request->group_id]);
        }
        $vigilance_commentaires = VCommentaire::select('pointeuse_id', 'commentaire', 'objet')
            ->where('date_vigilance', (clone $date_vigilance)->format('Y-m-d H:i:s'))
            ->whereNotNull('pointeuse_id')
            ->whereNull('agent_id')
            ->get();
        $pointages = DB::select("SELECT ptg.agent_id, ptg.pointeuse_id FROM pointages ptg
            LEFT JOIN sites s ON s.idsite = ptg.site_id
            LEFT JOIN agents a ON a.id = ptg.agent_id
            LEFT JOIN pointeuses p ON p.id = ptg.pointeuse_id
            WHERE (s.soft_delete is null or s.soft_delete = 0)
            and (ptg.soft_delete is null or ptg.soft_delete = 0)
            and (p.soft_delete is null or p.soft_delete = 0)
            and ptg.site_id = p.site_id
            and ptg.pointeuse_id is not null
            and ptg.date_pointage= ?", [$this->getDayOrNightDate()]);
        $vigilances = DB::select("SELECT agent_id, pointeuse_id FROM ademcotemp
        WHERE agent_id IS NOT NULL
        AND codeTevent = 1000
        AND dtarrived > ?
        ORDER BY dtarrived ASC", [$date_begin->format('Y-m-d H:i:s')]);

        foreach ($sites as $s) {
            $s->nb_pointage = 0;
            foreach ($pointages as $ptg) {
                if ($ptg->pointeuse_id == $s->pointeuse_id)
                    $s->nb_pointage++;
            }
            $ids = [];
            $s->nb_ok = 0;
            $s->nb_not_pointed = 0;
            foreach ($vigilances as $v) {
                if (!in_array($v->agent_id, $ids) && $v->pointeuse_id == $s->pointeuse_id) {
                    if (in_array($v->agent_id, array_column($pointages, 'agent_id'))) {
                        $s->nb_ok++;
                    } else {
                        $s->nb_not_pointed++;
                    }
                    $ids[] = $v->agent_id;
                }
            }
            $s->nb_manque = $s->nb_pointage - $s->nb_ok;
        }


        $current_vigilance = null;
        if ($request->pointeuse_id)
            $current_vigilance = $this->get_pointeuse_story($request->pointeuse_id);
        return response()->json([
            'commentaires' => $vigilance_commentaires,
            'datetime_vigilance' => $date_vigilance->format('Y-m-d H:i:s'),
            'date_vigilance' => $date_vigilance->format('H:i'),
            'sites' => $sites,
            'current_datetime' => (new \DateTime)->format('Y-m-d H:i:s'),
            'current_date' => (new \DateTime)->format('H:i'),
            'current_vigilance' => $current_vigilance,
        ]);
    }

    public function get_pointeuse_story($pointeuse_id)
    {
        $current_date = new \DateTime;
        $date_pointage = $this->getDayOrNightDate();
        if ($current_date >= (clone $current_date)->setTime(5, 50, 0) && $current_date < (clone $current_date)->setTime(17, 50, 0)) {
            $date_limit = (clone $current_date)->setTime(5, 50, 0);
        } else {
            $date_limit = (clone $current_date)->setTime(17, 50, 0);
            if ($current_date < (clone $current_date)->setTime(5, 50, 0))
                $date_limit = $date_limit->sub(new \DateInterval('P1D'));
        }
        $vigilances = DB::select("SELECT adm.idademco as 'id', adm.agent_id, adm.dtarrived, a.societe_id, a.nom, a.numero_employe, a.num_emp_soit,
            a.numero_stagiaire, adm.pointeuse_id
            FROM ademcotemp adm
            LEFT JOIN agents a ON a.id = adm.agent_id
            WHERE
                adm.codeTevent = 1000
                and adm.dtarrived > ?
                and adm.agent_id is not null and adm.pointeuse_id = ?
                ORDER BY adm.dtarrived ASC", [$date_limit->format('Y-m-d H:i:s'), $pointeuse_id]);
        $vigilance_commentaires = VCommentaire::select('pointeuse_id', 'agent_id', 'date_vigilance', 'commentaire', 'objet')
            ->where('date_vigilance', '>=', $date_limit->format('Y-m-d H:i:s'))
            ->where('pointeuse_id', $pointeuse_id)
            ->get();
        $pointages = DB::select("SELECT ptg.id, a.societe_id, a.nom, a.numero_employe, a.num_emp_soit, a.numero_stagiaire,
            ptg.id as 'pointage_id', a.id as agent_id, ptg.pointeuse_id
            FROM pointages ptg
            LEFT JOIN pointeuses p ON p.id = ptg.pointeuse_id
            LEFT JOIN agents a ON a.id = ptg.agent_id
            WHERE (ptg.soft_delete is null or ptg.soft_delete = 0)
                and ptg.site_id = p.site_id
                and ptg.date_pointage = ? and ptg.pointeuse_id = ?", [$date_pointage, $pointeuse_id]);

        $reclamations = DB::select("SELECT r.id, r.date_pointage, r.agent_id, r.agent_not_registered, ag.nom, ag.societe_id, ag.numero_employe,
            ag.num_emp_soit, ag.numero_stagiaire, s.nom as 'site',
            COALESCE(GROUP_CONCAT(DISTINCT n.numero SEPARATOR ', '), '') AS phone_agent
            FROM reclamations r
            LEFT JOIN agents ag on ag.id = r.agent_id
            LEFT JOIN sites s on s.pointeuse_id = ?
            LEFT JOIN pointages p on p.date_pointage = ? AND p.agent_id = r.agent_id
            LEFT JOIN numeros n on n.id_site = s.idsite
            WHERE r.date_pointage = ? and p.id is null AND r.site_id = s.idsite
            GROUP BY r.id ", [$pointeuse_id, $date_pointage, $date_pointage]);

        return [
            'vigilances' => $vigilances,
            'commentaires' => $vigilance_commentaires,
            'pointages' => $pointages,
            'reclamations' => $reclamations,
        ];
    }

    public function show_pointeuse($pointeuse_id)
    {
        return response()->json($this->get_pointeuse_story($pointeuse_id));
    }
}
