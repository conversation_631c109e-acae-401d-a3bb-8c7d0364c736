<?php

namespace App\Http\Controllers;

use App\Zone;
use Illuminate\Http\Request;

class ZoneController extends Controller
{
    public function site($id){
        $zones = Zone::where('idsite', $id)->whereNull('soft_delete')->with('capteur')->get();
        return response()->json($zones);
    }
    public function store(Request $request){
        $zone = Zone::where('NumZone', $request->num)->where('idsite', $request->idsite)->first();
        if($zone == null)
            $zone = new Zone();
        $zone->NumZone = $request->num;
        $zone->idsite = $request->idsite;
        $zone->nomZone = $request->nom;
        $zone->idcapteur = $request->idcapteur;
        $zone->soft_delete = null;
        return response()->json($zone->save());
    }
    public function update($id, Request $request){
        if(Zone::where('id', '<>', $id)->where('NumZone', $request->num)->where('idsite', $request->idsite)->first() == null){
            $zone = Zone::find($id);
            $zone->NumZone = $request->num;
            $zone->nomZone = $request->nom;
            $zone->idcapteur = $request->idcapteur;
            $zone->idsite = $request->idsite;
            return response()->json($zone->save());
        }
        return response()->json(["error" => "Ce numero de zone existe déjà"]);
    }
    public function delete($id){
        $zone = Zone::where('idzone', $id)->first();
        $zone->soft_delete = 1;
        return response()->json($zone->save());
    }
}