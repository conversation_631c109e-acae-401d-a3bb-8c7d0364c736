<?php

namespace App\Http\Middleware;

use Closure;

class AuthConfig
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if($request->username && $request->secret){
            $authUser = User::select('id')
                ->where('email', $request->username)
                ->where('secret', $request->secret)
                ->first();
            if($authUser != null and in_array($authUser->role, ['room', 'tech', 'root', 'rh', 'client'])){
                $request->authId = $authUser->id;
                $request->authRole = $authUser->role;
                $request->authGroupId = $authUser->group_vigilance_id;
                return $next($request);
            }
        }
        return response()->json(false);
    }
}
