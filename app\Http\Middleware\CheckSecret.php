<?php

namespace App\Http\Middleware;

use Closure;
use App\User;

class CheckSecret
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if($request->username && $request->secret){
            $authUser = User::select('id', 'role', 'group_vigilance_id')
                ->where('email', $request->username)
                ->where('secret', $request->secret)
                ->first();
            if($authUser != null){
                $request->authId = $authUser->id;
                $request->authRole = $authUser->role;
                $request->authGroupId = $authUser->group_vigilance_id;
                return $next($request);
            }
        }
        return response()->json(false);
    }
}
