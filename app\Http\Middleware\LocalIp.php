<?php

namespace App\Http\Middleware;

use Closure;

class LocalIp
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $domain = $request->root();
        if (starts_with($domain, 'http://'))
            $domain = substr ($domain, 7);
        $array = explode(":", $domain);
        if(count($array) == 2)
            $domain = $array[0];
        if(in_array($domain, ["localhost", "127.0.0.1", "************", "************", "************", "************", "************", "*************", "**************", "**************"]))
            return $next($request);
        return response()->json(false);
    }
}
