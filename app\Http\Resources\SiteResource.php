<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;

class SiteResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $phoneNumbers = DB::table('numeros')
            ->where('id_site', $this->idsite)
            ->pluck('numero')
            ->toArray();

        return [
            'idsite' => $this->idsite,
            'nom' => $this->nom,
            'nb_agent' => $this->nb_agent_day,
            'group_pointage_id' => $this->group_pointage_id,
            'pointage_biometrique' => $this->pointage_biometrique,
            //'agents' => $this->whenLoaded('agents'),
            'phone_agent' => implode(', ', $phoneNumbers),
        ];
    }
}
