<?php

namespace App;

use Illuminate\Database\Eloquent\Model;


class Log extends Model
{
    protected $primaryKey = 'idademco';
    protected $table = 'ademcomemlog';
    public  $timestamps = false;
    protected $fillable = [

    ];
    public function alarm(){
        return $this->belongsTo('App\Alarm', 'codeTevent', 'code');
    }
    public function site(){
        return $this->belongsTo('App\Site', 'site_id', 'idsite');
    }
    public function rapport(){
        return $this->belongsTo('App\Rapport', 'rapport_id');
    }
}