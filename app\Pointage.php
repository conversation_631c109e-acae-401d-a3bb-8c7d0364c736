<?php

namespace App;


use Illuminate\Database\Eloquent\Model;

class Pointage extends Model
{
    public  $timestamps = false;
    public $fillable = [
        'agent_id', 'pointeuse_id', 'site_id', 'dtarrived', 'date_pointage',
    ];
    public function site(){
        return $this->belongsTo('App\Site', 'site_id', 'idsite');
    }
    public function agent(){
        return $this->belongsTo('App\Agent', 'agent_id', 'id');
    }
}