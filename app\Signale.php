<?php

namespace App;


use Illuminate\Database\Eloquent\Model;

class Signale extends Model
{
    public  $timestamps = false;
    protected $fillable = [
        'received_code',
        'emit_code',
        'user_id',
        'prom'
    ];

    public function received(){
        return $this->belongsTo('App\Alarm', 'received_code', 'code');
    }

    public function emit(){
        return $this->belongsTo('App\Alarm', 'emit_code', 'code');
    }
}