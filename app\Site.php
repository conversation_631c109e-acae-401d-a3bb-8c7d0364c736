<?php

namespace App;


use Illuminate\Database\Eloquent\Model;

class Site extends Model
{

    protected $fillable = [
        'idsite',
        'prom',
        'nom',
        'adresse',
        'numeropuces',
        'idcentrale',
        'idClient',
        'gprs',
        'sms',
        'lastupdate',
        'date_last_signal',
        'last_signal',
        'last_vigilance',
        'group_id',
        'status',
        'transmitter',
        'transmitter_2',
        'port',
        'notify_arm',
        'day',
        'night',
        'pointage',
        'pointage_day',
        'pointage_night',
        'phone_agent',
        'nb_agent_day',
        'nb_agent_night',
        'arm',
        'without_system',
        'centrale_drx',
        'vigilance',
        'pointeuse',
        'pointage_biometrique',
        'group_pointage_id',
        'interval_vigilance_jour',
        'interval_vigilance_nuit',
        'soft_delete',
        'correct_transmitter',
        'created_at',
        'group_diag_id',
        'professionnel',
        'interval_test',
        'commentaire',
        'date_report_diag',
        'joignable',
        'diagnostique_id',
        'eventQualify',
        'total_hour',
        'horaire_vigilance_id',
        'horaire_pointage_id',
        'secteur_id',
        'notify_outage',
        'pointeuse_id',
        'intervention',
        'intervention_id',
        'group_intervention_id',
        'last_panic',
        'admin_updated_at',
        'synchronized_at',
        'checkphone',
        'do_checkphone',
        'sms_check',
        'last_sms_check',
        'last_transmission_2',
    ];

    protected $primaryKey = 'idsite';
    protected $table = 'sites';
    public  $timestamps = false;
    protected $with = ['phone_agent'];
    public function centrale()
    {
        return $this->belongsTo('App\Centrale', 'idcentrale', 'idcentrale');
    }
    public function client()
    {
        return $this->belongsTo('App\Client', 'idClient', 'idClient');
    }
    public function pointeuse_o()
    {
        return $this->belongsTo('App\Pointeuse', 'pointeuse_id');
    }
    public function secteur()
    {
        return $this->belongsTo('App\Secteur', 'secteur_id');
    }
    public function historiques()
    {
        return $this->hasMany('App\Historique', 'prom', 'prom');
    }
    public function signales()
    {
        return $this->hasMany('App\Signale', 'prom', 'prom');
    }
    public function zones()
    {
        return $this->hasMany('App\Zone', 'idzone', 'idzone');
    }
    public function habilites()
    {
        return $this->hasMany('App\Habilite', 'idsite', 'idsite');
    }
    public function agents()
    {
        return $this->hasMany('App\Agent', 'site_id');
    }

    public function phone_agent()
    {
        return $this->hasMany('App\Numero', 'id_site', 'idsite');
    }
}
