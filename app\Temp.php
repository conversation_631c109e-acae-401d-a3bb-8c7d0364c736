<?php

namespace App;

use Illuminate\Database\Eloquent\Model;


class Temp extends Model
{
    protected $primaryKey = 'idademco';
    protected $table = 'ademcotemp';
    public  $timestamps = false;
    protected $fillable = [

    ];
    public function alarm(){
        return $this->belongsTo('App\Alarm', 'codeevent', 'code');
    }
    public function site(){
        return $this->belongsTo('App\Site', 'prom', 'prom');
    }
}