<?php

namespace App;

use Illuminate\Database\Eloquent\Model;


class Zone extends Model
{
    protected $primaryKey = 'idzone';
    protected $guarded = ['idzone'];
    protected $table = 'zonesites';
    protected $fillable = ['idzone', 'NumZone', 'nomZone', 'idcapteur', 'soft_delete'];
    protected $visible = ['idzone', 'NumZone', 'nomZone', 'capteur'];
    public  $timestamps = false;

    public function capteur(){
        return $this->belongsTo('App\Capteur', 'idcapteur', 'idcapteur');
    }
}