const { default: axios } = require("axios")
const FormData = require("form-data")
const https = require('https')
const agent = new https.Agent({
	rejectUnauthorized: false
})


exports.db_config_ipbx = {
	host: "**************",
	user: "api",
	port: "3306",
	database: "cdr",
	password: "AdmDir2024"
}

exports.db_config_zo = {
	host: "*************",
	user: "tls",
	port: "3371",
	database: "tls",
	password: "AdmDir2025"
}

exports.db_config_cdr = {
	host: "**************",
	user: "api",
	port: "3306",
	database: "cdr",
	password: "AdmDir2024"
}
exports.db_config_aro = {
    host: "************",
    user: "tls",
    port: "3306",
    database: "tls",
    password: "123456"
}
exports.db_config_maroho = {
    host: "************",
    user: "aro",
    port: "3453",
    database: "tls_alarm",
    password: "*CE#Vt(kZ$bQrCpw"
}
exports.db_config_306 = {
    host: "*************",
    user: "aro",
    port: "4353",
    database: "tls_alarm",
    password: "LX#+s4bmKZSEe*bL"
}
exports.db_config_admin = {
    host: "************",
    user: "admin",
    port: "3306",
    database: "admin",
    password: "#$DminMyDrX1@2023"
}
exports.db_config_admin_test = {
    host: "localhost",
    user: "aro",
    port: "3306",
    database: "admin",
    password: "AdmDir2025"
}

exports.db_config_ovh = {
    host: "************",
    port: "3307",
    user: "tls",
    database: "tls_alarm",
    password: "#$NoGGOvH1@tls2023"
}
exports.db_config_tls_formation = {
    host: "************",
    port: "3306",
    user: "tls",
    database: "tls_alarm",
    password: "Srv$$OvH@tls2023"
}
exports.db_config_ovh_test = {
    host: "localhost",
    user: "root",
    port: "3306",
    database: "tls",
    password: "admin"
}
exports.tokenTest = "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************-hV2iveq7L9Zj7NN9PQAU8alYu6TXzjSIhpP-GNUNAwP5Jf2jHNYhMtfDtHi1xlZaYSf2dNWNfEOEC5LsgNXE2R4RtEgzA9fm5cT5dU5afLC7sE4s_bZq1vxwjqTN6fw2MnR2nqFAh2NhZGGMXmnawCzSAa_SO2IkcY6MJxzz8BELRDDOi1lNTtP-M_HBMAs_DkIDhPk1UDu63cOJzBafztN7oxxuJ8jUyd1TqbfGIpoipam60Nie9ChlLO-RSQjmcq1ZraX70pPjEX4Z6sIkormulmR7OUrYp-or5noUOOyKkiHEfU5kXX9UyrAKYz7O9Sm0pr3ueLYOfAChBqenQAsMQEc2EZbJ3FBlqNHd0mweb4xSunTk1avtx7_x2g6MI4caEM8DiKQ2AYyBC44ZQU2qV9KOAY53LH1-GUlkIYculwYPRI5j1FjTE5BFeY1VqPkeHAxdzXXA4d5TM0E6M1steG1dkcWz3OirAqBOVaWjePLm_wTDP0ufFLhIIMcVkjtHb1LWLbPOkICqesKXmYQo5-w5I4fAsVdEEzD-3rSaOAB4NkMk_XN74Md_tQPcwf2TpxGBueRHfsOZRBQw"
exports.tokenAdmin = "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
exports.formDataOption = {
	httpsAgent: agent,
	headers:
	{
		'Authorization': 'Bearer ' + this.tokenAdmin
	}
}

exports.sendMail = (poolAdmin, destination, subject, text, attachements, callback, isTask) => {
	let data = new FormData()
	data.append("objet", subject)
	data.append("content", text)
	attachements.forEach((file, index) => {
		data.append(`files[${index}]`, file.content, file.filename)
	})
	console.log(destination)
	destination.forEach((email, index) => {
		data.append(`emails[${index}]`, email)
	})
	axios.post("https://app.dirickx.mg:8001/api/message/add", data, {
		httpsAgent: agent,
		headers:
		{
			...data.getHeaders(),
			'Authorization': 'Bearer ' + this.tokenAdmin
		}
	})
		.then(({ data }) => {
			console.log(data)
			console.log("message interne send successfully...")
			callback(true)
		})
		.catch((e) => {
			console.log(e)
			console.log("message interne export send error!")
			callback(false)
		})
}

exports.auth_mail_tls = {
	sender: "Dirickx Guard <<EMAIL>>",
    user: "<EMAIL>",
    pass: "ArTl$DrXP4$21"
}

exports.auth_mail_tls_guard = {
	sender: "Dirickx Guard <<EMAIL>>",
    user: "<EMAIL>",
    pass: "78TrEN$c!4T$exBKKR#ocm"
}
