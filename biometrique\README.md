# Biometrique Server

This folder contains the biometric server implementation for handling device connections and USSD responses.

## Recent Changes

### USSD Response Parsing Improvements

The USSD response parsing has been improved to handle various phone number formats:

1. Added support for the `261` prefix (without `+` or `?`)
2. Enhanced pattern matching to handle phone numbers with spaces and dashes
3. Improved standardization of phone numbers to the `0XXXXXXXXX` format
4. Added support for different carrier USSD codes:
   - `ussd0015*123#` for Airtel
   - `ussd0015#888#` for Orange
   - `ussd0015#120#` for Telma

These changes make the system more robust when handling USSD responses from different carriers and in different formats.

### Device ID Assignment Improvements

The device ID assignment process has been enhanced to prevent ID collisions:

1. When assigning a new device ID, the system now checks if the ID is already in use by a connected device
2. If the ID is already in use, the system increments the ID and tries again
3. This prevents duplicate device IDs and ensures each device has a unique identifier

### Files Modified

- `server.current.local.js`:
  - Updated USSD response parsing in two places:
    - Main USSD response handler (around line 1260)
    - `specialDeviceRegistry.processUssdResponse` method (around line 148)
  - Enhanced device ID assignment to prevent collisions:
    - `specialDeviceRegistry.assignNewDeviceId` method (around line 282)

## Testing

A comprehensive test suite has been created to verify the functionality. The tests are located in the `tests` folder.

### USSD Response Parsing Tests

Tests for the USSD response parsing functionality verify that phone numbers in various formats are correctly extracted and standardized.

### Device ID Assignment Tests

Tests for the device ID assignment functionality verify that the system correctly handles ID collisions and assigns unique IDs to devices.

To run all tests:

```
node tests/run-all-tests.js
```

To run a specific test:

```
node tests/test-device-id-assignment.js
```

## Server Configuration

- Port: 2701
- Default transmitter: 127.0.0.1 (localhost)

## Device Communication

The server handles various types of messages from biometric devices, including:

- Connection establishment
- USSD responses
- SIM information
- Device ID changes
- Enrollment of fingerprints
- Arming/disarming
- Date synchronization
