var net = require('net');
const mysql = require('mysql2');
const db_config = require("../auth").db_config_zo
var exec = require('child_process').exec;

const sqlUpdateSim = "UPDATE pointeuses set sim = ? where id = ?"
const sqlSelectEmpreinte = "SELECT empreinte_id FROM agent_pointeuses WHERE pointeuse_id = ?"
const sqlInsertEmpreinte = "INSERT INTO agent_pointeuses (empreinte_id, pointeuse_id, created_at, updated_at) " +
    "VALUES "
const sqlDeleteEmpreinte = (pointeuseId, empreinteIds) => {
    return "DELETE FROM agent_pointeuses WHERE pointeuse_id = " + pointeuseId + " and empreinte_id in (" +
        empreinteIds.join(", ") + ")"
}

function tryToConnect() {
    const connection = mysql.createConnection(db_config)
    // const connection = mysql.createConnection({ host: 'localhost', port: 3306, user: 'tls', password: 'Srv$$OvH@tls2023', database: 'tls_alarm' })

    connection.connect(function (err) {
        if (err) {
            console.log('error when connecting to db:', err)
            process.exit(0)
        }
        else {
            var client = new net.Socket();
            var pointeuses = {}
            client.connect(2701, '************', function () {
                // client.connect(2701, '127.0.0.1', function () {
                setTimeout(() => {
                    const command = process.argv[2]
                    console.log(command)
                    client.write(command)
                }, 100)
            })

            if ((/^remind(\d{4})(\d{2}):(.+)/gs).test(process.argv[2]))
                client.setTimeout(1000)
            else if ((/^setIdP(\d{4})(\d{4})/gs).test(process.argv[2]))
                client.setTimeout(10000)
            else
                client.setTimeout(50000)

            client.on('data', function (data) {
                console.log(data.toString())
                // get phone number
                if ((/(\d{4})\+261(\d{9})/gs).test(data)) {
                    console.log('succefully get sim number')
                    const message = (/(\d{4})\+261(\d{9})/gs).exec(data)
                    const idPointeuse = message[1]
                    const numSim = '0' + message[2]
                    console.log("data in client js")
                    connection.query(sqlUpdateSim, [numSim, idPointeuse], (err, result) => {
                        if (err) {
                            console.log(err)
                        }
                        else {
                            console.log('success')
                            setTimeout(() => {
                                client.end()
                            }, 1000);
                        }
                    })
                }
                // listId
                else if ((/(\d{4}):((\d+,)+(\d+)?)/gs).test(data)) {
                    const message = (/(\d{4}):((\d+,)+(\d+)?)/gs).exec(data)
                    const pointeuseId = message[1]
                    const stringIds = message[2]
                    if (!pointeuses[pointeuseId]) {
                        pointeuses[pointeuseId] = []
                        stringIds.split(',').forEach((id) => {
                            pointeuses[pointeuseId].push(Number.parseInt(id))
                        })
                    }
                    else {
                        stringIds.split(',').forEach((id) => {
                            pointeuses[pointeuseId].push(Number.parseInt(id))
                        })
                    }
                }
                else if ((/(\d{4}):end/gs).test(data)) {
                    const message = (/(\d{4}):end/gs).exec(data)
                    const pointeuseId = message[1]
                    if (pointeuses[pointeuseId]) {
                        let clearListIds = []
                        pointeuses[pointeuseId].forEach(id => {
                            if (id && !clearListIds.includes(id))
                                clearListIds.push(id)
                        })
                        delete pointeuses[pointeuseId]
                        console.log("clearListIds: " + clearListIds.length)
                        console.log(clearListIds)
                        console.log("------------")
                        connection.query(sqlSelectEmpreinte, [pointeuseId], (err, dataEmpreinte) => {
                            if (err)
                                console.error(err)
                            else {
                                const empreinteIds = dataEmpreinte.map(d => Number.parseInt(d.empreinte_id))
                                console.log("empreinteIds: " + empreinteIds.length)
                                console.log(empreinteIds)
                                console.log("------------")
                                let digitNotFoundFromDatabase = []
                                let digitNotFoundFromDevice = []
                                clearListIds.forEach((id) => {
                                    if (!empreinteIds.includes(id))
                                        digitNotFoundFromDatabase.push(id)
                                })
                                empreinteIds.forEach((id) => {
                                    if (!clearListIds.includes(id))
                                        digitNotFoundFromDevice.push(id)
                                })
                                console.log("digitNotFoundFromDatabase: " + digitNotFoundFromDatabase.length)
                                console.log(digitNotFoundFromDatabase)
                                console.log("------------")
                                console.log("digitNotFoundFromDevice: " + digitNotFoundFromDevice.length)
                                console.log(digitNotFoundFromDevice)
                                console.log("------------")
                                let queryString = []
                                digitNotFoundFromDatabase.map(empreinteId => {
                                    queryString.push("(" + empreinteId + ", " + pointeuseId + ",now() ,now())")
                                })
                                if (queryString.length) {
                                    connection.query(sqlInsertEmpreinte + queryString.join(", "), [], (err, result) => {
                                        if (err)
                                            console.log(err)
                                        else {
                                            console.log("insert successfull!")
                                        }
                                    })
                                }

                                if (digitNotFoundFromDevice.length) {
                                    connection.query(sqlDeleteEmpreinte(pointeuseId, digitNotFoundFromDevice), [], (err, result) => {
                                        if (err)
                                            console.log(err)
                                        else {
                                            console.log("delete successfull!")
                                        }
                                    })
                                }
                                console.log("success client!")
                                setTimeout(() => {
                                    client.end()
                                }, 1500);
                            }
                        })
                    }
                }
                else if (['error', 'success', 'mysql_error', 'forbidden_error', 'device_error', 'device_not_found', 'password_error'
                    , 'error_empreinte', 'duplicate_template_part', 'template_out_of_bound', 'already_used'].includes(data.toString().trim())) {
                    client.end()
                }
            });

            client.on('timeout', () => {
                console.error("timeout")
                client.end()
            })

            client.on('close', function () {
                console.log('Connection closed')
                connection.destroy()
            });
        }
    })
    connection.on('error', function (err) {
        connection = null
        console.log('db error', err);
        if (err.code === 'PROTOCOL_CONNECTION_LOST') {
            tryToConnect()
        } 
        else if(err.code === 'ECONNREFUSED'){
            console.log("ECONNREFUSED")
        }else {
            throw err;
        }
    })
}
setTimeout(() => {
    tryToConnect()
})
