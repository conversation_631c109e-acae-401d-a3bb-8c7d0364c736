var net = require('net')
const moment = require('moment')
const mysql = require('mysql')

moment.locale('fr')

const db_config = require("../../auth").db_config_ovh
const pool = mysql.createPool(db_config)

const sqlSelectAgentPointeuse = "SELECT ap.id, ap.digit, ap.pointeuse_id, ap.empreinte_id, ap.agent_id FROM tls_alarm.agent_pointeuses ap " +
    "left join pointeuses p on p.id = ap.pointeuse_id " +
    "left join agents a on a.id = ap.agent_id " +
    "where p.last_connection > '" + moment().subtract(6, "minutes").format("YYYY-MM-DD HH:mm:ss") + "' " +
    "and get_digit = 1 and (ap.soft_delete is null or ap.soft_delete = 0) and (a.soft_delete is null or a.soft_delete = 0) " +
    "order by p.last_get_template"
const sqlUpdatePointeuse = "UPDATE pointeuses SET last_get_template = now() where id = ?"

function onlyUnique(value, index, array) {
    return array.indexOf(value) === index;
}

function isOutOfInterval() {
    if(moment().isAfter(moment().set({minute: 0, second: 0})) && moment().isBefore(moment().set({minute: 5, second: 0})))
        return false
    else if(moment().isAfter(moment().set({minute: 55, second: 0})) && moment().isBefore(moment().add(1, "hour").set({minute: 0, second: 0})))
        return false
    else if(!(moment().isAfter(moment().set({hour: 5, minute: 0, second: 0})) && moment().isBefore(moment().set({hour: 17, minute: 0, second: 0}))) 
    && moment().isAfter(moment().set({minute: 25, second: 0})) && moment().isBefore(moment().set({minute: 40, second: 0})))
        return false
    return true
}

function doGetTemplate(){
    if(isOutOfInterval()){
        pool.query(sqlSelectAgentPointeuse, [], async (err, agents) => {
            if(err)
                console.error(err)
            else if(agents.length > 0){
                console.log("---------")
                const agent = agents[0]
                const nbId = agents.map(a => a.pointeuse_id).filter(onlyUnique).length
                console.log("nb id : " + nbId)
                console.log(agent)
                let client = new net.Socket()
                client.connect(2701, '57.128.20.26', function() {
                    client.write('startCon_clt')
                    setTimeout(() => {
                        const pointeuseId = ("000" + agent.pointeuse_id).slice(-4)
                        const empreinteId = ("00" + agent.empreinte_id).slice(-3)
                        const digitNumber = agent.digit.toString()
                        const agentId = ("0000" + agent.agent_id).slice(-5)
                        const request = "getTmp" + pointeuseId + empreinteId + digitNumber + agentId + "user1"
                        console.log(request)
                        client.write(request)
                    }, 200)
                })
                client.setTimeout(30000)
                client.on("data", (data) => {
                    console.log(data.toString())
                    if(data.toString().trim() == 'success'){
                        console.log(true)
                        client.destroy()
                    }
                    if(data.toString().trim() == 'device_not_found'){
                        console.log(false)
                        client.destroy()
                    }
                    if(['error', 'mysql_error', 'forbidden_error', 'device_error', 'password_error'
                        , 'error_empreinte', 'duplicate_template_part', 'template_out_of_bound'].includes(data.toString().trim())){
                            console.log(false)
                            client.destroy()
                    }
                })
                client.on("timeout", () => {
                    console.log("timeout...")
                    console.log(false)
                    client.destroy()
                })
                client.on("close", () => {
                    console.log("closed!")
                    pool.query(sqlUpdatePointeuse, [agent.pointeuse_id], async (err, result) => {
                        if(err)
                            console.log(err)
                        setTimeout(() => {
                            doGetTemplate()
                        }, 300000/nbId)
                    })
                })
            }
            else {
                console.log("no template to get ...")
                setTimeout(() => {
                    doGetTemplate()
                }, 60000)
            }
        })
    }
    else {
        console.log("interval time denied")
        setTimeout(() => {
            doGetTemplate()
        }, 60000)
    }
}

doGetTemplate()