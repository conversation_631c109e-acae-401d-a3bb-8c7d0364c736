var net = require('net')
const moment = require('moment')
const mysql = require('mysql')
const { argv } = require('process')

moment.locale('fr')

const db_config = require("../../auth").db_config_ovh
const pool = mysql.createPool(db_config)

let not_found_devices = []

const sqlSelectAgentPointeuse = "SELECT ap.id, ap.pointeuse_id, ap.empreinte_id, ap.agent_id FROM tls_alarm.agent_pointeuses ap " +
    "left join pointeuses p on p.id = ap.pointeuse_id " +
    "left join agents a on a.id = ap.agent_id " +
    "where p.last_connection > '" + moment().subtract(6, "minutes").format("YYYY-MM-DD HH:mm:ss") + "' " +
    "and (" +
        "(ap.last_vigilance is not null and ap.last_vigilance < '" + moment().subtract(45, "days").format("YYYY-MM-DD HH:mm:ss") + "') " +
        "or (ap.last_vigilance is null and ap.updated_at < '" + moment().subtract(30, "days").format("YYYY-MM-DD HH:mm:ss") + "') " +
        "or a.soft_delete = 1 " +
        "or ap.soft_delete = 1 " +
    ") " +
    "order by ap.pointeuse_id"

function doRemindDevice(agents, index){
    const agent = agents[index]
    console.log("---------")
    console.log(agent)
    if(index < agents.length){
        if(!not_found_devices.includes(agent.pointeuse_id)){
            var client = new net.Socket()
            client.connect(2701, '57.128.20.26', function() {
                client.write('startCon_clt')
                setTimeout(() => {
                    const pointeuseId = ("000" + agent.pointeuse_id).slice(-4)
                    const empreinteId = ("00" + agent.empreinte_id).slice(-3)
                    const request = "delete" + pointeuseId + empreinteId
                    console.log(request)
                    client.write(request)
                }, 200)
            })
            client.setTimeout(30000)
            client.on("data", (data) => {
                console.log(data.toString())
                if(data.toString().trim() == 'success'){
                    console.log(true)
                    client.destroy()
                }
                if(data.toString().trim() == 'device_not_found'){
                    console.log(false)
                    not_found_devices.push(agent.pointeuse_id)
                    client.destroy()
                }
                if(['error', 'mysql_error', 'forbidden_error', 'device_error', 'password_error'
                    , 'error_empreinte', 'duplicate_template_part', 'template_out_of_bound'].includes(data.toString().trim())){
                        console.log(false)
                        client.destroy()
                }
            })
            client.on("timeout", () => {
                console.log("timeout...")
                not_found_devices.push(agent.pointeuse_id)
                console.log(false)
                client.destroy()
            })
            client.on("close", () => {
                setTimeout(() => {
                    console.log("closed!")
                    doRemindDevice(agents, index+1)
                }, 3000);
            })
        }
        else {
            console.log("skip request")
            doRemindDevice(agents, index+1)
        }
    }
    else {
        console.log("well done.")
        pool.end()
    }
}

if(!process.argv[2] || (process.argv[2] == "task" 
    && moment().isAfter(moment().set({"hour": 11, "minute": 15})) 
    && moment().isBefore(moment().set({"hour": 11, "minute": 45})))
){
    pool.query(sqlSelectAgentPointeuse, [], async (err, agents) => {
        console.log("after query")
        if(err)
            console.error(err)
        else if(agents.length > 0){
            console.log("Nb agent: " + agents.length)
            doRemindDevice(agents, 0)
        }
        else {
            console.log("no digit to delete")
            pool.end()
        }
    })
}
else 
    console.log("skip delete empreinte")
