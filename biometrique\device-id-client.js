// Device ID client for testing device ID assignment
var net = require('net');

// Create a socket client
var client = new net.Socket();

// Connect to the server
client.connect(2701, '127.0.0.1', function () {
    console.log('Connected to server');
    
    // Get the command from command line arguments
    const command = process.argv[2];
    if (!command) {
        console.log('No command specified. Usage: node device-id-client.js <command>');
        client.end();
        return;
    }
    
    console.log('Sending command:', command);
    client.write(command);
});

// Set a timeout
client.setTimeout(30000);

// Handle data received from the server
client.on('data', function (data) {
    console.log('Received from server:', data.toString());
    
    // Handle ID change response
    if ((/setIdOk(\d{4})(\d{4})/gs).test(data)) {
        console.log('Successfully received ID change confirmation');
        const message = (/setIdOk(\d{4})(\d{4})/gs).exec(data);
        const oldId = message[1];
        const newId = message[2];
        console.log(`Device ID changed from ${oldId} to ${newId}`);
        
        // Close the connection after a delay
        setTimeout(() => {
            client.end();
        }, 1000);
    }
    // Handle success/error responses
    else if (['success', 'error'].includes(data.toString().trim())) {
        console.log(`Received ${data.toString().trim()} response`);
        setTimeout(() => {
            client.end();
        }, 1000);
    }
});

// Handle timeout
client.on('timeout', () => {
    console.log('Connection timed out');
    client.end();
});

// Handle connection close
client.on('close', function () {
    console.log('Connection closed');
    process.exit(0);
});

// Handle errors
client.on('error', function (err) {
    console.log('Connection error:', err);
    process.exit(1);
});
