const { isEmpty } = require('lodash');
var net = require('net');
let deviceID = isEmpty(process.argv[2]) ? '0006' : process.argv[2];
const simCard = '+261331233217'
var pointeuse = new net.Socket()
var isConnected = false

pointeuse.connect(2701, '127.0.0.1', function (err, socket) {
    if (err) throw err;
    console.log('Launching connection to the pointeuse server');
    // console.log(socket)
    pointeuse.write('startCon' + deviceID)
})

pointeuse.on('data', function (data) {
    // if (typeof data != "string") throw new Error('data must be a string')
    console.log(data.toString())
    if ((/startCon(\d{4})/gs).test(data)) {
        const message = (/startCon(\d{4})/gs).exec(data)
        const pointeuseId = message[1]
        if (deviceID == pointeuseId) {
            console.log('Device Connected')
            isConnected = true
        }
        // pointeuse.write('getSim'+deviceID)
    }
    else if ((/getSim(\d{4})/gs).test(data)) {
        const message = (/getSim(\d{4})/gs).exec(data)
        const pointeuseId = message[1]
        if (deviceID == pointeuseId) {
            console.log('Sending sim card number')
            pointeuse.write(deviceID + simCard)
        }
    }
    else if ((/listId(\d{4})/gs).test(data)) {
        console.log('Sending list of IDs')
        pointeuse.write(deviceID + ':1,2,')
        pointeuse.write(deviceID + ':end')
    }
    else if (('setDat' + deviceID) in data) {
        console.log('Getting date')
        // pointeuse.write(deviceID + ':01/01/2022')
    }
    else if ((/enroll(\d{4})(\d{5})(\d{1})/gs).test(data)) {
        const message = (/enroll(\d{4})(\d{4})(\d{5})(\d{1})/gs).exec(data)
        const empreinteId = message[1]
        const agent_id = message[3]
        const digit = message[4]
        if (empreinteId == deviceID) {
            console.log('enroll request received')
            pointeuse.write('enrollok' + deviceID + agent_id + '001' + digit)
        }
    }
    else if ((/delete(\d{4})(\d{3})/gs).test(data)) {
        const message = (/delete(\d{4})(\d{3})/gs).exec(data)
        const empreinteId = message[2]
        console.log('delete request received')
        pointeuse.write('deleteok' + deviceID + empreinteId)
    }
    else if ((/^getTmp\d{4}\d{3}\d{1}\d{5}/gs).test(data)) {
        const message = (/^getTmp(\d{4})(\d{3})\d{1}\d{5}$/gs).exec(data)
        const pointeuseId = message[1]

    }
    else if ((/^enrDir(\d{4})(\d{1})(\d{5})/gs).test(data)) {
        const message = (/^enrDir(\d{4})(\d{1})(\d{5})/gs).exec(data)
        const pointeuseId = message[1]
        if (pointeuseId == deviceID) {
            console.log('register mode send in device')
            pointeuse.write('enrDirOk' + deviceID)
        }
    }
    else if ((/^enrDirCancel(\d{4})/gs).test(data)) {
        const message = (/^enrDirCancel(\d{4})/gs).exec(data)
        const pointeuseId = message[1]
        if (pointeuseId == deviceID) {
            console.log('register mode cancel send in device')
            pointeuse.write('enrDirCancelOk' + deviceID)
        }
    }
    else if ((/^ussd(\d{4})(.+)#$/gs).test(data)) {
        const message = (/^ussd(\d{4})(.+)#$/gs).exec(data)
        const pointeuseId = message[1]
        const ussdCode = message[2] + '#'
        if (deviceID == pointeuseId) {
            console.log('Received USSD code: ' + ussdCode)

            // Simulate different USSD responses based on the code
            let ussdResponse = ''
            if (ussdCode === '*123#') {
                // Airtel response
                ussdResponse = 'Votre numéro est le 261331234567'
            } else if (ussdCode === '#888#') {
                // Orange response
                ussdResponse = 'Votre numéro est le 261 32 123 45 67'
            } else if (ussdCode === '#120#') {
                // Telma response
                ussdResponse = 'Votre numéro est le 261-34-123-45-67'
            } else {
                // Default response
                ussdResponse = 'Votre numéro est le 261331234567'
            }

            pointeuse.write('ussdResp' + deviceID + ussdResponse)
            console.log('Sending USSD response: ' + ussdResponse)
        }
    }
    else if ((/^setIdP\d{4}\d{4}/gs).test(data)) {
        const message = (/^setIdP(\d{4})(\d{4})/gs).exec(data)
        const pointeuseId = message[1]
        const newId = message[2]
        console.log(newId)
        if (pointeuseId === deviceID) {
            console.log('setIdP send in device')
            pointeuse.write('setIdOk' + deviceID + newId)
            deviceID = newId
        }
    }
    else {
        console.log('Received unknown message:', data.toString())
    }
})

setInterval(() => {
    if (isConnected) {
        // console.log('sending Vigilance')
        // pointeuse.write('250318101100' + deviceID + '10000021000001')
        console.log(deviceID)
    }
}, 10000)

setInterval(() => {
    if (isConnected) {
        console.log('sending test cyclique')
        pointeuse.write(deviceID)
    }
}, 300000)
