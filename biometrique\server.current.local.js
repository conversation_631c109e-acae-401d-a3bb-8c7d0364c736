const net = require('net')
const fs = require('fs')
const moment = require('moment')
const mysql = require('mysql')
const axios = require('axios')

const port = 2701;
// const transmitter = '************'
const transmitter = '127.0.0.1'

const recoveryPath = 'recovery/biometrique/'
const logFile = 'logs/biometrique/' + moment().format("YYYYMMDDHHmmss") + ".log"

const { db_config_ovh, sendMail } = require("../auth")
// const pool = mysql.createPool(db_config_ovh);
const pool = mysql.createPool({ host: 'localhost', port: 3306, user: 'tls', password: 'Srv$$OvH@tls2023', database: 'tls_alarm' })

const sqlInsertLog = "INSERT INTO ademcomemlog(dtarrived, pointeuse_id, pointeuse_user_id, codeevent, eventQualify, " +
    "messageType, istraite, transmitter, port) values (?, ?, ?, ?, ?, ?, ?, ?, ?)"
const sqlInsertLogWithLoyalty = "INSERT INTO ademcomemlog(dtarrived, pointeuse_id, pointeuse_user_id, codeevent, eventQualify, " +
    "messageType, istraite, transmitter, port, loyalty_level, is_date_server) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
const sqlSelectArmStatus = "SELECT arm, password FROM pointeuses where id = ?"
const sqlUpdateArmStatus = "UPDATE pointeuses set arm = ? where id = ?"
const sqlUpdateLastConnection = "UPDATE pointeuses set last_connection = now() where id = ?"
const sqlUpdateRegisterMode = "UPDATE pointeuses set register_mode = ? where id= ?"
const sqlInsertHistoriquePointeuses = "INSERT INTO `historique_pointeuses` (`pointeuse_id`, `user_id`, `objet`, `detail`, `created_at`, `updated_at`) VALUES (?, ?, ?, ?, now(), now());"

const dest = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]

let pointeuses = {}
let empreintes = {}
var sockets = {}
var clientSockets = []
let userIds = []
let tryErr = {}

// Replace the single specialDevice with a registry
const specialDeviceRegistry = {
    devices: {},
    maxRetries: 3,
    retryInterval: 5000,

    // Add a new device claiming to be 0015
    registerDevice: function (socket) {
        const deviceKey = `${socket.remoteAddress}:${socket.remotePort}`;

        if (!this.devices[deviceKey]) {
            this.devices[deviceKey] = {
                socket: socket,
                originalId: "0015",
                currentId: null,
                sim: null,
                state: 'connected',
                retryCount: 0,
                lastActivity: Date.now(),
                timer: null,
                isLegitimate: false
            };

            this.startMonitoring(deviceKey);
            this.getSimInfo(deviceKey);
            console.log(`New device registered as 0015 from ${deviceKey}`);
        }
    },

    // Start monitoring a device
    startMonitoring: function (deviceKey) {
        this.clearMonitoring(deviceKey);

        this.devices[deviceKey].timer = setInterval(() => {
            const device = this.devices[deviceKey];

            // Check connection status
            if (!device.socket || device.socket.destroyed) {
                this.handleDisconnection(deviceKey);
                return;
            }

            // Check for stalled devices
            if (Date.now() - device.lastActivity > 30000) { // 30 seconds inactivity
                console.log(`Device ${deviceKey} inactive, disconnecting`);
                device.socket.end();
                return;
            }

            // Retry SIM info if needed
            if (device.state === 'connected' && !device.sim) {
                this.getSimInfo(deviceKey);
            }
        }, this.retryInterval);
    },

    // Get SIM info from device
    getSimInfo: async function (deviceKey) {
        const device = this.devices[deviceKey];

        if (device?.socket && !device.socket.destroyed) {
            try {
                // Try getSim0015 first
                device.socket.write("ussd0015#888#");
                device.lastActivity = Date.now();
                device.simAttempt = 1;
                console.log(`Requested SIM info from ${deviceKey} using USSD #888#`);

                // Set timeout for USSD attempts if getSim0015 fails
                setTimeout(() => {
                    if (!device.sim && device.simAttempt === 1) {
                        device.socket.write("ussd0015#120#");
                        device.simAttempt = 2;
                        console.log(`Retrying SIM info from ${deviceKey} using USSD #120#`);

                        // Set timeout for second USSD attempt
                        setTimeout(() => {
                            if (!device.sim && device.simAttempt === 2) {
                                device.socket.write("ussd0015*123#");
                                device.simAttempt = 3;
                                console.log(`Retrying SIM info from ${deviceKey} using USSD *123#`);
                            }
                        }, 10000); // Wait 10 seconds before trying *123#
                    }
                }, 10000); // Wait 10 seconds before trying first USSD

            } catch (err) {
                console.error(`Error getting SIM from ${deviceKey}:`, err);
                this.handleDisconnection(deviceKey);
            }
        }
    },

    // Process SIM info received from device
    processSimInfo: function (deviceKey, simNumber) {
        const device = this.devices[deviceKey];
        device.sim = "0" + simNumber;
        device.lastActivity = Date.now();

        console.log(`Device ${deviceKey} has SIM: ${device.sim}`);

        // Find matching ID in database
        this.findMatchingIdInDB(deviceKey);
    },

    processUssdResponse: function (deviceKey, ussdResponse) {
        const device = this.devices[deviceKey];
        if (!device) return;

        console.log(`Processing USSD response for ${deviceKey}:`, ussdResponse);

        const phonePatterns = [
            /(\+?261[\s-]*\d{2}[\s-]*\d{3}[\s-]*\d{2}[\s-]*\d{2})/,
            /(\+?261[\s-]*\d{2}[\s-]*\d{2}[\s-]*\d{3}[\s-]*\d{2})/,
            /(\+?261[\s-]*\d{9})/,
            /(261[\s-]*\d{9})/,
            /(\b0[\s-]*\d{2}[\s-]*\d{3}[\s-]*\d{2}[\s-]*\d{2}\b)/,
            /(\b0[\s-]*\d{2}[\s-]*\d{2}[\s-]*\d{3}[\s-]*\d{2}\b)/,
            /(\b0[\s-]*\d{9}\b)/,
            /(\b\d{9}\b)/
        ];

        for (const pattern of phonePatterns) {
            const match = ussdResponse.match(pattern);
            if (match) {
                let cleaned = match[1].replace(/[\s-]/g, '');

                let simNumber;
                if (cleaned.startsWith('+261')) {
                    simNumber = '0' + cleaned.slice(4);
                } else if (cleaned.startsWith('261')) {
                    simNumber = '0' + cleaned.slice(3);
                } else if (cleaned.startsWith('0')) {
                    simNumber = cleaned;
                } else if (cleaned.length === 9) {
                    simNumber = '0' + cleaned;
                }

                // Validate the simNumber format
                if (simNumber && /^0\d{9}$/.test(simNumber)) {
                    device.sim = simNumber;
                    device.lastActivity = Date.now();
                    console.log(`Device ${deviceKey} has SIM: ${device.sim} (from USSD)`);
                    this.findMatchingIdInDB(deviceKey);
                    return;
                }
            }
        }

        // If we get here, either no phone number was found or it was invalid
        console.log(`Invalid or no phone number in USSD response for ${deviceKey}, attempt: ${device.simAttempt}`);

        // Continue with next USSD attempt if available
        if (device.simAttempt < 3) {
            // The next attempt will be triggered by the existing timeout in getSimInfo
            return;
        }

        // If this was the last attempt, mark the device as failed
        console.log(`All USSD attempts failed for ${deviceKey}`);
        device.state = 'failed';
        this.handleDisconnection(deviceKey);
    },

    // Find device ID in DB with matching SIM
    findMatchingIdInDB: function (deviceKey) {
        const device = this.devices[deviceKey];
        const query = "SELECT id FROM pointeuses WHERE sim = ? AND id != 15";

        pool.query(query, [device.sim], (err, results) => {
            device.lastActivity = Date.now();

            if (err) {
                console.error(`Database error for ${deviceKey}:`, err);
                this.retryDatabaseOperation(deviceKey);
                return;
            }

            if (results.length > 0) {
                const newId = results[0].id.toString().padStart(4, '0');
                console.log(`Found matching ID in DB for ${deviceKey}: ${newId}`);
                this.changeDeviceId(deviceKey, newId);
            } else {
                console.log(`No matching ID found for ${deviceKey}`);
                device.state = 'unverified';
                this.handleUnverifiedDevice(deviceKey);
            }
        });
    },

    // Change device ID to match database
    changeDeviceId: function (deviceKey, newId) {
        const device = this.devices[deviceKey];
        device.state = 'id_changing';
        device.currentId = newId;

        if (device.socket && !device.socket.destroyed) {
            try {
                const command = "setIdP0015" + newId;
                device.socket.write(command);
                device.lastActivity = Date.now();
                console.log(`Sent ID change to ${deviceKey}: ${command}`);
            } catch (err) {
                console.error(`Error changing ID for ${deviceKey}:`, err);
                this.handleDisconnection(deviceKey);
            }
        }
    },

    // Confirm ID change was successful
    confirmIdChange: function (deviceKey) {
        const device = this.devices[deviceKey];
        device.state = 'ready';
        device.lastActivity = Date.now();

        console.log(`Device ${deviceKey} successfully changed to ${device.currentId}`);

        // Update socket reference
        sockets[device.currentId] = device.socket;
        delete sockets[device.originalId];

        // For legitimate devices, update SIM
        if (device.isLegitimate) {
            this.updateSimInDatabase(deviceKey);
            this.cleanupDuplicates(deviceKey);
        } else {
            console.log(`Device ${deviceKey} released as normal device with ID ${device.currentId}`);
        }

        // Remove from special registry
        this.removeDevice(deviceKey);
    },

    // Clean up other devices with same SIM
    cleanupDuplicates: function (legitimateDeviceKey) {
        const legitimateDevice = this.devices[legitimateDeviceKey];

        Object.keys(this.devices).forEach(key => {
            if (key !== legitimateDeviceKey &&
                this.devices[key].sim === legitimateDevice.sim) {
                console.log(`Closing duplicate device ${key}`);
                this.devices[key].socket.end();
                this.removeDevice(key);
            }
        });
    },

    // Handle unverified devices (no matching DB entry)
    handleUnverifiedDevice: function (deviceKey) {
        const device = this.devices[deviceKey];
        console.log(`No matching SIM found for ${deviceKey}, releasing as normal device`);

        // 1. Assign a new ID from the database
        this.assignNewDeviceId(deviceKey);
    },
    assignNewDeviceId: function (deviceKey) {
        const device = this.devices[deviceKey];

        // Get next available ID from database
        const query = "SELECT MAX(id) as maxId FROM pointeuses";

        pool.query(query, (err, results) => {
            if (err) {
                console.error(`Database error getting new ID for ${deviceKey}:`, err);
                this.retryDatabaseOperation(deviceKey, 'assignNewDeviceId');
                return;
            }

            // Get the next ID based on the max ID in the database
            let nextId = results[0].maxId + 1;
            let newId = nextId.toString().padStart(4, '0');

            // Check if this ID is already in use by a connected device
            const isIdInUse = () => {
                // Get all currently connected device IDs
                const connectedIds = Object.values(this.devices)
                    .filter(d => d.id && d.id !== '0015') // Exclude special device ID
                    .map(d => d.id);

                // Check if our candidate ID is already in use
                return connectedIds.includes(newId);
            };

            // If the ID is already in use, increment and try again
            while (isIdInUse()) {
                console.log(`ID ${newId} is already in use by a connected device, trying next ID`);
                nextId++;
                newId = nextId.toString().padStart(4, '0');
            }

            console.log(`Assigning new ID ${newId} to ${deviceKey}`);

            // 2. Update the device's ID
            this.changeDeviceId(deviceKey, newId);

            // 3. Add new record to database
            this.createNewPointeuseRecord(deviceKey, newId);
        });
    },

    createNewPointeuseRecord: function (deviceKey, newId) {
        const device = this.devices[deviceKey];
        const query = `
            INSERT INTO pointeuses
            (id, sim, last_connection, created_at, updated_at)
            VALUES (?, ?, NOW(), NOW(), NOW())
        `;

        // pool.query(query, [parseInt(newId), device.sim], (err) => {
        //     if (err) {
        //         console.error(`Error creating pointeuse record for ${deviceKey}:`, err);
        //         this.retryDatabaseOperation(deviceKey, 'createNewPointeuseRecord', newId);
        //         return;
        //     }

        //     console.log(`Created new pointeuse record for ${newId}`);
        //     // Device will be confirmed when we get the setIdOk response
        // });
    },
    // Remove device from registry
    removeDevice: function (deviceKey) {
        this.clearMonitoring(deviceKey);
        delete this.devices[deviceKey];
    },

    // Clear monitoring for a device
    clearMonitoring: function (deviceKey) {
        if (this.devices[deviceKey]?.timer) {
            clearInterval(this.devices[deviceKey].timer);
            this.devices[deviceKey].timer = null;
        }
    },

    // Handle disconnection
    handleDisconnection: function (deviceKey) {
        const device = this.devices[deviceKey];

        if (device.retryCount < this.maxRetries) {
            device.retryCount++;
            console.log(`Reconnecting device ${deviceKey} (attempt ${device.retryCount})`);
            setTimeout(() => this.checkReconnection(deviceKey), this.retryInterval);
        } else {
            console.log(`Max retries reached for ${deviceKey}`);
            this.removeDevice(deviceKey);
        }
    },

    // Check if device reconnected
    checkReconnection: function (deviceKey) {
        // In a real implementation, you'd check if the device reconnected
        // For this example, we'll just remove it
        this.removeDevice(deviceKey);
    }
};

const saveOnLateDirectory = (queryValue, pointeuseId, codeEvent) => {
    let queryValues = []
    queryValues.push(queryValue)
    fs.writeFile(
        'recovery/biometrique_late/' + moment().format("YYMMDDHHmmss") + '_' + pointeuseId + '_' + codeEvent + '.json',
        JSON.stringify(queryValues),
        (err) => {
            console.error(err)
        }
    )
}

const MAX_RETRIES = 3
const RETRY_DELAY = 3000 //ms

async function withRetry(asyncOperation, retries = MAX_RETRIES) {
    let lastError;
    for (let attempt = 1; attempt <= retries; attempt++) {
        try {
            return await asyncOperation();
        } catch (error) {
            lastError = error;
            console.error(`Attempt ${attempt} failed: ${error.message}`);
            if (attempt < retries) {
                await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
            }
        }
    }
    throw lastError; // All retries failed
}

// new Concept

class DataInstance {
    constructor() {
        this.data = null; // Placeholder for assigned data
    }

    initialize(data) {
        this.data = data;
        this.timestamp = new Date();
        console.log(`Instance initialized with data: ${this.data.toString()} at ${this.timestamp}`);
    }

    async process(onComplete, socket) {
        console.log(`Processing data: ${this.data.toString()}`);
        if (typeof this.data == "string") {
            if (this.data == "sendNewId") {
                setTimeout(() => {
                    // axios.get('http://************:8080/api/pointeuses/new_id')
                    axios.get('http://localhost:8000/api/pointeuses/new_id')
                        .then(({ data }) => {
                            if (data) {
                                let newId = "000" + (data.lastId + 1)
                                console.log("newIdP" + newId.slice(newId.length - 4))
                                socket.write("newIdP" + newId.slice(newId.length - 4))
                            }
                        })
                        .catch((err) => {
                            console.log(err)
                        })
                }, 1000)
            }
            // Check for special device connection
            else if (/startCon0015/.test(this.data)) {
                specialDeviceRegistry.registerDevice(socket);
                sockets["0015"] = socket;
            }

            // Handle SIM info response from special device
            else if (/0015\+261\d{9}/.test(this.data)) {
                const deviceKey = `${socket.remoteAddress}:${socket.remotePort}`;
                const device = specialDeviceRegistry.devices[deviceKey];

                if (device && device.state === 'connected') {
                    const simMatch = /0015\+261(\d{9})/.exec(this.data);
                    if (simMatch) {
                        specialDeviceRegistry.processSimInfo(deviceKey, simMatch[1]);
                    }
                }
            }

            else if (/^ussdResp0015(.+)$/gs.test(this.data)) {
                const deviceKey = `${socket.remoteAddress}:${socket.remotePort}`;
                const device = specialDeviceRegistry.devices[deviceKey];

                if (device) {
                    const message = /^ussdResp0015(.+)$/gs.exec(this.data);
                    const ussdResponse = message[1];
                    specialDeviceRegistry.processUssdResponse(deviceKey, ussdResponse);
                }
                return;
            }

            // Handle ID change confirmation from special device
            else if (/setIdOk0015\d{4}/.test(this.data)) {
                const deviceKey = `${socket.remoteAddress}:${socket.remotePort}`;
                const device = specialDeviceRegistry.devices[deviceKey];

                if (device && device.state === 'id_changing') {
                    const idMatch = /setIdOk0015(\d{4})/.exec(this.data);
                    if (idMatch) {
                        // Check if this was a legitimate device (matched existing SIM)
                        device.isLegitimate = (idMatch[1] !== device.currentId);
                        specialDeviceRegistry.confirmIdChange(deviceKey);
                    }
                }
            }

            else if ((/setIdP(\d{4})(\d{4})/gs).test(this.data)) {
                const message = (/setIdP(\d{4})(\d{4})/gs).exec(this.data)
                const oldId = message[1]
                const newId = message[2]
                console.log(oldId + ": newIdP" + oldId + newId)
                socket.write("set id device...")
                if (sockets[oldId]) {
                    sockets[oldId].write("newIdP" + oldId + newId)
                    sockets[oldId].client = socket
                }
            }

            else if ((/setIdOk(\d{4})(\d{4})/gs).test(this.data)) {
                const message = (/setIdOk(\d{4})(\d{4})/gs).exec(this.data)
                const oldId = message[1]
                const newId = message[2]
                sockets[newId] = socket
                if (sockets[oldId]) {
                    if (sockets[oldId].client)
                        sockets[oldId].client.write("success")
                    delete sockets[oldId]
                }
            }
            else if ((/delAll(\d{4})/gs).test(this.data)) {
                const message = (/delAll(\d{4})/gs).exec(this.data)
                const pointeuseId = message[1]
                if (sockets[pointeuseId]) {
                    sockets[pointeuseId].write(this.data)
                    socket.write("del all digit send in device")
                    sockets[pointeuseId].client = socket
                }
            }
            else if ((/delAllOk(\d{4})/gs).test(this.data)) {
                const message = (/delAllOk(\d{4})/gs).exec(this.data)
                const pointeuseId = message[1]
                const sqlDeleteAllDigit = "DELETE FROM agent_pointeuses WHERE pointeuse_id = ?"

                pool.query(sqlDeleteAllDigit, [pointeuseId], (err) => {
                    if (err) {
                        console.error("Database query error:", err);
                        //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                        if (err.code === 'ECONNRESET' || err.code === 'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR') {
                            console.error("Fatal error detected. Restarting the connection pool...");
                            // Add logic to restart or reinitialize the connection pool here
                            return; // Do not terminate the server process abruptly
                        }
                        return; // Handle other errors gracefully
                    }

                    if (sockets[pointeuseId] && sockets[pointeuseId].client) {
                        try {
                            sockets[pointeuseId].client.write("success");
                        } catch (writeError) {
                            console.error("Error writing to socket:", writeError);
                        }
                    } else {
                        console.warn(`Socket not found for pointeuseId: ${pointeuseId}`);
                    }
                });

            }
            else if ((/delAllErr(\d{4})/gs).test(this.data)) {
                const message = (/delAllErr(\d{4})/gs).exec(this.data)
                const pointeuseId = message[1]
                if (sockets[pointeuseId] && sockets[pointeuseId].client)
                    sockets[pointeuseId].client.write("error")
            }
            else if ((/startCon(\d{4})/gs).test(this.data)) {
                const message = (/startCon(\d{4})/gs).exec(this.data)
                const pointeuseId = message[1]
                socket.write(this.data)
                sockets[pointeuseId] = socket
                const queryValue = [moment().format('YYYY-MM-DD HH:mm:ss'), pointeuseId, null, 603, 1, 18, 2, transmitter, port]
                pool.query(sqlInsertLog, queryValue, (err) => {
                    if (err) {
                        console.error("Error during query execution:", err);

                        if (err.code === 'ECONNRESET' || err.code === 'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR') {
                            console.error("Fatal error detected. Restarting the database connection pool...");
                            return;
                        }

                        console.warn("Query failed but not fatal. Consider retrying the operation if necessary.");
                        return;
                    }

                    console.log('Successfully inserted log for start connection');
                });

            }
            else if ((/startCon_clt/gs).test(this.data)) {
                clientSockets.push[socket]
            }
            else if ((/getSim(\d{4})user(\d+)/gs).test(this.data)) {
                const message = (/getSim(\d{4})user(\d+)/gs).exec(this.data)
                const pointeuseId = message[1]
                addUserIdToPointeuseId(pointeuseId, message[2])
                if (sockets[pointeuseId]) {
                    sockets[pointeuseId].write(this.data)
                    socket.write("getSim request send in device")
                    sockets[pointeuseId].client = socket
                }
                else
                    socket.write("device_not_found")
            }
            else if ((/\d{4}\+261\d{9}/gs).test(this.data)) {
                const message = (/(\d{4})\+261(\d{9})/gs).exec(this.data)
                const pointeuseId = message[1]
                const sim = "0" + message[2]

                const sqlUpdateSim = "UPDATE pointeuses set sim = ? where id = ?"
                pool.query(sqlUpdateSim, [sim, pointeuseId], (err, result) => {
                    if (err) {
                        //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                    }     // console.log(err)
                    else
                        console.log('Sim Updated')
                })

                if (sockets[pointeuseId] && sockets[pointeuseId].client) {
                    sockets[pointeuseId].client.write(sim)
                    setTimeout(() => {
                        sockets[pointeuseId].client.write("success")
                    }, 200);
                }
                pool.query(sqlInsertHistoriquePointeuses, [pointeuseId, getUserIdOfPointeuseId(pointeuseId), 'Mise a jour sim reussie', 'nouveau sim : ' + sim], (err, result) => {
                    if (err) { }
                    //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                    else
                        console.log('Succefully insert historique pointeuses')
                    deletePointeuseIdinUserIds(pointeuseId)
                })
            }
            else if ((/listId(\d{4})/gs).test(this.data)) {
                const message = (/listId(\d{4})/gs).exec(this.data)
                const pointeuseId = message[1]
                // addUserIdToPointeuseId(pointeuseId, message[2])
                if (sockets[pointeuseId]) {
                    sockets[pointeuseId].write(this.data)
                    socket.write("listId request send in device")
                    sockets[pointeuseId].client = socket
                }
                else
                    socket.write("device_not_found")
            }
            else if ((/resRem(\d{4})\d{6}/gs).test(this.data)) {
                const message = (/resRem(\d{4})\d{6}/gs).exec(this.data)
                const pointeuseId = message[1]
                if (sockets[pointeuseId]) {
                    sockets[pointeuseId].write(this.data)
                    socket.write("resRem request send in device")
                    sockets[pointeuseId].client = socket
                }
                else
                    socket.write("device_not_found")
            }
            else if (/^resRemOk\d{4}$/.test(this.data)) {
                const message = (/resRemOk(\d{4})/gs).exec(this.data)
                const pointeuseId = message[1]
                if (sockets[pointeuseId] && sockets[pointeuseId].client)
                    sockets[pointeuseId].client.write('success')
            }
            else if (/^setRem\d{4}\d{10}\d{6}$/.test(this.data)) {
                const message = (/^setRem(\d{4})\d{10}\d{6}$/gs).exec(this.data)
                const pointeuseId = message[1]
                if (sockets[pointeuseId]) {
                    sockets[pointeuseId].write(this.data)
                    socket.write('set remote send in device')
                    sockets[pointeuseId].client = socket
                }
                else
                    socket.write('device_not_found')
            }
            else if (/^setRemOk\d{4}$/.test(this.data)) {
                const message = (/^setRemOk(\d{4})$/gs).exec(this.data)
                const pointeuseId = message[1]
                if (sockets[pointeuseId] && sockets[pointeuseId].client)
                    sockets[pointeuseId].client.write('success')
            }
            else if ((/cnfRem(\d{4})\d{6}/gs).test(this.data)) {
                const message = (/cnfRem(\d{4})\d{6}/gs).exec(this.data)
                const pointeuseId = message[1]
                if (sockets[pointeuseId]) {
                    sockets[pointeuseId].write(this.data)
                    socket.write("config remote request send in device")
                    sockets[pointeuseId].client = socket
                }
                else
                    socket.write("device_not_found")
            }
            else if ((/^remind(\d{4})(\d{2}):(.+)/gs).test(this.data)) {
                const message = (/^remind(\d{4})(\d{2}):(.+)/gs).exec(this.data)
                const pointeuseId = message[1]
                if (sockets[pointeuseId]) {
                    sockets[pointeuseId].write(this.data)
                    socket.write("success")
                }
            }
            else if ((/(\d{4}):((\d+,)+)/gs).test(this.data)) {
                const message = (/(\d{4}):((\d+,)+)/gs).exec(this.data)
                const pointeuseId = message[1]
                if (sockets[pointeuseId] && sockets[pointeuseId].client)
                    sockets[pointeuseId].client.write(this.data)
            }
            else if ((/(\d{4}):end/gs).test(this.data)) {
                const message = (/(\d{4}):end/gs).exec(this.data)
                const pointeuseId = message[1]
                if (sockets[pointeuseId] && sockets[pointeuseId].client)
                    sockets[pointeuseId].client.write(this.data, () => {
                        setTimeout(() => {
                            if (sockets[pointeuseId].client)
                                sockets[pointeuseId].client.write("success")
                        }, 100)
                    })
            }
            else if ((/sendDate(\d{4})/gs).test(this.data)) {
                let message = (/sendDate(\d{4})/gs).exec(this.data)
                const pointeuseId = message[1]
                setTimeout(() => {
                    if (sockets[pointeuseId] != null) {
                        sockets[pointeuseId].write("setDat" + pointeuseId + moment().format('YY/MM/DD,HH:mm:ss'))
                        const queryValue = [moment().format('YYYY-MM-DD HH:mm:ss'), pointeuseId, null, 600, 1, 18, 1, transmitter, port]
                        pool.query(sqlInsertLog, queryValue, (err, result) => {
                            if (err) {
                                console.log(err)
                                //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                                if (err.code == 'ECONNRESET' || err.code == 'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR') {
                                    console.log("fatal_error_checked BIOMETRIQUE\n!!!!!!!!!!!!!!!!!!!!!!")
                                    // process.exit(1)
                                }
                                let queryValues = []
                                queryValues.push(queryValue)
                                fs.writeFile(
                                    recoveryPath + moment().format('YYMMDDHHmmss') + '_' + pointeuseId + '_600.json',
                                    JSON.stringify(queryValues),
                                    (err) => {
                                        console.error(err)
                                    }
                                )
                            }
                            else {
                                setTimeout(() => {
                                    socket.write("setDat" + pointeuseId + moment().format('YY/MM/DD,HH:mm:ss'))
                                }, 200)
                                console.log('Succefully insert sendDate')
                            }
                        })
                    }
                }, 500)

            }
            else if ((/tryErr(\d{4})/gs).test(this.data)) {
                let message = (/tryErr(\d{4})/gs).exec(this.data)
                let pointeuseId = message[1]

                const queryValue = [moment().format('YYYY-MM-DD HH:mm:ss'),
                    pointeuseId, null, 604, 1, 18, 1, transmitter, port]
                if (!tryErr[pointeuseId] || moment().diff(tryErr[pointeuseId], "minute") > 5) {
                    tryErr[pointeuseId] = moment()
                    pool.query(sqlInsertLog, queryValue, (err) => {
                        if (err) {
                            console.error(); (err)
                            //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                            if (err.code == 'ECONNRESET' || err.code == 'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR') {
                                console.log("fatal_error_checked BIOMETRIQUE\n!!!!!!!!!!!!!!!!!!!!!!")
                                // process.exit(1)
                            }
                            let queryValues = []
                            queryValues.push(queryValue)
                            fs.writeFile(
                                recoveryPath + moment().format("YYMMDDHHmmss") + '_' + pointeuseId + '_604.json',
                                JSON.stringify(queryValues),
                                (err) => {
                                    console.error(err)
                                }
                            )
                        }
                        else {
                            setTimeout(() => {
                                //socket.write('OK')
                            }, 1000)
                            console.log('Succefully insert tryErr')
                        }
                    })
                }
                else {
                    saveOnLateDirectory(queryValue, pointeuseId, "604")
                }
            }
            else if ((/^(\d{12})(\d{4})(\d{4})(\d{3})(\d{3})/gs).test(this.data)) {
                const logs = (/;/gs).test(this.data) ? this.data.split(';').filter((l) => l) : [this.data];
                processLogs(logs, sockets, pool, transmitter, port, sqlInsertLogWithLoyalty, recoveryPath);
            }
            else if ((/^(\d{12})(\d{4})(\d{4})(\d{3})$/gs).test(this.data)) {
                const message = (/(\d{12})(\d{4})(\d{4})(\d{3})/gs).exec(this.data)
                const dtarrived = message[1]
                const pointeuseId = message[2]
                let codeEvent = message[3]
                const empreinteId = message[4]
                let eventQualify = 1
                if (message[3] > 1000) {
                    const msg = (/(\d{1})(\d{3})/gs).exec(message[3])
                    eventQualify = msg[1]
                    codeEvent = msg[2]
                }

                const queryValue = [moment(dtarrived, 'YYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss'),
                    pointeuseId, empreinteId, Number.parseInt(codeEvent), eventQualify, 18, 1, transmitter, port]
                pool.query(sqlInsertLog, queryValue, (err, result) => {
                    if (err) {
                        console.log(err)
                        //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                        if (err.code == 'ECONNRESET' || err.code == 'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR') {
                            console.log("fatal_error_checked BIOMETRIQUE\n!!!!!!!!!!!!!!!!!!!!!!")
                            // process.exit(1)
                        }
                        let queryValues = []
                        queryValues.push(queryValue)
                        fs.writeFile(
                            recoveryPath + dtarrived + '_' + pointeuseId + '_' + codeEvent + '.json',
                            JSON.stringify(queryValues),
                            (err) => {
                                console.error(err)
                            }
                        )
                    }
                    else {
                        if (sockets[pointeuseId] && sockets[pointeuseId].client)
                            sockets[pointeuseId].client.write('received_' + pointeuseId)
                        console.log('Succefully insert SQL')
                    }
                })
            }

            /*
                Request enrollment agent
                - pointeuseId
                - userId database
                - digit number
            */
            else if ((/enroll(\d{4})(\d{5})(\d{1})user(\d+)/gs).test(this.data)) {
                const message = (/enroll(\d{4})(\d{5})(\d{1})user(\d+)/gs).exec(this.data)
                const pointeuseId = message[1]
                const dbUserId = message[2]
                const digitNumber = message[3]
                addUserIdToPointeuseId(pointeuseId, message[4])
                if (sockets[pointeuseId]) {
                    // axios.get('http://************:8080/api/agents/get_empreinte/' + dbUserId + '?digit=' + digitNumber + '&pointeuse_id=' + pointeuseId)
                    axios.get('http://localhost:8000/api/agents/get_empreinte/' + dbUserId + '?digit=' + digitNumber + '&pointeuse_id=' + pointeuseId)
                        .then(({ data }) => {
                            if (data) {
                                const template = data['digit' + digitNumber]
                                if (template)
                                    setTimeout(() => {
                                        const enrollRequest = "enroll" + pointeuseId + ("000" + template.length).slice(-4) + dbUserId + digitNumber
                                        console.log(enrollRequest)
                                        sockets[pointeuseId].write(enrollRequest)
                                        socket.write('enroll request send in device')
                                        sockets[pointeuseId].client = socket
                                    }, 1000)
                            }
                            else
                                socket.write("forbidden_error")
                        })
                        .catch((err) => {
                            console.log(err)
                        })
                }
                else
                    socket.write('device_not_found')
            }

            /* If begin enroll is ok
                - number of sequency data
                - pointeuseId
                - user id database
                - digit
            */
            else if ((/ack(\d{1})(\d{4})(\d{5})(\d{1})/gs).test(this.data)) {
                const message = (/ack(\d{1})(\d{4})(\d{5})(\d{1})/gs).exec(this.data)
                const templatePartNumber = message[1]
                const pointeuseId = message[2]
                const dbUserId = message[3]
                const digitNumber = message[4]
                console.log('dbUserId: ' + dbUserId)
                // axios.get('http://************:8080/api/agents/get_empreinte/' + dbUserId + '?digit=' + digitNumber + '&pointeuse_id=' + pointeuseId)
                axios.get('http://localhost:8000/api/agents/get_empreinte/' + dbUserId + '?digit=' + digitNumber + '&pointeuse_id=' + pointeuseId)
                    .then(({ data }) => {
                        if (data) {
                            const templates = data['digit' + digitNumber].match(/.{1,196}/g)
                            console.log(pointeuseId + templates[templatePartNumber - 1])
                            console.log("template part length: " + (pointeuseId + templates[templatePartNumber - 1]).length)
                            setTimeout(() => {
                                const partRequest = pointeuseId + templates[templatePartNumber - 1]
                                socket.write(partRequest)
                                if (sockets[pointeuseId] && sockets[pointeuseId].client)
                                    sockets[pointeuseId].client.write('template_part: ' + templatePartNumber)
                            }, 500)
                        }
                        else if (sockets[pointeuseId] && sockets[pointeuseId].client)
                            sockets[pointeuseId].client.write("forbidden_error")
                    })
                    .catch((err) => {
                        console.log(err)
                    })
            }

            /* If enrollment is ok
                - pointeuseId
                - user id database
                - user id POINTER
                - digit
            */
            else if ((/ackfinish\d{4}/.test(this.data))) {
                console.log('template uploaded')
                const message = (/ackfinish(\d{4})/gs).exec(this.data)
                const pointeuseId = message[1]
                if (sockets[pointeuseId] && sockets[pointeuseId].client)
                    sockets[pointeuseId].client.write('template uploaded')
            }
            else if ((/enrollok(\d{4})(\d{5})(\d{3})(\d{1})/gs).test(this.data)) {
                console.log("insert empreinte agent ok!!!")
                const sqlInsertAgentEmpreinte = "INSERT INTO agent_pointeuses(agent_id, pointeuse_id, empreinte_id, digit, created_at, updated_at) values (?, ?, ?, ?, now(), now())"
                const message = (/enrollok(\d{4})(\d{5})(\d{3})(\d{1})/gs).exec(this.data)
                const pointeuseId = message[1]
                const dbUserId = message[2]
                const empreinteId = message[3]
                const digitNumber = message[4]
                pool.query(sqlInsertAgentEmpreinte, [dbUserId, pointeuseId, empreinteId, digitNumber], (err, result) => {
                    if (err) {
                        console.log(err)
                        //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                    }
                    else if (sockets[pointeuseId] && sockets[pointeuseId].client)
                        sockets[pointeuseId].client.write('success')
                })
                pool.query(sqlInsertHistoriquePointeuses, [pointeuseId, getUserIdOfPointeuseId(pointeuseId), 'Enrolement d\'empreinte reussie.', ''], (err, result) => {
                    if (err) {
                        console.log(err)
                        //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                    }
                    else {
                        console.log("historique ajouté")
                    }
                    deletePointeuseIdinUserIds(pointeuseId)
                })
            }
            /*
                Delete empreinte
                0003001
            */
            else if ((/delete(\d{4})(\d{3})user(\d+)/gs).test(this.data)) {
                const message = (/delete(\d{4})(\d{3})user(\d+)/gs).exec(this.data)
                const pointeuseId = message[1]
                addUserIdToPointeuseId(pointeuseId, message[3])
                if (sockets[pointeuseId]) {
                    sockets[pointeuseId].write(this.data)
                    socket.write('delete send in device')
                    sockets[pointeuseId].client = socket
                }
                else
                    socket.write('device_not_found')
            }
            else if ((/deleteok(\d{4})(\d{3})/gs).test(this.data)) {
                console.log("delete empreinte ok on server")
                const message = (/deleteok(\d{4})(\d{3})/gs).exec(this.data)
                const pointeuseId = message[1]
                const empreinteId = message[2]
                const sqlDeleteAgentEmpreinte = "DELETE FROM agent_pointeuses WHERE pointeuse_id = ? and empreinte_id = ?"
                pool.query(sqlDeleteAgentEmpreinte, [pointeuseId, empreinteId], (err, result) => {
                    if (err) {
                        console.log(err)
                        //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                    }
                    else if (sockets[pointeuseId] && sockets[pointeuseId].client) {
                        sockets[pointeuseId].client.write("success")
                    }

                })
                pool.query(sqlInsertHistoriquePointeuses, [pointeuseId, getUserIdOfPointeuseId(pointeuseId), 'Suppression d\'empreinte reussie.', 'Agent : '], (err, result) => {
                    if (err) {
                        console.log(err)
                        //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                    }
                    else {
                        console.log("historique ajouté")
                    }
                    deletePointeuseIdinUserIds(pointeuseId)
                })
            }
            else if (/^\d{4}$/gs.test(this.data)) {
                let message = (/^(\d{4})$/gs).exec(this.data)
                const pointeuseId = message[1]
                if (!pointeuses[pointeuseId] || moment(pointeuses[pointeuseId]).isBefore(moment().subtract(15, "minutes"))) {
                    pointeuses[pointeuseId] = moment().format("YYYY-MM-DD HH:")
                    pool.query(sqlUpdateLastConnection, [pointeuseId], (err, result) => {
                        if (err) {
                            console.log(err)
                            //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                            if (err.code == 'ECONNRESET' || err.code == 'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR') {
                                console.log("fatal_error_checked BIOMETRIQUE\n!!!!!!!!!!!!!!!!!!!!!!")
                                // process.exit(1)
                            }
                        }
                    })
                }
            }
            // set password biometrique
            else if ((/^setPwd\d{4}\d{6}\d{6}$/gs).test(this.data)) {
                let message = (/^setPwd(\d{4})\d{6}\d{6}$/gs).exec(this.data)
                let pointeuseId = message[1]
                if (sockets[pointeuseId]) {
                    sockets[pointeuseId].write(this.data)
                    if (sockets.client)
                        sockets.client.write('set password send in device')
                }
                else if (sockets.client) {
                    sockets.client.write('device_not_found')
                }
            }
            // password response error
            else if (/^passwordError\d{4}$/gs.test(this.data)) {
                console.log(this.data)
                if (sockets.client) sockets.client.write('password_error')
            }
            // password response ok
            else if (/^setPwdOk\d{4}$/gs.test(this.data)) {
                console.log(this.data)
                if (sockets.client) sockets.client.write('success')
            }
            // password response error
            else if (/^armStatus\d{4}$/gs.test(this.data)) {
                const message = (/^armStatus(\d{4})$/gs).exec(this.data)
                const pointeuseId = message[1]
                pool.query(sqlSelectArmStatus, [pointeuseId], (err, result) => {
                    if (err) {
                        console.log(err)
                        //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                        if (err.code == 'ECONNRESET' || err.code == 'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR') {
                            console.log("fatal_error_checked BIOMETRIQUE\n!!!!!!!!!!!!!!!!!!!!!!")
                            // process.exit(1)
                        }
                    }
                    else if (result.length > 0) {
                        const pointeuse = result[0]
                        setTimeout(() => {
                            if (sockets[pointeuseId]) {
                                if (pointeuse.arm)
                                    sockets[pointeuseId].write("arming" + pointeuseId + (pointeuse.password ? pointeuse.password : "123456"))
                                else
                                    sockets[pointeuseId].write("disarm" + pointeuseId + (pointeuse.password ? pointeuse.password : "123456"))
                            }
                        }, 200)
                    }
                })
            }
            /*
                Request to get template from device
            */
            else if ((/^getTmp\d{4}\d{3}\d{1}\d{5}user(\d+)/gs).test(this.data)) {
                const message = (/^getTmp(\d{4})(\d{3})(\d{1})(\d{5})user(\d+)/gs).exec(this.data)
                const pointeuseId = message[1]
                addUserIdToPointeuseId(pointeuseId, message[5])
                if (sockets[pointeuseId]) {
                    socket.write('get template send in device')
                    sockets[pointeuseId].write(this.data)
                    sockets[pointeuseId].client = socket
                }
                else
                    socket.write('device_not_found')
            }
            else if ((/^getTmp\d{4}\d{3}\d{1}\d{5}\d{4}$/gs).test(this.data)) {
                console.log("template length from device")
                const message = (/^getTmp(\d{4})(\d{3})(\d{1})(\d{5})(\d{4})$/gs).exec(this.data)
                const pointeuseId = message[1]
                const empreinteId = message[2]
                const digitNumber = message[3]
                const agentId = message[4]
                const templateLength = message[5]
                empreintes[pointeuseId] = {
                    agentId: agentId,
                    digitNumber: digitNumber,
                    empreinteId: empreinteId,
                    templateLength: templateLength,
                    templates: []
                }
                if (sockets[pointeuseId]) {
                    const requestTmp = "tmp1" + pointeuseId
                    if (sockets[pointeuseId].client)
                        sockets[pointeuseId].client.write(requestTmp)
                    setTimeout(() => {
                        sockets[pointeuseId].write(requestTmp)
                    }, 1000)
                }
                else if (sockets[pointeuseId] && sockets[pointeuseId].client)
                    sockets[pointeuseId].client.write('device_not_found')
            }
            else if ((/^\d{4}\d{1}.+/gs).test(this.data)) {
                console.log("part of template from device")
                const message = (/^(\d{4})(\d{1})(.+)/gs).exec(this.data)
                const pointeuseId = message[1]
                const partNumber = message[2]
                const templatePart = message[3]
                if (empreintes[pointeuseId] && !empreintes[pointeuseId].templates.map(t => t.partNumber).includes(partNumber)) {
                    empreintes[pointeuseId].templates.push({
                        partNumber: partNumber,
                        templatePart: templatePart
                    })
                    if (sockets[pointeuseId]) {
                        const currentTemplate = empreintes[pointeuseId].templates.map(t => t.templatePart).join('')
                        console.log(currentTemplate.length, empreintes[pointeuseId].templateLength)
                        if (currentTemplate.length < empreintes[pointeuseId].templateLength) {
                            const requestTmp = "tmp" + (Number.parseInt(partNumber) + 1) + '' + pointeuseId
                            if (sockets[pointeuseId].client)
                                sockets[pointeuseId].client.write(requestTmp)
                            setTimeout(() => {
                                sockets[pointeuseId].write(requestTmp)
                            }, 1000)
                        }
                        else if (currentTemplate.length == empreintes[pointeuseId].templateLength) {
                            const { empreinteId, agentId, digitNumber } = empreintes[pointeuseId]
                            sockets[pointeuseId].write("getTmpOk" + pointeuseId)
                            const sqlSelectOpticPointeuse = "SELECT optic from pointeuses where id = ?"
                            pool.query(sqlSelectOpticPointeuse, [pointeuseId], (err, result) => {
                                if (err) {
                                    //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                                    console.log(err)
                                }
                                else {
                                    const optic = result[0].optic
                                    const sqlUpdateDigit = "UPDATE agents set digit" + digitNumber + " = ? , " + (optic ? "empreinte_optic = 1" : "empreinte = 1") + " where id = ?"
                                    console.log(empreintes[pointeuseId])
                                    pool.query(sqlUpdateDigit, [currentTemplate, agentId], (err, res) => {
                                        if (err) {
                                            //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                                        }
                                        else {
                                            console.log("empreinte register successfully")
                                            const sqlUpdateDigit = "UPDATE agent_pointeuses set get_digit = null where pointeuse_id = ? and empreinte_id = ? and agent_id = ?"
                                            pool.query(sqlUpdateDigit, [pointeuseId, empreinteId, agentId], (err, res) => {
                                                if (err) { }
                                                //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                                                else {
                                                    console.log("empreinte register successfully")
                                                    if (sockets[pointeuseId] && sockets[pointeuseId].client)
                                                        sockets[pointeuseId].client.write("success")
                                                }
                                            })
                                            pool.query(sqlInsertHistoriquePointeuses, [pointeuseId, getUserIdOfPointeuseId(pointeuseId), 'GetTemplate d\'empreinte reussie.', ''], (err, result) => {
                                                if (err) {
                                                    console.log(err)
                                                    //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                                                }
                                                else {
                                                    console.log("historique ajouté")
                                                }
                                                deletePointeuseIdinUserIds(pointeuseId)
                                            })
                                        }
                                    })
                                }
                            })
                        }
                        else if (sockets[pointeuseId] && sockets[pointeuseId].client)
                            sockets[pointeuseId].client.write('template_out_of_bound')
                    }
                    else if (sockets[pointeuseId] && sockets[pointeuseId].client)
                        sockets[pointeuseId].client.write('device_not_found')
                }
                else if (sockets[pointeuseId] && sockets[pointeuseId].client)
                    sockets[pointeuseId].client.write("duplicate_template_part")
            }

            // arm central biometrique
            else if ((/^arming\d{4}\d{6}$/gs).test(this.data)) {
                const message = (/^arming(\d{4})\d{6}$/gs).exec(this.data)
                const pointeuseId = message[1]
                if (sockets[pointeuseId]) {
                    sockets[pointeuseId].write(this.data)
                    if (sockets.client)
                        sockets.client.write('arm send in device')
                }
                else if (sockets.client) {
                    sockets.client.write('device_not_found')
                }
            }
            // disarm central biometrique
            else if ((/^disarm\d{4}\d{6}$/gs).test(this.data)) {
                const message = (/^disarm(\d{4})\d{6}$/gs).exec(this.data)
                const pointeuseId = message[1]
                if (sockets[pointeuseId]) {
                    sockets[pointeuseId].write(this.data)
                    if (sockets.client)
                        sockets.client.write('disarm send in device')
                }
                else if (sockets.client) {
                    sockets.client.write('device_not_found')
                }
            }
            else if (/^armingOk\d{4}$/gs.test(this.data)) {
                const message = (/^armingOk(\d{4})$/gs).exec(this.data)
                const pointeuseId = message[1]
                const queryValue = [moment().format('YYYY-MM-DD HH:mm:ss'), pointeuseId, null, 402, 3, 18, 1, transmitter, port]
                pool.query(sqlInsertLog, queryValue, (err, result) => {
                    if (err) {
                        console.log(err)
                        //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                        if (err.code == 'ECONNRESET' || err.code == 'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR') {
                            console.log("fatal_error_checked BIOMETRIQUE\n!!!!!!!!!!!!!!!!!!!!!!")
                            // process.exit(1)
                        }
                        let queryValues = []
                        queryValues.push(queryValue)
                        fs.writeFile(
                            recoveryPath + moment().format('YYMMDDHHmmss') + '_' + pointeuseId + '_402.json',
                            JSON.stringify(queryValues),
                            (err) => {
                                console.error(err)
                            }
                        )
                    }
                    else {
                        pool.query(sqlUpdateArmStatus, [1, pointeuseId], (err, result) => {
                            if (err) {
                                console.log(err)
                                //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                                if (err.code == 'ECONNRESET' || err.code == 'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR') {
                                    console.log("fatal_error_checked BIOMETRIQUE\n!!!!!!!!!!!!!!!!!!!!!!")
                                    // process.exit(1)
                                }
                            }
                            else {
                                setTimeout(() => {
                                    if (sockets.client) sockets.client.write('success')
                                }, 200)
                                console.log('update arm status')
                            }
                        })
                    }
                })
            }
            else if (/^disarmOk\d{4}$/gs.test(this.data)) {
                console.log(this.data)
                const message = (/^disarmOk(\d{4})$/gs).exec(this.data)
                const pointeuseId = message[1]
                const queryValue = [moment().format('YYYY-MM-DD HH:mm:ss'), pointeuseId, null, 402, 1, 18, 1, transmitter, port]
                pool.query(sqlInsertLog, queryValue, (err, result) => {
                    if (err) {
                        console.log(err)
                        //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                        if (err.code == 'ECONNRESET' || err.code == 'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR') {
                            console.log("fatal_error_checked BIOMETRIQUE\n!!!!!!!!!!!!!!!!!!!!!!")
                            // process.exit(1)
                        }
                        let queryValues = []
                        queryValues.push(queryValue)
                        fs.writeFile(
                            recoveryPath + moment().format('YYMMDDHHmmss') + '_' + pointeuseId + '_402.json',
                            JSON.stringify(queryValues),
                            (err) => {
                                console.error(err)
                            }
                        )
                    }
                    else {
                        pool.query(sqlUpdateArmStatus, [0, pointeuseId], (err, result) => {
                            if (err) {
                                console.log(err)
                                //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                                if (err.code == 'ECONNRESET' || err.code == 'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR') {
                                    console.log("fatal_error_checked BIOMETRIQUE\n!!!!!!!!!!!!!!!!!!!!!!")
                                    // process.exit(1)
                                }
                            }
                            else {
                                setTimeout(() => {
                                    if (sockets.client) sockets.client.write('success')
                                }, 200)
                                console.log("update disarm status")
                            }
                        })
                    }
                })
            }
            //ussd request
            else if ((/^ussd\d{4}.+#$/gs).test(this.data)) {
                const message = (/^ussd(\d{4})(.+)#$/gs).exec(this.data)
                const pointeuseId = message[1]

                if (sockets[pointeuseId]) {
                    sockets[pointeuseId].write(this.data)
                    sockets[pointeuseId].client = socket
                    socket.write('ussd send in device')
                }
                else
                    socket.write('device_not_found')
            }
            //ussd response
            else if ((/^ussdResp\d{4}.+$/gs).test(this.data)) {
                const message = (/^ussdResp(\d{4})(.+)$/gs).exec(this.data)
                const pointeuseId = message[1]
                const ussdResponse = message[2]

                // Look for phone number patterns in USSD response with more flexibility
                const phonePatterns = [
                    /(\+?261[\s-]*\d{2}[\s-]*\d{3}[\s-]*\d{2}[\s-]*\d{2})/,  // +261 XX XXX XX XX with spaces/dashes
                    /(\+?261[\s-]*\d{2}[\s-]*\d{2}[\s-]*\d{3}[\s-]*\d{2})/,  // +261 XX XX XXX XX with spaces/dashes
                    /(\+?261[\s-]*\d{9})/,                                  // +261XXXXXXXXX
                    /(261[\s-]*\d{9})/,                                     // 261XXXXXXXXX (without + or ?)
                    /(\b0[\s-]*\d{2}[\s-]*\d{3}[\s-]*\d{2}[\s-]*\d{2}\b)/,  // 0XX XXX XX XX with spaces/dashes
                    /(\b0[\s-]*\d{2}[\s-]*\d{2}[\s-]*\d{3}[\s-]*\d{2}\b)/,  // 0XX XX XXX XX with spaces/dashes
                    /(\b0[\s-]*\d{9}\b)/,                                   // 0XXXXXXXXX
                    /(\b\d{9}\b)/                                           // XXXXXXXXX
                ];

                let simNumber = null;
                for (const pattern of phonePatterns) {
                    const match = ussdResponse.match(pattern);
                    if (match) {
                        // Remove all non-digit characters except for leading + or ?
                        let cleaned = match[1].replace(/[\s-]/g, '');

                        // Standardize format to 0XXXXXXXXX
                        if (cleaned.startsWith('+261')) {
                            simNumber = '0' + cleaned.slice(4);
                        } else if (cleaned.startsWith('261')) {
                            simNumber = '0' + cleaned.slice(3);
                        } else if (cleaned.startsWith('0')) {
                            simNumber = cleaned;
                        } else if (cleaned.length === 9) {
                            simNumber = '0' + cleaned;
                        }
                        break;
                    }
                }

                if (simNumber) {
                    // Update database with SIM info
                    const updateSimQuery = "UPDATE pointeuses SET sim = ? WHERE id = ?";
                    pool.query(updateSimQuery, [simNumber, pointeuseId], (err, result) => {
                        if (err) {
                            console.log("Error updating SIM info:", err);
                            if (sockets[pointeuseId] && sockets[pointeuseId].client) {
                                sockets[pointeuseId].client.write('database_error');
                            }
                        } else {
                            console.log(`Updated SIM info for device ${pointeuseId}: ${simNumber}`);
                            if (sockets[pointeuseId] && sockets[pointeuseId].client) {
                                sockets[pointeuseId].client.write(ussdResponse);
                                setTimeout(() => {
                                    sockets[pointeuseId].client.write('success');
                                }, 300);
                            }
                        }
                    });
                } else {
                    // No SIM info found, just send the USSD response
                    if (sockets[pointeuseId] && sockets[pointeuseId].client) {
                        sockets[pointeuseId].client.write(ussdResponse);
                        setTimeout(() => {
                            sockets[pointeuseId].client.write('success');
                        }, 300);
                    }
                }

                if (!sockets[pointeuseId] || !sockets[pointeuseId].client) {
                    socket.write('device_not_found');
                }
            }
            //register agent
            else if ((/^enrDir(\d{4})(\d{1})(\d{5})user(\d+)/gs).test(this.data)) {
                const message = (/^enrDir(\d{4})(\d{1})(\d{5})user(\d+)$/gs).exec(this.data)
                const pointeuseId = message[1]
                addUserIdToPointeuseId(pointeuseId, message[4])
                if (sockets[pointeuseId]) {
                    sockets[pointeuseId].write(this.data)
                    socket.write('register mode send in device')
                    sockets[pointeuseId].client = socket
                }
                else
                    socket.write('device_not_found')
            }
            else if ((/^enrDirOk\d{4}/gs).test(this.data)) {
                console.log("enable register mode.")
                const message = (/enrDirOk(\d{4})/gs).exec(this.data)
                const pointeuseId = message[1]
                pool.query(sqlUpdateRegisterMode, [1, pointeuseId], (err, result) => {
                    if (err) {
                        console.log(err)
                        //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                        fs.writeFile(logFile, err,
                            (err) => { console.error(err) }
                        )
                    }
                    else if (sockets[pointeuseId] && sockets[pointeuseId].client)
                        sockets[pointeuseId].client.write("success")
                })
                pool.query(sqlInsertHistoriquePointeuses, [pointeuseId, getUserIdOfPointeuseId(pointeuseId), 'Enregistrement d\'empreinte reussie.', ''], (err, result) => {
                    if (err) {
                        console.log(err)
                        //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                    }
                    else {
                        console.log("historique ajouté")
                    }
                    deletePointeuseIdinUserIds(pointeuseId)
                })
            }
            else if ((/^enrDirCancel(\d{4})user(\d+)/gs).test(this.data)) {
                const message = (/^enrDirCancel(\d{4})user(\d+)$/gs).exec(this.data)
                const pointeuseId = message[1]
                addUserIdToPointeuseId(pointeuseId, message[2])
                if (sockets[pointeuseId]) {
                    sockets[pointeuseId].write(this.data)
                    socket.write('cancel register mode send in device')
                    sockets[pointeuseId].client = socket
                }
                else
                    socket.write('device_not_found')
            }
            else if ((/^enrDirCancelOk\d{4}/gs).test(this.data)) {
                console.log("disable register mode.")
                const message = (/^enrDirCancelOk(\d{4})/gs).exec(this.data)
                const pointeuseId = message[1]
                pool.query(sqlUpdateRegisterMode, [null, pointeuseId], (err, result) => {
                    if (err) {
                        console.log(err)
                        //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                        fs.writeFile(logFile, err,
                            (err) => { console.error(err) }
                        )
                    }
                    else if (sockets[pointeuseId] && sockets[pointeuseId].client)
                        sockets[pointeuseId].client.write("success")
                })
                pool.query(sqlInsertHistoriquePointeuses, [pointeuseId, getUserIdOfPointeuseId(pointeuseId), 'Annulation d\'enregistrement d\'empreinte reussie.', ''], (err, result) => {
                    if (err) {
                        console.log(err)
                        //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                    }
                    else {
                        console.log("historique ajouté")
                    }
                    deletePointeuseIdinUserIds(pointeuseId)
                })
            }
            //return value register
            else if ((/^regist\d{4}\d\d{5}\d{3}$/gs).test(this.data)) {
                const message = (/^regist(\d{4})(\d)(\d{5})(\d{3})$/gs).exec(this.data)
                const pointeuseId = message[1]
                const digitNumber = message[2]
                const dbUserId = message[3]
                const empreinteId = message[4]
                const sqlInsertAgentEmpreinteFromDevice = "INSERT INTO agent_pointeuses(agent_id, pointeuse_id, empreinte_id, digit, get_digit, created_at, updated_at) values (?, ?, ?, ?, 1, now(), now())"
                pool.query(sqlInsertAgentEmpreinteFromDevice, [dbUserId, pointeuseId, empreinteId, digitNumber], (err, result) => {
                    if (err) {
                        console.log(err)
                        //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                        fs.writeFile(logFile, err,
                            (err) => { console.error(err) }
                        )
                    }
                    else {
                        pool.query(sqlUpdateRegisterMode, [null, pointeuseId], (err, result) => {
                            if (err) { }
                            //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                            else
                                console.log("disable register mode")
                        })
                    }
                })
            }
            else {
                console.log("!!! other data")
                console.log('-------------')
            }
        }
        setTimeout(() => {
            console.log(`Processing complete for: ${this.data.toString()}`);
            onComplete(this); // Return the instance to the pool
        }, 1000); // Simulate processing delay
    }

    reset() {
        this.data = null; // Clear data for reuse
    }
}

// Define the InstancePool class
class InstancePool {
    constructor(size) {
        this.pool = [];
        for (let i = 0; i < size; i++) {
            this.pool.push(new DataInstance());
        }
    }

    acquire() {
        return this.pool.length > 0 ? this.pool.pop() : new DataInstance();
    }

    release(instance) {
        instance.reset(); // Reset the instance for reuse
        this.pool.push(instance);
        console.log(`Instance released back to pool. Pool size: ${this.pool.length}`);
    }

    logStatus() {
        console.log(`Pool status: ${this.pool.length} available instances`);
    }
}

// Create a pool with a fixed size
const poolSize = 50; // Adjust based on expected workload
const instancePool = new InstancePool(poolSize);

// end concept

function addUserIdToPointeuseId(pointeuseId, userId) {
    const index = userIds.findIndex(device => device.pointeuseId === pointeuseId)
    if (index !== -1) {
        userIds[index].userId = userId
    } else {
        userIds.push({ pointeuseId: pointeuseId, userId: userId })
    }
}

async function processLogs(logs, sockets, pool, transmitter, port, sqlInsertLogWithLoyalty, recoveryPath) {
    try {
        let notCorrupted = true;
        const parsedLogs = logs.map((data) => {
            try {
                const message = /^(\d{12})(\d{4})(\d{4})(\d{3})(\d{3})/gs.exec(data);
                const dtarrived = moment(moment(message[1], 'YYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss'), 'YYYY-MM-DD HH:mm:ss').isAfter(moment().subtract(1, 'months')) ? {
                    date: moment(message[1], 'YYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss'),
                    fromServer: false
                } : {
                    date: moment().format('YYYY-MM-DD HH:mm:ss'),
                    fromServer: true
                };
                const pointeuseId = message[2];
                let codeEvent = message[3];
                let eventQualify = 1;

                if (message[3] > 1000) {
                    const msg = /(\d{1})(\d{3})/gs.exec(message[3]);
                    eventQualify = msg[1];
                    codeEvent = msg[2];
                }

                const empreinteId = message[4];
                const loyaltyLevel = message[5];
                console.log(dtarrived)
                return {
                    queryValue: [
                        dtarrived['date'],
                        pointeuseId,
                        empreinteId,
                        Number.parseInt(codeEvent),
                        eventQualify,
                        18,
                        1,
                        transmitter,
                        port,
                        loyaltyLevel,
                        dtarrived['fromServer'],
                    ],
                    dtarrived,
                    pointeuseId,
                    codeEvent,
                };

            } catch (error) {
                console.log(error);
                // sendMail(pool, dest, "Server Biometrique Donnee corrumpue", data, [], () => { })
                notCorrupted = false;
            }
        });

        for (let i = 0; i < parsedLogs.length; i++) {
            try {
                const { queryValue, dtarrived, pointeuseId, codeEvent } = parsedLogs[i];
                try {
                    await pool.query(sqlInsertLogWithLoyalty, queryValue);
                    console.log(`Log ${i + 1}/${parsedLogs.length} inserted successfully.`);
                } catch (err) {
                    console.error("Error inserting log:", err);
                    if (err.code === 'ECONNRESET' || err.code === 'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR') {
                        //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(err), [], () => { })
                        console.error("Fatal database error detected. Attempting recovery...");
                    }
                    await fs.promises.writeFile(
                        `${recoveryPath}${dtarrived}_${pointeuseId}_${codeEvent}.json`,
                        JSON.stringify([queryValue]),
                    );
                }
                await new Promise((resolve) => setTimeout(resolve, 1000));
            } catch (err) {

            }
        }

        console.log("All logs processed successfully.");
        const lastLog = parsedLogs[parsedLogs.length - 1];
        const packetID = /(\d{12})(\d{4})(\d{4})(\d{3})(\d{3})(\d{4})$/gs.exec(logs[logs.length - 1])?.[6];
        if (notCorrupted && sockets[lastLog.pointeuseId]) {
            try {
                sockets[lastLog.pointeuseId].write(`received${lastLog.pointeuseId}${packetID}`);
            } catch (socketError) {
                console.error("Error writing to socket:", socketError);
            }
        }
    } catch (error) {
        console.error("Unexpected error in processLogs:", error);
        try {
            //sendmail(pool, dest, "Erreur Server Biometrique", formatErrorForApp(error), [], () => { })
        } catch (error) {
            console.error("Error sending email:", error);
        }
    }
}

function formatErrorForApp(err) {
    const stackTrace = err.stack ? err.stack.replace(/\n/g, '<br>') : 'No stack trace available';
    const otherProperties = Object.getOwnPropertyNames(err)
        .filter(prop => !['message', 'name', 'stack'].includes(prop))
        .map(prop => `<strong>${prop}:</strong> ${JSON.stringify(err[prop])}`)
        .join('<br>') || 'None';

    return `
<div style="font-family: Arial, sans-serif; color: #333;">
    <h2 style="color: #d9534f;">Error Report</h2>
    <div>
        <h4 style="margin-bottom: 5px;">Message:</h4>
        <p style="background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px;">${err.message || 'N/A'}</p>
    </div>
    <div>
        <h4 style="margin-bottom: 5px;">Error Type:</h4>
        <p style="background: #e2e3e5; color: #383d41; padding: 10px; border-radius: 5px;">${err.name || 'N/A'}</p>
    </div>
    <div>
        <h4 style="margin-bottom: 5px;">Stack Trace:</h4>
        <p style="background: #f1f1f1; color: #555; padding: 10px; border-radius: 5px; font-family: monospace; overflow-x: auto;">${stackTrace}</p>
    </div>
    <div>
        <h4 style="margin-bottom: 5px;">Other Properties:</h4>
        <p style="background: #f1f1f1; color: #555; padding: 10px; border-radius: 5px;">${otherProperties}</p>
    </div>
</div>`;
}

function deletePointeuseIdinUserIds(pointeuseId) {
    const index = userIds.findIndex(device => device.pointeuseId === pointeuseId)
    if (index !== -1) {
        userIds.splice(index, 1)
    }
}

function getUserIdOfPointeuseId(pointeuseId) {
    const index = userIds.findIndex(device => device.pointeuseId === pointeuseId)
    return index !== -1 ? userIds[index].userId : null
}

var server = net.createServer(function (socket) {
    socket.on('error', (err) => {
        //if(err ==='ERCONNRESET')
        Object.keys(sockets).map((key) => {
            if (sockets[key] == socket) delete sockets[key]
        })
        socket.end()
        console.log("*** erreur reset ***")
    })
    socket.on('timeout', () => {
        console.log("TIMEOUT close")
        socket.end()
    })

    socket.on('data', (data) => {
        console.log(moment().format('YY/MM/DD,HH:mm:ss') + "# " + data.toString())

        // for new concept
        const instance = instancePool.acquire()
        instance.initialize(data)

        instance.process((completedInstance) => {
            instancePool.release(completedInstance)
        }, socket)

        // end

    })
    socket.on('end', () => {
        const deviceKey = `${socket.remoteAddress}:${socket.remotePort}`;

        if (specialDeviceRegistry.devices[deviceKey]) {
            specialDeviceRegistry.removeDevice(deviceKey);
        }

        Object.keys(sockets).forEach(key => {
            if (sockets[key] === socket) {
                if (sockets[key].client) {
                    sockets[key].client.end();
                }
                delete sockets[key];
            }
        });
    });
})

server.on('connection', function (socket) {
    console.log('Buffer size : ' + socket.bufferSize);

    console.log('------------remote client info --------------');

    var rport = socket.remotePort;
    var raddr = socket.remoteAddress;
    var rfamily = socket.remoteFamily;

    console.log(moment().format('YYYY-MM-DD HH:mm:ss'));
    console.log('REMOTE Socket is listening at port' + rport);
    console.log('REMOTE Socket ip :' + raddr);
    console.log('REMOTE Socket is IP4/IP6 : ' + rfamily);

    console.log('--------------------------------------------')

    server.getConnections(function (error, count) {
        console.log('Number of concurrent connections to the server : ' + count);
    });

    socket.setEncoding('utf8');
})

server.on('close', (resp) => {
    console.log("Server Closed")
})

setTimeout(() => {
    server.listen(port, transmitter, () => {
        var address = server.address();
        var port = address.port;
        var family = address.family;
        var ipaddr = address.address;
        console.log('Server ' + family + ' is listening at ' + ipaddr + ':' + port);
        fs.writeFile(
            logFile,
            "Server started at " + moment().format("DD-MM-YYYY HH:mm:ss"),
            (err) => {
                console.error(err)
            }
        )
    });
}, 2000)

setInterval(() => {
    instancePool.logStatus();
}, 5000)


