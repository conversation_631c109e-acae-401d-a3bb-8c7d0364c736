const net = require('net')
const fs = require('fs')
const moment = require('moment')
const mysql = require('mysql2')
const axios = require('axios')

const port = 2702;
//const transmitter = '************'
const transmitter = '************'

const recoveryPath = 'recovery/biometrique/'
const logFile = 'logs/biometrique/' + moment().format("YYYYMMDDHHmmss") + ".log"

const { db_config_zo, sendMail } = require("../auth")
const pool = mysql.createPool(db_config_zo);
// const pool = mysql.createPool({ host: 'localhost', port: 3306, user: 'tls', password: 'Srv$$OvH@tls2023', database: 'tls_alarm' })

const sqlSelectArmStatus = "SELECT arm, password FROM pointeuses where id = ?"
const sqlUpdateArmStatus = "UPDATE pointeuses set arm = ? where id = ?"
const sqlUpdateLastConnection = "UPDATE pointeuses set last_connection = now() where id = ?"
const sqlUpdateRegisterMode = "UPDATE pointeuses set register_mode = ? where id= ?"
const sqlInsertHistoriquePointeuses = "INSERT INTO `historique_pointeuses` (`pointeuse_id`, `user_id`, `objet`, `detail`, `created_at`, `updated_at`) VALUES (?, ?, ?, ?, now(), now());"

const dest = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]

let pointeuses = {}
let empreintes = {}
var sockets = {}
var clientSockets = []
let userIds = []

const saveOnLateDirectory = (queryValue, pointeuseId, codeEvent) => {
    let queryValues = []
    queryValues.push(queryValue)
    fs.writeFile(
        'recovery/biometrique_late/' + moment().format("YYMMDDHHmmss") + '_' + pointeuseId + '_' + codeEvent + '.json',
        JSON.stringify(queryValues),
        (err) => {
            console.error(err)
        }
    )
}
const saveOnPeriodicTestDirectory = (queryValue, pointeuseId) => {
    let queryValues = []
    queryValues.push(queryValue)
    fs.writeFile(
        'recovery/biometrique_test/' + moment().format("YYMMDDHHmmss") + '_' + pointeuseId + '.json',
        JSON.stringify(queryValues),
        (err) => {
            console.error(err)
        }
    )
}

const saveOnAlarmDirectory = (fileName, content) => {
    fs.writeFile(
        'recovery/biometrique/' + fileName + '.json',
        JSON.stringify(content),
        (err) => {
            console.error(err)
        }
    )
    console.log(content)
}

const MAX_RETRIES = 3
const RETRY_DELAY = 3000 //ms

async function withRetry(asyncOperation, retries = MAX_RETRIES) {
    let lastError;
    for (let attempt = 1; attempt <= retries; attempt++) {
        try {
            return await asyncOperation();
        } catch (error) {
            lastError = error;
            console.error(`Attempt ${attempt} failed: ${error.message}`);
            if (attempt < retries) {
                await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
            }
        }
    }
    throw lastError; // All retries failed
}

// new Concept

class DataInstance {
    constructor() {
        this.data = null; // Placeholder for assigned data
    }

    initialize(data) {
        this.data = data;
        this.timestamp = new Date();
        //console.log(`Instance initialized with data: ${this.data.toString()} at ${this.timestamp}`);
    }

    async process(onComplete, socket) {
        //console.log(`Processing data: ${this.data.toString()}`);
        if (typeof this.data == "string") {
             if ((/startCon(\d{4})/gs).test(this.data)) {
                const message = (/startCon(\d{4})/gs).exec(this.data)
                const pointeuseId = message[1]
                socket.write(this.data)
                sockets[pointeuseId] = socket
                const queryValue = [moment().format('YYYY-MM-DD HH:mm:ss'), pointeuseId, null, 603, 1, 18, 2, transmitter, port]
                let queryValues = []
                queryValues.push(queryValue)
                saveOnAlarmDirectory(moment().format('YYYYMMDDHHmmss') + '_' + pointeuseId + '_603', queryValues)

            }
            else if ((/sendDate(\d{4})/gs).test(this.data)) {
                let message = (/sendDate(\d{4})/gs).exec(this.data)
                const pointeuseId = message[1]
                setTimeout(() => {
                    if (sockets[pointeuseId] != null) {
                        sockets[pointeuseId].write("setDat" + pointeuseId + moment().format('YY/MM/DD,HH:mm:ss'))
                        const queryValue = [moment().format('YYYY-MM-DD HH:mm:ss'), pointeuseId, null, 600, 1, 18, 1, transmitter, port]
                        saveOnLateDirectory(queryValue, pointeuseId, "600")
                    }
                }, 500)

            }
            else if ((/tryErr(\d{4})/gs).test(this.data)) {
                let message = (/tryErr(\d{4})/gs).exec(this.data)
                let pointeuseId = message[1]

                const queryValue = [moment().format('YYYY-MM-DD HH:mm:ss'),
                    pointeuseId, null, 604, 1, 18, 1, transmitter, port]
                saveOnLateDirectory(queryValue, pointeuseId, "604")
            }
            else if ((/^(\d{12})(\d{4})(\d{4})(\d{3})(\d{3})$/gs).test(this.data) || (/^(\d{12})(\d{4})(\d{4})(\d{3})(\d{3});/gs).test(this.data) || (/^(\d{12})(\d{4})(\d{4})(\d{3})(\d{3})(\d{4})$/gs).test(this.data)) {
                fs.appendFile(logFile, "\n" + this.data,
                    (err) => { console.error(err) }
                )
                const logs = (/;/gs).test(this.data) ? this.data.split(';').filter((l) => /^(\d{12})(\d{4})(\d{4})(\d{3})(\d{3})$/gs.test(l)) : [this.data];

                processLogs(logs, sockets, pool, transmitter, port, recoveryPath);
            }
            else if ((/^(\d{12})(\d{4})(\d{4})(\d{3})$/gs).test(this.data)) {
                fs.appendFile(logFile, "\n" + this.data,
                    (err) => { console.error(err) }
                )
                const message = (/(\d{12})(\d{4})(\d{4})(\d{3})/gs).exec(this.data)
                const dtarrived = message[1]
                const pointeuseId = message[2]
                let codeEvent = message[3]
                const empreinteId = message[4]
                let eventQualify = 1
                if (message[3] > 1000) {
                    const msg = (/(\d{1})(\d{3})/gs).exec(message[3])
                    eventQualify = msg[1]
                    codeEvent = msg[2]
                }

                const queryValue = [moment(dtarrived, 'YYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss'),
                    pointeuseId, empreinteId, Number.parseInt(codeEvent), eventQualify, 18, 1, transmitter, port]
                let queryValues = []
                queryValues.push(queryValue)
                saveOnAlarmDirectory(dtarrived + '_' + pointeuseId + '_' + codeEvent, queryValues)
            }
        }
        setTimeout(() => {
            //console.log(`Processing complete for: ${this.data.toString()}`);
            onComplete(this); // Return the instance to the pool
        }, 1000); // Simulate processing delay
    }

    reset() {
        this.data = null; // Clear data for reuse
    }
}

// Define the InstancePool class
class InstancePool {
    constructor(size) {
        this.pool = [];
        for (let i = 0; i < size; i++) {
            this.pool.push(new DataInstance());
        }
    }

    acquire() {
        return this.pool.length > 0 ? this.pool.pop() : new DataInstance();
    }

    release(instance) {
        instance.reset(); // Reset the instance for reuse
        this.pool.push(instance);
        // console.log(`Instance released back to pool. Pool size: ${this.pool.length}`);
    }

    logStatus() {
        // console.log(`Pool status: ${this.pool.length} available instances`);
    }
}

// Create a pool with a fixed size
const poolSize = 50; // Adjust based on expected workload
const instancePool = new InstancePool(poolSize);

// end concept

function addUserIdToPointeuseId(pointeuseId, userId) {
    const index = userIds.findIndex(device => device.pointeuseId === pointeuseId)
    if (index !== -1) {
        userIds[index].userId = userId
    } else {
        userIds.push({ pointeuseId: pointeuseId, userId: userId })
    }
}

async function processLogs(logs, sockets, pool, transmitter, port, recoveryPath) {
    //try {
        let notCorrupted = true;
        const parsedLogs = logs.map((data) => {
            try {
                const message = /^(\d{12})(\d{4})(\d{4})(\d{3})(\d{3})/gs.exec(data);
                const dtarrived = moment(moment(message[1], 'YYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss'), 'YYYY-MM-DD HH:mm:ss').isAfter(moment().subtract(1, 'months')) ? {
                    date: moment(message[1], 'YYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss'),
                    fromServer: false
                } : {
                    date: moment().format('YYYY-MM-DD HH:mm:ss'),
                    fromServer: true
                };
                const pointeuseId = message[2];
                let codeEvent = message[3];
                let eventQualify = 1;

                if (message[3] > 1000) {
                    const msg = /(\d{1})(\d{3})/gs.exec(message[3]);
                    eventQualify = msg[1];
                    codeEvent = msg[2];
                }

                const empreinteId = message[4];
                const loyaltyLevel = message[5];

                return {
                    queryValue: [
                        dtarrived['date'],
                        pointeuseId,
                        empreinteId,
                        Number.parseInt(codeEvent),
                        eventQualify,
                        18,
                        1,
                        transmitter,
                        port,
                        loyaltyLevel,
                    ],
                    dtarrived,
                    pointeuseId,
                    codeEvent,
                };

            } catch (error) {
                sendMail(pool, dest, "Server Biometrique Donnee corrumpue", data, [], () => { })
                notCorrupted = false;
            }
        });

        for (let i = 0; i < parsedLogs.length; i++) {
            const { queryValue, dtarrived, pointeuseId, codeEvent } = parsedLogs[i];
            if(dtarrived['date']){
                saveOnAlarmDirectory(`${dtarrived['date']}_${pointeuseId}_${codeEvent}`, [queryValue])
            }
            else {
                console.log("error date !!!!!!!!!!!!!", parsedLogs[i])
            }
        }

        const lastLog = parsedLogs[parsedLogs.length - 1];
        const packetID = /(\d{12})(\d{4})(\d{4})(\d{3})(\d{3})(\d{4})$/gs.exec(logs[logs.length - 1])?.[6];
        if (notCorrupted && sockets[lastLog.pointeuseId]) {
            try {
                sockets[lastLog.pointeuseId].write(`received${lastLog.pointeuseId}${packetID}`);
            } catch (socketError) {
                console.error("Error writing to socket:", socketError);
            }
        }
}

function formatErrorForApp(err) {
    const stackTrace = err.stack ? err.stack.replace(/\n/g, '<br>') : 'No stack trace available';
    const otherProperties = Object.getOwnPropertyNames(err)
        .filter(prop => !['message', 'name', 'stack'].includes(prop))
        .map(prop => `<strong>${prop}:</strong> ${JSON.stringify(err[prop])}`)
        .join('<br>') || 'None';

    return `
<div style="font-family: Arial, sans-serif; color: #333;">
    <h2 style="color: #d9534f;">Error Report</h2>
    <div>
        <h4 style="margin-bottom: 5px;">Message:</h4>
        <p style="background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px;">${err.message || 'N/A'}</p>
    </div>
    <div>
        <h4 style="margin-bottom: 5px;">Error Type:</h4>
        <p style="background: #e2e3e5; color: #383d41; padding: 10px; border-radius: 5px;">${err.name || 'N/A'}</p>
    </div>
    <div>
        <h4 style="margin-bottom: 5px;">Stack Trace:</h4>
        <p style="background: #f1f1f1; color: #555; padding: 10px; border-radius: 5px; font-family: monospace; overflow-x: auto;">${stackTrace}</p>
    </div>
    <div>
        <h4 style="margin-bottom: 5px;">Other Properties:</h4>
        <p style="background: #f1f1f1; color: #555; padding: 10px; border-radius: 5px;">${otherProperties}</p>
    </div>
</div>`;
}

function deletePointeuseIdinUserIds(pointeuseId) {
    const index = userIds.findIndex(device => device.pointeuseId === pointeuseId)
    if (index !== -1) {
        userIds.splice(index, 1)
    }
}

function getUserIdOfPointeuseId(pointeuseId) {
    const index = userIds.findIndex(device => device.pointeuseId === pointeuseId)
    return index !== -1 ? userIds[index].userId : null
}

var server = net.createServer(function (socket) {
    socket.setTimeout(900000);
    socket.on('error', (err) => {
        //if(err ==='ERCONNRESET')
        Object.keys(sockets).map((key) => {
            if (sockets[key] == socket) delete sockets[key]
        })
        socket.destroy()
        console.log("*** erreur reset ***")
    })
    socket.on('timeout', () => {
        console.log("TIMEOUT close")
        socket.destroy()
    })

    socket.on('data', (data) => {
        //console.log(moment().format('YY/MM/DD,HH:mm:ss') + "# " + data.toString())
        // for new concept
        const instance = instancePool.acquire()
        instance.initialize(data)

        instance.process((completedInstance) => {
            instancePool.release(completedInstance)
        }, socket)

        // end

    })
    socket.on('end', () => {
        Object.keys(sockets).map((key) => {
            if (sockets[key].client && sockets[key].client == socket) {
                delete sockets[key].client
            }
            else if (sockets[key] == socket) {
                if (sockets[key].client)
                    sockets[key].client.end()
                setTimeout(() => {
                    delete sockets[key]
                }, 1000);
            }
            socket.destroy()
        })
    })
})

server.on('connection', function (socket) {
    // console.log('Buffer size : ' + socket.bufferSize);

    // console.log('------------remote client info --------------');

    var rport = socket.remotePort;
    var raddr = socket.remoteAddress;
    var rfamily = socket.remoteFamily;

    // console.log(moment().format('YYYY-MM-DD HH:mm:ss'));
    // console.log('REMOTE Socket is listening at port' + rport);
    // console.log('REMOTE Socket ip :' + raddr);
    // console.log('REMOTE Socket is IP4/IP6 : ' + rfamily);

    // console.log('--------------------------------------------')

    server.getConnections(function (error, count) {
        // console.log('Number of concurrent connections to the server : ' + count);
    });

    socket.setEncoding('utf8');
})

server.on('close', (resp) => {
    console.log("Server Closed")
})

setTimeout(() => {
    server.listen(port, "************", () => {
        var address = server.address();
        var port = address.port;
        var family = address.family;
        var ipaddr = address.address;
        console.log('Server ' + family + ' is listening at ' + ipaddr + ':' + port);
        fs.writeFile(
            logFile,
            "Server started at " + moment().format("DD-MM-YYYY HH:mm:ss"),
            (err) => {
                console.error(err)
            }
        )
    });
}, 2000)

setInterval(() => {
    instancePool.logStatus();
}, 5000)
