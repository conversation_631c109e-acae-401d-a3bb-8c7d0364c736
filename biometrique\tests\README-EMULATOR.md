# Biometrique Server Emulator Tests

This folder contains test scripts for testing the biometrique server with emulated devices.

## Test Scripts

### USSD Response Parsing Test

The `test-ussd-with-emulator.bat` script tests the USSD response parsing functionality:

1. Starts the server
2. Starts a device emulator with ID 0015
3. Sends USSD commands with different carrier codes:
   - `ussd0015*123#` for Airtel
   - `ussd0015#888#` for Orange
   - `ussd0015#120#` for Telma
4. The device responds with USSD responses containing phone numbers in different formats
5. The server parses the phone numbers and extracts the SIM numbers
6. The SIM numbers are stored in the database

To run the test:

```
.\test-ussd-with-emulator.bat
```

### Device ID Assignment Test

The `test-device-id-with-emulator.bat` script tests the device ID assignment functionality with collision detection:

1. Starts the server
2. Starts a device emulator with ID 0043
3. Starts another device emulator with ID 0044
4. Starts a device emulator with ID 0015 (special device ID)
5. The server assigns a new ID to the special device (0045 or higher)
6. The server logs the ID collision detection and assignment process

To run the test:

```
.\test-device-id-with-emulator.bat
```

## Client Scripts

### USSD Client

The `ussd-client.js` script is used to send USSD-related commands to the server and process the responses.

Usage:

```
node ussd-client.js <command>
```

Examples:

```
node ussd-client.js ussd0015*123#  # Airtel
node ussd-client.js ussd0015#888#  # Orange
node ussd-client.js ussd0015#120#  # Telma
```

### Device ID Client

The `device-id-client.js` script is used to send device ID-related commands to the server and process the responses.

Usage:

```
node device-id-client.js <command>
```

Example:

```
node device-id-client.js listId0043
```

## Device Emulator

The `pointeuseEmulator.js` script emulates a biometric device that connects to the server and responds to various commands.

Usage:

```
node pointeuseEmulator.js <deviceID>
```

Example:

```
node pointeuseEmulator.js 0015
```
