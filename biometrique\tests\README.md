# USSD Response Parsing Tests

This folder contains test scripts for validating the USSD response parsing functionality in the biometrique server.

## Test Files

- **test-ussd-parser.js**: Basic tests for the original USSD response parsing implementation.
- **improved-ussd-parser.js**: Tests for the improved USSD response parsing with better pattern matching.
- **test-ussd-function.js**: Standalone tests for the USSD parsing function.
- **test-261-prefix.js**: Tests specifically for handling the '261' prefix (without + or ?).
- **test-final.js**: Comprehensive tests covering all phone number formats.
- **test-ussd-device.js**: Simulates a device sending USSD responses to test server integration.

## Running Tests

To run all tests, use the run-all-tests.js script:

```
node run-all-tests.js
```

To run a specific test file:

```
node test-final.js
```

## Test Coverage

The tests cover the following phone number formats:

1. +261 format: `+261XXXXXXXXX`
2. 261 format (without +): `261XXXXXXXXX`
3. 0 prefix format: `0XXXXXXXXX`
4. 9-digit format: `XXXXXXXXX`
5. Formats with spaces: `+261 XX XXX XX XX`, `0XX XXX XX XX`
6. Formats with dashes: `+261-XX-XXX-XX-XX`, `0XX-XXX-XX-XX`

## Expected Output

All phone numbers should be standardized to the format `0XXXXXXXXX` regardless of the input format.
