// Integrated test script for USSD response parsing and device ID assignment
const { spawn } = require('child_process');
const net = require('net');
const fs = require('fs');
const path = require('path');

// Configuration
const SERVER_PORT = 2701;
const SERVER_HOST = '127.0.0.1';
const LOG_FILE = path.join(__dirname, 'integrated-test-log.txt');

// Clear previous log file
if (fs.existsSync(LOG_FILE)) {
    fs.unlinkSync(LOG_FILE);
}

// Log function
function log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}\n`;
    console.log(message);
    fs.appendFileSync(LOG_FILE, logMessage);
}

// Start the server
log('Starting the server...');
const server = spawn('node', ['biometrique/server.current.local.js'], {
    stdio: 'pipe',
    shell: true
});

server.stdout.on('data', (data) => {
    log(`SERVER: ${data.toString().trim()}`);
});

server.stderr.on('data', (data) => {
    log(`SERVER ERROR: ${data.toString().trim()}`);
});

// Wait for the server to start
setTimeout(() => {
    // Test 1: USSD Response Parsing
    log('\n=== TEST 1: USSD RESPONSE PARSING ===\n');

    // Start the device emulator with ID 0015
    log('Starting device emulator with ID 0015...');
    const device = spawn('node', ['biometrique/pointeuseEmulator.js', '0015'], {
        stdio: 'pipe',
        shell: true
    });

    device.stdout.on('data', (data) => {
        log(`DEVICE: ${data.toString().trim()}`);
    });

    device.stderr.on('data', (data) => {
        log(`DEVICE ERROR: ${data.toString().trim()}`);
    });

    // Wait for the device to connect
    setTimeout(() => {
        // Send a USSD request to the device
        log('Sending USSD request to the device...');

        const client = new net.Socket();

        client.connect(SERVER_PORT, SERVER_HOST, () => {
            log('Client connected to server');
            // Send Airtel USSD code
            client.write('ussd0015*123#');
            log('Sent: ussd0015*123#');
        });

        client.on('data', (data) => {
            log(`CLIENT RECEIVED: ${data.toString().trim()}`);

            // If we receive a USSD response, close the connection
            if (data.toString().includes('ussdResp')) {
                log('Received USSD response, closing connection');
                client.end();
            }
        });

        client.on('close', () => {
            log('Client connection closed');

            // Test 2: Device ID Assignment
            setTimeout(() => {
                log('\n=== TEST 2: DEVICE ID ASSIGNMENT ===\n');

                // Start device emulators with IDs 0043 and 0044
                log('Starting device emulator with ID 0043...');
                const device1 = spawn('node', ['biometrique/pointeuseEmulator.js', '0043'], {
                    stdio: 'pipe',
                    shell: true
                });

                device1.stdout.on('data', (data) => {
                    log(`DEVICE 1: ${data.toString().trim()}`);
                });

                setTimeout(() => {
                    log('Starting device emulator with ID 0044...');
                    const device2 = spawn('node', ['biometrique/pointeuseEmulator.js', '0044'], {
                        stdio: 'pipe',
                        shell: true
                    });

                    device2.stdout.on('data', (data) => {
                        log(`DEVICE 2: ${data.toString().trim()}`);
                    });

                    // Wait for devices to connect
                    setTimeout(() => {
                        // Start a device emulator with ID 0015 (should get a new ID)
                        log('Starting device emulator with ID 0015 (should get a new ID)...');
                        const device3 = spawn('node', ['biometrique/pointeuseEmulator.js', '0015'], {
                            stdio: 'pipe',
                            shell: true
                        });

                        device3.stdout.on('data', (data) => {
                            log(`DEVICE 3: ${data.toString().trim()}`);

                            // If we see a message about ID change, the test is successful
                            if (data.toString().includes('setIdP')) {
                                log('Device ID change detected!');
                            }
                        });

                        // Wait for the device to get a new ID
                        setTimeout(() => {
                            log('\n=== TESTS COMPLETED ===\n');
                            log('Check the log file for detailed output: ' + LOG_FILE);

                            // Clean up
                            device.kill();
                            device1.kill();
                            device2.kill();
                            device3.kill();
                            server.kill();

                            process.exit(0);
                        }, 10000);
                    }, 2000);
                }, 2000);
            }, 2000);
        });

        client.on('error', (err) => {
            log(`CLIENT ERROR: ${err.message}`);
            process.exit(1);
        });
    }, 2000);
}, 3000);
