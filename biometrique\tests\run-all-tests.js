// Test runner script to run all USSD parsing tests
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Running all biometrique server tests...\n');

// Get all test files in the directory
const testDir = __dirname;
const testFiles = fs.readdirSync(testDir)
    .filter(file => file.startsWith('test-') && file.endsWith('.js') && file !== 'run-all-tests.js');

// Group test files by category
const ussdTests = testFiles.filter(file => file.includes('ussd') || file.includes('261'));
const deviceIdTests = testFiles.filter(file => file.includes('device-id'));
const otherTests = testFiles.filter(file => !ussdTests.includes(file) && !deviceIdTests.includes(file));

console.log('Test categories:');
console.log(`- USSD Response Parsing: ${ussdTests.length} tests`);
console.log(`- Device ID Assignment: ${deviceIdTests.length} tests`);
console.log(`- Other tests: ${otherTests.length} tests`);
console.log('');

console.log(`Found ${testFiles.length} test files to run:\n`);
testFiles.forEach(file => console.log(`- ${file}`));
console.log('');

// Run each test file
let passedTests = 0;
let failedTests = 0;

testFiles.forEach(file => {
    const testPath = path.join(testDir, file);
    console.log(`\n========== Running ${file} ==========\n`);

    try {
        const output = execSync(`node "${testPath}"`, { encoding: 'utf8' });
        console.log(output);

        // Check if all tests passed
        if (output.includes('failed: 0') || output.includes('0 failed')) {
            console.log(`✅ All tests in ${file} PASSED`);
            passedTests++;
        } else {
            console.log(`❌ Some tests in ${file} FAILED`);
            failedTests++;
        }
    } catch (error) {
        console.error(`Error running ${file}:`);
        console.error(error.message);
        failedTests++;
    }

    console.log(`\n========== End of ${file} ==========\n`);
});

// Print summary
console.log('\n========== Test Summary ==========');
console.log(`Total test files: ${testFiles.length}`);
console.log(`Passed: ${passedTests}`);
console.log(`Failed: ${failedTests}`);

if (failedTests === 0) {
    console.log('\n✅ All test files passed successfully!');
} else {
    console.log(`\n❌ ${failedTests} test file(s) failed.`);
}
