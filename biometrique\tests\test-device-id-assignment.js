// Test script for device ID assignment with collision detection
const assert = require('assert');

// Mock the database pool
const mockPool = {
    query: (query, callback) => {
        // Simulate a database response with maxId = 42
        callback(null, [{ maxId: 42 }]);
    }
};

// Mock the specialDeviceRegistry
const mockRegistry = {
    devices: {
        // Connected devices with their IDs
        'device1': { id: '0043' },  // This is the ID that would be assigned next (42+1)
        'device2': { id: '0044' },  // This is the ID that would be tried after the first collision
        'device3': { id: '0015' },  // Special device ID that should be ignored in collision detection
        'newDevice': { id: null }   // New device without an ID yet
    },
    
    // Mock methods
    changeDeviceId: function(deviceKey, newId) {
        console.log(`Changing device ID for ${deviceKey} to ${newId}`);
        this.devices[deviceKey].id = newId;
        this.lastAssignedId = newId;
    },
    
    createNewPointeuseRecord: function(deviceKey, newId) {
        console.log(`Creating new pointeuse record for ${deviceKey} with ID ${newId}`);
    },
    
    // The function we're testing
    assignNewDeviceId: function (deviceKey) {
        const device = this.devices[deviceKey];

        // Get next available ID from database
        const query = "SELECT MAX(id) as maxId FROM pointeuses";

        // Use our mock pool
        mockPool.query(query, (err, results) => {
            if (err) {
                console.error(`Database error getting new ID for ${deviceKey}:`, err);
                return;
            }

            // Get the next ID based on the max ID in the database
            let nextId = results[0].maxId + 1;
            let newId = nextId.toString().padStart(4, '0');
            
            // Check if this ID is already in use by a connected device
            const isIdInUse = () => {
                // Get all currently connected device IDs
                const connectedIds = Object.values(this.devices)
                    .filter(d => d.id && d.id !== '0015') // Exclude special device ID
                    .map(d => d.id);
                
                // Check if our candidate ID is already in use
                return connectedIds.includes(newId);
            };
            
            // If the ID is already in use, increment and try again
            while (isIdInUse()) {
                console.log(`ID ${newId} is already in use by a connected device, trying next ID`);
                nextId++;
                newId = nextId.toString().padStart(4, '0');
            }
            
            console.log(`Assigning new ID ${newId} to ${deviceKey}`);

            // 2. Update the device's ID
            this.changeDeviceId(deviceKey, newId);

            // 3. Add new record to database
            this.createNewPointeuseRecord(deviceKey, newId);
        });
    }
};

// Run the test
console.log("Testing device ID assignment with collision detection...\n");

// Test case: Assign a new ID to a device when there are ID collisions
console.log("Test case: Assign a new ID with collisions");
console.log("Expected behavior: Skip IDs 0043 and 0044 (already in use) and assign 0045");
mockRegistry.assignNewDeviceId('newDevice');

// Wait for the async operation to complete
setTimeout(() => {
    // Check the result
    const assignedId = mockRegistry.lastAssignedId;
    console.log(`\nResult: Device was assigned ID ${assignedId}`);
    
    // Verify the ID is not one of the already used IDs
    const usedIds = ['0043', '0044'];
    const passed = !usedIds.includes(assignedId) && assignedId === '0045';
    
    console.log(`Test ${passed ? 'PASSED' : 'FAILED'}`);
    console.log(`Expected ID: 0045`);
    console.log(`Actual ID: ${assignedId}`);
    
    console.log("\nTest Summary: " + (passed ? "1 passed, 0 failed" : "0 passed, 1 failed"));
}, 1000);
