@echo off
echo Starting device ID assignment test with emulator...

REM Start the server in a new window
start cmd /k "node biometrique\server.current.local.js"

REM Wait for the server to start
timeout /t 3

REM Start a device emulator with ID 0043 (this will be a connected device)
start cmd /k "node biometrique\pointeuseEmulator.js 0043"

REM Send a command to check the device ID
start cmd /k "node biometrique\device-id-client.js listId0043"

REM Wait for the device to connect
timeout /t 2

REM Start another device emulator with ID 0044 (this will be another connected device)
start cmd /k "node biometrique\pointeuseEmulator.js 0044"

REM Send a command to check the device ID
start cmd /k "node biometrique\device-id-client.js listId0044"

REM Wait for the device to connect
timeout /t 2

REM Start a device emulator with ID 0015 (special device ID that should get a new ID)
start cmd /k "node biometrique\pointeuseEmulator.js 0015"

REM Wait for the device to connect and get a new ID
timeout /t 5

REM Send a command to check the new device ID (should be 0045 or higher)
start cmd /k "node biometrique\device-id-client.js getSim0045"

echo Test completed. Check the output in the server window to verify the results.
echo.
echo Expected results:
echo 1. The first device should connect with ID 0043
echo 2. The second device should connect with ID 0044
echo 3. The third device (0015) should be assigned a new ID
echo 4. The new ID should be 0045 or higher (not 0043 or 0044)
echo 5. The server should log the ID collision detection and assignment process
