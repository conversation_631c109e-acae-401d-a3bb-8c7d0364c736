// Final test script for USSD response parsing with 261 prefix
const assert = require('assert');

// Function to parse USSD response and extract SIM number
function parseUssdResponse(ussdResponse) {
    // Look for phone number patterns in USSD response with more flexibility
    const phonePatterns = [
        /(\+?261[\s-]*\d{2}[\s-]*\d{3}[\s-]*\d{2}[\s-]*\d{2})/,  // +261 XX XXX XX XX with spaces/dashes
        /(\+?261[\s-]*\d{2}[\s-]*\d{2}[\s-]*\d{3}[\s-]*\d{2})/,  // +261 XX XX XXX XX with spaces/dashes
        /(\+?261[\s-]*\d{9})/,                                  // +261XXXXXXXXX
        /(261[\s-]*\d{9})/,                                     // 261XXXXXXXXX (without + or ?)
        /(\b0[\s-]*\d{2}[\s-]*\d{3}[\s-]*\d{2}[\s-]*\d{2}\b)/,  // 0XX XXX XX XX with spaces/dashes
        /(\b0[\s-]*\d{2}[\s-]*\d{2}[\s-]*\d{3}[\s-]*\d{2}\b)/,  // 0XX XX XXX XX with spaces/dashes
        /(\b0[\s-]*\d{9}\b)/,                                   // 0XXXXXXXXX
        /(\b\d{9}\b)/                                           // XXXXXXXXX
    ];

    let simNumber = null;
    for (const pattern of phonePatterns) {
        const match = ussdResponse.match(pattern);
        if (match) {
            // Remove all non-digit characters except for leading + or ?
            let cleaned = match[1].replace(/[\s-]/g, '');
            
            // Standardize format to 0XXXXXXXXX
            if (cleaned.startsWith('+261')) {
                simNumber = '0' + cleaned.slice(4);
            } else if (cleaned.startsWith('261')) {
                simNumber = '0' + cleaned.slice(3);
            } else if (cleaned.startsWith('0')) {
                simNumber = cleaned;
            } else if (cleaned.length === 9) {
                simNumber = '0' + cleaned;
            }
            break;
        }
    }
    
    return simNumber;
}

// Test cases
const testCases = [
    {
        name: "Test +261 format with spaces",
        input: "Votre numéro est le +261 33 123 45 67",
        expected: "0331234567"
    },
    {
        name: "Test with dashes",
        input: "Votre numéro est le 032-12-345-67",
        expected: "0321234567"
    },
    {
        name: "Test with spaces",
        input: "Votre numéro est le 034 12 345 67",
        expected: "0341234567"
    },
    {
        name: "Test 261 prefix (without +)",
        input: "Votre numéro est le 261331234567",
        expected: "0331234567"
    },
    {
        name: "Test 261 prefix with spaces",
        input: "Votre numéro est le 261 33 123 45 67",
        expected: "0331234567"
    },
    {
        name: "Test 261 prefix with dashes",
        input: "Votre numéro est le 261-33-123-45-67",
        expected: "0331234567"
    },
    {
        name: "Test 0015+261331234567 format (from device)",
        input: "0015+261331234567",
        expected: "0331234567"
    },
    {
        name: "Test 0015261331234567 format (from device without +)",
        input: "0015261331234567",
        expected: "0331234567"
    }
];

// Run tests
console.log("Final testing of USSD response parsing function...\n");

let passedTests = 0;
let failedTests = 0;

testCases.forEach((test, index) => {
    const result = parseUssdResponse(test.input);
    const passed = result === test.expected;
    
    console.log(`Test ${index + 1}: ${test.name}`);
    console.log(`  Input: "${test.input}"`);
    console.log(`  Expected: "${test.expected}"`);
    console.log(`  Result: "${result || 'null'}"`);
    console.log(`  Status: ${passed ? 'PASSED' : 'FAILED'}`);
    console.log();
    
    if (passed) {
        passedTests++;
    } else {
        failedTests++;
    }
});

console.log(`Test Summary: ${passedTests} passed, ${failedTests} failed`);
