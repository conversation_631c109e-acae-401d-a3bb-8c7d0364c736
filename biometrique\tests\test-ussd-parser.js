// Test script for USSD response parsing
const assert = require('assert');

// Function to parse USSD response and extract SIM number
function parseUssdResponse(ussdResponse) {
    // Look for phone number patterns in USSD response
    const phonePatterns = [
        /(\+?261\d{9})/,      // +261XXXXXXXXX
        /(\?261\d{9})/,       // ?261XXXXXXXXX
        /(\b0\d{9}\b)/,       // 0XXXXXXXXX
        /(\b\d{9}\b)/         // XXXXXXXXX
    ];

    let simNumber = null;
    for (const pattern of phonePatterns) {
        const match = ussdResponse.match(pattern);
        if (match) {
            simNumber = match[1];
            // Standardize format to 0XXXXXXXXX
            if (simNumber.startsWith('+261')) {
                simNumber = '0' + simNumber.slice(4);
            } else if (simNumber.startsWith('?261')) {
                simNumber = '0' + simNumber.slice(4);
            } else if (simNumber.length === 9) {
                simNumber = '0' + simNumber;
            }
            break;
        }
    }
    
    return simNumber;
}

// Test cases
const testCases = [
    {
        name: "Test +261 format",
        input: "Your phone number is +261321234567",
        expected: "0321234567"
    },
    {
        name: "Test ?261 format",
        input: "Your phone number is ?261321234567",
        expected: "0321234567"
    },
    {
        name: "Test 0 prefix format",
        input: "Your phone number is 0321234567",
        expected: "0321234567"
    },
    {
        name: "Test 9-digit format",
        input: "Your phone number is 321234567",
        expected: "0321234567"
    },
    {
        name: "Test with surrounding text",
        input: "Your balance is 5000 Ar. Your phone number is 0331234567. Thank you.",
        expected: "0331234567"
    },
    {
        name: "Test with multiple numbers",
        input: "Primary: 0321234567, Secondary: 0331234567",
        expected: "0321234567"  // Should match the first one
    },
    {
        name: "Test with no valid number",
        input: "Your balance is 5000 Ar. Thank you for using our service.",
        expected: null
    },
    {
        name: "Test with Telma format",
        input: "Votre numéro est le 034 12 345 67",
        expected: null  // This will fail because of spaces
    },
    {
        name: "Test with Orange format",
        input: "Votre numéro est le 032-12-345-67",
        expected: null  // This will fail because of dashes
    }
];

// Run tests
console.log("Running USSD response parser tests...\n");
let passedTests = 0;
let failedTests = 0;

testCases.forEach((test, index) => {
    const result = parseUssdResponse(test.input);
    const passed = result === test.expected;
    
    console.log(`Test ${index + 1}: ${test.name}`);
    console.log(`  Input: "${test.input}"`);
    console.log(`  Expected: ${test.expected === null ? 'null' : '"' + test.expected + '"'}`);
    console.log(`  Result: ${result === null ? 'null' : '"' + result + '"'}`);
    console.log(`  Status: ${passed ? 'PASSED' : 'FAILED'}`);
    console.log();
    
    if (passed) {
        passedTests++;
    } else {
        failedTests++;
    }
});

console.log(`Test Summary: ${passedTests} passed, ${failedTests} failed`);

// Additional test cases for improved pattern matching
console.log("\nAdditional tests with improved patterns:");

// Improved function with better pattern matching
function improvedParseUssdResponse(ussdResponse) {
    // Look for phone number patterns in USSD response with more flexibility
    const phonePatterns = [
        /(\+?261\s*\d{2}\s*\d{3}\s*\d{2}\s*\d{2})/,  // +261 XX XXX XX XX with optional spaces
        /(\+?261\s*\d{2}\s*\d{2}\s*\d{3}\s*\d{2})/,  // +261 XX XX XXX XX with optional spaces
        /(\+?261\s*\d{9})/,                         // +261XXXXXXXXX
        /(\?261\s*\d{9})/,                          // ?261XXXXXXXXX
        /(\b0\s*\d{2}\s*\d{3}\s*\d{2}\s*\d{2}\b)/,  // 0XX XXX XX XX with optional spaces
        /(\b0\s*\d{2}\s*\d{2}\s*\d{3}\s*\d{2}\b)/,  // 0XX XX XXX XX with optional spaces
        /(\b0\s*\d{9}\b)/,                          // 0XXXXXXXXX
        /(\b\d{9}\b)/                               // XXXXXXXXX
    ];

    let simNumber = null;
    for (const pattern of phonePatterns) {
        const match = ussdResponse.match(pattern);
        if (match) {
            // Remove all non-digit characters except for leading + or ?
            let cleaned = match[1].replace(/[^\d+?]/g, '');
            
            // Standardize format to 0XXXXXXXXX
            if (cleaned.startsWith('+261')) {
                simNumber = '0' + cleaned.slice(4);
            } else if (cleaned.startsWith('?261')) {
                simNumber = '0' + cleaned.slice(4);
            } else if (cleaned.startsWith('261')) {
                simNumber = '0' + cleaned.slice(3);
            } else if (cleaned.startsWith('0')) {
                simNumber = cleaned;
            } else if (cleaned.length === 9) {
                simNumber = '0' + cleaned;
            }
            break;
        }
    }
    
    return simNumber;
}

const improvedTestCases = [
    {
        name: "Test with spaces",
        input: "Votre numéro est le 034 12 345 67",
        expected: "0341234567"
    },
    {
        name: "Test with dashes",
        input: "Votre numéro est le 032-12-345-67",
        expected: "0321234567"
    },
    {
        name: "Test with different spacing",
        input: "Votre numéro est le 033 12 34 567",
        expected: "0331234567"
    },
    {
        name: "Test with +261 and spaces",
        input: "Votre numéro est le +261 33 123 45 67",
        expected: "0331234567"
    }
];

let improvedPassedTests = 0;
let improvedFailedTests = 0;

improvedTestCases.forEach((test, index) => {
    const result = improvedParseUssdResponse(test.input);
    const passed = result === test.expected;
    
    console.log(`Improved Test ${index + 1}: ${test.name}`);
    console.log(`  Input: "${test.input}"`);
    console.log(`  Expected: ${test.expected === null ? 'null' : '"' + test.expected + '"'}`);
    console.log(`  Result: ${result === null ? 'null' : '"' + result + '"'}`);
    console.log(`  Status: ${passed ? 'PASSED' : 'FAILED'}`);
    console.log();
    
    if (passed) {
        improvedPassedTests++;
    } else {
        improvedFailedTests++;
    }
});

console.log(`Improved Test Summary: ${improvedPassedTests} passed, ${improvedFailedTests} failed`);
