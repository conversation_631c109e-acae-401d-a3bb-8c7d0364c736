@echo off
echo Starting USSD response parsing test with device emulator...

REM Start the server in a new window
start cmd /k "node biometrique\server.current.local.js"

REM Wait for the server to start
timeout /t 3

REM Start the device emulator with ID 0015 (special device ID)
start cmd /k "node biometrique\pointeuseEmulator.js 0015"

REM Wait for the device to connect
timeout /t 2

REM Send a command to request USSD response with Airtel code
start cmd /k "node biometrique\ussd-client.js ussd0015*123#"

REM Wait for the USSD response to be processed
timeout /t 3

REM Send a command to request USSD response with Orange code
start cmd /k "node biometrique\ussd-client.js ussd0015#888#"

REM Wait for the USSD response to be processed
timeout /t 3

REM Send a command to request USSD response with Telma code
start cmd /k "node biometrique\ussd-client.js ussd0015#120#"

REM Wait for the USSD response to be processed
timeout /t 3

echo Test completed. Check the output in the server window to verify the results.
echo.
echo Expected results:
echo 1. The device should connect with ID 0015
echo 2. The server should send USSD commands with different carrier codes:
echo    - ussd0015*123# for Airtel
echo    - ussd0015#888# for Orange
echo    - ussd0015#120# for Telma
echo 3. The device should respond with USSD responses containing phone numbers in different formats
echo 4. The server should parse the phone numbers correctly and extract the SIM numbers
echo 5. The SIM numbers should be stored in the database
