@echo off
echo Starting test with device emulator...

REM Start the server in a new window
start cmd /k "node biometrique\server.current.local.js"

REM Wait for the server to start
timeout /t 3

REM Start the device emulator with ID 0015 (special device ID)
start cmd /k "node biometrique\pointeuseEmulator.js 0015"

REM Wait for the device to connect
timeout /t 2

REM Send a USSD response command to test USSD parsing
start cmd /k "node biometrique\client.local.js ussdResp0015Votre numero est le 261331234567"

REM Wait for the USSD response to be processed
timeout /t 3

REM Start another device emulator with a different ID
start cmd /k "node biometrique\pointeuseEmulator.js 0043"

REM Wait for the device to connect
timeout /t 2

REM Start another device emulator that will get a new ID (should be 0044 or higher)
start cmd /k "node biometrique\pointeuseEmulator.js 0015"

echo Test completed. Check the output in each window to verify the results.
echo.
echo Expected results:
echo 1. The first device should connect with ID 0015
echo 2. The USSD response should be parsed correctly and the SIM number should be extracted
echo 3. The second device should connect with ID 0043
echo 4. The third device should be assigned a new ID (0044 or higher)
