// USSD client for testing USSD response parsing
const net = require('net');
const mysql = require('mysql2'); // Changed from mysql to mysql2
const moment = require('moment');
const auth = require('../auth');

// Database configuration
const dbConfig = auth.db_config_zo; // Using original database configuration

// Create MySQL connection
const connection = mysql.createConnection(dbConfig);

// Get the next device that needs SIM info update
const getNextDevice = () => {
    return new Promise((resolve, reject) => {
        const query = `
            SELECT id, last_sim_updated_at
            FROM pointeuses
            WHERE id != 15 AND last_connection > DATE_SUB(NOW(), INTERVAL 10 MINUTE)
            AND (soft_delete IS NULL OR soft_delete = 0)
            ORDER BY last_get_sim ASC, id ASC
            LIMIT 1
        `;

        connection.query(query, (err, results) => {
            if (err) {
                reject(err);
                return;
            }

            if (results.length === 0) {
                reject(new Error('No devices found'));
                return;
            }

            const deviceId = results[0].id.toString().padStart(4, '0');
            const deviceInfo = {
                id: results[0].id,
                deviceId: deviceId,
                lastSimUpdatedAt: results[0].last_sim_updated_at
            };

            // Update last_get_sim timestamp
            const updateQuery = `
                UPDATE pointeuses
                SET last_get_sim = ?
                WHERE id = ?
            `;

            connection.query(updateQuery, [moment().format('YYYY-MM-DD HH:mm:ss'), results[0].id], (updateErr) => {
                if (updateErr) {
                    reject(updateErr);
                    return;
                }
                resolve(deviceInfo);
            });
        });
    });
};

// Connect to the database
connection.connect(async function (err) {
    if (err) {
        console.log('Error connecting to database:', err);
        process.exit(1);
    }

    console.log('Connected to database');

    try {
        // Get the next device to update
        const deviceInfo = await getNextDevice();
        console.log(`Processing device: ${deviceInfo.deviceId}`);
        console.log(`Last SIM updated at: ${deviceInfo.lastSimUpdatedAt || 'Never'}`);

        // Create a socket client
        const client = new net.Socket();

        // Track if we found a valid phone number
        let foundValidPhoneNumber = false;

        // Define phone patterns
        const phonePatterns = [
            /(\+?261[\s-]*\d{2}[\s-]*\d{3}[\s-]*\d{2}[\s-]*\d{2})/,
            /(\+?261[\s-]*\d{2}[\s-]*\d{2}[\s-]*\d{3}[\s-]*\d{2})/,
            /(\+?261[\s-]*\d{9})/,
            /(261[\s-]*\d{9})/,
            /(\b0[\s-]*\d{2}[\s-]*\d{3}[\s-]*\d{2}[\s-]*\d{2}\b)/,
            /(\b0[\s-]*\d{2}[\s-]*\d{2}[\s-]*\d{3}[\s-]*\d{2}\b)/,
            /(\b0[\s-]*\d{9}\b)/,
            /(\b\d{9}\b)/
        ];

        // Function to update last_sim_updated_at when a valid phone number is found
        const updateLastSimUpdatedAt = (phoneNumber) => {
            const updateQuery = `
                UPDATE pointeuses
                SET last_sim_updated_at = ?
                WHERE id = ?
            `;

            connection.query(updateQuery, [moment().format('YYYY-MM-DD HH:mm:ss'), deviceInfo.id], (err) => {
                if (err) {
                    console.error('Error updating last_sim_updated_at:', err);
                } else {
                    console.log(`Updated last_sim_updated_at for device ${deviceInfo.id}`);
                }
            });
        };

        // Function to send all USSD commands in sequence
        const sendUssdCommands = (retryCount = 0, maxRetries = 1) => {
            console.log(`Starting USSD command sequence (attempt ${retryCount + 1} of ${maxRetries + 1})`);

            // Try getSim command first
            console.log(`Sending USSD #888#`);
            client.write(`ussd${deviceInfo.deviceId}#888#`);

            // Schedule USSD attempts - always try all commands
            setTimeout(() => {
                if (foundValidPhoneNumber) return; // Skip if we already found a number

                console.log(`Sending USSD #120#`);
                client.write(`ussd${deviceInfo.deviceId}#120#`);

                setTimeout(() => {
                    if (foundValidPhoneNumber) return; // Skip if we already found a number

                    console.log(`Sending USSD *123#`);
                    client.write(`ussd${deviceInfo.deviceId}*123#`);

                    // After all commands have been sent, wait a bit and then check if we need to retry
                    setTimeout(() => {
                        if (foundValidPhoneNumber) {
                            // We found a valid number, no need to retry
                            return;
                        }

                        if (retryCount < maxRetries) {
                            // We haven't found a valid number yet, but we can retry
                            console.log(`No valid phone number found after attempt ${retryCount + 1}. Retrying...`);
                            sendUssdCommands(retryCount + 1, maxRetries);
                        } else {
                            // We've exhausted all retries
                            console.log(`No valid phone number found after ${maxRetries + 1} attempts. Giving up.`);
                            client.end();
                        }
                    }, 30000); // Wait 30 seconds after the last command

                }, 30000); // Wait 30 seconds before trying *123#
            }, 30000); // Wait 30 seconds before trying #120#
        };

        // Connect to the server
        client.connect(2702, '41.188.49.14', function () {
            // client.connect(2701, '127.0.0.1', function () {
            console.log('Connected to server');

            // Start the USSD command sequence
            sendUssdCommands();
        });

        // Set a timeout for the entire operation
        client.setTimeout(200000); // 200 seconds total timeout to accommodate retries


        // Handle data received from the server
        client.on('data', function (data) {
            const response = data.toString();
            console.log('Received from server:', response);

            // Check if the response contains a valid phone number using the patterns
            for (const pattern of phonePatterns) {
                if (pattern.test(response)) {
                    foundValidPhoneNumber = true;
                    const match = response.match(pattern);
                    const phoneNumber = match[0].replace(/[\s-]/g, '');
                    console.log('Found valid phone number:', phoneNumber);

                    // Update the last_sim_updated_at field in the database
                    updateLastSimUpdatedAt(phoneNumber);

                    // End the connection immediately when a valid phone number is found
                    console.log('Valid phone number found, closing connection');
                    setTimeout(() => client.end(), 1000);
                    return;
                }
            }

            // If no valid phone number is found, log it
            if (!foundValidPhoneNumber && response.trim() !== '') {
                console.log('No valid phone number in this response');
            }
        });

        // Handle timeout
        client.on('timeout', () => {
            console.log('Connection timed out');
            client.end();
        });

        // Handle connection close
        client.on('close', function () {
            console.log('Connection closed');
            connection.end();
            process.exit(0);
        });

        // Handle errors
        client.on('error', function (err) {
            console.log('Connection error:', err);
            connection.end();
            process.exit(1);
        });

    } catch (error) {
        console.error('Error:', error);
        connection.end();
        process.exit(1);
    }
});

// Handle database errors
connection.on('error', function (err) {
    console.log('Database error:', err);
    process.exit(1);
});

