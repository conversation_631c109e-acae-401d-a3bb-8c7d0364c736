<?php
$servername = "localhost";
$username = "root";
$password = "";

function intervalDayOrNightDate(){
    if(new \DateTime >= (new \DateTime)->setTime(07, 0, 0) &&
            new \DateTime < (new \DateTime)->setTime(18, 0, 0))
        return [ 
            'begin' => (new \DateTime)->setTime(07, 0, 0)->format('Y-m-d H:i:s'),
            'end' => (new \DateTime)->setTime(17, 50, 0)->format('Y-m-d H:i:s')
        ];
    else if(new \DateTime < (new \DateTime)->setTime(07, 0, 0))
        return [ 
            'begin' => (new \DateTime)->setTime(18, 0, 0)->sub(new \DateInterval('P1D'))->format('Y-m-d H:i:s'),
            'end' => (new \DateTime)->setTime(06, 20, 0)->format('Y-m-d H:i:s')
        ];
    return [ 
        'begin' => (new \DateTime)->setTime(18, 0, 0)->format('Y-m-d H:i:s'),
        'end' => (new \DateTime)->setTime(06, 20, 0)->add(new \DateInterval('P1D'))->format('Y-m-d H:i:s')
    ];
}
try{
	$conn = new PDO("mysql:host=$servername;dbname=tls_server", $username, $password);
	$conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
	$sql_select_ademco = "SELECT a.prom, a.dtarrived, a.site_id, s.group_id FROM tls_server.ademcotemp a 
        left join sites s on a.site_id = s.idsite 
        where codeTevent = 1000 and a.dtarrived > :begin and a.dtarrived < :end";
    $stmt = $conn->prepare($sql_select_ademco);
    $interval = (object) intervalDayOrNightDate();
    $stmt->execute(['begin' => $interval->begin, 'end' => $interval->end]);
    $vigilances = $stmt->fetchAll();
    
    foreach ($vigilances as $v) {
        echo $v->dtarrived;
    }
}
catch(PDOException $e){
	echo "Connection failed: ". $e->getMessage();
}
/*$conn = new mysqli($servername, $username, $password);

if($conn->connect_error){
	die("Connection failed: " . $conn->connect_error);
}
echo "Connected successfully";
?>*/