const axios = require('axios')
const moment = require('moment')
const mysql = require('mysql')

let currentPort = Math.floor(Math.random() * 15 + 2)
console.log("currentPort: " + currentPort)
const querysend = {
	'event': 'txsms',
	'userid': '0',
	'num': '',
	'port': '',
	'encoding': '0',
}
const deletesms = {
	'event': "deletesms",
	'smsbox': "s",
	'clear': "2"
}

const auth = {
    username: 'ApiUserAdmin',
    password: '1234'
}

const db_config = require("./auth").db_config_zo
const pool = mysql.createPool(db_config);

function setTimeoutCheckCentrale(currentVigilance, sites, index){
	setTimeout(() => {
		checkCentrale(currentVigilance, sites, index)
	}, 3000)
}

function checkCentrale(currentVigilance, sites, index){
	if(index < sites.length){
		if(currentPort > 16)
			currentPort = 3
		else
			currentPort++
		querysend.num = sites[index].prom
		querysend.port = currentPort
		querysend.smsinfo = sites[index].check
		axios.post('http://192.9.97.209:80/API/TaskHandle', querysend, {auth: auth})
		.then(({data}) => {
			console.log(data)
			setTimeoutCheckCentrale(currentVigilance, sites, index+1)
		})
		.catch((e) => {
			console.error(e)
			setTimeoutCheckCentrale(currentVigilance, sites, index)
		})	
	}
	else {
		console.log("all test send")
		pool.query(sqlUpdateLastTestManque, [currentVigilance.value.format("YYYY-MM-DD HH:mm:ss")], (err, result) => {
			if(err)
				console.log(err)
			waitBeforeCheck()
		})
	}
}

const sqlSelectLastTestManque = "SELECT value FROM params p WHERE p.key = 'last_test_manque'"
const sqlUpdateLastTestManque = "UPDATE params p SET p.value = ? WHERE p.key = 'last_test_manque'"

const sqlSelectCoupure = "SELECT transmitter FROM coupures WHERE vigilance = ?"

function sqlSelectJourFerie(date_vigilance){
	return "SELECT id from jour_feries where date = '" + moment(date_vigilance).format("YYYY-MM-DD") + "'"
}

function sqlSelectSite(date_vigilance, ferie) {
	const horaire = (moment(date_vigilance).format('HH:mm:ss') == '07:00:00') ? 'day' : 'night'
	const field = horaire + '_' + moment(date_vigilance).day()
	console.log("isFerie: " + ferie)
	if(ferie)
		return "SELECT s.idsite as 'id', s.nom, s.prom, s.numeropuces, c.check, s.transmitter " +
			"FROM sites s  " +
			"LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id " +
			"LEFT JOIN centrales c ON c.idcentrale = c.idcentrale " +
			"WHERE (s.soft_delete is null or s.soft_delete = 0) " +
			"and s.vigilance = 1 and c.check is not null and s.date_last_signal > '" + moment().subtract(1, "day").format("YYYY-MM-DD HH:mm:ss") + "' " +
			"and (h.id is null or (h." + field + " is not null and h." + field  + " = 1) or (h." + horaire + "_ferie is not null and h." + horaire + "_ferie = 1)) " +
			"ORDER BY s.group_pointage_id DESC, s.nom"
	else
		return "SELECT s.idsite as 'id', s.nom, s.prom, s.numeropuces, c.check, s.transmitter " +
			"FROM sites s  " +
			"LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id " +
			"LEFT JOIN centrales c ON c.idcentrale = c.idcentrale " +
			"WHERE (s.soft_delete is null or s.soft_delete = 0) " +
			"and s.vigilance = 1 and c.check is not null and s.date_last_signal > '" + moment().subtract(1, "day").format("YYYY-MM-DD HH:mm:ss") + "' " +
			"and (h.id is null or (h." + field + " is not null and h." + field + " = 1)) " +
			"ORDER BY s.group_pointage_id DESC, s.nom"
}

function sqlSelectCommentaire(date_vigilance) {
	return "SELECT c.site_id, c.agent_id, c.pointeuse_id, c.commentaire, c.objet, c.date_vigilance " +
		"FROM v_commentaires c " +
		"where c.date_vigilance = '" + date_vigilance + "' " +
		"order by c.date_vigilance"
}

function sqlSelectVigilance(siteIds, date_vigilance) {
	return "SELECT adm.idademco as 'id', adm.site_id, adm.dtarrived, adm.agent_id " +
		"from ademcotemp adm " +
		"where codeTevent=1000 and dtarrived >= '" + date_vigilance.begin.format("YYYY-MM-DD HH:mm:ss") + "' " +
		"and dtarrived < '" + date_vigilance.end.format("YYYY-MM-DD HH:mm:ss") + "' " +
        "and adm.site_id in (" + siteIds.join(',') + ") " +
		"order by dtarrived asc"
}

function checkTransmission(){
	console.log("-------------")
	console.log("datetime : " + moment().format("YYYY-MM-DD HH:mm:ss"))
    const currentVigilance = getCurrentVigilance()
	
	if(moment().isBefore(currentVigilance.limit)) {
		console.log("check not yet")
		waitBeforeCheck()
	}
	else {
		pool.query(sqlSelectLastTestManque, [], (err, result) => {
			if(err){
				console.error(err)
				waitBeforeCheck()
			}
			else { 
				if(result[0].value == currentVigilance.value.format("YYYY-MM-DD HH:mm:ss")) {
					console.log("check already done")
					waitBeforeCheck()
				}
				else {
					console.log("do check ...")
					pool.query(sqlSelectCoupure, [currentVigilance.value.format("YYYY-MM-DD HH:mm:ss")], (err, coupures) => {
						if(err){
							console.error(err)
							waitBeforeCheck()
						}
						else {
							pool.query(sqlSelectJourFerie(currentVigilance.horaire), [], (err, ferie) => {
								if(err){
									console.error(err)
									waitBeforeCheck()
								}
								else {
									pool.query(sqlSelectSite(currentVigilance.horaire, (ferie && ferie.length > 0)), [], (err, sites) => {
										if(err){
											console.error(err)
											waitBeforeCheck()
										}
										else{
											console.log("Nb site: " + sites.length)
											pool.query(sqlSelectCommentaire(currentVigilance.value.format("YYYY-MM-DD HH:mm:ss")), [], (err, commentaires) => {
												if(err){
													console.error(err)
													waitBeforeCheck()
												}
												else {
													console.log("Nb commentaire: " + commentaires.length)
													pool.query(sqlSelectVigilance(sites.map(s => s.id), currentVigilance), [], async (err, vigilances) => {
														if(err){
															console.error(err)
															waitBeforeCheck()
														}
														else {
															console.log("Nb vigilance: " + vigilances.length)
															sites.map(s => {
																let vi = 0
																while(vi<vigilances.length){
																	const vg = vigilances[vi]
																	let dtarrived = moment(vg.dtarrived)
																	if( vg.site_id == s.id 
																		&& dtarrived.isAfter(currentVigilance.begin) && dtarrived.isBefore(currentVigilance.end)){
																			s.has_vigilance = true
																			break
																	}
																	else vi++
																}
																let ci = 0
																while(ci<commentaires.length){
																	const cm = commentaires[ci]
																	if(!cm.pointeuse_id && cm.site_id == s.id
																		&& moment(cm.date_vigilance).isSame(currentVigilance.value.format("YYYY-MM-DD HH:mm:ss"))
																		&& /check-phone \d{2}h\d{2}/.test(cm.objet.toLowerCase())){
																			s.has_vigilance = true
																			break
																	}
																	else ci++
																}
																let cpi = 0
																while(cpi<coupures.length){
																	const coupure = coupures[cpi]
																	if(coupure.transmitter == s.transmitter){
																		s.has_coupure = true
																		break
																	}
																	else cpi++
																}
															})
															const clearSites = sites.filter(s => (!s.has_vigilance && !s.has_coupure))
															console.log("nb site : " + clearSites.length)
															console.log(clearSites)
															axios.post('http://192.9.97.209:80/API/DeleteSMS', deletesms, {auth: auth})
															.then(({data}) => {
																console.log(data)
																checkCentrale(currentVigilance, clearSites, 0)
															})
															.catch((e) => {
																console.error(e)
																checkCentrale(currentVigilance, clearSites, 0)
															})
														}
													})
												}
											})
										}
									})
								}
							})
						}
					})
				}
	
			}
		})
	}
}

function waitBeforeCheck(){
	setTimeout(() => checkTransmission(), 30000)
}

function getCurrentVigilance(){
	let currentDate = moment()
	let intervals = []
	let horaire = ''
	if(moment().isAfter(moment().set({hour: 5, minute: 50, second:0})) && moment().isBefore(moment().set({hour: 17, minute: 50, second:0}))){
		horaire = currentDate.format("YYYY-MM-DD") + " 07:00:00"
		let vigilanceJour = moment(currentDate.format("YYYY-MM-DD") + " 05:50:00")
		while(vigilanceJour.isBefore(moment(currentDate.format("YYYY-MM-DD") + " 17:50:00"))){
			let begin = vigilanceJour.clone()
			let nom = vigilanceJour.clone().add('10', 'minutes')
			let limit = vigilanceJour.clone().add('40', 'minutes')
			let end = vigilanceJour.clone().add('1', 'hour')
			intervals.push({
				begin: begin,
				value: nom,
                limit: limit,
				end: end
			})
			vigilanceJour.add('1', 'hour')
		}
	}
	else {
		let vigilanceNuit = null
		let limitVigilance = null
		if(moment().isAfter(moment().set({hour: 17, minute: 50, second:0})) && moment().isBefore(moment().set({hour: 23, minute: 59, second: 59}))){
			horaire = currentDate.format("YYYY-MM-DD") + " 18:00:00"
			vigilanceNuit = moment(currentDate.format("YYYY-MM-DD") + " 17:50:00")
			limitVigilance = moment(currentDate.clone().add(1, 'day').format("YYYY-MM-DD") + " 05:50:00")
		}
		else {
			horaire = currentDate.clone().subtract(1, "day").format("YYYY-MM-DD") + " 18:00:00"
			vigilanceNuit = moment(currentDate.clone().subtract(1, 'day').format("YYYY-MM-DD") + " 17:50:00")
			limitVigilance = moment(currentDate.format("YYYY-MM-DD") + " 05:50:00")
		}
		while(vigilanceNuit.isBefore(limitVigilance)){
			let begin = vigilanceNuit.clone()
			let nom = vigilanceNuit.clone().add('10', 'minutes')
			let limit = vigilanceNuit.clone().add('20', 'minutes')
			let end = vigilanceNuit.clone().add('30', 'minutes')
			intervals.push({
				begin: begin,
				value: nom,
                limit: limit,
				end: end,
			})
			vigilanceNuit.add('30', 'minutes')
		}
	}
    let currentVigilance = ''
    intervals.forEach(itv => {
        if(moment().isAfter(itv.begin) && moment().isBefore(itv.end)){
            currentVigilance = itv
        }
    })
	if(currentVigilance) currentVigilance.horaire = horaire
    return currentVigilance;
}

checkTransmission()