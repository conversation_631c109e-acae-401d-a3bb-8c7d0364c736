const mysql = require('mysql2');
const moment = require('moment');

 const db_config = require("./auth").db_config_zo;

const pool = mysql.createPool(db_config);

const sqlCheckSync = `
    SELECT service, last_sync_update
    FROM synchronisations
    WHERE last_sync_update NOT NULL
    AND last_sync_update < ?
`;

const sqlInsertPanic = `
    INSERT INTO ademcomemlog(site_id, messageType, eventQualify, codeevent, zones, istraite, dtarrived)
    VALUES (6414 , 18, 1, 120, ?, 1, NOW())
`;

function getZoneForService(service) {
    const serviceToZoneMap = {
        'absence': 1,
        'action': 2,
        'agent': 3,
        'alarm': 4,
        'comment_pointage': 5,
        'contact': 6,
        'habilite': 7,
        'horaire': 8,
        'indice_appel': 9,
        'numero': 10,
        'pointage': 11,
        'rapport': 12,
        'reclamation': 13,
        'service24': 14,
        'site_to_admin': 15,
        'test periodique': 16,
        'admin_users': 17,
        'zone': 18,
    };

    return serviceToZoneMap[service] || 0;
}

const lastLogged = {};

function checkSynchronisations() {
    const fiveMinutesAgo = moment().subtract(5, 'minutes').format('YYYY-MM-DD HH:mm:ss');

    pool.query(sqlCheckSync, [fiveMinutesAgo], (err, results) => {
        if (err) {
            console.error('Error checking synchronisations:', err);

            setTimeout(checkSynchronisations, 15 * 1000);
            return;
        }

        if (results.length === 0) {
            console.log('All services are synced within the last 5 minutes.');
        } else {
            console.log(`Found ${results.length} services with outdated sync.`);
            results.forEach((row) => {
                const { service, last_sync_update } = row;
                const zone = getZoneForService(service);

                const now = moment();
                if (!lastLogged[service] || now.diff(lastLogged[service], 'seconds') > 60) {
                    pool.query(sqlInsertPanic, [zone], (err) => {
                        if (err) {
                            console.error(`Error inserting panic log for service ${service}:`, err);
                        } else {
                            console.log(`Inserted panic log for service ${service} (zone ${zone}).`);
                            lastLogged[service] = now;
                        }
                    });
                } else {
                    console.log(`Skipping duplicate log for service ${service}.`);
                }
            });
        }

        setTimeout(checkSynchronisations, 3 * 1000);
    });
}

checkSynchronisations();
