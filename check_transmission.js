const axios = require('axios')
const moment = require('moment')
const mysql = require('mysql2')
const { exec } = require("child_process")
const net = require('net');

let currentPort = Math.floor(Math.random() * 16 + 2)
console.log("currentPort: " + currentPort)
const querysend = {
    'event': 'txsms',
    'userid': '0',
    'num': '',
    'port': '',
    'encoding': '0',
    'smsinfo': 'communications test'
}
let deletesms = {
    event: "deletesms",
    smsbox: "s",
    clear: 1
}

const auth = {
    username: 'ApiUserAdmin',
    password: '1234'
}

const db_config = require("./auth").db_config_zo
const pool = mysql.createPool(db_config);

const sqlSelectMissingTransmission = "SELECT numero, last_sms_reception FROM sim_gateways WHERE " +
    "numero in ('0321130738', '0321130739', '0321130741', '0321130742', '0321130743', '0321130744', '0321130745') and " +
    "(last_sms_reception is null or last_sms_reception < ?) "
const sqlSelectMissingTransmissionSmpp = "SELECT numero, last_sms_reception FROM sim_gateways WHERE " +
    "numero in ('0321134413', '0321154685', '41.188.49.14:2511') and " + //, '57.128.20.26:2511'
    "(last_sms_reception is null or last_sms_reception < ?) "

const sqlUpdateCheckTransmission = "UPDATE sim_gateways SET last_sms_check = now() where numero = ? "

const sqlInsertPanicTransmission = "INSERT INTO ademcomemlog(prom, messageType, eventQualify, codeevent, zones, istraite, dtarrived) " +
    "values (?, 18, 1, 120, 0, 1, now())"
const sqlSelectCoupure = "SELECT id from coupures where transmitter = ? and vigilance = ?"
const sqlInsertCoupure = "INSERT INTO coupures(transmitter, vigilance, created_at, updated_at) values(?, ?, now(), now())"

function getCurrentVigilance() {
    let currentDate = moment()
    let intervals = []
    let horaire = ''
    if (moment().isAfter(moment().set({ hour: 5, minute: 50, second: 0 })) && moment().isBefore(moment().set({ hour: 17, minute: 50, second: 0 }))) {
        horaire = currentDate.format("YYYY-MM-DD") + " 07:00:00"
        let vigilanceJour = moment(currentDate.format("YYYY-MM-DD") + " 05:50:00")
        while (vigilanceJour.isBefore(moment(currentDate.format("YYYY-MM-DD") + " 17:50:00"))) {
            let begin = vigilanceJour.clone()
            let nom = vigilanceJour.clone().add('10', 'minutes')
            let limit = vigilanceJour.clone().add('40', 'minutes')
            let end = vigilanceJour.clone().add('1', 'hour')
            intervals.push({
                begin: begin,
                value: nom,
                limit: limit,
                end: end
            })
            vigilanceJour.add('1', 'hour')
        }
    }
    else {
        let vigilanceNuit = null
        let limitVigilance = null
        if (moment().isAfter(moment().set({ hour: 17, minute: 50, second: 0 })) && moment().isBefore(moment().set({ hour: 23, minute: 59, second: 59 }))) {
            horaire = currentDate.format("YYYY-MM-DD") + " 18:00:00"
            vigilanceNuit = moment(currentDate.format("YYYY-MM-DD") + " 17:50:00")
            limitVigilance = moment(currentDate.clone().add(1, 'day').format("YYYY-MM-DD") + " 05:50:00")
        }
        else {
            horaire = currentDate.clone().subtract(1, "day").format("YYYY-MM-DD") + " 18:00:00"
            vigilanceNuit = moment(currentDate.clone().subtract(1, 'day').format("YYYY-MM-DD") + " 17:50:00")
            limitVigilance = moment(currentDate.format("YYYY-MM-DD") + " 05:50:00")
        }
        while (vigilanceNuit.isBefore(limitVigilance)) {
            let begin = vigilanceNuit.clone()
            let nom = vigilanceNuit.clone().add('10', 'minutes')
            let limit = vigilanceNuit.clone().add('20', 'minutes')
            let end = vigilanceNuit.clone().add('30', 'minutes')
            intervals.push({
                begin: begin,
                value: nom,
                limit: limit,
                end: end,
            })
            vigilanceNuit.add('30', 'minutes')
        }
    }
    let currentVigilance = ''
    intervals.forEach(itv => {
        if (moment().isAfter(itv.begin) && moment().isBefore(itv.end)) {
            currentVigilance = itv
        }
    })
    if (currentVigilance) currentVigilance.horaire = horaire
    return currentVigilance;
}

function setTimeoutInsertPanic(nums, index) {
    setTimeout(() => {
        insertPanic(nums, index)
    }, 3000)
}

function insertPanic(nums, index) {
    if (index < nums.length) {
        console.log("insert panic " + nums[index])
        let transmitter = nums[index]
        let prom = nums[index]
        if (nums[index] == '57.128.20.26:2511') {
            prom = "0001"
            transmitter = '57.128.20.26'
        }
        else if (nums[index] == '41.188.49.14:2511') {
            prom = "0002"
            transmitter = '41.188.49.14'
        }
        console.log(prom)
        pool.query(sqlInsertPanicTransmission, [prom], (err, res) => {
            if (err) {
                console.error(err)
                waitBeforeCheck(1000)
            }
            else {
                pool.query(sqlSelectCoupure, [transmitter, getCurrentVigilance().value.format("YYYY-MM-DD HH:mm:ss")], (err, coupures) => {
                    if (err) {
                        console.error(err)
                        waitBeforeCheck(1000)
                    }
                    else if (coupures.length == 0) {
                        pool.query(sqlInsertCoupure, [transmitter, getCurrentVigilance().value.format("YYYY-MM-DD HH:mm:ss")], (err) => {
                            if (err) {
                                console.error(err)
                                waitBeforeCheck(1000)
                            }
                            else
                                setTimeoutInsertPanic(nums, index + 1)
                        })
                    }
                    else
                        setTimeoutInsertPanic(nums, index + 1)
                })
            }
        })
    }
    else {
        waitBeforeCheck(18000)
    }
}

function checkPanic() {
    console.log("check panic")
    const minParam = process.argv[2] ? Number.parseInt(process.argv[2]) : 25
    pool.query(sqlSelectMissingTransmission, [moment().subtract(minParam + 2, "minutes").format("YYYY-MM-DD HH:mm:ss")], (err, result) => {
        if (err) {
            console.error(err)
            waitBeforeCheck()
        }
        else {
            let getways = []
            if (result.length > 0) getways = getways.concat(result)
            const minParamSmpp = Number.parseInt(minParam / 5)
            pool.query(sqlSelectMissingTransmissionSmpp, [moment().subtract(minParamSmpp + 2, "minutes").format("YYYY-MM-DD HH:mm:ss")], (err, result) => {
                if (err) {
                    console.error(err)
                    waitBeforeCheck()
                }
                else {
                    if (result.length > 0) getways = getways.concat(result)
                    if (getways.length > 0)
                        insertPanic(getways.map(g => g.numero), 0)
                    else {
                        console.log("transmission ok...")
                        waitBeforeCheck()
                    }
                }
            })
        }
    })
}

function setTimeoutSendTest(nums, panics, index) {
    setTimeout(() => {
        sendTest(nums, panics, index)
    }, 3000)
}

function sendTest(nums, index) {
    if (index < nums.length) {
        console.log("send test " + nums[index])
        if (/\d+\.\d+\.\d+\.\d+:\d+/.test(nums[index]))
            sendGPRS(nums, index)
        else
            sendSMS(nums, index)
    }
    else {
        setTimeout(() => {
            checkPanic()
        }, 15000);
    }
}

function sendSMS(nums, index) {
    if (currentPort == 16)
        currentPort = 1
    else
        currentPort++

    querysend.num = nums[index]
    querysend.port = currentPort
    axios.post('http://192.9.97.209:80/API/TaskHandle', querysend, { auth: auth })
        .then(({ data }) => {
            console.log(data)
            pool.query(sqlUpdateCheckTransmission, [nums[index]], (err, res) => {
                if (err) {
                    console.error(err)
                    waitBeforeCheck(1000)
                }
                else {
                    setTimeoutSendTest(nums, index + 1)
                }
            })
        })
        .catch((e) => {
            console.error(e)
            setTimeoutSendTest(nums, index + 1)
        })
}

function sendGPRS(nums, index) {
    const groups = /(\d+\.\d+\.\d+\.\d+):(\d+)/.exec(nums[index])
    const ip = groups[1]
    const port = groups[2]
    console.log("sendGprs")
    console.log(ip, port)
    var client = new net.Socket();
    client.setTimeout(10000)
    client.connect(port, ip, () => {
        console.log('Start connection data...')
        client.write('communications test')
        setTimeout(() => {
            console.log('Connection closed!')
            client.destroy()
        })
        pool.query(sqlUpdateCheckTransmission, [nums[index]], (err, res) => {
            if (err) {
                console.error(err)
                waitBeforeCheck(1000)
            }
            else {
                setTimeoutSendTest(nums, index + 1)
            }
        }, 200)
    })
    client.on("timeout", () => {
        console.log("timeout")
        client.destroy()
        setTimeoutSendTest(nums, index + 1)
    })
    client.on("error", (err) => {
        console.log(err.code)
        client.destroy()
        setTimeoutSendTest(nums, index + 1)
    })
}

function checkTransmission() {
    console.log("-------------")
    console.log("check transmission")
    console.log(moment().format("YYYY-MM-DD HH:mm:ss"))
    const minParam = process.argv[2] ? Number.parseInt(process.argv[2]) : 25
    console.log("minParam: " + moment().subtract(minParam, "minutes").format("YYYY-MM-DD HH:mm:ss"))
    pool.query(sqlSelectMissingTransmission, [moment().subtract(minParam, "minutes").format("YYYY-MM-DD HH:mm:ss")], (err, result) => {
        if (err) {
            console.error(err)
            waitBeforeCheck()
        }
        else {
            let getways = []
            if (result.length > 0) getways = getways.concat(result)
            const minParamSmpp = Number.parseInt(minParam / 5)
            pool.query(sqlSelectMissingTransmissionSmpp, [moment().subtract(minParamSmpp, "minutes").format("YYYY-MM-DD HH:mm:ss")], (err, result) => {
                if (err) {
                    console.error(err)
                    waitBeforeCheck()
                }
                else {
                    if (result.length > 0) getways = getways.concat(result)
                    console.log(getways)
                    if (getways.length > 0)
                        sendTest(getways.map(g => g.numero), 0)
                    else {
                        console.log("transmission ok...")
                        waitBeforeCheck()
                    }
                }
            })
        }
    })
}

function waitBeforeCheck(timeout) {
    setTimeout(() => checkTransmission(), timeout ? timeout : 40000)
}

checkTransmission()
