const moment = require('moment')

function getCurrentVigilance(){
	let currentDate = moment().set({h: 0, m: 0, s: 13})
	let intervals = []
	let horaire = ''
	if(currentDate.isAfter(moment().set({hour: 5, minute: 50, second:0})) && currentDate.isBefore(moment().set({hour: 17, minute: 50, second:0}))){
		horaire = currentDate.format("YYYY-MM-DD") + " 07:00:00"
		let vigilanceJour = moment(currentDate.format("YYYY-MM-DD") + " 05:50:00")
		while(vigilanceJour.isBefore(moment(currentDate.format("YYYY-MM-DD") + " 17:50:00"))){
			let begin = vigilanceJour.clone()
			let nom = vigilanceJour.clone().add('10', 'minutes')
			let limit = vigilanceJour.clone().add('40', 'minutes')
			let end = vigilanceJour.clone().add('1', 'hour')
			intervals.push({
				begin: begin,
				value: nom,
                limit: limit,
				end: end
			})
			vigilanceJour.add('1', 'hour')
		}
	}
	else {
		let vigilanceNuit = null
		let limitVigilance = null
		if(currentDate.isAfter(moment().set({hour: 17, minute: 50, second:0})) && currentDate.isBefore(moment().set({hour: 23, minute: 59, second: 59}))){
            
            console.log("before minuit")
			horaire = currentDate.format("YYYY-MM-DD") + " 18:00:00"
			vigilanceNuit = moment(currentDate.format("YYYY-MM-DD") + " 17:50:00")
			limitVigilance = moment(currentDate.clone().add(1, 'day').format("YYYY-MM-DD") + " 05:50:00")
		}
		else {
            console.log("after minuit")
			horaire = currentDate.clone().subtract(1, "day").format("YYYY-MM-DD") + " 18:00:00"
			vigilanceNuit = moment(currentDate.clone().subtract(1, 'day').format("YYYY-MM-DD") + " 17:50:00")
			limitVigilance = moment(currentDate.format("YYYY-MM-DD") + " 05:50:00")
		}
		while(vigilanceNuit.isBefore(limitVigilance)){
			let begin = vigilanceNuit.clone()
			let nom = vigilanceNuit.clone().add('10', 'minutes')
			let limit = vigilanceNuit.clone().add('20', 'minutes')
			let end = vigilanceNuit.clone().add('30', 'minutes')
			intervals.push({
				begin: begin,
				value: nom,

                limit: limit,
				end: end,
			})
            console.log(nom.format("YYYY-MM-DD HH:mm:ss"))
			vigilanceNuit.add('30', 'minutes')
		}
	}
    let currentVigilance = ''
    intervals.forEach(itv => {
        if(currentDate.isAfter(itv.begin) && currentDate.isBefore(itv.end)){
            currentVigilance = itv
        }
    })
	if(currentVigilance) {
        currentVigilance.horaire = horaire
        console.log("----\n" + currentVigilance.value.format("YYYY-MM-DD HH:mm:ss"))
    }
    return currentVigilance;
}

getCurrentVigilance()