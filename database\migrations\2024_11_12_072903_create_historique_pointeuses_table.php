<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateHistoriquePointeusesTable extends Migration
{
    public function up()
    {
        Schema::create('historique_pointeuses', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('pointeuse_id');
            $table->unsignedBigInteger('user_id');
            $table->string('objet');
            $table->text('detail')->nullable();
            $table->timestamps();

            // Foreign key constraints
            // $table->foreign('pointeuse_id')->references('id')->on('pointeuses')->onDelete('cascade');
            // $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('historique_pointeuses');
    }
}