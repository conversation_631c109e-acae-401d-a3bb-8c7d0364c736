<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateHistoriquePointagesTable extends Migration
{
    public function up()
    {
        Schema::create('historique_pointages', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('pointage_id');
            $table->unsignedBigInteger('user_id');
            $table->string('objet');
            $table->text('detail')->nullable();
            $table->timestamps();

            // Foreign key constraints
            // $table->foreign('pointage_id')->references('id')->on('pointages')->onDelete('cascade');
            // $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('historique_pointages');
    }
}
