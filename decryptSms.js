
const moment = require('moment')
const fs = require('fs')
const recoveryPath = 'recovery/centrale/'

const decryptSMS = (message, transmitter, prom, datetime, port, pathname) => {
    let errno = true

    let zone = '000'
    let code = null
    let partition = '00'
    let eventQualify = 1
    let messageSplit = []
    let messageSplitR = message.split('\r')
    let pointeuseId = null
    for(let i = 0; i<messageSplitR.length; i++){
        messageSplitR[i].split('\n').forEach((mss) => {
            if(mss)
                messageSplit.push(mss)
        })
    }
    let rowData = []

    if(message && /^>#\d{6} N[A-Z]{2}\d{4}.+/.test(message)){
        let found = false
        let messageMatch = (/^>#\d{6} N([A-Z]{2})(\d{4}).+/gs).exec(message)
        const eventText = messageMatch[1]
        zone = messageMatch[2].slice(-3)
        if(eventText == "BA"){
            code = 130
            found = true
        }
        else if(eventText == "TA"){
            code = 137
            found = true
        }
        else if(["BT", "YC"].includes(eventText)){
            // Trouble de communication
            code = 350
            found = true
        }
        if(found){
            rowData.push({
                dtarrived: moment(datetime, "YYYYMMDDHHmmss").format("YYYY/MM/DD HH:mm:ss"),
                codeevent: code,
                eventQualify: eventQualify,
                zones: zone,
                partition: partition,
                prom: prom,
                transmitter: transmitter,
                port: port,
            })
            errno = false
        }
    }
    else if(message && />#\d{6} N[A-Z]{2}.+/.test(message)){
        let found = false
        let messageMatch = (/^>#\d{6} N([A-Z]{2}).+/gs).exec(message)
        const eventText = messageMatch[1]
        if(eventText == "RP"){
            code = 602
            found = true
        }
        else if(eventText == "BC"){
            code = 406
            found = true
        }
        else if(eventText == "CL"){
            code = 401
            eventQualify = 3
            found = true
        }
        else if(["OP", "OG"].includes(eventText)){
            code = 401
            found = true
        }
        else if(eventText == "RR"){
            //Warm start panel
            code = 305
            found = true
        }
        else if(eventText == "OR"){
            //Disarm alarm
            code = 465
            found = true
        }
        else if(eventText == "CR"){
            //Recent close
            code = 459
            found = true
        }
        else if(eventText == "CG"){
            code = 441
            found = true
        }
        if(found){
            rowData.push({
                dtarrived: moment(datetime, "YYYYMMDDHHmmss").format("YYYY/MM/DD HH:mm:ss"),
                codeevent: code,
                eventQualify: eventQualify,
                zones: zone,
                partition: partition,
                prom: prom,
                transmitter: transmitter,
                port: port,
                pointeuse_id: pointeuseId,
            })
            errno = false
        }
    }
    else if(/^SOS panique/.test(messageSplit[0]) && /^Pointeuse : \d+/.test(messageSplit[1])
        && /^Zone : \d+/.test(messageSplit[2]))
    {
        code = '120'
        zone = /^Zone : (\d+)/.exec(messageSplit[2])[1]
        rowData.push({
            dtarrived: moment(datetime, "YYYYMMDDHHmmss").format("YYYY/MM/DD HH:mm:ss"),
            codeevent: code,
            eventQualify: eventQualify,
            zones: zone,
            partition: partition,
            prom: prom,
            transmitter: transmitter,
            port: port,
            pointeuse_id: pointeuseId,
        })
    }
    else if(/^Zone : \d+/.test(messageSplit[1]) && /^Area : \d+/.test(messageSplit[2])
        && /^Event: .+/.test(messageSplit[3]) && /^Time : .+/.test(messageSplit[4]))
    {
        zone = /^Zone : (\d+)/.exec(messageSplit[1])[1]
        partition = /^Area : (\d+)/.exec(messageSplit[2])[1]
        const codeText = /^Event: (.+)/.exec(messageSplit[3])[1].toLowerCase()
        if(['armed', 'arm'].includes(codeText)){
            eventQualify = 3
            code = '402'
        }
        else if(codeText == 'tamper')
            code = '137'
        else if(codeText == 'system ac loss')
            code = '301'
        else if(codeText == 'system ac recovery'){
            code = '301'
            eventQualify = 3
        }
        else if(['disarmed', 'disarm'].includes(codeText))
            code = '402'
        else if(codeText == 'periodic test report')
            code = '602'
        else if(codeText == 'alarm cancel')
            code = '406'
        else if(codeText == 'perimeter')
            code = '131'
        else if(['panic', 'panic alarm'].includes(codeText))
            code = '120'
        else if(['urgence', 'emergency'].includes(codeText))
            code = '101'

        if(code && moment(datetime, "YYYYMMDDHHmmss").isValid()){
            rowData.push({
                dtarrived: moment(datetime, "YYYYMMDDHHmmss").format("YYYY/MM/DD HH:mm:ss"),
                codeevent: code,
                eventQualify: eventQualify,
                zones: zone,
                partition: partition,
                prom: prom,
                transmitter: transmitter,
                port: port,
                pointeuse_id: pointeuseId,
            })
            errno = false
        }
    }
    else if(/Event: .+/.test(message)){
        fs.appendFile(pathname, '\n------------------------\nIN_FOR_EACH_LOOP\n', (err) => {
            if(err) console.error(err);
        })
        for(let i=0; i<messageSplit.length; i++){
            let codeText = ""
            code = ''
            zone = 0
            let panelUser = null;
            let panelArea = null;
            if(/^Event: .+/.test(messageSplit[i]))
                codeText = /^Event: (.+)/.exec(messageSplit[i])[1].toLowerCase()
            if(i > 0 && /^Zone: \d+/.test(messageSplit[i-1]))
                zone = /^Zone: (\d+)/.exec(messageSplit[i-1])[1].toLowerCase()
            if(i > 0 && /^User: .+/.test(messageSplit[i-1])){
                fs.appendFile(pathname, '\nHAS_USER_VALUE', (err) => {
                    if(err) console.error(err);
                })
                panelUser = /^User: (.+)/.exec(messageSplit[i-1])[1].toLowerCase()
            }
            if(i > 1 && /^Area: \d+/.test(messageSplit[i-2])){
                fs.appendFile(pathname, '\nHAS_AREA_VALUE', (err) => {
                    if(err) console.error(err);
                })
                panelArea = /^Area: (\d+)/.exec(messageSplit[i-2])[1].toLowerCase()
            }
            
            if(['armed', 'arm', 'home armed'].includes(codeText)){
                eventQualify = 3
                code = '402'
            }
            else if(codeText == 'tamper')
                code = '137'
            else if(codeText == 'fire')
                code = '110'
            else if(codeText == '24 hour')
                code = '133'
            else if(codeText == 'system ac loss')
                code = '301'
            else if(codeText == 'system ac recovery'){
                code = '301'
                eventQualify = 3
            }
            else if(['disarmed', 'disarm'].includes(codeText))
                code = '402'
            else if(codeText == 'periodic test report')
                code = '602'
            else if(codeText == 'alarm cancel')
                code = '406'
            else if(codeText == 'entry/exit')
                code = '134'
            else if(['gas detected', 'detection gaz'].includes(codeText))
                code = '151'
            else if(codeText == 'fire')
                code = '110'
            else if(codeText == 'smoke')
                code = '111'
            else if(codeText == 'combustion')
                code = '112'
            else if(codeText == 'water flow')
                code = '113'
            else if(codeText == 'heat')
                code = '114'
            else if(codeText == 'pull station')
                code = '115'
            else if(codeText == 'duct')
                code = '116'
            else if(codeText == 'flame')
                code = '117'
            else if(codeText == 'near alarm')
                code = '118'
            else if(['panic', 'panic alarm'].includes(codeText))
                code = '120'
            else if(codeText == 'urgence')
                code = '120'
            else if(codeText == 'duress')
                code = '121'
            else if(codeText == 'audible')
                code = '123'
            else if(codeText == 'duress – access granted')
                code = '124'
            else if(codeText == 'duress – egress granted')
                code = '125'
            else if(codeText == 'burglary')
                code = '130'
            else if(codeText == 'perimeter')
                code = '131'
            else if(codeText == 'interior')
                code = '132'
            else if(codeText == 'outdoor')
                code = '136'
            else if(codeText == 'tamper')
                code = '137'
            else if(codeText == 'near alarm')
                code = '138'
            else if(codeText == 'intrusion verifier')
                code = '139'
            else if(codeText == 'ac loss')
                code = '301'
            else if(codeText == 'low system battery')
                code = '302'
            else if(codeText == 'panel programming changed')
                code = '306'
            else if(codeText == 'communication trouble')
                code = '350'
            else if(['loss of supervision - rf', 'sensor loss'].includes(codeText))
                code = '381'
            else if(['rf low battery', 'rf device low batt.'].includes(codeText))
                code = '384'
            else if(codeText == 'personal emergency')
                code = '101'
            else if(codeText == 'battery low voltage alert.')
                code = '302'
            else if(['urgence', 'emergency'].includes(codeText))
                code = '101'

            if(code && moment(datetime, "YYYYMMDDHHmmss").isValid()){
                rowData.push({
                    dtarrived: moment(datetime, "YYYYMMDDHHmmss").format("YYYY/MM/DD HH:mm:ss"),
                    codeevent: code,
                    eventQualify: eventQualify,
                    zones: zone,
                    partition: partition,
                    prom: prom,
                    transmitter: transmitter,
                    port: port,
                    pointeuse_id: pointeuseId,
                    panel_user: panelUser,
                    panel_area: panelArea
                })
                errno = false
            }
        }
    }
    else if(/Alarme Système: .+/.test(message) && messageSplit.length == 3){
        console.log(messageSplit[0])
        const codeText = /^Alarme Système: (.+)/.exec(messageSplit[0])[1].toLowerCase()
        zone = (/^#(\d{2}) Zone/.exec(messageSplit[1]))[1]
        if(codeText == "mouvement")
            code = '131'
        else if(codeText == "porte/fenêtre")
            code = '130'

        if(code && moment(datetime, "YYYYMMDDHHmmss").isValid()){
            rowData.push({
                dtarrived: moment(datetime, "YYYYMMDDHHmmss").format("YYYY/MM/DD HH:mm:ss"),
                codeevent: code,
                eventQualify: eventQualify,
                zones: zone,
                partition: partition,
                prom: prom,
                transmitter: transmitter,
                port: port,
                pointeuse_id: pointeuseId,
            })
            errno = false
        }
    }
    // AGS
    else if(/AC Power Fault/.test(message)){
        rowData.push({
            dtarrived: moment(datetime, "YYYYMMDDHHmmss").format("YYYY/MM/DD HH:mm:ss"),
            codeevent: 301,
            eventQualify: eventQualify,
            zones: zone,
            partition: partition,
            prom: prom,
            transmitter: transmitter,
            port: port,
            pointeuse_id: pointeuseId,
        })
        errno = false
    }
    else if(/Battery Fault/.test(message)){
        rowData.push({
            dtarrived: moment(datetime, "YYYYMMDDHHmmss").format("YYYY/MM/DD HH:mm:ss"),
            codeevent: 302,
            eventQualify: eventQualify,
            zones: zone,
            partition: partition,
            prom: prom,
            transmitter: transmitter,
            port: port,
            pointeuse_id: pointeuseId,
        })
        errno = false
    }
    else if(/Addr\d+ .+/.test(message)){
        const groups = /Addr(\d+) (.+)/.exec(message)
        const zone = groups[1]
        console.log(groups)
        if(/Fire Alarm/.test(groups[2]))
            code = 110
        
        if(code && moment(datetime, "YYYYMMDDHHmmss").isValid()){
            rowData.push({
                dtarrived: moment(datetime, "YYYYMMDDHHmmss").format("YYYY/MM/DD HH:mm:ss"),
                codeevent: code,
                eventQualify: eventQualify,
                zones: zone,
                partition: partition,
                prom: prom,
                transmitter: transmitter,
                port: port,
                pointeuse_id: pointeuseId,
            })
            errno = false
        }
    }
    else if(/^<\d{7}>/.test(messageSplit[0])){
        if('Système Désarmé' == messageSplit[1])
            code = '402'
        else if('Système Armé' == messageSplit[1]){
            code = '402'
            eventQualify = 3
        }
        else if('Système Armé Partiellement' == messageSplit[1])
            code = '456'
        else if("Perte d'alimentation électrique" == messageSplit[1])
            code = '301'
        else if("Retour de l'alimentation électrique" == messageSplit[1]){
            code = '301'
            eventQualify = 3
        }
        else if('Batterie faible' == messageSplit[1])
            code = '302'
        else if(['Alarme Panique', 'SOS'].includes(messageSplit[1]))
            code = '120'
        else if('Alarme Incendie' == messageSplit[1])
            code = '110'
        else if('Alarme Gaz' == messageSplit[1])
            code = '151'
        else if(['Alarme Système', 'Alarme Porte', 'Alarme Salon', 'Alarme Chambre', 'Alarme Balcon', 'Alarme Fenêtre'].includes(messageSplit[1]))
            code = '130'
        else if('Alarme Périmètre' == messageSplit[1])
            code = '131'
        
        if(messageSplit[2])
            zone = /^Zone:(\d+)/.exec(messageSplit[2])[1]

        if(code && moment(datetime, "YYYYMMDDHHmmss").isValid()){
            rowData.push({
                dtarrived: moment(datetime, "YYYYMMDDHHmmss").format("YYYY/MM/DD HH:mm:ss"),
                codeevent: code,
                eventQualify: eventQualify,
                zones: zone,
                partition: partition,
                prom: prom,
                transmitter: transmitter,
                port: port,
                pointeuse_id: pointeuseId,
            })
            errno = false
        }
    }
    else if(messageSplit.length > 1 && /Message central ORION:/.test(messageSplit[0])){
        for(var i=1; i<messageSplit.length; i++){
            const msg = messageSplit[i]
            if(/Alarme urgence/.test(msg)){
                code = 120
            }
            else if(/Vol/.test(msg)){
                code = 131
            }
            else if(/Perimetre/.test(msg)){
                code = 131
            }
            
            if(code)  {
                rowData.push({
                    dtarrived: moment(datetime, "YYYYMMDDHHmmss").format("YYYY/MM/DD HH:mm:ss"),
                    codeevent: code,
                    eventQualify: eventQualify,
                    zones: zone,
                    partition: partition,
                    prom: prom,
                    transmitter: transmitter,
                    port: port,
                    pointeuse_id: pointeuseId,
                })
                errno = false
            }
        }
    } 
    else if(messageSplit.length > 1 && /Security system to remind you:/.test(messageSplit[0])){
        for(var i=1; i<messageSplit.length; i++){
            const msg = messageSplit[i]
            if(/Panic Alarm/.test(msg))
                code = 120
            else if(/System AC Loss/.test(msg))
                code = 301
            else if(/\d+ +Tamper/.test(msg)){
                zone = (/(\d)+ +Tamper/).exec(msg)[1]
                code = 137
            }
            else if(/\d+ +Perimeter/.test(msg)){
                zone = (/(\d)+ +Perimeter/).exec(msg)[1]
                code = 131
            }
            else if(/Fire/.test(msg))
                code = 110
            else if(/Perimeter/.test(msg))
                code = 131
            else if(/Armed/.test(msg)){
                eventQualify = 3
                code = '401'
            }
            else if(/Disarm/.test(msg))
                code = '401'
            else if(/Entry\/Exit/.test(msg))
                code = '134'
            else if(/Burglary/.test(msg))
                code = '130'
            else if(/Bell Trouble/.test(msg))
                code = '927'
            else if(/Communications Test/.test(msg))
                code = '602'
            else if(/RF Device Low Batt./.test(msg))
                code = '384'
            else if(/Home Armed/.test(msg)) {
                eventQualify = 3
                code = '402'
            }
            
            if(code)  {
                rowData.push({
                    dtarrived: moment(datetime, "YYYYMMDDHHmmss").format("YYYY/MM/DD HH:mm:ss"),
                    codeevent: code,
                    eventQualify: eventQualify,
                    zones: zone,
                    partition: partition,
                    prom: prom,
                    transmitter: transmitter,
                    port: port,
                    pointeuse_id: pointeuseId,
                })
                errno = false
            }
        }
    }
    else if(messageSplit.length > 1 && /Securite systeme a vous rappeler:/.test(messageSplit[0])){
        for(var i=1; i<messageSplit.length; i++){
            const msg = messageSplit[i]
            if(/\d+ +Panique alarme/.test(msg)){
                zone = (/(\d)+ +Panique alarme/).exec(msg)[1]
                code = 120
            }
            else if(/\d+ +Feu/.test(msg)){
                zone = (/(\d)+ +Feu/).exec(msg)[1]
                code = 110
            }
            else if(/\d+ +Perimetre/.test(msg)){
                zone = (/(\d)+ +Perimetre/).exec(msg)[1]
                code = 131
            }
            else if(/Panique alarme/.test(msg)){
                code = 120
            }
            else if(/Feu/.test(msg)){
                code = 110
            }
            else if(/Perimetre/.test(msg)){
                code = 131
            }
            else if(/Arme/.test(msg)){
                eventQualify = 3
                code = '401'
            }
            else if(/Desarme/.test(msg)){
                code = '401'
            }
            if(code)  {
                rowData.push({
                    dtarrived: moment(datetime, "YYYYMMDDHHmmss").format("YYYY/MM/DD HH:mm:ss"),
                    codeevent: code,
                    eventQualify: eventQualify,
                    zones: zone,
                    partition: partition,
                    prom: prom,
                    transmitter: transmitter,
                    port: port,
                    pointeuse_id: pointeuseId,
                })
                errno = false
            }
        }
    }
    //AX PRO
    else if(messageSplit.length > 5 && messageSplit[2] == "AX PRO"){
        if(messageSplit[1] == "Alarme de panique sonore")
            code = '120'
        else if(messageSplit[1] == "Désarmé")
            code = '402'
        else if(messageSplit[1] == "Armé"){
            code = '402'
            eventQualify = 3
        }
        else if(messageSplit[1] == "Armé en mode à domicile")
            code = '456'
        else if(messageSplit[1] == "Alarme intrusion")
            code = '139'
        else if(messageSplit[1] == "Échec de l’armement")
            code = '454'
        
        fs.appendFile(pathname, '\nmessageSplit[3]: ' + messageSplit[3] + '\n', (err) => {
            if(err) console.error(err);
        })
        if(/Partition \d{1} \(.+\) - Zone \d{1}/.test(messageSplit[3])){
            const group = /Partition (\d{1}) \(.+\) - Zone (\d{1})/.exec(messageSplit[3])
            partition = group[1]
            zone = group[2]
            fs.appendFile(pathname, '\npartition: ' + partition + ', zone: ' + zone +'\n', (err) => {
                if(err) console.error(err);
            })
        }
        else if(/Partition \d{1} \(.+\)/.test(messageSplit[3])){
            partition = /Partition (\d{1}) \(.+\)/.exec(messageSplit[3])[1]
            fs.appendFile(pathname, '\npartition: ' + partition + '\n', (err) => {
                if(err) console.error(err);
            })
        }

        if(code && moment(datetime, "YYYYMMDDHHmmss").isValid()){
            rowData.push({
                dtarrived: moment(datetime, "YYYYMMDDHHmmss").format("YYYY/MM/DD HH:mm:ss"),
                codeevent: code,
                eventQualify: eventQualify,
                zones: zone,
                partition: partition,
                prom: prom,
                transmitter: transmitter,
                port: port,
                pointeuse_id: pointeuseId,
            })
            errno = false
        }
    }
    else if(message.match(/Nom\(s\):.+/)) {
        const messageSpliBar = message.split('|')
        const alarm = messageSpliBar[0]
        const region = messageSpliBar[1]
        if(alarm && alarm.toLowerCase().match(/nom\(s\):.+/)){
            const codeText = /nom\(s\):(.+)/.exec(alarm.toLowerCase())[1]
            if(codeText == "capteur de porte")
                code = 130
            else if(codeText.match(/capteur d eau .+/))
                code = 140
            else if(codeText == "bouton panic")
                code = 120
            else if(codeText == "panic button")
                code = 120
            else if(codeText == "controle vigilance")
                code = 1000
            else if(codeText == "wired1")
                code = 1000
            else if(codeText == "batterie de panneau basse")
                code = 302
            else if(codeText == "ac power off")
                code = 301
            else if(codeText == "ac power on"){
                eventQualify = 3
                code = 301
            }
        }
        if(region && region.toLowerCase().match(/région:\d+/))
            zone = /région:(\d+)/.exec(region.toLowerCase())[1]

        if(code && moment(datetime, "YYYYMMDDHHmmss").isValid()){
            rowData.push({
                dtarrived: moment(datetime, "YYYYMMDDHHmmss").format("YYYY/MM/DD HH:mm:ss"),
                codeevent: code,
                eventQualify: eventQualify,
                zones: zone,
                partition: partition,
                prom: prom,
                transmitter: transmitter,
                port: port,
                pointeuse_id: pointeuseId,
            })
            errno = false
        }
    }
    else if(message.match(/Test cyclique \d+/s)){
        let messageMatch = (/Test cyclique (\d+)/g).exec(message)
        pointeuseId = messageMatch[1]
        code = 602
        rowData.push({
            dtarrived: moment(datetime, "YYYYMMDDHHmmss").format("YYYY/MM/DD HH:mm:ss"),
            codeevent: code,
            eventQualify: eventQualify,
            zones: zone,
            partition: partition,
            prom: prom,
            transmitter: transmitter,
            port: port,
            pointeuse_id: pointeuseId,
        })
        errno = false
    }
    else {
        messageSplit.forEach(msg => {
            zone = '000'
            code = null
            partition = '00'
            eventQualify = 1
            console.log("Message >> " + msg)
            if(msg)
                msg = msg.toLowerCase().trim()

            if(msg.match(/sos/s))
                code = 120
            else if(msg.match(/alarme auto-protection/s))
                code = 137
            else if(msg.match(/communications test/s))
                code = 602
            else if(msg.match(/220 volt en défaut/))
                code = 301
            else if(msg.match(/alimentation secteur/)){
                code = 301
                eventQualify = 3
            }
            else if(msg.match(/dispatch panique/s))
                code = 120
            else if(msg.match(/hote alarme repos/) || msg.match(/hote alarme au repos/)){
                code = 401
            }
            else if(msg.match(/hote alarme active/)){
                code = 401
                eventQualify = 3
            }
            else if(msg.match(/^zone +\d+ +alarme$/)){
                zone = (/zone +(\d+) +alarme/).exec(msg)[1]
                code = 131
            }
            else if(msg.match(/^\w+ +alarme$/)){
                const codeText = (/(\w+) +alarme/).exec(msg)[1]
                if(codeText == "door")
                    code = 130
                else if(["remote", "hote"].includes(codeText))
                    code = 120
                else if(codeText == "pir")
                    code = 131
            }
            else if(msg.match(/host disarm successfull/)){
                code = 401
            }
            else if(msg.match(/host arm successfull/)){
                code = 401
                eventQualify = 3
            }
            else if(msg.match(/host power down/)){
                code = 301
            }
            else if(msg.match(/power on the host/)){
                code = 301
                eventQualify = 3
            }
            // PSTN GSM 816
            else if(msg == 'arming'){
                eventQualify = 3
                code = 402
            }
            else if(msg == 'disarming'){
                code = 402
            }
            else if(msg == 'theft alarm by remote control'){
                code = 120
            }
            else if(msg == 'ac power failed'){
                code = 301
            }
            else if(msg == 'low battery'){
                code = 302
            }
            else if(msg.match(/zone \d+ alarm/)){
                let messageMatch = /zone (\d+) alarm/g.exec(msg)
                zone = messageMatch[1]
                code = 130
            }
            // end* PSTN

            else if(msg.match(/^\w+ +alarm$/)){
                const codeText = (/(\w+) +alarm/).exec(msg)[1]
                if(codeText == "door")
                    code = 130
                else if(["remote", "hote", "host"].includes(codeText.toLowerCase()))
                    code = 120
                else if(codeText == "pir")
                    code = 131
            }
            else if(msg.match(/dispatch incendie/s)){
                code = 110
            }
            else if(msg.match(/absence secteur centrale/s)){
                code = 301
            }
            else if(msg.match(/retablissment secteur centrale/s)){
                code = 301
                eventQualify = 3
            }
            else if(msg.match(/debut alarme intrusion/)){
                code = 401
                eventQualify = 3
            }
            else if(msg.match(/alarme intrusion zone \d+/)){
                let messageMatch = (/alarme intrusion zone (\d+)/g).exec(msg)
                code = '130'
                zone = messageMatch[1]
            }
            else if(msg.match(/disarmee on part. \d+/s)){
                let messageMatch = (/disarmee on part. (\d+)/g).exec(msg)
                zone = messageMatch[1]
                code = 401
            }
            else if(msg.match(/disarmee ar part. \d+/s)){
                let messageMatch = (/disarmee ar part. (\d+)/g).exec(msg)
                zone = messageMatch[1]
                code = 401
            }
            else if(msg.match(/armee on part. \d+/s)){
                let messageMatch = (/armee on part. (\d+)/g).exec(msg)
                code = 401
                zone = messageMatch[1]
                eventQualify = 3

            }
            else if(msg.match(/armee ar part. \d+/s)){
                let messageMatch = (/armee ar part. (\d+)/g).exec(msg)
                code = 401
                zone = messageMatch[1]
                eventQualify = 3
            }
            else if(msg.match(/alarm system report:.+/s)){
                let messageMatch = (/alarm system report:(.*)/g).exec(msg)
                msg = messageMatch[1]
            }

            else if(msg.match(/controle vigilance zone ?\d*/)){
                let messageMatch = (/controle vigilance zone ?(\d*)/g).exec(msg)
                zone = messageMatch[1]
                code = '1000'
            }
            else if(msg.match(/bouton controle vigilance zone ?\d*/)){
                let messageMatch = (/bouton controle vigilance zone ?(\d*)/g).exec(msg)
                zone = messageMatch[1]
                code = '1000'
            }
            else if(msg.match(/controle vigilance/)){
                code = '1000'
            }
            else if(msg.match(/bouton panique zone ?\d*/)){
                let messageMatch = (/bouton panique zone ?(\d*)/g).exec(msg)
                zone = messageMatch[1]
                code = '120'
            }
            // ST VGT
            else if(msg.match(/wireless zone \d+ alarm!/)){
                let messageMatch = /wireless zone (\d+) alarm!/g.exec(msg)
                zone = messageMatch[1]
                code = '120'
            }

            else if(msg.match(/^area \d+ +zone \d* .*/s)){
                let messageMatch = (/^area (\d+) +zone (\d*) (.*)/gs).exec(msg)
                partition = messageMatch[1]
                zone = messageMatch[2]
                let codeText = messageMatch[3]
                if(codeText == 'entry/exit')
                    code = '134'
                else if(['gas detected', 'detection gaz'].includes(codeText))
                    code = '151'
                else if(codeText == 'fire')
                    code = '110'
                else if(codeText == 'smoke')
                    code = '111'
                else if(codeText == 'combustion')
                    code = '112'
                else if(codeText == 'water flow')
                    code = '113'
                else if(codeText == 'heat')
                    code = '114'
                else if(codeText == 'pull station')
                    code = '115'
                else if(codeText == 'duct')
                    code = '116'
                else if(codeText == 'flame')
                    code = '117'
                else if(codeText == 'near alarm')
                    code = '118'
                else if(['panic', 'panic alarm'].includes(codeText))
                    code = '120'
                else if(codeText == 'urgence')
                    code = '120'
                else if(codeText == 'duress')
                    code = '121'
                else if(codeText == 'audible')
                    code = '123'
                else if(codeText == 'duress – access granted')
                    code = '124'
                else if(codeText == 'duress – egress granted')
                    code = '125'
                else if(codeText == 'burglary')
                    code = '130'
                else if(codeText == 'perimeter')
                    code = '131'
                else if(codeText == 'interior')
                    code = '132'
                else if(codeText == 'outdoor')
                    code = '136'
                else if(codeText == 'tamper')
                    code = '137'
                else if(codeText == 'near alarm')
                    code = '138'
                else if(codeText == 'intrusion verifier')
                    code = '139'
                else if(codeText == 'ac loss')
                    code = '301'
                else if(codeText == 'low system battery')
                    code = '302'
                else if(codeText == 'panel programming changed')
                    code = '306'
                else if(codeText == 'communication trouble')
                    code = '350'
                else if(['loss of supervision - rf', 'sensor loss'].includes(codeText))
                    code = '381'
                else if(['rf low battery', 'rf device low batt.'].includes(codeText))
                    code = '384'
                else if(codeText == 'personal emergency')
                    code = '101'
                else if(codeText == 'battery low voltage alert.')
                    code = '302' 
                else if(codeText == 'incendie')
                    code = '110'
            }
            else if(msg.match(/^zone \d* .*/s)){
                let messageMatch = (/^zone (\d*) (.*)/gs).exec(msg)
                zone = messageMatch[1]
                let codeText = messageMatch[2]
                
                if(codeText == 'entry/exit')
                    code = '134'
                else if(['gas detected', 'detection gaz'].includes(codeText))
                    code = '151'
                else if(codeText == 'fire')
                    code = '110'
                else if(codeText == 'smoke')
                    code = '111'
                else if(codeText == 'combustion')
                    code = '112'
                else if(codeText == 'water flow')
                    code = '113'
                else if(codeText == 'heat')
                    code = '114'
                else if(codeText == 'pull station')
                    code = '115'
                else if(codeText == 'duct')
                    code = '116'
                else if(codeText == 'flame')
                    code = '117'
                else if(codeText == 'near alarm')
                    code = '118'
                else if(['panic', 'panic alarm'].includes(codeText))
                    code = '120'
                else if(['urgence', "panic/urgence"].includes(codeText))
                    code = '120'
                else if(codeText == 'duress')
                    code = '121'
                else if(codeText == 'audible')
                    code = '123'
                else if(codeText == 'duress – access granted')
                    code = '124'
                else if(codeText == 'duress – egress granted')
                    code = '125'
                else if(['burglary', 'intrusion'].includes(codeText))
                    code = '130'
                else if(['perimeter', 'perimetrique'].includes(codeText))
                    code = '131'
                else if(['interior', 'interieure'].includes(codeText))
                    code = '132'
                else if(codeText == 'outdoor')
                    code = '136'
                else if(codeText == 'tamper')
                    code = '137'
                else if(codeText == 'near alarm')
                    code = '138'
                else if(codeText == 'intrusion verifier')
                    code = '139'
                else if(codeText == 'ac loss')
                    code = '301'
                else if(codeText == 'low system battery')
                    code = '302'
                else if(codeText == 'panel programming changed')
                    code = '306'
                else if(codeText == 'communication trouble')
                    code = '350'
                else if(['loss of supervision - rf', 'sensor loss'].includes(codeText))
                    code = '381'
                else if(['rf low battery', 'rf device low batt.'].includes(codeText))
                    code = '384'
                else if(codeText == 'personal emergency')
                    code = '101'
                else if(codeText == 'battery low voltage alert.')
                    code = '302' 
                else if(codeText == 'incendie')
                    code = '110'
            }
            else if(msg == 'bell trouble')
                code = '927'
            else if(['system arm', 'systeme arme', 'arme'].includes(msg)){
                eventQualify = 3
                code = '402'
            }
            else if(['system disarm', 'systeme desarme', 'desarme'].includes(msg))
                code = '402'
            else if(msg == 'periodic test report')
                code = '602'
            else if(msg == 'medical')
                code = '100'
            else if(msg == 'personal emergency')
                code = '101'
            else if(msg == 'fail to report in')
                code = '102'
            else if(msg == '24 hour (safe)')
                code = '133'
            else if(msg == 'entry'){
                code = '134'
                eventQualify = 3
            }
            else if(msg == 'exit')
                code = '134'
            else if(msg == 'day'){
                code = '135'
                eventQualify = 3
            }
            else if(msg == 'night')
                code = '135'
            else if(['system ac loss','ac loss'].includes(msg))
                code = '301'
            else if(['ac recovery', 'system ac recovery', 'retablissement batterie centrale'].includes(msg)){
                code = '301'
                eventQualify = 3
            }
            else if(['system low battery', 'system low batt.', 'system battery loss', 'batterie basse centrale'].includes(msg))
                code = '302'
            else if(msg == 'panel programming changed')
                code = '306'
            else if(msg == 'communication trouble')
                code = '350'
            else if(msg == 'loss of supervision - rf')
                code = '381'
            else if(msg == 'rf low battery')
                code = '384'
            else if(msg == 'open')
                code = '400'
            else if(msg == 'close'){
                code = '400'
                eventQualify = 3
            }
            else if(msg == 'open by user'){
                code = '401'
                eventQualify = 3
            }
            else if(msg == 'close by user')
                code = '401'
            else if(msg == 'group open')
                code = '402'
            else if(msg == 'group close'){
                code = '402'
                eventQualify = 3
            }
            else if(msg == 'automatic open')
                code = '403'
            else if(msg == 'automatic close'){
                code = '403'
                eventQualify = 3
            }
            else if(msg == 'late to open')
                code = '404'
            else if(msg == 'late to close'){
                code = '404'
                eventQualify = 3
            }
            else if(msg == 'deferred open')
                code = '405'
            else if(msg == 'deferred close'){
                code = '405'
                eventQualify = 3
            }
            else if(msg == 'cancel')
                code = '406'
            else if(msg == 'remote arm'){
                code = '407'
                eventQualify = 3
            }
            else if(msg == 'remote disarm')
                code = '407'
            else if(msg == 'quick arm')
                code = '408'
            else if(msg == 'keyswitch open')
                code = '409'
            else if(msg == 'keyswitch close'){
                code = '409'
                eventQualify = 3
            }
            else if(msg == 'armed stay')
                code = '441'
            else if(msg == 'keyswitch armed stay')
                code = '442'
            else if(msg == 'failed to open')
                code = '453'
            else if(msg == 'failed to close')
                code = '454'
            else if(msg == 'partial arm')
                code = '456'
            // K3
            else if((['entrance alarm!', 'key sos help!', 'panic help alarm!', ]).includes(msg))
                code = '120'
            else if((['smoke detector alarm!']).includes(msg))
                code = '111'
            else if((['water leakage alarm!']).includes(msg))
                code = '113'
            else if((['gas leakage alarm!']).includes(msg))
                code = '151'
            else if(msg.match(/^imei:\d{15}$/)){
                code = '602'
                const dataCentrale = prom + ", 7, " + datetime
                fs.writeFile(recoveryPath + prom + "_" + datetime, dataCentrale, (err) => {
                    console.error(err)
                })
            }
            else if(msg.match(/^ \d+  \w+/)){
                const messageMatch = (/^ (\d+)  (\w+)/g).exec(msg)
                zone = messageMatch[1]
                const codeText = messageMatch[2]
                if(codeText == 'perimetre')
                    code = '131'
            }
            else if(msg.match(/^area \d+ +user \d+ .+/)){
                const messageMatch = (/area (\d+) +user \d+ (.+)/g).exec(msg)
                partition = messageMatch[1]
                const codeText = messageMatch[2]
                if(['armed', 'arm'].includes(codeText)){
                    eventQualify = 3
                    code = '401'
                }
                else if(['disarmed', 'disarm'].includes(codeText))
                    code = '401'
                else if(codeText == 'arm failure')
                    code = '454'
                else if(codeText == 'alarm cancel')
                    code = '406'
                else if(['panic', 'panic alarm'].includes(codeText))
                    code = '120'
                else if(codeText == 'urgence')
                    code = '120'
            }
            else if(msg.match(/^partition \d+ +utilisateur \d+ .+/)){
                const messageMatch = (/partition (\d+) +utilisateur \d+ (.+)/g).exec(msg)
                partition = messageMatch[1]
                const codeText = messageMatch[2]
                if(codeText == 'systeme desarme')
                    code = '401'
                else if(codeText == 'systeme arme'){
                    eventQualify = 3
                    code = '401'
                }
                else if(codeText == 'systeme interieur')
                    code = '132'
                else if(codeText == 'urgence')
                    code = '120'
                else if(msg.match(/batterie systeme faible/)){
                    code = '302'
                }
            }
            else if(msg.match(/^partition \d+ +zone +\d+ .+/)){
                const messageMatch = (/partition (\d+) +zone +(\d+) (.+)/g).exec(msg)
                partition = messageMatch[1]
                zone = messageMatch[2]
                const codeText = messageMatch[3].trim()
                if(codeText == 'perimetre')
                    code = '131'
                else if(codeText == 'incendie')
                    code = '110'
            }
            else if(msg.match(/^user \d+ +.+/)){
                const messageMatch = (/user \d+ +(.+)/g).exec(msg)
                const codeText = messageMatch[1]
                if(codeText == 'armed'){
                    eventQualify = 3
                    code = '401'
                }
                else if(['disarmed', 'disarm'].includes(codeText))
                    code = '401'
                else if(['panic', 'panic alarm'].includes(codeText))
                    code = '120'
                else if(codeText == 'urgence')
                    code = '120'
            }
            // FR
            else if(msg.match(/echec armement/))
                code = '454'
            else if(msg.match(/batterie systeme faible/)){
                code = '302'
            }
            else if(msg.match(/ message test$/))
                code = '602'
            else if(['rapport de test periodique', 'zone bypass'].includes(msg))
                code = '602'
            else if(['systeme stay', 'system stay'].includes(msg))
                code ='456'
            

            // Coupure
            else if(msg.match(/^ac power goes off .*/)){
                code = '301'
            }
            else if(msg.match(/^perte alimentation/)){
                code = '301'
            }
            else if(msg.match(/^alimentation retablie/)){
                code = '301'
                eventQualify = 3
            }
            // AGILITY
            else if(msg.match(/d.{1}sarm.{1} partition \d/)){
                code = '401'
            }
            else if(msg.match(/arm.{1} partition \d/)){
                code = '401'
                eventQualify = 3
            }
            else if(msg.match(/ d.{1}sarm.{1}/)){
                code = '401'
            }
            else if(msg.match(/ arm.{1}/)){
                code = '401'
                eventQualify = 3
            }
            else if(msg.match(/faut batterie basse/)){
                code = '302'
            }
            else if(msg.match(/^ac power goes on$/) || msg.match(/tablissement batterie basse/)){
                code = '301'
                eventQualify = 3
            }
            else if(msg.match(/faut alimentation secteur/)){
                code = '301'
                eventQualify = 3
            }
            else if(msg.match(/cteur de mvt$/)){
                // desarmé détecteur de mvt
                code = '602'
            }
            else if(msg.match(/alarme panique partition \d/)){
                const messageMatch = /alarme panique partition (\d)/g.exec(msg)
                partition = messageMatch[1]
                code = '120'
            }
            else if(msg.match(/alarme panique/)){
                code = '120'
            }
            else if(msg.match(/alarme niveau de gaz partition \d/)){
                const messageMatch = /alarme niveau de gaz partition (\d)/g.exec(msg)
                partition = messageMatch[1]
                code = '151'
            }
            else if(msg.match(/alarme niveau de gaz/)){
                code = '151'
            }
            else if(msg.match(/alarme intrusion partition \d/)){
                const messageMatch = /alarme intrusion partition (\d)/g.exec(msg)
                partition = messageMatch[1]
                code = '130'
            }
            else if(msg.match(/marche totale/)){
                code = '401'
                eventQualify = 3
            }
            else if(msg.match(/fin alarme/)){
                code = '401'
            }
            else if(msg.match(/alarme intrusion/)){
                code = '130'
            }

            if(code && moment(datetime, "YYYYMMDDHHmmss").isValid()){
                rowData.push({
                    dtarrived: moment(datetime, "YYYYMMDDHHmmss").format("YYYY/MM/DD HH:mm:ss"),
                    codeevent: code,
                    eventQualify: eventQualify,
                    zones: zone,
                    partition: partition,
                    prom: prom,
                    transmitter: transmitter,
                    port: port,
                    pointeuse_id: pointeuseId,
                })
                errno = false
            }
        })
    }
    if(errno)
        fs.appendFile(pathname, '\n------------------------\nERRNO_INTERPRET\n', (err) => {
            if(err) console.error(err);
        })
    return rowData
}

exports.decryptSMS = decryptSMS;
/*
const rows = decryptSMS("Rapport alarme:"+
    "\nUtilisateur 98 Arme total"+
    "\nZone 03 Intrusion"+
    "\nUtilisateur 01 Systeme desarme"
    , "0321130739"
    , "0321131618"
    , "2024-09-10 07:08:09"
    , null
    ,'logs/sms/208/' + moment().format('YYYYMMDDHHmmss') + '.log')
console.log(rows)
*/