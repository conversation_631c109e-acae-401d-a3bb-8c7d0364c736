services:
  tls:
    image: registry.gitlab.com/aronomeniaina/tls:latest
    container_name: tls_src
    restart: always
    environment:
      - TZ=Indian/Antananarivo
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_HOST=db
      - DB_DATABASE=tls
      - DB_USERNAME=tls
      - DB_PASSWORD=${MYSQL_PASSWORD}
      - CLIENTJS=node /opt/app/tls/biometrique/client.js
      - DB_CONNECTION_SECOND=mysql
      - DB_HOST_SECOND=**************
      - DB_PORT_SECOND=3306
      - DB_DATABASE_SECOND=cdr
      - DB_USERNAME_SECOND=api
      - DB_PASSWORD_SECOND=AdmDir2024
    depends_on:
      - db
    ports:
      - "0.0.0.0:8000:8000"
      
  anomalie_bouton:
    build:
      context: .
      dockerfile: services/anomalie_bouton/Dockerfile
    container_name: anomalie_bouton_service
  
  biometrique:
    image: registry.gitlab.com/aronomeniaina/tls/biometrique:latest
    container_name: biometrique_service
    environment:
      - TZ=Indian/Antananarivo
    restart: always
  
  bouton_tana:
    image: registry.gitlab.com/aronomeniaina/tls/bouton_tana:latest
    container_name: bouton_tana_service
    environment:
      - TZ=Indian/Antananarivo
    restart: always

  bouton_province:
    image: registry.gitlab.com/aronomeniaina/tls/bouton_province:latest
    container_name: bouton_province_service
    environment:
      - TZ=Indian/Antananarivo
    restart: always

  diag_biometrique:
    image: registry.gitlab.com/aronomeniaina/tls/diag_biometrique:latest
    container_name: diag_biometrique_service
    environment:
      - TZ=Indian/Antananarivo
    restart: always

  diag_centrale:
    image: registry.gitlab.com/aronomeniaina/tls/diag_centrale:latest
    container_name: diag_centrale_service
    environment:
      - TZ=Indian/Antananarivo
    restart: always

  appelle:
    image: registry.gitlab.com/aronomeniaina/tls/appelle:latest
    container_name: appelle_service
    environment:
      - TZ=Indian/Antananarivo
    restart: always

  hour:
    build:
      context: .
      dockerfile: services/hour/Dockerfile
    container_name: hour_service

  intervention_abusif:
    build:
      context: .
      dockerfile: services/intervention_abusif/Dockerfile
    container_name: intervention_abusif_service
  
  intervention:
    build:
      context: .
      dockerfile: services/intervention/Dockerfile
    container_name: intervention_service

  manque_check:
    build:
      context: .
      dockerfile: services/manque_check/Dockerfile
    container_name: manque_check_service

  pointage_eclosia:
    image: registry.gitlab.com/aronomeniaina/tls/pointage_eclosia:latest
    container_name: pointage_eclosia_service
    environment:
      - TZ=Indian/Antananarivo
    restart: always

  pointage_tmv:
    image: registry.gitlab.com/aronomeniaina/tls/pointage_tmv:latest
    container_name: pointage_tmv_service
    environment:
      - TZ=Indian/Antananarivo
    restart: always

  pointage:
    image: registry.gitlab.com/aronomeniaina/tls/pointage:latest
    container_name: pointage_service
    environment:
      - TZ=Indian/Antananarivo
    restart: always


  rapport_mailing:
    image: registry.gitlab.com/aronomeniaina/tls/rapport_mailing:latest
    container_name: rapport_mailing_service
    environment:
      - TZ=Indian/Antananarivo
    restart: always
    volumes:
      - ./export/views:/opt/app/tls/export/views

  rapport:
    image: registry.gitlab.com/aronomeniaina/tls/rapport:latest
    container_name: rapport_service
    environment:
      - TZ=Indian/Antananarivo
    restart: always

  service_abusif:
    build:
      context: .
      dockerfile: services/service_abusif/Dockerfile
    container_name: service_abusif_service

  transmitter:
    build:
      context: .
      dockerfile: services/transmitter/Dockerfile
    container_name: transmitter_service

  do_aquittement:
    build:
      context: .
      dockerfile: services/do_aquittement/Dockerfile
    container_name: do_aquittement_service

  manque_sanction:
    image: registry.gitlab.com/aronomeniaina/tls/manque_sanction:latest
    container_name: manque_sanction
    environment:
      - TZ=Indian/Antananarivo
    restart: always

  sync_a_rappeler:
    image: registry.gitlab.com/aronomeniaina/tls/sync_a_rappeler:latest
    container_name: sync_a_rappeler_service
    environment:
      - TZ=Indian/Antananarivo
    restart: always

  delete_vigilance:
    image: registry.gitlab.com/aronomeniaina/tls/delete_vigilance:latest
    container_name: delete_vigilance_service
    restart: always

  server_biometrique:
    image: registry.gitlab.com/aronomeniaina/tls/server_biometrique:latest
    container_name: server_biometrique_service
    environment:
      - TZ=Indian/Antananarivo
    restart: always
    volumes:
      - recovery-volume:/opt/app/tls/recovery/biometrique
      - recovery-late-volume:/opt/app/tls/recovery/biometrique_late
      - recovery-test-volume:/opt/app/tls/recovery/biometrique_test
      - recovery-gprs:/opt/app/tls/recovery/gprs
    ports:
      - "2702:2702"

  rcv_biometrique:
    image: registry.gitlab.com/aronomeniaina/tls/rcv_biometrique:latest
    container_name: rcv_biometrique_service
    environment:
      - TZ=Indian/Antananarivo
    restart: always
    volumes:
      - recovery-volume:/opt/app/tls/recovery/biometrique
      - recovery-late-volume:/opt/app/tls/recovery/biometrique_late
      - recovery-test-volume:/opt/app/tls/recovery/biometrique_test
      - recovery-gprs:/opt/app/tls/recovery/gprs

  ********************:
    image: registry.gitlab.com/aronomeniaina/tls/********************:latest
    container_name: ********************_service
    environment:
      - TZ=Indian/Antananarivo
    restart: always
    volumes:
      - recovery-volume:/opt/app/tls/recovery/biometrique
      - recovery-late-volume:/opt/app/tls/recovery/biometrique_late
      - recovery-test-volume:/opt/app/tls/recovery/biometrique_test
      - recovery-gprs:/opt/app/tls/recovery/gprs
  
  rcv_test_biometrique:
    image: registry.gitlab.com/aronomeniaina/tls/rcv_test_biometrique:latest
    container_name: rcv_test_biometrique_service
    environment:
      - TZ=Indian/Antananarivo
    restart: always
    volumes:
      - recovery-volume:/opt/app/tls/recovery/biometrique
      - recovery-late-volume:/opt/app/tls/recovery/biometrique_late
      - recovery-test-volume:/opt/app/tls/recovery/biometrique_test
      - recovery-gprs:/opt/app/tls/recovery/gprs

  rcv_gprs:
    image: registry.gitlab.com/aronomeniaina/tls/rcv_gprs:latest
    container_name: rcv_gprs_service
    environment:
      - TZ=Indian/Antananarivo
    restart: always
    volumes:
      - recovery-volume:/opt/app/tls/recovery/biometrique
      - recovery-late-volume:/opt/app/tls/recovery/biometrique_late
      - recovery-test-volume:/opt/app/tls/recovery/biometrique_test
      - recovery-gprs:/opt/app/tls/recovery/gprs
  
  gprs_server:
    image: registry.gitlab.com/aronomeniaina/tls/gprs:latest
    container_name: gprs-server
    restart: always
    ports:
      - "2511:2511"
      - "2512:2512"
    volumes:
      - logs2511:/app/logs/gprs/2511
      - logs2512:/app/logs/gprs/2512
      - recovery-gprs:/app/recovery/gprs
      - ./services/gprs/config.properties:/app/config.properties
    environment:
      - TZ=Indian/Antananarivo
  
  portainer:
    image: portainer/portainer-ce
    container_name: portainer
    restart: always
    ports:
      - "9069:9000"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer_data:/data
  
  nginx_portainer:
    image: nginx:latest
    container_name: nginx_proxy_portainer
    ports:
      - "9443:9443"
    volumes:
      - ./nginx_portainer.conf:/etc/nginx/conf.d/default.conf:ro
      - ./ptls.dirickx.mg:/etc/letsencrypt:ro
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro 
    depends_on:
      - portainer

  db:
    image: mysql:8.0.0
    container_name: mysql_db
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: tls
      MYSQL_USER: tls
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    ports:
      - "3307:3306"
    volumes:
      - ./mysql_data:/var/lib/mysql
      - ./mysql_conf/my.cnf:/etc/mysql/conf.d/my.cnf
      - ./mysql_certs:/etc/mysql/certs
      - /etc/localtime:/etc/localtime:ro

volumes:
  recovery-volume:
  recovery-late-volume:
  recovery-test-volume:
  recovery-gprs:
  logs2511:
  logs2512:
  portainer_data:

