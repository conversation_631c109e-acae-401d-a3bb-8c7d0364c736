const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")

moment.locale('fr')

const {db_config_zo, db_config_admin, sendMail} = require("../auth")
const poolOvh = mysql.createPool(db_config_zo)
const poolAdmin = mysql.createPool(db_config_admin)
const isTask = process.argv[2] == 'task'

function getDayOrNightExport(){
	let beginDay = moment().set({hour:5, minute:50, second:0})
	let endDay = moment().set({hour:17, minute:50, second:0})
	if(moment().isAfter(beginDay) && moment().isBefore(endDay))
		return moment().subtract(1, 'day').format("YYYY-MM-DD") + " 18:00:00"
	else {
		if(moment().isBefore(beginDay))
			return moment().subtract(1, 'day').format("YYYY-MM-DD") + " 07:00:00"
		return moment().format("YYYY-MM-DD") + " 07:00:00"
	}
}

const sqlSelectLastExportAnomalieVigilance = "SELECT p.key, value FROM params p WHERE p.key = 'last_anomalie_bouton'"

function sqlUpdateLastExport(dateString){
	return "UPDATE params p SET  p.value = '"+ dateString + "' " +
		"WHERE p.key = 'last_anomalie_bouton'"
}

const destination_test = ["<EMAIL>", "<EMAIL>"]
const destination_task = ["<EMAIL>", "<EMAIL>"]

function generateMissingCommentExcelFile(workbook, header, sites){
	const cols = ['B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 
	'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 
	'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ',]
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
	const fontHeader = { size: 16, bold: true }
	const fontBold = { bold: true }
	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	const fillHeader = {
		type: 'pattern',
		pattern: 'solid',
		fgColor: {argb:'ffbbbbbb'}
	}

	const worksheet = workbook.addWorksheet("Control Vigilance")
	worksheet.getColumn('A').width = 15
	cols.forEach(col => {
		worksheet.getColumn(col).width = 10
	})

	console.log(header)
	
	let	line = 1
	worksheet.mergeCells('A'+ line +':' + cols[cols.length - 1] + '' + line)
	worksheet.getCell('A1').value =  header + " (" + sites.length + " sites)"
	worksheet.getCell('A1').font = fontHeader

	line++
	sites.forEach(s => {
		worksheet.mergeCells('A'+ line + ':' + cols[cols.length - 1] + line)
		worksheet.getCell('A' + line).value = s.nom
		worksheet.getCell('A' + line).font = fontBold
		
		line++
		worksheet.getCell('A' + line).value = "Opérateur responsable: " + (s.operateur ? s.operateur.toUpperCase() : "")
		
		line++
		worksheet.getCell("A" + line).value = "Vigilance"
		worksheet.getCell("A" + line).border = borderStyle
		worksheet.getCell("A" + line).fill = fillHeader
		
		s.without_comments.forEach((itv, itvIndex) => {
			worksheet.getCell(cols[itvIndex] + line).value = itv.nom
			worksheet.getCell(cols[itvIndex] + line).alignment = alignmentStyle
			worksheet.getCell(cols[itvIndex] + line).border = borderStyle
		})
		line = line+2
	})
}

function generateMissingVigilanceExcelFile(workbook, header, sites){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
	const fontHeader = { size: 16, bold: true }
	const fontBold = { bold: true }
	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	const fillHeader = {
		type: 'pattern',
		pattern: 'solid',
		fgColor: {argb:'ffbbbbbb'}
	}

	const worksheet = workbook.addWorksheet("Control Vigilance")
	worksheet.getColumn('A').width = 10
	worksheet.getColumn('B').width = 40
	worksheet.getColumn('C').width = 80

	console.log(header)
	
	let	line = 1
	worksheet.mergeCells('A'+ line +':' + 'C' + line)
	worksheet.getCell('A1').value =  header + " (" + sites.length + " sites)"
	worksheet.getCell('A1').font = fontHeader

	line++
	sites.forEach(s => {
		worksheet.mergeCells('A'+ line + 'C:' + line)
		worksheet.getCell('A' + line).value = s.nom
		worksheet.getCell('A' + line).font = fontBold
		
		s.agents.forEach((ag) => {
			line++
			worksheet.getCell('A' + line).value = (
				ag.societe_id == 1 ? 'DGM-' + ag.numero_employe :
				ag.societe_id == 2 ? 'SOIT-' + ag.num_emp_soit :
				ag.societe_id == 3 ? 'ST-' + ag.numero_stagiaire :
				ag.societe_id == 4 ? 'SM' :
				ag.numero_employe ? ag.numero_employe :
				ag.numero_stagiaire ? ag.numero_stagiaire :
				'Ndf'
			) + ' ' + ag.nom
		})
		
		line++
		worksheet.getCell("A" + line).value = "Vigilance"
		worksheet.getCell('A' + line).alignment = alignmentStyle
		worksheet.getCell("A" + line).border = borderStyle
		worksheet.getCell("A" + line).fill = fillHeader
		worksheet.getCell("B" + line).value = "Mesure prise"
		worksheet.getCell("B" + line).border = borderStyle
		worksheet.getCell("B" + line).fill = fillHeader
		worksheet.getCell("C" + line).value = "Commentaire"
		worksheet.getCell("C" + line).border = borderStyle
		worksheet.getCell("C" + line).fill = fillHeader
		
		s.without_vigilances.forEach((itv) => {
			line++
			worksheet.getCell('A' + line).value = itv.nom
			worksheet.getCell('A' + line).alignment = alignmentStyle
			worksheet.getCell('A' + line).border = borderStyle
			worksheet.getCell('B' + line).value = itv.commentaire
			worksheet.getCell('B' + line).border = borderStyle
			worksheet.getCell('C' + line).value = itv.detail
			worksheet.getCell('C' + line).border = borderStyle
		})
		line = line+2
	})
}

function generateWithVigilanceExcelFile(workbook, header, sites){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
	const fontHeader = { size: 16, bold: true }
	const fontBold = { bold: true }
	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	const fillHeader = {
		type: 'pattern',
		pattern: 'solid',
		fgColor: {argb:'ffbbbbbb'}
	}

	const worksheet = workbook.addWorksheet("Control Vigilance")
	worksheet.getColumn('A').width = 10
	worksheet.getColumn('B').width = 10
	worksheet.getColumn('C').width = 40
	worksheet.getColumn('D').width = 80

	console.log(header)
	
	let	line = 1
	worksheet.mergeCells('A'+ line +':' + 'C' + line)
	worksheet.getCell('A1').value =  header + " (" + sites.length + " sites)"
	worksheet.getCell('A1').font = fontHeader

	line++
	sites.forEach(s => {
		worksheet.mergeCells('A'+ line + 'D:' + line)
		worksheet.getCell('A' + line).value = s.nom
		worksheet.getCell('A' + line).font = fontBold
		
		s.agents.forEach((ag) => {
			line++
			worksheet.getCell('A' + line).value = (
				ag.societe_id == 1 ? 'DGM-' + ag.numero_employe :
				ag.societe_id == 2 ? 'SOIT-' + ag.num_emp_soit :
				ag.societe_id == 3 ? 'ST-' + ag.numero_stagiaire :
				ag.societe_id == 4 ? 'SM' :
				ag.numero_employe ? ag.numero_employe :
				ag.numero_stagiaire ? ag.numero_stagiaire :
				'Ndf'
			) + ' ' + ag.nom
		})
		
		line++
		worksheet.getCell("A" + line).value = "Vigilance"
		worksheet.getCell('A' + line).alignment = alignmentStyle
		worksheet.getCell("A" + line).border = borderStyle
		worksheet.getCell("A" + line).fill = fillHeader
		worksheet.getCell("B" + line).value = "Heure"
		worksheet.getCell('B' + line).alignment = alignmentStyle
		worksheet.getCell("B" + line).border = borderStyle
		worksheet.getCell("B" + line).fill = fillHeader
		worksheet.getCell("C" + line).value = "Mesure prise"
		worksheet.getCell("C" + line).border = borderStyle
		worksheet.getCell("C" + line).fill = fillHeader
		worksheet.getCell("D" + line).value = "Commentaire"
		worksheet.getCell("D" + line).border = borderStyle
		worksheet.getCell("D" + line).fill = fillHeader
		
		s.with_vigilances.forEach((itv) => {
			line++
			worksheet.getCell('A' + line).value = itv.nom
			worksheet.getCell('A' + line).alignment = alignmentStyle
			worksheet.getCell('A' + line).border = borderStyle
			worksheet.getCell('B' + line).value = itv.value
			worksheet.getCell('B' + line).alignment = alignmentStyle
			worksheet.getCell('B' + line).border = borderStyle
			worksheet.getCell('C' + line).value = itv.commentaire
			worksheet.getCell('C' + line).border = borderStyle
			worksheet.getCell('D' + line).value = itv.detail
			worksheet.getCell('D' + line).border = borderStyle
		})
		line = line+2
	})
}


function sqlSelectJourFerie(date_vigilance){
	return "SELECT id from jour_feries where date = '" + moment(date_vigilance).format("YYYY-MM-DD") + "'"
}

function sqlSelectSite(date_vigilance, ferie) {
	const horaire = (moment(date_vigilance).format('HH:mm:ss') == '07:00:00') ? 'day' : 'night'
	const field = horaire + '_' + moment(date_vigilance).day()
	console.log("isFerie: " + ferie)
	if(ferie)
		return "SELECT s.idsite as 'id', s.nom, s.phone_agent, u.email as 'operateur' " +
			"FROM sites s  " +
			"LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id " +
			"LEFT JOIN group_sites g ON g.id = s.group_id " +
			"LEFT JOIN ( " +
			"	SELECT r.vigilance_group_id, r.user_id FROM repartition_taches r " + 
			" 	WHERE r.horaire = '" + date_vigilance + "' " +
			"	GROUP BY r.vigilance_group_id " +
			") y ON y.vigilance_group_id = s.group_id " +
			"LEFT JOIN users u ON u.id = y.user_id " +
			"WHERE (s.soft_delete is null or s.soft_delete = 0) " +
			"and (s.vigilance is not null and s.vigilance = 1) " +
			"and (h.id is null or (h." + field + " is not null and h." + field  + " = 1) or (h." + horaire + "_ferie is not null and h." + horaire + "_ferie = 1)) " +
			"ORDER BY s.group_pointage_id DESC"
	else
		return "SELECT s.idsite as 'id', s.nom, s.phone_agent, u.email as 'operateur' " +
			"FROM sites s  " +
			"LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id " +
			"LEFT JOIN group_sites g ON g.id = s.group_id " +
			"LEFT JOIN ( " +
			"	SELECT r.vigilance_group_id, r.user_id FROM repartition_taches r " + 
			" 	WHERE r.horaire = '" + date_vigilance + "' " +
			"	GROUP BY r.vigilance_group_id " +
			") y ON y.vigilance_group_id = s.group_id " +
			"LEFT JOIN users u ON u.id = y.user_id " +
			"WHERE (s.soft_delete is null or s.soft_delete = 0) " +
			"and (s.vigilance is not null and s.vigilance = 1) " +
			"and (h.id is null or (h." + field + " is not null and h." + field + " = 1)) " +
			"ORDER BY s.group_pointage_id DESC"
}
function sqlSelectPointage(siteIds, date_vigilance) {
	return "SELECT ptg.id, ptg.site_id, a.societe_id, a.nom, a.numero_employe, a.num_emp_soit, a.numero_stagiaire, " +
		"ptg.id as 'pointage_id', a.id as agent_id, ptg.dtarrived, ptg.pointeuse_id, s.nom as 'site', a.empreinte, ptg.motif, ptg.soft_delete " + 
		"FROM pointages ptg " +
		"LEFT JOIN agents a ON a.id = ptg.agent_id " +
		"LEFT JOIN sites s ON s.idsite = ptg.site_id " +
		"WHERE (ptg.soft_delete is null or ptg.soft_delete = 0) " +
		"and ptg.pointeuse_id is null and (ptg.vigilance is not null and ptg.vigilance = 1) " +
		"and ptg.site_id in (" + siteIds.join(',') + ") " +
		"and ptg.date_pointage = '" + date_vigilance + "' "
}
function sqlSelectCommentaire(siteIds, date_vigilance) {
	let begin_date = ''
	let end_date = ''
	if(moment(date_vigilance).format('HH:mm:ss') == '07:00:00'){
		begin_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 06:50:00'
		end_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 17:50:00'
	}
	else {
		begin_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 17:50:00'
		end_date = moment(date_vigilance).add(1, 'day').format('YYYY-MM-DD') + ' 06:50:00'
	}
	return "SELECT c.site_id, c.agent_id, c.commentaire, c.objet, c.date_vigilance " +
		"FROM v_commentaires c " +
		"WHERE c.site_id is not null " +
		"and c.date_vigilance >= '" + begin_date + "' " +
		"and c.date_vigilance < '" + end_date + "' " +
        "and c.site_id in (" + siteIds.join(',') + ") " +
		"order by c.date_vigilance"
}
function sqlSelectVigilance(siteIds, date_vigilance) {
	let begin_date = ''
	let end_date = ''
	if(moment(date_vigilance).format('HH:mm:ss') == '07:00:00'){
		begin_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 06:50:00'
		end_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 17:50:00'
	}
	else {
		begin_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 17:50:00'
		end_date = moment(date_vigilance).add(1, 'day').format('YYYY-MM-DD') + ' 06:50:00'
	}
	return "SELECT adm.idademco as 'id', adm.site_id, adm.dtarrived " +
		"from ademcotemp adm " +
		"where agent_id is null and codeTevent=1000 and dtarrived >= '" + begin_date + "' " +
		"and dtarrived < '" + end_date + "' " +
		"and adm.site_id in (" + siteIds.join(',') + ") " +
		"order by dtarrived asc"
}

function getVigilanceInterval(date_vigilance){
	let currentVigilance = moment(date_vigilance)
	let intervals = []
	if(currentVigilance.format('HH:mm:ss') == '07:00:00'){
		let vigilanceJour = moment(currentVigilance.format("YYYY-MM-DD") + " 05:50:00")
		while(vigilanceJour.isBefore(moment(currentVigilance.format("YYYY-MM-DD") + " 17:50:00"))){
			let begin = vigilanceJour.clone()
			let nom = vigilanceJour.clone().add('10', 'minutes').format('HH:mm')
			let end = vigilanceJour.clone().add('1', 'hour').clone()
			intervals.push({
				begin: begin,
				nom: nom,
				end: end
			})
			vigilanceJour.add('1', 'hour')
		}
	}
	else {
		let vigilanceNuit = moment(currentVigilance.format("YYYY-MM-DD") + " 17:50:00")
		let limitVigilance = moment(currentVigilance.clone().add(1, 'day').format("YYYY-MM-DD") + " 05:50:00")
		while(vigilanceNuit.isBefore(limitVigilance)){
			let begin = vigilanceNuit.clone()
			let nom = vigilanceNuit.clone().add('10', 'minutes').format('HH:mm')
			let end = vigilanceNuit.clone().add('30', 'minutes').clone()
			intervals.push({
				begin: begin,
				nom: nom,
				end: end,
			})
			vigilanceNuit.add('30', 'minutes')
		}
	}
	return intervals
}

function doVigilanceBouton(date_vigilance){
	console.log("doAnomalieVigilanceBouton")
	poolOvh.query(sqlSelectJourFerie(date_vigilance), [], (err, ferie) => {
		if(err)
			console.error(err)
		else {
			poolOvh.query(sqlSelectSite(date_vigilance, (ferie && ferie.length > 0)), [], (err, sites) => {
				if(err)
					console.error(err)
				else if(sites){
					console.log("Nb site: " + sites.length)
					if(sites.length > 0){
						poolOvh.query(sqlSelectPointage(sites.map(s => s.id), date_vigilance), [], (err, pointages) => {
							if(err)
								console.error(err)
							else {
								console.log("Nb pointage: " + pointages.length)
								poolOvh.query(sqlSelectCommentaire(sites.map(s => s.id), date_vigilance), [], (err, commentaires) => {
									if(err)
										console.error(err)
									else {
										console.log("Nb commentaire: " + commentaires.length)
										poolOvh.query(sqlSelectVigilance(sites.map(s => s.id), date_vigilance), [], async (err, vigilances) => {
											if(err)
												console.error(err)
											else if(vigilances){
												console.log("Nb vigilance: " + vigilances.length)
												const dateVgString = moment(date_vigilance).format("DD MMMM YYYY")
													+ ' ' + (moment(date_vigilance).format("HH:mm:ss") == '07:00:00' ? 'Jour' : 'Nuit')
												
												sites.map(s => {
													s.without_comments = []
													s.without_vigilances = []
													s.with_vigilances = []
													s.agents = []
													s.intervals = getVigilanceInterval(date_vigilance)
													let pi = 0
													while(pi < pointages.length){
														const p = pointages[pi]
														if(p.site_id == s.id){
															s.agents.push(p)
															pointages.splice(pi, 1)
														}
														else pi++
													}
													s.intervals.forEach(itv => {
														let vi = 0
														while(vi<vigilances.length){
															const vg = vigilances[vi]
															let dtarrived = moment(vg.dtarrived)
															if( vg.site_id == s.id 
																&& dtarrived.isAfter(itv.begin) && dtarrived.isBefore(itv.end)){
																	if(!itv.value)
																		itv.value = dtarrived.format('HH:mm')
																	vigilances.splice(vi, 1)
															}
															else vi++
														}
														let ci = 0
														while(ci<commentaires.length){
															const cm = commentaires[ci]
															if(cm.site_id == s.id
																&& moment(cm.date_vigilance).isSame(itv.begin.clone().add(10, "minutes"))){
																	itv.commentaire = cm.objet
																	itv.detail = cm.commentaire
																	if(/check-phone \d{2}h\d{2}/.test(cm.objet.toLowerCase())){
																		const hours = /check-phone (\d{2})h(\d{2})/.exec(cm.objet.toLowerCase())
																		itv.value = hours[1] + ':' + hours[2]
																	}
																	commentaires.splice(ci, 1)
																	break
															}
															else ci++
														}
													})
												})
												
												sites.map(s => {
													s.intervals.map(itv => {
														if(!itv.value && !itv.commentaire)
															s.without_comments.push(itv)
														else if(!itv.value && itv.commentaire && !/check-phone \d{2}h\d{2}/.test(itv.commentaire.toLowerCase()))
															s.without_vigilances.push(itv)
														else if(itv.value && itv.commentaire && !/check-phone \d{2}h\d{2}/.test(itv.commentaire.toLowerCase()))
															s.with_vigilances.push(itv)
													})
												})

												let arrayBuffer = []

												let siteWithoutComments = []
												let siteWithoutVigilances = []
												let siteWithVigilances = []
												sites.map(s => {
													if(s.without_comments.length > 1) siteWithoutComments.push(s)
													if(s.without_vigilances.length > 1) siteWithoutVigilances.push(s)
													if(s.with_vigilances.length > 1) siteWithVigilances.push(s)
												})

												if(siteWithoutComments.length > 0){
													const workbookWithoutComment = new Excel.Workbook()
													siteWithoutComments.sort((a, b) => (b.without_comments.length - a.without_comments.length))
													generateMissingCommentExcelFile(workbookWithoutComment, "Manque de vigilance sans commentaire ni mesure prise du "
													+ dateVgString, siteWithoutComments, getVigilanceInterval(date_vigilance))
													const withoutCommentBuffer = await workbookWithoutComment.xlsx.writeBuffer()
													arrayBuffer.push({
														filename: "Manque de vigilance sans commentaire ni mesure prise du " + dateVgString + ".xlsx",
														content: withoutCommentBuffer
													})
												}
												
												if(siteWithoutVigilances.length > 0){
													const workbookWithoutVigilance = new Excel.Workbook()
													siteWithoutVigilances.sort((a, b) => (b.without_vigilances.length - a.without_vigilances.length))
													generateMissingVigilanceExcelFile(workbookWithoutVigilance, "Manque de vigilance avec commentaire du "
													+ dateVgString, siteWithoutVigilances, getVigilanceInterval(date_vigilance))
													const withoutVigilanceBuffer = await workbookWithoutVigilance.xlsx.writeBuffer()
													arrayBuffer.push({
														filename: "Manque de vigilance avec commentaire du " + dateVgString + ".xlsx",
														content: withoutVigilanceBuffer
													})
												}
												if(siteWithVigilances.length > 0){
													const workbookWithVigilance = new Excel.Workbook()
													siteWithVigilances.sort((a, b) => (b.without_vigilances.length - a.without_vigilances.length))
													generateWithVigilanceExcelFile(workbookWithVigilance, "Vigilance transmis mais commenté du "
													+ dateVgString, siteWithVigilances, getVigilanceInterval(date_vigilance))
													const withVigilanceBuffer = await workbookWithVigilance.xlsx.writeBuffer()
													arrayBuffer.push({
														filename: "Vigilance Transmis mais commenté du " + dateVgString + ".xlsx",
														content: withVigilanceBuffer
													})
												}

												sendMail(
													poolAdmin,
													isTask ? destination_task : destination_test,
													"Anomalie Vigilance Bouton de Ronde " + " du " + dateVgString, 
													"Veuillez trouver ci-joint l'anomalie de vigilance des boutons de rondes.",
													arrayBuffer, 
													(response) => {
														if(response && isTask) {
															poolOvh.query(sqlUpdateLastExport(date_vigilance), [], (e, r) =>{
																if(e)
																	console.error(e)
																else
																	console.log("update last anomalie button export: " + r)
																process.exit(1)
															})
														}
														else
															process.exit(1)
													},
													isTask
												)
											}
										})
									}
								})
							}
						})		
					}
				}
			})
		}
	})
}

if(/^\d{4}-\d{2}-\d{2}$/.test(process.argv[2]) && /^\d{2}:\d{2}:\d{2}$/.test(process.argv[3])){
	console.log("send test...")
	doVigilanceBouton(process.argv[2] + ' ' + process.argv[3])
}
else if(process.argv[2] == 'task'){
	let date_vigilance = getDayOrNightExport() 
	poolOvh.query(sqlSelectLastExportAnomalieVigilance, [], (err, params) => {
		if(err)
			console.error(err)
		else if(params && params[0]){
			if(params[0].value != date_vigilance)
				doVigilanceBouton(date_vigilance)
			else {
				console.log("export anomalie bouton already done!")
				process.exit()
			}
		}
	})
}
else console.log("please specify command!")