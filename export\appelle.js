const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")

moment.locale('fr')
const {db_config_zo, db_config_ipbx, db_config_admin, sendMail} = require("../auth")
const pool_tls = mysql.createPool(db_config_zo)
const pool_ipbx = mysql.createPool(db_config_ipbx)
const poolAdmin = mysql.createPool(db_config_admin)

const isTask = (process.argv[2] == 'task')
const destination_vg = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
const destination_test = ["<EMAIL>"]

const sqlSelectCdr = "SELECT CAST(c.uniqueid AS CHAR) as uniqueid, c.datetime, c.src, c.dst, c.duration, c.calltype, c.disposition " +
    "FROM cdr c WHERE c.uniqueid >= ? and c.uniqueid < ? ORDER BY c.uniqueid ASC, c.datetime ASC"
    const sqlSelectAdminUser = (ids) => 
        "SELECT name, email, flotte FROM admin_users WHERE flotte IN (" + ids.map(id => `'${id}'`).join(',') + ")";
    const sqlSelectNumero = (ids) => 
        "SELECT numero, id_site, id_contact FROM numeros WHERE numero IN (" + ids.join(',') + ")"

const getListSite = (ids, callback) => {
    const sqlSelectSite = (ids) => "SELECT idsite, nom FROM sites WHERE idsite IN (" + ids.map(id => `'${id}'`).join(',') + ")"
    pool_tls.query(sqlSelectSite(ids), [], async (err, sites) => {
        if(err) 
            console.error(err)
        else {
            console.log("nb sites : " + sites.length)
            callback(sites)
        }
    })
}

const getListHabilite = (ids, callback) => {
    const sqlSelectSite = (ids) => `SELECT c.idcontact, c.nom, c.prenom, s.nom as site
        FROM contacts c
        LEFT JOIN habilites h ON c.idContact = h.idcontact
        LEFT JOIN sites s ON s.idsite = h.idsite
        WHERE c.idcontact IN (${ids.join(',')})`
    pool_tls.query(sqlSelectSite(ids), [], async (err, habilites) => {
        if(err) 
            console.error(err)
        else {
            console.log("nb habilites : " + habilites.length)
            callback(habilites)
        }
    })
}

const getAppelleCompact = (dateService) => {
    const beginService = moment(dateService).subtract(30, "minutes").format("YYYY-MM-DD HH:mm:ss")
	pool_ipbx.query(sqlSelectCdr, [moment(beginService).format("X"), moment(beginService).add(12, "hours").format("X")], async (err, cdr) => {
		if(err) 
            console.error(err)
		else {
            console.log("nb cdr : " + cdr.length)
            let appelles = {};
            const puces = [];
        
            for (const c of cdr) {
                if (!appelles[c.uniqueid]) {
                    appelles[c.uniqueid] = {
                        uniqueid: c.uniqueid,
                        calltype: c.calltype,
                        cdr: []
                    };
        
                    if (c.calltype === "Inbound") {
                        if (/^\+261\d{9}$/.test(c.src)) {
                            appelles[c.uniqueid].numero = "0" + c.src.slice(4);
                        } else if (/^\d{9,10}$/.test(c.src)) {
                            appelles[c.uniqueid].numero = "0" + c.src;
                        } else {
                            console.error("Invalid src:", c.src);
                            continue;
                        }
                        puces.push(appelles[c.uniqueid].numero);
                    } else if (c.calltype === "Outbound") {
                        let num = c.dst;
                        const match = num.match(/\((\d+)\)/);
                        if (match) {
                            num = match[1]; 
                        }
                        appelles[c.uniqueid].numero = num;
                        puces.push(appelles[c.uniqueid].numero);
                    } else {
                        appelles[c.uniqueid].numero = `${c.dst} -> ${c.src}`;
                    }
                }
        
                appelles[c.uniqueid].cdr.push({
                    datetime: c.datetime,
                    src: c.src,
                    dst: c.dst,
                    disposition: c.disposition
                });
            }
        
            // Determine disposition
            for (const key in appelles) {
                const dispositions = appelles[key].cdr.map(c => c.disposition);
                if (dispositions.includes('ANSWERED')) 
                    appelles[key].disposition = 'ANSWERED';
                else if (dispositions.includes('NO ANSWER')) 
                    appelles[key].disposition = 'NO ANSWER';
                else if (dispositions.includes('BUSY')) 
                    appelles[key].disposition = 'BUSY';
                 else
                    appelles[key].disposition = dispositions[0];
            }
            
            const invalides = puces.filter(num => /[()]/.test(num));
            console.log("Numéros invalides :", invalides);
            pool_tls.query(sqlSelectAdminUser(puces), [], async (err, admin_users) => {
                if(err) 
                    console.error(err)
                else {
                    console.log("nb admin_users : " + admin_users.length)
                    pool_tls.query(sqlSelectNumero(puces), [], async (err, numeros) => {
                        if(err) 
                            console.error(err)
                        else {
                            console.log("nb numeros : " + numeros.length)
                            const contact_ids = numeros.filter(n => n.id_contact).map(n => n.id_contact);
                            const site_ids = numeros.filter(n => n.id_site).map(n => n.id_site);

                            if (contact_ids.length) {
                                getListHabilite(contact_ids, (habilites) => {
                                    if(site_ids.length)
                                        getListSite(site_ids, (sites) => {
                                            orderAppelleObjet(dateService, numeros, appelles, admin_users, habilites, sites)
                                        })
                                    else
                                        orderAppelleObjet(dateService, numeros, appelles, admin_users, habilites, [])
                                })
                            }
                            else if(site_ids.length) {
                                getListSite(site_ids, (sites) => {
                                    orderAppelleObjet(dateService, numeros, appelles, admin_users, [], sites)
                                })
                            }
                            else
                                orderAppelleObjet(dateService, numeros, appelles, admin_users, [], [])

                        }
                    })
                }
            })
        }
    })
}

const orderAppelleObjet = async (dateService, numeros, appelles, admin_users, habilites, sites) => {
    for (const key in appelles) {
        const a = appelles[key];
        let contactSet = false;

        for (const n of numeros) {
            for (const h of habilites) {
                if (h.idcontact === n.id_contact && a.numero === n.numero) {
                    a.client = `${h.nom} ${h.prenom}`;
                    a.site = h.site;
                    a.contacttype = "client";
                    contactSet = true;
                    break;
                }
            }

            if (!contactSet) {
                for (const u of admin_users) {
                    if (a.numero === u.flotte) {
                        a.name = u.name;
                        a.email = u.email;
                        a.contacttype = "user";
                        contactSet = true;
                        break;
                    }
                }
            }

            if (!contactSet) {
                for (const s of sites) {
                    if (s.idsite === n.id_site && a.numero === n.numero) {
                        a.site = s.nom;
                        a.contacttype = "agent";
                        contactSet = true;
                        break;
                    }
                }
            }

            if (contactSet) break;
        }
    }
    const appelleCompact = {
        appelles: Object.values(appelles),
        habilites,
        sites,
        admin_users
    }

    console.log("nb appelles : " + Object.values(appelles).length)
    console.log(moment(appelleCompact.appelles[0].cdr[0].datetime).format("YYYY-MM-DD HH:mm:ss"))
    console.log(moment(appelleCompact.appelles[appelleCompact.appelles.length - 1].cdr[0].datetime).format("YYYY-MM-DD HH:mm:ss"))

    let appelleGroupByNumero = {}
    Object.values(appelles).forEach(a => {
        if(!Object.keys(appelleGroupByNumero).includes(a.numero)){
            appelleGroupByNumero[a.numero] = []
        }
        appelleGroupByNumero[a.numero].push(a)
    })
    console.log("appelleGroupByNumero : " + Object.keys(appelleGroupByNumero).length)
    let nonRappeler = {}
    Object.keys(appelleGroupByNumero).forEach(key => {
        const currentNumero = appelleGroupByNumero[key]
        if(["NO ANSWER", "BUSY"].includes(currentNumero[currentNumero.length-1].disposition)){
            nonRappeler[key] = currentNumero[currentNumero.length-1]
        }
    })
    console.log("nonRappeler : ", Object.keys(nonRappeler).length)
    
    
    const arrayFile = []
    const workbookAppelle = new Excel.Workbook()
    const dateString = moment(dateService).format("ddd DD MMM YYYY") + (moment(dateService).format("HH:mm") == "06:00" ? " JOUR" : " NUIT")
    generateAppelleExcelFile(workbookAppelle, appelles, nonRappeler)
    const appelleBuffer = await workbookAppelle.xlsx.writeBuffer()
    arrayFile.push({
        filename: "Appelle " + dateString + ".xlsx",
        content: appelleBuffer
    })

    sendMail(
        poolAdmin,
        isTask ? destination_vg : destination_test,
        "Rapport Appelle Controlroom " + dateString, 
        "Veuillez trouver ci-joint le rapport de la gestion des appelles controlroom du " + dateString + "." ,
        arrayFile, 
        (response) => {
            if(response && isTask){
                const sqlUpdateLastCallExport = "UPDATE params p SET p.value = ? WHERE p.key = 'last_export_call'"
                pool_tls.query(sqlUpdateLastCallExport, [dateService], (e, r) =>{
                    if(e)
                        console.error(e)
                    else
                        console.log("update last diag export: " + r)
                    process.exit(1)
                })
            }
            else
                process.exit(1)
        },
        isTask
    )
}

function getBeginService() {
    const now = moment();
    const startMorning = moment().set({ hour: 5, minute: 50, second: 0 });
    const endMorning = moment().set({ hour: 17, minute: 50, second: 0 });
    let dateTask = null
    if (now.isSameOrAfter(startMorning) && now.isBefore(endMorning)) {
        dateTask = moment().set({ hour: 6, minute: 0, second: 0 })
    } else if (now.isBefore(startMorning)) {
        dateTask = moment().subtract(1, 'day').set({ hour: 18, minute: 0, second: 0 })
    }
    else
        dateTask = moment().set({ hour: 18, minute: 0, second: 0 })
    return dateTask.subtract(12, "hours").format('YYYY-MM-DD HH:mm:ss');
}

function formatContact(numero){
    return numero.slice(0, 3) + " " + numero.slice(3, 5) + " " + numero.slice(5, 8) + " " + numero.slice(8, 10)
}

function generateAppelleExcelFile(workbook, appelles, nonRappeler){
    const toCalls = []
    Object.keys(nonRappeler).forEach(key => {
        if(nonRappeler[key].calltype == "Inbound")
            toCalls.push(nonRappeler[key])

    })
    console.log("toCalls : " + toCalls.length)
    //console.log(nonRappeler)
    const borderStyle = {
        top: {style:'thin'},
        left: {style:'thin'},
        bottom: {style:'thin'},
        right: {style:'thin'}
    }
    const fontHeader = { size: 16, bold: true }
    const fontBold = { bold: true }

    const fillContrast = {
        type: 'pattern',
        pattern:'solid',
        fgColor:{argb:'ffeaeaea'}
    }
    

    const worksheet = workbook.addWorksheet("Non rappelé")
    worksheet.getColumn('A').width = 12
    worksheet.getColumn('B').width = 40
    worksheet.getColumn('C').width = 40
    worksheet.getColumn('D').width = 60

    let line = 1
    worksheet.mergeCells('A'+ line +':' + 'D' + line)
    worksheet.getCell('A' + line).value = "Contact non rappelé (" + toCalls.length + ")"
    worksheet.getCell('A' + line).font = fontHeader
    
    line++
    worksheet.getCell('A' + line).value = "Date"
    worksheet.getCell('A' + line).border = borderStyle
    worksheet.getCell('A' + line).font = fontBold
    worksheet.getCell('B' + line).value = "Contact"
    worksheet.getCell('B' + line).border = borderStyle
    worksheet.getCell('B' + line).font = fontBold
    worksheet.getCell('C' + line).value = "Status"
    worksheet.getCell('C' + line).border = borderStyle
    worksheet.getCell('C' + line).font = fontBold
    worksheet.getCell('D' + line).value = "Description"
    worksheet.getCell('D' + line).border = borderStyle
    worksheet.getCell('D' + line).font = fontBold

    line++
    toCalls.forEach((ap) => {
        worksheet.getCell('A' + line).value = moment.unix(ap.uniqueid).format("YY-MM-DD HH:mm:ss")
        worksheet.getCell('A' + line).border = borderStyle
        worksheet.getCell('B' + line).value = formatContact(ap.numero)
        worksheet.getCell('B' + line).border = borderStyle
        worksheet.getCell('C' + line).value = (
            ap.disposition == "NO ANSWER" ? 'Non abouti'
            : ap.disposition == "ANSWERED" ? 'Abouti' 
            : ap.disposition == "BUSY" ? 'Occupé' 
            : ap.disposition
        )
        worksheet.getCell('C' + line).border = borderStyle
        worksheet.getCell('D' + line).value = (
            ap.contacttype == "client" ?
                `Client : ${ap.client} : ${ap.site}`
            : ap.contacttype == "agent" ?
                `Agent : ${ap.site}`
            : ap.contacttype == "user" ?
                `Employé : ${ap.name} ${ap.email}`
            :
                ``
        )
        worksheet.getCell('D' + line).border = borderStyle
        line++
    })
}

if(process.argv[2] != "task") {
    console.log("test export call")
    getAppelleCompact(process.argv[2] + " " + process.argv[3])
}
else {
    const sqlSelectDateExportCall = "SELECT p.value FROM params p WHERE p.key = 'last_export_call'"
    pool_tls.query(sqlSelectDateExportCall, [], async (err, params) => {
        if(err) 
            console.error(err)
        else {
            const dateService = getBeginService()
            if(params[0].value == dateService){
                console.log("export call already done")
                process.exit(1)
            }
            else {
                console.log("do export call")
                getAppelleCompact(dateService)
            }
        }
    })
}


