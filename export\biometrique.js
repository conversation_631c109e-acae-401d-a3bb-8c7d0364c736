const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")

moment.locale('fr')


const {db_config_zo, db_config_admin, sendMail} = require("../auth")
const poolOvh = mysql.createPool(db_config_zo)
const poolAdmin = mysql.createPool(db_config_admin)

function getDayOrNightExport(){
	let beginDay = moment().set({hour:5, minute:50, second:0})
	let endDay = moment().set({hour:17, minute:50, second:0})
	if(moment().isAfter(beginDay) && moment().isBefore(endDay))
		return moment().subtract(1, 'day').format("YYYY-MM-DD") + " 18:00:00"
	else {
		if(moment().isBefore(beginDay))
			return moment().subtract(1, 'day').format("YYYY-MM-DD") + " 07:00:00"
		return moment().format("YYYY-MM-DD") + " 07:00:00"
	}
}

const sqlSelectDateVigilancePointeuseExport = "SELECT value FROM params p WHERE p.key = 'last_export_vigilance_pointeuse'"
function sqlUpdateLastDiagExport(dateString){
	return "UPDATE params p SET p.value = '" + dateString + "' " +
		"WHERE p.key = 'last_export_vigilance_pointeuse'"
}

const isTask = (process.argv[2] == 'task')
const destination_vg = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", 
	"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", 
	"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", 
	"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", 
	"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", 
	"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
	"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
const destination_test = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>","<EMAIL>","<EMAIL>"]
// const destination_test = ["<EMAIL>"]

function generateCheckExcelFile(workbook, header, sites){
	const cols = ['B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 
	'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 
	'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ',]
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
	const fontHeader = { size: 16, bold: true }
	const fontBold = { bold: true }
	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	const fillRed = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'fff44336'}
	}
	const fillHeader = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'ffbbbbbb'}
	}

	const worksheet = workbook.addWorksheet("Control Vigilance")
	worksheet.getColumn('A').width = 20
	cols.forEach(col => {
		worksheet.getColumn(col).width = 10
	})

	console.log(header)
	
	worksheet.mergeCells('A1:' + cols[cols.length - 1] + '1')
	worksheet.getCell('A1').value = header + " (" + sites.length + " sites)"
	worksheet.getCell('A1').font = fontHeader
	
	let	line = 3
	sites.forEach(s => {
		worksheet.mergeCells('A'+ line +':' + cols[cols.length - 1] + line)
		worksheet.getCell('A' + line).value = s.nom
		worksheet.getCell('A' + line).font = fontBold
		
		s.agents.forEach((ag) => {
			line++
			worksheet.getCell('A' + line).value = (
				ag.societe_id == 1 ? 'DGM-' + ag.numero_employe :
				ag.societe_id == 2 ? 'SOIT-' + ag.num_emp_soit :
				ag.societe_id == 3 ? 'ST-' + ag.numero_stagiaire :
				ag.societe_id == 4 ? 'SM' :
				ag.numero_employe ? ag.numero_employe :
				ag.numero_stagiaire ? ag.numero_stagiaire :
				'Ndf'
			) + ' ' + ag.nom
		})
		
		line++
		worksheet.getCell("A" + line).value = "Vigilance"
		worksheet.getCell("A" + line).border = borderStyle
		worksheet.getCell("A" + line).fill = fillHeader
		worksheet.getCell("A" + (line+1)).value = "Heure"
		worksheet.getCell("A" + (line+1)).border = borderStyle
		s.intervals.forEach((itv, itvIndex) => {
			worksheet.getCell(cols[itvIndex] + line).value = itv.nom
			worksheet.getCell(cols[itvIndex] + line).alignment = alignmentStyle
			worksheet.getCell(cols[itvIndex] + line).border = borderStyle
			worksheet.getCell(cols[itvIndex] + line).fill = fillHeader
			worksheet.getCell(cols[itvIndex] + (line+1)).value = itv.value ? itv.value : 'X'
			worksheet.getCell(cols[itvIndex] + (line+1)).alignment = alignmentStyle
			worksheet.getCell(cols[itvIndex] + (line+1)).border = borderStyle
			if(!itv.value)
				worksheet.getCell(cols[itvIndex] + (line+1)).fill = fillRed
			if(itv.commentaire)
				worksheet.getCell(cols[itvIndex] + line).note = itv.commentaire
		})
		line = line+3
	})
}

function generateVgExcelFile(workbook, header, pointeuses){

	const cols = ['B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 
	'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 
	'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ',]
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
	const fontHeader = { size: 16, bold: true }
	const fontBold = { bold: true }
	const fontColor = { color:  { argb: 'fff44336'}}
	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	const fillRed = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'fff44336'}
	}
	const fillHeader = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'ffbbbbbb'}
	}

	const worksheet = workbook.addWorksheet("Control Vigilance")
	worksheet.getColumn('A').width = 50
	cols.forEach(col => {
		worksheet.getColumn(col).width = 10
	})

	console.log(header)
	
	worksheet.mergeCells('A1:' + cols[cols.length - 1] + '1')
	worksheet.getCell('A1').value = header + " (" + pointeuses.length + " pointeuses)"
	worksheet.getCell('A1').font = fontHeader
	
	let	line = 3
	pointeuses.forEach(p => {
		line++
		worksheet.mergeCells('A'+ line +':' + cols[cols.length - 1] + line)
		worksheet.getCell('A' + line).value = p.nom
		worksheet.getCell('A' + line).font = fontBold
		
		line++
		worksheet.getCell('A' + line).value = "Agent"
		worksheet.getCell('A' + line).border = borderStyle
		worksheet.getCell('A' + line).fill = fillHeader
		p.intervals.forEach((itv, itvIndex) => {
			worksheet.getCell(cols[itvIndex] + line).value = itv.nom
			if(itv.commentaire)
				worksheet.getCell(cols[itvIndex] + line).note = itv.commentaire
			worksheet.getCell(cols[itvIndex] + line).alignment = alignmentStyle
			worksheet.getCell(cols[itvIndex] + line).border = borderStyle
			worksheet.getCell(cols[itvIndex] + line).fill = fillHeader
		})

		line++
		p.agents.forEach((ag) => {
			worksheet.getCell('A' + line).value = (
				ag.societe_id == 1 ? 'DGM-' + ag.numero_employe :
				ag.societe_id == 2 ? 'SOIT-' + ag.num_emp_soit :
				ag.societe_id == 3 ? 'ST-' + ag.numero_stagiaire :
				ag.societe_id == 4 ? 'SM' :
				ag.numero_employe ? ag.numero_employe :
				ag.numero_stagiaire ? ag.numero_stagiaire :
				'Ndf'
			) + ' ' + ag.nom
			worksheet.getCell('A' + line).border = borderStyle
			if(!ag.intervals)
				console.log(ag)
			if(ag.intervals)
				ag.intervals.forEach((itv, itvIndex) => {
					worksheet.getCell(cols[itvIndex] + line).value = itv.nom
					if(itv.nom == 'X'){
						worksheet.getCell(cols[itvIndex] + line).fill = fillRed
					}
					if(itv.commentaire)
						worksheet.getCell(cols[itvIndex] + line).note = itv.commentaire
					worksheet.getCell(cols[itvIndex] + line).alignment = alignmentStyle
					worksheet.getCell(cols[itvIndex] + line).border = borderStyle
				})
			line++
		})
	})
}

function generateAnomalieExcelFile(workbook, pointage_manuelles, commentaires){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
	const fontHeader = { size: 16, bold: true }
	const fontBold = { bold: true }

	const fillContrast = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'ffeaeaea'}
	}
	

	const worksheet = workbook.addWorksheet("Anomalie Pointage")
	worksheet.getColumn('A').width = 12
	worksheet.getColumn('B').width = 40
	worksheet.getColumn('C').width = 40
	worksheet.getColumn('D').width = 60

	let line = 1
	pointage_manuelles.forEach((ptg) => {
		line++
		worksheet.mergeCells('A'+ line +':' + 'D' + line)
		worksheet.getCell('A' + line).value = ptg.title +" (" + ptg.list.length + ")"
		worksheet.getCell('A' + line).font = fontHeader
		
		line++
		worksheet.getCell('A' + line).value = "Matricule"
		worksheet.getCell('A' + line).border = borderStyle
		worksheet.getCell('A' + line).font = fontBold
		worksheet.getCell('B' + line).value = "Agent"
		worksheet.getCell('B' + line).border = borderStyle
		worksheet.getCell('B' + line).font = fontBold
		worksheet.getCell('C' + line).value = "Site"
		worksheet.getCell('C' + line).border = borderStyle
		worksheet.getCell('C' + line).font = fontBold
		worksheet.getCell('D' + line).value = "Motif"
		worksheet.getCell('D' + line).border = borderStyle
		worksheet.getCell('D' + line).font = fontBold

		line++
		ptg.list.forEach((ag) => {
			worksheet.getCell('A' + line).value = (
				ag.societe_id == 1 ? 'DGM-' + ag.numero_employe :
				ag.societe_id == 2 ? 'SOIT-' + ag.num_emp_soit :
				ag.societe_id == 3 ? 'ST-' + ag.numero_stagiaire :
				ag.societe_id == 4 ? 'SM' :
				ag.numero_employe ? ag.numero_employe :
				ag.numero_stagiaire ? ag.numero_stagiaire :
				'Ndf'
			)
			worksheet.getCell('A' + line).border = borderStyle
			worksheet.getCell('B' + line).value = ag.nom
			worksheet.getCell('B' + line).border = borderStyle
			worksheet.getCell('C' + line).value = ag.site
			worksheet.getCell('C' + line).border = borderStyle
			worksheet.getCell('D' + line).value = ag.motif
			worksheet.getCell('D' + line).border = borderStyle
			line++
		})
	})
	if(commentaires.length > 0){
		line++
		worksheet.mergeCells('A'+ line +':' + 'D' + line)
		worksheet.getCell('A' + line).value = "Commentaires (" + commentaires.length + ')'
		worksheet.getCell('A' + line).font = fontHeader

		line++
		worksheet.getCell('A' + line).value = "Vigilance"
		worksheet.getCell('A' + line).border = borderStyle
		worksheet.getCell('A' + line).font = fontBold
		worksheet.getCell('B' + line).value = "Pointeuse"
		worksheet.getCell('B' + line).border = borderStyle
		worksheet.getCell('B' + line).font = fontBold
		worksheet.getCell('C' + line).value = "Agent"
		worksheet.getCell('C' + line).border = borderStyle
		worksheet.getCell('C' + line).font = fontBold
		worksheet.getCell('D' + line).value = "Commentaire"
		worksheet.getCell('D' + line).border = borderStyle
		worksheet.getCell('D' + line).font = fontBold

		let lastPointeuseId = 0
		let lastIsColored = true
		commentaires.forEach(cm => {
			if(cm.pointeuse_id != lastPointeuseId){
				lastIsColored = !lastIsColored
				lastPointeuseId = cm.pointeuse_id
			}
			line++
			worksheet.getCell('A' + line).value = moment(cm.date_vigilance).format('HH:mm')
			worksheet.getCell('A' + line).border = borderStyle
			worksheet.getCell('B' + line).value = cm.pointeuse
			worksheet.getCell('B' + line).border = borderStyle
			if(cm.agent){
				worksheet.getCell('C' + line).value = (
					cm.societe_id == 1 ? 'DGM-' + cm.numero_employe :
					cm.societe_id == 2 ? 'SOIT-' + cm.num_emp_soit :
					cm.societe_id == 3 ? 'ST-' + cm.numero_stagiaire :
					cm.societe_id == 4 ? 'SM' :
					cm.numero_employe ? cm.numero_employe :
					cm.numero_stagiaire ? cm.numero_stagiaire :
					'Ndf'
				) + '  ' + cm.agent
			}
			worksheet.getCell('C' + line).border = borderStyle
			worksheet.getCell('D' + line).value = cm.objet + (cm.commentaire ? ': ' + cm.commentaire : '')
			worksheet.getCell('D' + line).border = borderStyle
			if(!lastIsColored){
				worksheet.getCell('A' + line).fill = fillContrast
				worksheet.getCell('B' + line).fill = fillContrast
				worksheet.getCell('C' + line).fill = fillContrast
				worksheet.getCell('D' + line).fill = fillContrast
			}
		})
	}
}


function sqlSelectGroupVigilance(){
	return "SELECT vigilance_group_id, nom " +
		"FROM group_sites " +
		"WHERE id in (SELECT min(id) FROM group_sites GROUP BY vigilance_group_id)"
} 

function sqlSelectJourFerie(date_vigilance){
	return "SELECT id from jour_feries where date = '" + moment(date_vigilance).format("YYYY-MM-DD") + "'"
}
function sqlSelectPointeuse(date_vigilance, ferie) {
	const horaire = (moment(date_vigilance).format('HH:mm:ss') == '07:00:00') ? 'day' : 'night'
	const field = horaire + '_' + moment(date_vigilance).day()
	console.log("isFerie: " + ferie)
	if(ferie)
		return "SELECT p.id, p.nom, s.nom, p.last_connection, p.last_vigilance, p.site_id, g.vigilance_group_id, s.phone_agent " +
			"FROM pointeuses p " +
			"LEFT JOIN sites s ON s.idsite = p.site_id " +
			"LEFT JOIN group_sites g ON g.id = s.group_id " +
			"LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id " +
			"WHERE s.pointeuse = 1 and (p.soft_delete is null or p.soft_delete = 0) " +
			"and (s.soft_delete is null or s.soft_delete = 0) " +
			"and (h.id is null or h." + field  + " = 1 or h." + horaire + "_ferie = 1) " +
			"ORDER BY s.group_pointage_id DESC, s.nom"
	else
		return "SELECT p.id, p.nom, s.nom, p.last_connection, p.last_vigilance, p.site_id, g.vigilance_group_id, s.phone_agent " +
			"FROM pointeuses p " +
			"LEFT JOIN sites s ON s.idsite = p.site_id " +
			"LEFT JOIN group_sites g ON g.id = s.group_id " +
			"LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id " +
			"WHERE s.pointeuse = 1 and (p.soft_delete is null or p.soft_delete = 0) " +
			"and (s.soft_delete is null or s.soft_delete = 0) " +
			"and (h.id is null or h." + field + " = 1) " +
			"ORDER BY s.group_pointage_id DESC, s.nom"
}
function sqlSelectPointage(date_vigilance, pointeuse_ids) {
	return "SELECT ptg.id, ptg.site_id, a.societe_id, a.nom, a.numero_employe, a.num_emp_soit, a.numero_stagiaire, ptg.type_pointage_id, " +
		"ptg.id as 'pointage_id', a.id as agent_id, ptg.dtarrived, ptg.pointeuse_id, s.nom as 'site', p.optic, a.empreinte, a.empreinte_optic, ptg.motif, ptg.soft_delete " + 
		"FROM pointages ptg " +
		"LEFT JOIN agents a ON a.id = ptg.agent_id " +
		"LEFT JOIN sites s ON s.idsite = ptg.site_id " +
		"LEFT JOIN pointeuses p ON p.id = ptg.pointeuse_id " +
		"WHERE (s.soft_delete is null or s.soft_delete = 0)  " +
		"and (p.soft_delete is null or p.soft_delete = 0)  " +
		"and (ptg.pointeuse_id is not null or ptg.dtarrived is not null) " +
		"and ptg.date_pointage= '" +  date_vigilance + "' " +
		"and p.id in (" + pointeuse_ids.join(",") + ") "
}
function sqlSelectAgentPointeuse(pointeuse_ids) {
	return "select ap.id, ap.pointeuse_id, ap.agent_id from agent_pointeuses ap where pointeuse_id in ( " + pointeuse_ids.join(",") + " ) "
}
function sqlSelectCommentaire(date_vigilance, pointeuse_ids, site_ids) {
	let begin_date = ''
	let end_date = ''
	if(moment(date_vigilance).format('HH:mm:ss') == '07:00:00'){
		begin_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 05:50:00'
		end_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 17:50:00'
	}
	else {
		begin_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 17:50:00'
		end_date = moment(date_vigilance).add(1, 'day').format('YYYY-MM-DD') + ' 06:50:00'
	}
	return "SELECT c.pointeuse_id, c.agent_id, c.commentaire, c.objet, c.commentaire, c.date_vigilance, p.nom as 'pointeuse', a.nom as 'agent', " +
		"c.site_id, a.societe_id, a.numero_employe, a.num_emp_soit, a.numero_stagiaire " +
		"FROM v_commentaires c " +
		"LEFT JOIN pointeuses p ON p.id = c.pointeuse_id " +
		"LEFT JOIN agents a ON a.id = c.agent_id " +
		"WHERE c.pointeuse_id is not null " +
		"and c.date_vigilance >= '" + begin_date + "' " +
		"and c.date_vigilance < '" + end_date + "' " +
		"and (c.pointeuse_id in (" + pointeuse_ids.join(",") + ") or c.site_id in (" + site_ids.join(",") + ")) " +
		"order by p.id, c.date_vigilance"
}
function sqlSelectCheckphone(date_vigilance, site_ids) {
	let begin_date = ''
	let end_date = ''
	if(moment(date_vigilance).format('HH:mm:ss') == '07:00:00'){
		begin_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 05:50:00'
		end_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 17:50:00'
	}
	else {
		begin_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 17:50:00'
		end_date = moment(date_vigilance).add(1, 'day').format('YYYY-MM-DD') + ' 06:50:00'
	}
	return "SELECT adm.idademco as 'id', adm.dtarrived, adm.site_id " +
		"from " + (moment(date_vigilance).isBefore(moment().subtract(1, "day")) ? " ademcolog " : " ademcotemp ") + " adm " +
		"where codeTevent=1000 and dtarrived >= '" + begin_date + "' " +
		"and dtarrived < '" + end_date + "' " +
		"and adm.pointeuse_id is null " +
		"and adm.site_id in (" + site_ids.join(",") + ") " +
		"order by dtarrived asc"
}
function sqlSelectVigilance(date_vigilance, pointeuse_ids) {
	let begin_date = ''
	let end_date = ''
	if(moment(date_vigilance).format('HH:mm:ss') == '07:00:00'){
		begin_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 05:40:00'
		end_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 17:50:00'
	}
	else {
		begin_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 17:40:00'
		end_date = moment(date_vigilance).add(1, 'day').format('YYYY-MM-DD') + ' 05:50:00'
	}
	return "SELECT adm.idademco as 'id', adm.agent_id, adm.dtarrived, a.societe_id, a.nom, a.numero_employe, a.num_emp_soit, " +
		"a.numero_stagiaire, adm.pointeuse_id " +
		"from " + (moment(date_vigilance).isBefore(moment().subtract(1, "day")) ? " ademcolog " : " ademcotemp ") + " adm " +
		"left join agents a ON a.id = adm.agent_id " +
		"where agent_id is not null and codeTevent=1000  and dtarrived >= '" + begin_date + "' " +
		"and dtarrived < '" + end_date + "' " +
		"and adm.pointeuse_id in (" + pointeuse_ids.join(",") + ") " +
		"order by dtarrived asc"
}

function getVigilanceInterval(date_vigilance){
	let currentVigilance = moment(date_vigilance)
	let intervals = []
	if(currentVigilance.format('HH:mm:ss') == '07:00:00'){
		let vigilanceJour = moment(currentVigilance.format("YYYY-MM-DD") + " 05:40:00")
		while(vigilanceJour.isBefore(moment(currentVigilance.format("YYYY-MM-DD") + " 17:50:00"))){
			let begin = vigilanceJour.clone()
			let nom, end
			if(intervals.length == 0 ){
				nom = vigilanceJour.clone().add('20', 'minutes').format('HH:mm')
				end = vigilanceJour.clone().add('1', 'hour').add('10', 'minutes').clone()
			}
			else{
				nom = vigilanceJour.clone().add('10', 'minutes').format('HH:mm')
				end = vigilanceJour.clone().add('1', 'hour').clone()
			}
			// let nom = vigilanceJour.clone().add('10', 'minutes').format('HH:mm')
			// let end = vigilanceJour.clone().add('1', 'hour').clone()

			intervals.push({
				begin: begin,
				nom: nom,
				end: end
			})
			if(intervals.length == 1)
				vigilanceJour.add('10', 'minutes')
			vigilanceJour.add('1', 'hour')
		}
	}
	else {
		let vigilanceNuit = moment(currentVigilance.format("YYYY-MM-DD") + " 17:40:00")
		let limitVigilance = moment(currentVigilance.clone().add(1, 'day').format("YYYY-MM-DD") + " 05:50:00")
		while(vigilanceNuit.isBefore(limitVigilance)){
			let begin = vigilanceNuit.clone()
			let nom, end
			if(intervals.length == 0 ){
				nom = vigilanceNuit.clone().add('20', 'minutes').format('HH:mm')
				end = vigilanceNuit.clone().add('40', 'minutes').clone()
			}
			else{
				nom = vigilanceNuit.clone().add('10', 'minutes').format('HH:mm')
				end = vigilanceNuit.clone().add('30', 'minutes').clone()
			}
			// let nom = vigilanceNuit.clone().add('10', 'minutes').format('HH:mm')
			// let end = vigilanceNuit.clone().add('30', 'minutes').clone()
			intervals.push({
				begin: begin,
				nom: nom,
				end: end,
			})
			if(intervals.length == 1)
				vigilanceNuit.add('10', 'minutes')
			vigilanceNuit.add('30', 'minutes')
		}
	}
	return intervals
}

function doVigilancePointeuse(date_vigilance){
	console.log("doVigilancePointeuse")
	poolOvh.query(sqlSelectJourFerie(date_vigilance), [], (err, ferie) => {
		if(err)
			console.error(err)
		else {
			poolOvh.query(sqlSelectGroupVigilance(), [], (err, groups) => {
				if(err)
					console.error(err)
				else {
					console.log("Nb group: " + groups.length)
					poolOvh.query(sqlSelectPointeuse(date_vigilance, (ferie && ferie.length > 0), ), [], (err, pointeuses) => {
						console.log("after query")
						if(err)
							console.error(err)
						else {
							console.log("Nb appareil: " + pointeuses.length)
							const pointeuse_ids = pointeuses.map(p => p.id)
							const site_ids = pointeuses.map(p => p.site_id)
							if(pointeuses.length > 0){
								poolOvh.query(sqlSelectPointage(date_vigilance, pointeuse_ids), [], (err, pointages) => {
									if(err)
										console.error(err)
									else {
										console.log("Nb pointage: " + pointages.length)
										poolOvh.query(sqlSelectAgentPointeuse(pointeuse_ids), [], (err, aps) => {
											if(err)
												console.error(err)
											else {
												aps.forEach(ap => {
													pointages.forEach(ptg => {
														if(ptg.agent_id == ap.agent_id && ptg.pointeuse_id == ap.pointeuse_id)
															ptg.agent_pointeuse_id = ap.id
													})
												})
												poolOvh.query(sqlSelectCommentaire(date_vigilance, pointeuse_ids, site_ids), [], (err, commentaires) => {
													if(err)
														console.error(err)
													else {
														console.log("Nb commentaire: " + commentaires.length)
														poolOvh.query(sqlSelectVigilance(date_vigilance, pointeuse_ids), [], async (err, vigilances) => {
															if(err)
																console.error(err)
															else {
																console.log("Nb vigilance: " + vigilances.length)
																poolOvh.query(sqlSelectCheckphone(date_vigilance, site_ids), [], async (err, checkphones) => {
																	if(err)
																		console.error(err)
																	else {
																		console.log("Nb checkphone: " + checkphones.length)
																		const sites = []
																		pointeuses.map(p => {
																			checkphones.forEach(chk => {
																				if(p.site_id == chk.site_id && !sites.map(s => s.site_id).includes(chk.site_id)){
																					sites.push(p)
																				}
																			})
																			p.agents = []
																			let intervals = getVigilanceInterval(date_vigilance)
																			pointages.map(ptg => {
																				if(!ptg.soft_delete && ptg.pointeuse_id == p.id){ //  && ptg.site_id == p.site_id
																					ptg.intervals = getVigilanceInterval(date_vigilance)
																					ptg.intervals.forEach(itv => {
																						itv.nom = 'X'
																						vigilances.forEach(vg => {
																							let dtarrived = moment(vg.dtarrived)
																							if(vg.agent_id == ptg.agent_id && itv.nom == 'X'
																								&& dtarrived.isAfter(itv.begin) && dtarrived.isBefore(itv.end)){
																									itv.nom = dtarrived.format('HH:mm')
																							}
																						})
																						commentaires.forEach(cm => {
																							if(cm.agent_id == ptg.agent_id 
																								&& moment(cm.date_vigilance).isSame(itv.begin.clone().add(10, "minutes"))){
																								itv.commentaire = cm.objet + ' : ' + cm.commentaire
																							}
																							else if(!cm.agent_id && cm.pointeuse_id == ptg.pointeuse_id ){
																								for(let i=0; i<intervals.length; i++){
																									if(moment(cm.date_vigilance).isSame(intervals[i].begin.clone().add(10, "minutes")))
																										intervals[i].commentaire = cm.objet + ' : ' + cm.commentaire
																								}
																							}
																						})
																					})
																					p.agents.push(ptg)
																				}
																			})
																			p.intervals = intervals
																		})
																		console.log("Nb site: " + sites.length)
																		sites.map(s => {
																			s.intervals = getVigilanceInterval(date_vigilance)
																			s.agents = []
																			pointages.map(ptg => {
																				if(!ptg.soft_delete && ptg.site_id == s.site_id){
																					s.agents.push(ptg)
																				}
																			})
																			s.intervals.forEach(itv => {
																				checkphones.forEach(vg => {
																					let dtarrived = moment(vg.dtarrived)
																					if(vg.site_id == s.site_id && !itv.value 
																						&& dtarrived.isAfter(itv.begin) && dtarrived.isBefore(itv.end))
																					{
																						itv.value = dtarrived.format('HH:mm')
																					}
																				})
																				commentaires.forEach(cm => {
																					if(cm.site_id == s.site_id 
																						&& moment(cm.date_vigilance).isSame(itv.begin.clone().add(10, "minutes")))
																					{
																						itv.commentaire = cm.objet + ' : ' + cm.commentaire
																					}
																				})
																			})
																		})
																		
																		let late_agents = []
																		let not_enrolled_agents = []
																		let not_registered_agents = []
																		let renfort_agents = []
																		let mouvement_agents = []
																		let soon_agents = []
																		let hard_digit_agents = []
																		let broken_device_agents = []
																		let new_install_agents = []
																		let not_define_agents = []
																		let late_opened_agents = []
																		let deleted_pointages = []
																		let no_digit_agents = []
																		let not_enrol_agents = []

																		let pointage_manuelles = []
																		pointages.map(p => { 
																			if(p.soft_delete)
																				deleted_pointages.push(p)
																			else if(!p.dtarrived){
																				if(!p.agent_pointeuse_id){
																					if((p.optic ? p.empreinte_optic : p.empreinte))
																						not_enrol_agents.push(p)
																					else 
																						no_digit_agents.push(p)
																				}
																				else {
																					if(p.type_pointage_id == 1)
																						late_agents.push(p)
																					else if(p.type_pointage_id == 2)
																						not_enrolled_agents.push(p)
																					else if(p.type_pointage_id == 3)
																						not_registered_agents.push(p)
																					else if(p.type_pointage_id == 4)
																						renfort_agents.push(p)
																					else if(p.type_pointage_id == 5)
																						mouvement_agents.push(p)
																					else if(p.type_pointage_id == 6)
																						soon_agents.push(p)
																					else if(p.type_pointage_id == 7)
																						hard_digit_agents.push(p)
																					else if(p.type_pointage_id == 8)
																						broken_device_agents.push(p)
																					else if(p.type_pointage_id == 9)
																						new_install_agents.push(p)
																					else if(p.type_pointage_id == 10)
																						late_opened_agents.push(p)
																					else
																						not_define_agents.push(p)
																				}
																			}
																		}) 
																		
																		if(late_agents.length > 0)
																			pointage_manuelles.push({
																				title: "Agent en retard à la prise de service",
																				list: late_agents
																			})
																		if(not_enrolled_agents.length > 0)
																			pointage_manuelles.push({
																				title: "Agent pas encore enrolé au moment du pointage",
																				list: not_enrolled_agents
																			})
																		if(not_registered_agents.length > 0)
																			pointage_manuelles.push({
																				title: "Agent pas encore enregistré au moment du pointage",
																				list: not_registered_agents
																			})
																		if(renfort_agents.length > 0)
																			pointage_manuelles.push({
																				title: "Agent en renfort",
																				list: renfort_agents
																			})
																		if(mouvement_agents.length > 0)
																			pointage_manuelles.push({
																				title: "Agent en mouvement au moment du pointage",
																				list: mouvement_agents
																			})
																		if(soon_agents.length > 0)
																			pointage_manuelles.push({
																				title: "Pointage trop tôt",
																				list: soon_agents
																			})
																		if(hard_digit_agents.length > 0)
																			pointage_manuelles.push({
																				title: "Empreinte difficile",
																				list: hard_digit_agents
																			})
																		if(broken_device_agents.length > 0)
																			pointage_manuelles.push({
																				title: "Pointeuse en panne",
																				list: broken_device_agents
																			})
																		if(new_install_agents.length > 0)
																			pointage_manuelles.push({
																				title: "Nouvelle installation",
																				list: new_install_agents
																			})
																		if(late_opened_agents.length > 0)
																			pointage_manuelles.push({
																				title: "Site pas encore ouvert au moment du pointage",
																				list: late_opened_agents
																			})
																		if(no_digit_agents.length > 0)
																			pointage_manuelles.push({
																				title: "Agent pas encore enregistré",
																				list: no_digit_agents
																			})
																		if(not_enrol_agents.length > 0)
																			pointage_manuelles.push({
																				title: "Agent pas encore enrollé",
																				list: not_enrol_agents
																			})
																		if(deleted_pointages.length > 0)
																			pointage_manuelles.push({
																				title: "Pointage annulé",
																				list: deleted_pointages
																			})
																		if(not_define_agents.length > 0)
																			pointage_manuelles.push({
																				title: "Pointage manuel non définie",
																				list: not_define_agents
																			})

																		const dateVgString = moment(date_vigilance).format("DD MMMM YYYY")
																			+ ' ' + (moment(date_vigilance).format("HH:mm:ss") == '07:00:00' ? 'Jour' : 'Nuit')
																		
																		const arrayFile = []

																		const workbookAnomalie = new Excel.Workbook()
																		generateAnomalieExcelFile(workbookAnomalie, pointage_manuelles, commentaires)
																		const anomalieSiteBuffer = await workbookAnomalie.xlsx.writeBuffer()
																		arrayFile.push({
																			filename: "Anomalie Pointeuse Biométrique " + dateVgString + ".xlsx",
																			content: anomalieSiteBuffer
																		})

																		const workbookCheckphone = new Excel.Workbook()
																		generateCheckExcelFile(workbookCheckphone, "Rapport Checkphone Pointeuse Biométrique du " + dateVgString, sites)
																		const checkphoneBuffeur = await workbookCheckphone.xlsx.writeBuffer()	
																		arrayFile.push({
																			filename: "Checkphone Pointeuse Biométrique " + dateVgString + ".xlsx",
																			content: checkphoneBuffeur
																		})

																		for(var i=0; i<groups.length; i++){
																			const grp = groups[i]
																			const g_pointeuses = []
																			pointeuses.forEach((p, index) => {
																				if(p.vigilance_group_id == grp.vigilance_group_id)
																					g_pointeuses.push(p)
																			})
																			if(g_pointeuses.length > 0){
																				grp.pointeuses = g_pointeuses
																				const workbookVigilance = new Excel.Workbook()
																				generateVgExcelFile(workbookVigilance, "Rapport Vigilance " + grp.nom + " du " + dateVgString, grp.pointeuses, getVigilanceInterval(date_vigilance))
																				const vigilanceBuffer = await workbookVigilance.xlsx.writeBuffer()
																				arrayFile.push({
																					filename: "Vigilance biométrique " + grp.nom + " " + moment(date_vigilance).format("DD-MM-YY")
																						+ ' ' + (moment(date_vigilance).format("HH:mm:ss") == '07:00:00' ? 'Jour' : 'Nuit') + ".xlsx",
																					content: vigilanceBuffer
																				})
																			}
																		}
																		sendMail(
																			poolAdmin,
																			isTask ? destination_vg : destination_test,
																			"Rapport Pointeuse Biométrique du " + dateVgString, 
																			"Veuillez trouver ci-joint le rapport vigilance et anomalie des pointeuses biométrique.",
																			arrayFile, 
																			(response) => {
																				if(response && isTask){
																					poolOvh.query(sqlUpdateLastDiagExport(date_vigilance), [], (e, r) =>{
																						if(e)
																							console.error(e)
																						else
																							console.log("update last diag export: " + r)
																						process.exit(1)
																					})
																				}
																				else
																					process.exit(1)
																			},
																			isTask
																		)
																	}
																})
															}
														})
													}
												})
											}
										})
									}
								})		
							}
						}
					})
				}
			})
		}
	})
}

if(/^\d{4}-\d{2}-\d{2}$/.test(process.argv[2]) && /^\d{2}:\d{2}:\d{2}$/.test(process.argv[3])){
	console.log("send test...")
	doVigilancePointeuse(process.argv[2] + ' ' + process.argv[3])
}
else if(isTask){
	let date_vigilance = getDayOrNightExport()
	poolOvh.query(sqlSelectDateVigilancePointeuseExport, [], (err, result) => {
		if(err)
			console.error(err)
		else if(result && result[0].value == date_vigilance){
			console.log("export vigilance pointeuse already done!")
			process.exit(1)
		}
		else 
			doVigilancePointeuse(date_vigilance)
	})
}
else
	console.log("please specify command!")