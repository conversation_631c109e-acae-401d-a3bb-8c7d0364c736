const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")
const nodemailer = require("nodemailer")

moment.locale('fr')

const { db_config_zo, db_config_admin, sendMail } = require("../auth")
const poolOvh = mysql.createPool(db_config_zo)
const poolAdmin = mysql.createPool(db_config_admin)


const sqlSelectDateDiagExport = "SELECT value FROM params p WHERE p.key = 'last_export_diag_biometrique'"
const sqlUpdateLastDiagExport = "UPDATE params p SET p.value = DATE_FORMAT(NOW(), '%Y-%m-%d') WHERE p.key = 'last_export_diag_biometrique'"

const isTask = (process.argv[2] == "task")
const destination_diag = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>"]
if (moment().day() == 2)
    destination_diag.concat(["<EMAIL>", "<EMAIL>"])
const destination_test = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
// const destination_test = ["<EMAIL>"]

function capitalizeFirstLetter(string) {
    const arrayString = string.split(' ').map((s) => (
        s.trim().charAt(0).toUpperCase() + s.trim().slice(1).toLowerCase()
    ))
    return arrayString.join(' ')
}

function generateDiagExcelFile(workbook, header, data, region) {
    const borderStyle = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
    }
    const fontHeader = { size: 16, bold: true }
    const fontBold = { bold: true }
    const fontRow = { color: { argb: 'FFe91e63' } }
    const alignmentStyle = { vertical: 'middle', horizontal: 'center' }

    const fillHeader = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '88888888' }
    }
    const fillTitle = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '88336666' }
    }

    const worksheet = workbook.addWorksheet(region)
    worksheet.getColumn('A').width = 30
    worksheet.getColumn('B').width = 50
    worksheet.getColumn('C').width = 30
    worksheet.getColumn('D').width = 15
    worksheet.getColumn('E').width = 15
    worksheet.getColumn('F').width = 30
    worksheet.getColumn('G').width = 15
    worksheet.getColumn('H').width = 15
    worksheet.getColumn('I').width = 30

    worksheet.mergeCells('A1:F1')
    worksheet.getCell('A1').value = header + " (" + data.length + " sites)"
    worksheet.getCell('A1').font = fontHeader
    worksheet.getCell('A1').alignment = alignmentStyle

    worksheet.getCell('A2').value = "Client"
    worksheet.getCell('A2').border = borderStyle
    worksheet.getCell('A2').fill = fillHeader
    worksheet.getCell('A2').font = fontBold
    worksheet.getCell('A2').alignment = alignmentStyle
    worksheet.mergeCells('A2:A3')

    worksheet.getCell('B2').value = "Site"
    worksheet.getCell('B2').border = borderStyle
    worksheet.getCell('B2').fill = fillHeader
    worksheet.getCell('B2').font = fontBold
    worksheet.getCell('B2').alignment = alignmentStyle
    worksheet.mergeCells('B2:B3')

    worksheet.getCell('C2').value = "Dernière transmission SMS"
    worksheet.getCell('C2').border = borderStyle
    worksheet.getCell('C2').fill = fillHeader
    worksheet.getCell('C2').font = fontBold
    worksheet.getCell('C2').alignment = alignmentStyle
    worksheet.mergeCells('C2:E2')
    worksheet.getCell('C3').value = "Signal"
    worksheet.getCell('C3').border = borderStyle
    worksheet.getCell('C3').fill = fillHeader
    worksheet.getCell('C3').font = fontBold
    worksheet.getCell('C3').alignment = alignmentStyle
    worksheet.getCell('D3').value = "Date"
    worksheet.getCell('D3').border = borderStyle
    worksheet.getCell('D3').fill = fillHeader
    worksheet.getCell('D3').font = fontBold
    worksheet.getCell('D3').alignment = alignmentStyle
    worksheet.getCell('E3').value = "Heure"
    worksheet.getCell('E3').border = borderStyle
    worksheet.getCell('E3').fill = fillHeader
    worksheet.getCell('E3').font = fontBold
    worksheet.getCell('E3').alignment = alignmentStyle

    worksheet.getCell('F2').value = "Dernière transmission GPRS"
    worksheet.getCell('F2').border = borderStyle
    worksheet.getCell('F2').fill = fillHeader
    worksheet.getCell('F2').font = fontBold
    worksheet.getCell('F2').alignment = alignmentStyle
    worksheet.mergeCells('F2:H2')
    worksheet.getCell('F3').value = "Signal"
    worksheet.getCell('F3').border = borderStyle
    worksheet.getCell('F3').fill = fillHeader
    worksheet.getCell('F3').font = fontBold
    worksheet.getCell('F3').alignment = alignmentStyle
    worksheet.getCell('G3').value = "Date"
    worksheet.getCell('G3').border = borderStyle
    worksheet.getCell('G3').fill = fillHeader
    worksheet.getCell('G3').font = fontBold
    worksheet.getCell('G3').alignment = alignmentStyle
    worksheet.getCell('H3').value = "Heure"
    worksheet.getCell('H3').border = borderStyle
    worksheet.getCell('H3').fill = fillHeader
    worksheet.getCell('H3').font = fontBold
    worksheet.getCell('H3').alignment = alignmentStyle

    worksheet.getCell('I2').value = "Puce"
    worksheet.getCell('I2').border = borderStyle
    worksheet.getCell('I2').fill = fillHeader
    worksheet.getCell('I2').font = fontBold
    worksheet.getCell('I2').alignment = alignmentStyle
    worksheet.mergeCells('I2:I3')

    let professionnel = 1
    worksheet.getCell('A4').value = "PROFESSIONNEL"
    worksheet.getCell('A4').border = borderStyle
    worksheet.getCell('A4').font = fontBold
    worksheet.getCell('A4').alignment = alignmentStyle
    worksheet.getCell('A4').fill = fillTitle
    worksheet.mergeCells('A4:I4')

    let line = 5
    data.forEach(row => {
        if (row.professionnel && professionnel != row.professionnel) {
            worksheet.getCell('A' + line).value = "PARTICULIER"
            worksheet.getCell('A' + line).border = borderStyle
            worksheet.getCell('A' + line).font = fontBold
            worksheet.getCell('A' + line).alignment = alignmentStyle
            worksheet.getCell('A' + line).fill = fillTitle
            worksheet.mergeCells('A' + line + ':I' + line)
            professionnel = 0
            line++
        }
        // if (moment(row.date_last_signal).isBefore(moment().subtract(24, "hours"))) {
        if (!row.last_transmission_gprs || moment(row.last_transmission_gprs).isBefore(moment().subtract(24, 'hours'))) {
            worksheet.getCell('A' + line).font = fontRow
            worksheet.getCell('B' + line).font = fontRow
            worksheet.getCell('C' + line).font = fontRow
            worksheet.getCell('D' + line).font = fontRow
            worksheet.getCell('E' + line).font = fontRow
            worksheet.getCell('F' + line).font = fontRow
            worksheet.getCell('G' + line).font = fontRow
            worksheet.getCell('H' + line).font = fontRow
            worksheet.getCell('I' + line).font = fontRow
        }
        worksheet.getCell('A' + line).value = row.client
        worksheet.getCell('A' + line).border = borderStyle
        worksheet.getCell('A' + line).alignment = alignmentStyle
        worksheet.getCell('B' + line).value = capitalizeFirstLetter(row.site)
        worksheet.getCell('B' + line).border = borderStyle
        worksheet.getCell('B' + line).alignment = alignmentStyle
        let signal = ''
        if (row.code_sms == '301')
            if (row.eventQualify == 3)
                signal = 'Courant rétablit'
            else signal = 'Coupure de courant JIRAMA 220V'
        else if (row.signal_sms)
            if (row.eventQualify == 3)
                signal = row.signal_sms.replace("Armement/Désarmement", "Armement")
            else
                signal = row.signal_sms.replace("Armement/Désarmement", "Désarmement")
        else if (row.code_sms)
            signal = 'Alarme non définie'

        worksheet.getCell('C' + line).value = capitalizeFirstLetter(signal)
        worksheet.getCell('C' + line).border = borderStyle
        worksheet.getCell('C' + line).alignment = alignmentStyle
        worksheet.getCell('D' + line).value = (row.last_transmission_sms ? moment(row.last_transmission_sms).format('DD-MM-YYYY') : '')
        worksheet.getCell('D' + line).border = borderStyle
        worksheet.getCell('D' + line).alignment = alignmentStyle
        worksheet.getCell('E' + line).value = (row.last_transmission_sms ? moment(row.last_transmission_sms).format('HH:mm:ss') : '')
        worksheet.getCell('E' + line).border = borderStyle
        worksheet.getCell('E' + line).alignment = alignmentStyle

        if (row.code_gprs == '301')
            if (row.eventQualify == 3)
                signal = 'Courant rétablit'
            else signal = 'Coupure de courant JIRAMA 220V'
        else if (row.signal_gprs)
            if (row.eventQualify == 3)
                signal = row.signal_gprs.replace("Armement/Désarmement", "Armement")
            else
                signal = row.signal_gprs.replace("Armement/Désarmement", "Désarmement")
        else if (row.code_sms)
            signal = 'Alarme non définie'

        worksheet.getCell('F' + line).value = capitalizeFirstLetter(signal)
        worksheet.getCell('F' + line).border = borderStyle
        worksheet.getCell('F' + line).alignment = alignmentStyle
        worksheet.getCell('G' + line).value = (row.last_transmission_gprs ? moment(row.last_transmission_gprs).format('DD-MM-YYYY') : '')
        worksheet.getCell('G' + line).border = borderStyle
        worksheet.getCell('G' + line).alignment = alignmentStyle
        worksheet.getCell('H' + line).value = (row.last_transmission_gprs ? moment(row.last_transmission_gprs).format('HH:mm:ss') : '')
        worksheet.getCell('H' + line).border = borderStyle
        worksheet.getCell('H' + line).alignment = alignmentStyle

        worksheet.getCell('I' + line).value = row.puce
        worksheet.getCell('I' + line).border = borderStyle
        worksheet.getCell('I' + line).alignment = alignmentStyle
        line++
    })
}
const sqlSelectDiagExport = `select p.id, p.nom, c.Societe as 'client', s.nom as 'site',
    e1.code as 'code_sms', e1.Description as 'signal_sms', s.last_transmission_sms,
	e2.code as 'code_gprs', e2.Description as 'signal_gprs', s.last_transmission_gprs,
	s.interval_test, p.sim as 'puce', s.professionnel, p.group_diag_id
	from pointeuses p
	left join sites s on s.idsite = p.site_id
	left join clients c on c.idClient = s.idClient
	left join eventcode e1 on e1.code = p.last_event_sms
	left join eventcode e2 on e2.code = p.last_event_gprs
	where (p.soft_delete is null or p.soft_delete = 0)
	order by professionnel desc, s.nom`

function doDiag() {
    console.log("doDiag pointeuse")
    poolOvh.query(sqlSelectDiagExport, [], async (err, result) => {
        console.log("after query")
        if (err)
            console.error(err)
        else if (result.length == 0) {
            console.error("no data fetch")
        }
        else {
            console.log("Nb site: " + result.length)
            let tana = []
            let tamatave = []
            let province = []
            result.map((row) => {
                if (row.group_diag_id == 1)
                    tana.push(row)
                else if (row.group_diag_id == 2)
                    tamatave.push(row)
                else if (row.group_diag_id == 3)
                    province.push(row)
            })
            const workbookTana = new Excel.Workbook()
            generateDiagExcelFile(workbookTana, "DIAGNOSTIQUE TANA " + moment().format("DD MMMM YYYY"), tana, "Tana")
            generateDiagExcelFile(workbookTana, "DIAGNOSTIQUE TAMATAVE " + moment().format("DD MMMM YYYY"), tamatave, "Tamatave")
            generateDiagExcelFile(workbookTana, "DIAGNOSTIQUE PROVINCE " + moment().format("DD MMMM YYYY"), province, "Province")
            const tanaSiteBuffer = await workbookTana.xlsx.writeBuffer()
            sendMail(
                poolAdmin,
                isTask ? destination_diag : destination_test,
                "Rapport diagnostique Pointeuse " + moment().format('DD-MM-YY'),
                "Veuillez trouver ci-joint les rapports diagnostiques Tana, Tamatave et Province.",
                [
                    {
                        filename: "Rapport diagnostique Pointeuse " + moment().format("DD-MM-YYYY") + ".xlsx",
                        content: tanaSiteBuffer
                    },
                ],
                (response) => {
                    if (response && isTask)
                        poolOvh.query(sqlUpdateLastDiagExport, [], (e, r) => {
                            if (e)
                                console.error(e)
                            else
                                console.log("update last diag export: " + r)
                            process.exit(1)
                        })
                    else
                        process.exit(1)
                },
                isTask
            )
        }
    })
}

if (process.argv[2] == 'test') {
    console.log("send test...")
    doDiag()
}
else if (isTask) {
    if (moment().isAfter(moment().set({ hour: 7, minute: 0 }))) {
        poolOvh.query(sqlSelectDateDiagExport, [], (err, result) => {
            if (err)
                console.error(err)
            else if (result && moment().format("YYYY-MM-DD") == result[0].value) {
                console.log("export diag already done!")
                process.exit()
            }
            else
                doDiag()
        })
    }
    else
        console.log("skip diag...")
}
else
    console.log("please specify command!")
