const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")

moment.locale('fr')

const {db_config_zo, db_config_admin, sendMail} = require("../auth")
const poolOvh = mysql.createPool(db_config_zo)
const poolAdmin = mysql.createPool(db_config_admin)

const sqlSelectDateDiagExport = "SELECT value FROM params p WHERE p.key = 'last_export_diag'"
const sqlUpdateLastDiagExport = "UPDATE params p SET p.value = ? WHERE p.key = 'last_export_diag'"

const isTask = (process.argv[2] == "task")
const destination_diag = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
	"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
	"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
if(moment().day() == 2) {
	const destination_direction = ["<EMAIL>", "<EMAIL>"]
	destination_direction.forEach(dest => destination_diag.push(dest))
}
const destination_test = ["<EMAIL>", "<EMAIL>"]

function capitalizeFirstLetter(string) {
	const  arrayString = string.split(' ').map((s) => (
		s.trim().charAt(0).toUpperCase() + s.trim().slice(1).toLowerCase()
	))
	return arrayString.join(' ')
}
function generateDiagExcelFile(workbook, sites){
	const tana = sites.filter(s => s.group_diag_id == 1)
	const tamatave = sites.filter(s => s.group_diag_id == 2)
	const province = sites.filter(s => s.group_diag_id == 3)
	generateWorkSheet(workbook, "TANA", tana)
	generateWorkSheet(workbook, "TAMATAVE", tamatave)
	generateWorkSheet(workbook, "PROVINCE", province)
}
function generateWorkSheet(workbook, header, data){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
	const fontHeader = { size: 16, bold: true }
	const fontBold = { bold: true }
	const fontError = { color: { argb: 'FFe91e63' } }
	const fontWarning = { color: { argb: 'FFfb8c00' } }
	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	const alignLeftStyle = { vertical: 'middle' }
	
	const fillHeader = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'88888888'}
	}
	const fillTitle = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'88336666'}
	}

	const worksheet = workbook.addWorksheet(header)
	worksheet.getColumn('A').width = 15
	worksheet.getColumn('B').width = 15
	worksheet.getColumn('C').width = 50
	worksheet.getColumn('D').width = 30
	worksheet.getColumn('E').width = 15
	worksheet.getColumn('F').width = 15
	worksheet.getColumn('G').width = 30
	worksheet.getColumn('H').width = 15
	worksheet.getColumn('I').width = 15
	worksheet.getColumn('J').width = 25
	worksheet.getColumn('K').width = 20

	worksheet.mergeCells('A1:K1')
	worksheet.getCell('A1').value = "DIAGNOSTIQUE " + header + " " + moment().format("DD MMMM YYYY") + " (" + data.length + " sites)"
	worksheet.getCell('A1').font = fontHeader
	worksheet.getCell('A1').alignment = alignmentStyle
	worksheet.getCell('A2').value = "Prom"
	worksheet.getCell('A2').border = borderStyle
	worksheet.getCell('A2').fill = fillHeader
	worksheet.getCell('A2').font = fontBold
	worksheet.getCell('A2').alignment = alignmentStyle
	worksheet.mergeCells('A2:A3')
	worksheet.getCell('B2').value = "Puce"
	worksheet.getCell('B2').border = borderStyle
	worksheet.getCell('B2').fill = fillHeader
	worksheet.getCell('B2').font = fontBold
	worksheet.getCell('B2').alignment = alignmentStyle
	worksheet.mergeCells('B2:B3')
	worksheet.getCell('C2').value = "Site"
	worksheet.getCell('C2').border = borderStyle
	worksheet.getCell('C2').fill = fillHeader
	worksheet.getCell('C2').font = fontBold
	worksheet.getCell('C2').alignment = alignmentStyle
	worksheet.mergeCells('C2:C3')
	worksheet.getCell('D2').value = "Transmission SMS"
	worksheet.getCell('D2').border = borderStyle
	worksheet.getCell('D2').fill = fillHeader
	worksheet.getCell('D2').font = fontBold
	worksheet.getCell('D2').alignment = alignmentStyle
	worksheet.mergeCells('D2:F2')
	worksheet.getCell('D3').value = "Signal"
	worksheet.getCell('D3').border = borderStyle
	worksheet.getCell('D3').fill = fillHeader
	worksheet.getCell('D3').font = fontBold
	worksheet.getCell('D3').alignment = alignmentStyle
	worksheet.getCell('E3').value = "Date"
	worksheet.getCell('E3').border = borderStyle
	worksheet.getCell('E3').fill = fillHeader
	worksheet.getCell('E3').font = fontBold
	worksheet.getCell('E3').alignment = alignmentStyle
	worksheet.getCell('F3').value = "Heure"
	worksheet.getCell('F3').border = borderStyle
	worksheet.getCell('F3').fill = fillHeader
	worksheet.getCell('F3').font = fontBold
	worksheet.getCell('F3').alignment = alignmentStyle

	worksheet.getCell('G2').value = "Transmission GPRS"
	worksheet.getCell('G2').border = borderStyle
	worksheet.getCell('G2').fill = fillHeader
	worksheet.getCell('G2').font = fontBold
	worksheet.getCell('G2').alignment = alignmentStyle
	worksheet.mergeCells('G2:I2')
	worksheet.getCell('G3').value = "Signal"
	worksheet.getCell('G3').border = borderStyle
	worksheet.getCell('G3').fill = fillHeader
	worksheet.getCell('G3').font = fontBold
	worksheet.getCell('G3').alignment = alignmentStyle
	worksheet.getCell('H3').value = "Date"
	worksheet.getCell('H3').border = borderStyle
	worksheet.getCell('H3').fill = fillHeader
	worksheet.getCell('H3').font = fontBold
	worksheet.getCell('H3').alignment = alignmentStyle
	worksheet.getCell('I3').value = "Heure"
	worksheet.getCell('I3').border = borderStyle
	worksheet.getCell('I3').fill = fillHeader
	worksheet.getCell('I3').font = fontBold
	worksheet.getCell('I3').alignment = alignmentStyle

	worksheet.getCell('J2').value = "Observation"
	worksheet.getCell('J2').border = borderStyle
	worksheet.getCell('J2').fill = fillHeader
	worksheet.getCell('J2').font = fontBold
	worksheet.getCell('J2').alignment = alignmentStyle
	worksheet.getCell('K2').border = borderStyle
	worksheet.getCell('K2').fill = fillHeader
	worksheet.getCell('K2').font = fontBold
	worksheet.getCell('K2').alignment = alignmentStyle
	worksheet.mergeCells('J2:K3')

	let professionnel = 1
	worksheet.getCell('A4').value = "PROFESSIONNEL"
	worksheet.getCell('A4').border = borderStyle
	worksheet.getCell('A4').font = fontBold
	worksheet.getCell('A4').alignment = alignmentStyle
	worksheet.getCell('A4').fill = fillTitle
	worksheet.mergeCells('A4:K4')
	
	let line = 5
	data.forEach(row => {
		if(row.professionnel != null && professionnel != row.professionnel){
			worksheet.getCell('A' + line).value = "PARTICULIER"
			worksheet.getCell('A' + line).border = borderStyle
			worksheet.getCell('A' + line).font = fontBold
			worksheet.getCell('A' + line).alignment = alignmentStyle
			worksheet.getCell('A' + line).fill = fillTitle
			worksheet.mergeCells('A' + line + ':K' + line)
			professionnel = 0
			line++
		}
		if(row.prom == row.puce){
			if(!row.last_transmission_sms || moment(row.last_transmission_sms).isBefore(moment().subtract(row.interval_test, 'hour'))){
				worksheet.getCell('A' + line).font = fontError
				worksheet.getCell('B' + line).font = fontError
				worksheet.getCell('C' + line).font = fontError
				worksheet.getCell('D' + line).font = fontError
				worksheet.getCell('E' + line).font = fontError
				worksheet.getCell('F' + line).font = fontError
				worksheet.getCell('G' + line).font = fontError
				worksheet.getCell('H' + line).font = fontError
				worksheet.getCell('I' + line).font = fontError
				worksheet.getCell('J' + line).font = fontError
				worksheet.getCell('K' + line).font = fontError
			}
		}
		else {
			if( (!row.last_transmission_sms || moment(row.last_transmission_sms).isBefore(moment().subtract(row.interval_test, 'hour')))
				&& (!row.last_transmission_gprs  || moment(row.last_transmission_gprs).isBefore(moment().subtract(row.interval_test, 'hour')))
			){
				worksheet.getCell('A' + line).font = fontError
				worksheet.getCell('B' + line).font = fontError
				worksheet.getCell('C' + line).font = fontError
				worksheet.getCell('D' + line).font = fontError
				worksheet.getCell('E' + line).font = fontError
				worksheet.getCell('F' + line).font = fontError
				worksheet.getCell('G' + line).font = fontError
				worksheet.getCell('H' + line).font = fontError
				worksheet.getCell('I' + line).font = fontError
				worksheet.getCell('J' + line).font = fontError
				worksheet.getCell('K' + line).font = fontError
			}
			else if(!row.last_transmission_sms || !row.last_transmission_gprs
				|| moment(row.last_transmission_sms).isBefore(moment().subtract(row.interval_test, 'hour'))
				|| moment(row.last_transmission_gprs).isBefore(moment().subtract(row.interval_test, 'hour'))
			){
				worksheet.getCell('A' + line).font = fontWarning
				worksheet.getCell('B' + line).font = fontWarning
				worksheet.getCell('C' + line).font = fontWarning
				worksheet.getCell('D' + line).font = fontWarning
				worksheet.getCell('E' + line).font = fontWarning
				worksheet.getCell('F' + line).font = fontWarning
				worksheet.getCell('G' + line).font = fontWarning
				worksheet.getCell('H' + line).font = fontWarning
				worksheet.getCell('I' + line).font = fontWarning
				worksheet.getCell('J' + line).font = fontWarning
				worksheet.getCell('K' + line).font = fontWarning
			}
		}
		worksheet.getCell('A' + line).value = row.prom
		worksheet.getCell('A' + line).border = borderStyle
		worksheet.getCell('A' + line).alignment = alignmentStyle
		worksheet.getCell('B' + line).value = row.puce
		worksheet.getCell('B' + line).border = borderStyle
		worksheet.getCell('B' + line).alignment = alignmentStyle
		worksheet.getCell('C' + line).value = capitalizeFirstLetter(row.site)
		worksheet.getCell('C' + line).border = borderStyle
		worksheet.getCell('C' + line).alignment = alignLeftStyle
		let signal = ''
        if(row.code_sms == '301')
            if(row.eventQualify == 3)
				signal = 'Courant rétablit'
            else signal = 'Coupure de courant JIRAMA 220V'
        else if(row.signal_sms)
            if(row.eventQualify == 3)
				signal = row.signal_sms.replace("Armement/Désarmement", "Armement")
            else
				signal = row.signal_sms.replace("Armement/Désarmement", "Désarmement")
        else if(row.code_sms)
			signal = 'Alarme non définie'
		worksheet.getCell('D' + line).value = capitalizeFirstLetter(signal)
		worksheet.getCell('D' + line).border = borderStyle
		worksheet.getCell('D' + line).alignment = alignLeftStyle
		worksheet.getCell('E' + line).value = (row.last_transmission_sms ? moment(row.last_transmission_sms).format('DD-MM-YYYY') : '')
		worksheet.getCell('E' + line).border = borderStyle
		worksheet.getCell('E' + line).alignment = alignmentStyle
		worksheet.getCell('F' + line).value = (row.last_transmission_sms ? moment(row.last_transmission_sms).format('HH:mm:ss') : '')
		worksheet.getCell('F' + line).border = borderStyle
		worksheet.getCell('F' + line).alignment = alignmentStyle


		if(row.prom != row.puce){
			if(row.code_gprs == '301')
				if(row.eventQualify == 3)
					signal = 'Courant rétablit'
				else signal = 'Coupure de courant JIRAMA 220V'
			else if(row.signal_gprs)
				if(row.eventQualify == 3)
					signal = row.signal_gprs.replace("Armement/Désarmement", "Armement")
				else
					signal = row.signal_gprs.replace("Armement/Désarmement", "Désarmement")
			else if(row.code_gprs)
				signal = 'Alarme non définie'
			worksheet.getCell('G' + line).value = capitalizeFirstLetter(signal)
			worksheet.getCell('G' + line).border = borderStyle
			worksheet.getCell('G' + line).alignment = alignLeftStyle
			worksheet.getCell('H' + line).value = (row.last_transmission_gprs ? moment(row.last_transmission_gprs).format('DD-MM-YYYY') : '')
			worksheet.getCell('H' + line).border = borderStyle
			worksheet.getCell('H' + line).alignment = alignmentStyle
			worksheet.getCell('I' + line).value = (row.last_transmission_gprs ? moment(row.last_transmission_gprs).format('HH:mm:ss') : '')
			worksheet.getCell('I' + line).border = borderStyle
			worksheet.getCell('I' + line).alignment = alignmentStyle
		}
		else {
			worksheet.getCell('G' + line).border = borderStyle
			worksheet.getCell('H' + line).border = borderStyle
			worksheet.getCell('I' + line).border = borderStyle
		}

		worksheet.getCell('J' + line).value = row.diagnostique
		worksheet.getCell('J' + line).border = borderStyle
		worksheet.getCell('J' + line).alignment = alignmentStyle
		worksheet.getCell('K' + line).value = (row.joignable ? 'JOIGNABLE' : 'INJOIGNABLE')
		worksheet.getCell('K' + line).border = borderStyle
		worksheet.getCell('K' + line).alignment = alignmentStyle
		line++
	})
}
const sqlSelectDiagExport = `select prom, c.Societe as 'client', s.nom as 'site', 
	e1.code as 'code_sms', e1.Description as 'signal_sms', s.last_transmission_sms,
	e2.code as 'code_gprs', e2.Description as 'signal_gprs', s.last_transmission_gprs,
	diagnostique_id, d.nom as 'diagnostique', interval_test, joignable, numeropuces as 'puce',  professionnel, group_diag_id
	from sites s 
	left join clients c on c.idClient = s.idClient
	left join group_diag_sites gd on gd.id = s.group_diag_id
	left join diagnostiques d on d.id = s.diagnostique_id
	left join eventcode e1 on e1.code = s.last_event_sms
	left join eventcode e2 on e2.code = s.last_event_gprs
	where (soft_delete is null or soft_delete = 0)
	and (without_system is null or without_system = 0)
	and (centrale_drx is null or centrale_drx = 0)
	order by professionnel desc, s.nom`

function doDiag(dateString){
	console.log("doDiag")
	poolOvh.query(sqlSelectDiagExport, [], async (err, result) => {
		console.log("after query")
		if(err)
			console.error(err)
		else if(result.length == 0){
			console.error("no data fetch")
		}
		else{
			console.log("Nb site: " + result.length)

			const nbSite = result.length
			let nbSitePanne = 0
			let nbGprsSite = 0
			let nbGprsNoSmsSite = 0
			let nbSmsSite = 0

			result.forEach(row => {
				if(row.prom == row.puce){
					nbSmsSite++
					if(!row.last_transmission_sms || moment(row.last_transmission_sms).isBefore(moment().subtract(row.interval_test, 'hour'))){
						nbSitePanne++
					}
				}
				else {
					nbGprsSite++
					if( (!row.last_transmission_sms || moment(row.last_transmission_sms).isBefore(moment().subtract(row.interval_test, 'hour')))
						&& (!row.last_transmission_gprs  || moment(row.last_transmission_gprs).isBefore(moment().subtract(row.interval_test, 'hour')))
					){
						nbSitePanne++
					}
					else if(!row.last_transmission_sms || !row.last_transmission_gprs
					//	|| moment(row.last_transmission_sms).isBefore(moment().subtract(row.interval_test, 'hour'))
					//	|| moment(row.last_transmission_gprs).isBefore(moment().subtract(row.interval_test, 'hour'))
					){
						nbGprsNoSmsSite++	
					}
				}
			})
			const site12 = result.filter((s) => s.interval_test == 12)
			const workbook12 = new Excel.Workbook()
			generateDiagExcelFile(workbook12, site12)
			const site12Buffer = await workbook12.xlsx.writeBuffer()
			const site24 = result.filter((s) => s.interval_test == 24)
			const workbook24 = new Excel.Workbook()
			generateDiagExcelFile(workbook24, site24)
			const site24Buffer = await workbook24.xlsx.writeBuffer()
			sendMail(
				poolAdmin,
				isTask ? destination_diag : destination_test,
				"Rapport de diagnostique " + moment().format('DD-MMM-YYYY'), 
				`
				<p>Veuillez trouver ci-joint les rapports diagnostiques Tana, Tamatave et Province.<p>
				<ul>
					<li>Nombre de centrale Data sans récepteur SMS : ${nbGprsNoSmsSite}/${nbGprsSite}</li>
					<li>Nombre de centrale en panne : ${nbSitePanne}/${nbSite}</li>
				</ul>
				`,
				[
					{
						filename: "Rapport diagnostique Pack Alarme" + ".xlsx",
						content: site12Buffer
					},
					{
						filename: "Rapport diagnostique K3" + ".xlsx",
						content: site24Buffer
					},
				], 
				(response) => {
					if(response && isTask)
						poolOvh.query(sqlUpdateLastDiagExport, [dateString], (e, r) =>{
							if(e)
								console.error(e)
							else
								console.log("update last diag export: " + r)
							process.exit(1)
						})
					else 
						process.exit(1)
				},
				isTask
			)
		}
	})
}

if(process.argv[2] == 'test'){
	console.log("send test...")
	doDiag()
}
else if(isTask){
	if(moment().isAfter(moment().set({hour: 15, minute: 0}))){
		poolOvh.query(sqlSelectDateDiagExport, [], (err, result) => {
			if(err)
				console.error(err)
			else if(result && moment().format("YYYY-MM-DD") + " 15:00:00" == result[0].value){
				console.log("export diag pack alarme already done!")
				process.exit()
			}
			else
				doDiag(moment().format("YYYY-MM-DD") + " 15:00:00")
		})
	}
	else if(moment().isAfter(moment().set({hour: 7, minute: 0}))){
		poolOvh.query(sqlSelectDateDiagExport, [], (err, result) => {
			if(err)
				console.error(err)
			else if(result && moment().format("YYYY-MM-DD") + " 07:00:00" == result[0].value){
				console.log("export diag pack alarme already done!")
				process.exit()
			}
			else
				doDiag(moment().format("YYYY-MM-DD") + " 07:00:00")
		})
	}
	else 
		console.log("skip diag 12 ...")
}
else
	console.log("please specify command!")