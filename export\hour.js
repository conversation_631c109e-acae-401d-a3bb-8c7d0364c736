const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")
const nodemailer = require("nodemailer")

moment.locale('fr')

let year = ''
let month = ''

const {db_config_zo, auth_mail_tls} = require("../auth")
const pool = mysql.createPool(db_config_zo)

let transporter = nodemailer.createTransport({
	host: "ssl0.ovh.net",
	port: 465,
	secure: true, // upgrade later with STARTTLS
	auth: auth_mail_tls,
    tls: {
        rejectUnauthorized: false
    }
  })
const destination_test = {
	to: "<EMAIL>",
}

function sendMail(destination, subject, text, attachements, callback){
	const message = {
		from: "<EMAIL>",
		to: destination.to,
		cc: destination.cc,
		subject: subject,
		html: "<p>Bonjour,</p>" + 
			"<p>" + text + "</p>" +
			"<p>Cordialement,</p>",
		attachments: attachements
	};
	transporter.sendMail(message , (err, info) => {
		if(err)
			console.error(err)
		else console.log(info)
		callback()
	})
}

function capitalizeFirstLetter(string) {
	const  arrayString = string.split(' ').map((s) => (
		s.trim().charAt(0).toUpperCase() + s.trim().slice(1).toLowerCase()
	))
	return arrayString.join(' ')
}
function generateHourAgentSiteExcelFile(workbook, secteur, sites, agents){
	const header = secteur.nom.toUpperCase()
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
	const fontHeader = { size: 16, bold: true }
	const fontBold = { bold: true }
	const fontGreen = { color: { argb: 'FF336666' } }
	const fontRed = { color: { argb: 'FFe91e63' } }
	const alignmentStyle = { horizontal: 'center' }
	const alignRight = { horizontal: 'right' }
	
	const fillHeader = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'77888888'}
	}
	const fillGreen = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'77336666'}
	}
	const fillRed = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'77e91e63'}
	}
	const fillHour = {
		type: 'pattern',
		pattern:'solid',
		fgColor: {argb: '779c27b0'}
	}
	const fillAgent = {
		type: 'pattern',
		pattern:'solid',
		fgColor: {argb: '77336666'}
	}
	
    const fillNotSetBill = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'88ffc107'}
	}

	const worksheet = workbook.addWorksheet(header, {
		properties:{
			tabColor:{
				argb:((secteur.total_hour_facture - secteur.total_hour_agent >= 0) ? '77336666' : '77e91e63')
			}
		}
	})
	worksheet.getColumn('A').width = 10
	worksheet.getColumn('B').width = 50
	worksheet.getColumn('C').width = 40
	worksheet.getColumn('D').width = 15
	worksheet.getColumn('E').width = 15
	worksheet.getColumn('F').width = 15
	worksheet.getColumn('G').width = 15
	worksheet.getColumn('H').width = 15
	worksheet.getColumn('I').width = 15
	worksheet.getColumn('J').width = 15
	worksheet.getColumn('K').width = 15

	worksheet.mergeCells('A1:J1')
	worksheet.getCell('A1').value = header + " (" + sites.length + " sites)"
	worksheet.getCell('A1').font = fontHeader
	
	worksheet.getCell('A2').value = "Base de calcul pour le nombre d'heure contrat: "
	worksheet.mergeCells('A2:B2')
	worksheet.getCell('C2').value = secteur.heure_contrat + ' heures'

	worksheet.getCell('A4').value = "Nombre d'agent selon la facturation: "
	worksheet.mergeCells('A4:B4')
	worksheet.getCell('C4').value = sites.map(s => s.nb_agent_facturation).reduce((a, b) => a+b, 0).toFixed(2)
	worksheet.getCell('A5').value = "Nombre d'agent selon le pointage: "
	worksheet.mergeCells('A5:B5')
	worksheet.getCell('C5').value = sites.map(s => s.nb_agent_pointage).reduce((a, b) => a+b, 0).toFixed(2)
	worksheet.getCell('A6').value = "Nombre d'agent total embauché: "
	worksheet.mergeCells('A6:B6')
	worksheet.getCell('C6').value = sites.map(s => s.nb_agent).reduce((a, b) => a+b, 0).toFixed(2)
	
	let line = 8
	worksheet.getCell('A' + line).value = "ID"
	worksheet.getCell('A' + line).border = borderStyle
	worksheet.getCell('A' + line).fill = fillHeader
	worksheet.getCell('A' + line).font = fontBold
	worksheet.getCell('A' + line).alignment = alignmentStyle
	worksheet.mergeCells('A' + line + ':A' + (line + 1))
	worksheet.getCell('B' + line).value = "Nom"
	worksheet.getCell('B' + line).border = borderStyle
	worksheet.getCell('B' + line).fill = fillHeader
	worksheet.getCell('B' + line).font = fontBold
	worksheet.getCell('B' + line).alignment = alignmentStyle
	worksheet.mergeCells('B' + line + ':B' + (line + 1))
	worksheet.getCell('C' + line).value = "Horaire"
	worksheet.getCell('C' + line).border = borderStyle
	worksheet.getCell('C' + line).fill = fillHeader
	worksheet.getCell('C' + line).font = fontBold
	worksheet.getCell('C' + line).alignment = alignmentStyle
	worksheet.mergeCells('C' + line + ':C' + (line + 1))
	worksheet.getCell('D' + line).value = "Nb. agent jour"
	worksheet.getCell('D' + line).border = borderStyle
	worksheet.getCell('D' + line).fill = fillHeader
	worksheet.getCell('D' + line).font = fontBold
	worksheet.getCell('D' + line).alignment = alignmentStyle
	worksheet.mergeCells('D' + line + ':D' + (line + 1))
	worksheet.getCell('E' + line).value = "Nb. agent nuit"
	worksheet.getCell('E' + line).border = borderStyle
	worksheet.getCell('E' + line).fill = fillHeader
	worksheet.getCell('E' + line).font = fontBold
	worksheet.getCell('E' + line).alignment = alignmentStyle
	worksheet.mergeCells('E' + line + ':E' + (line + 1))

	worksheet.getCell('F' + line).value = "Nombre d'heure"
	worksheet.getCell('F' + line).border = borderStyle
	worksheet.getCell('F' + line).alignment = alignmentStyle
	worksheet.getCell('F' + line).fill = fillHour
	worksheet.getCell('F' + line).font = fontBold
	worksheet.mergeCells('F' + line + ':H' + line)
	worksheet.getCell('F' + (line + 1)).alignment = alignmentStyle
	worksheet.getCell('F' + (line + 1)).value = "H. facturé"
	worksheet.getCell('F' + (line + 1)).border = borderStyle
	worksheet.getCell('F' + (line + 1)).fill = fillHour
	worksheet.getCell('F' + (line + 1)).font = fontBold
	worksheet.getCell('F' + (line + 1)).alignment = alignmentStyle
	worksheet.getCell('G' + (line + 1)).value = "H. travaillé"
	worksheet.getCell('G' + (line + 1)).border = borderStyle
	worksheet.getCell('G' + (line + 1)).fill = fillHour
	worksheet.getCell('G' + (line + 1)).font = fontBold
	worksheet.getCell('G' + (line + 1)).alignment = alignmentStyle
	worksheet.getCell('H' + (line + 1)).value = "Difference"
	worksheet.getCell('H' + (line + 1)).border = borderStyle
	worksheet.getCell('H' + (line + 1)).fill = fillHour
	worksheet.getCell('H' + (line + 1)).font = fontBold
	worksheet.getCell('H' + (line + 1)).alignment = alignmentStyle
	
	worksheet.getCell('I' + line).value = "Nombre d'agent selon:"
	worksheet.getCell('I' + line).border = borderStyle
	worksheet.getCell('I' + line).alignment = alignmentStyle
	worksheet.getCell('I' + line).fill = fillAgent
	worksheet.getCell('I' + line).font = fontBold
	worksheet.mergeCells('I' + line + ':K' + line)
	worksheet.getCell('I' + (line + 1)).alignment = alignmentStyle
	worksheet.getCell('I' + (line + 1)).value = "La facturation"
	worksheet.getCell('I' + (line + 1)).border = borderStyle
	worksheet.getCell('I' + (line + 1)).fill = fillAgent
	worksheet.getCell('I' + (line + 1)).font = fontBold
	worksheet.getCell('I' + (line + 1)).alignment = alignmentStyle
	worksheet.getCell('J' + (line + 1)).value = "Le pointage"
	worksheet.getCell('J' + (line + 1)).border = borderStyle
	worksheet.getCell('J' + (line + 1)).fill = fillAgent
	worksheet.getCell('J' + (line + 1)).font = fontBold
	worksheet.getCell('J' + (line + 1)).alignment = alignmentStyle
	worksheet.getCell('K' + (line + 1)).value = "L'embauche"
	worksheet.getCell('K' + (line + 1)).border = borderStyle
	worksheet.getCell('K' + (line + 1)).fill = fillAgent
	worksheet.getCell('K' + (line + 1)).font = fontBold
	worksheet.getCell('K' + (line + 1)).alignment = alignmentStyle
	line += 2

	sites.forEach(row => {
		if(!row.total_hour_facturation){
			worksheet.getCell('A' + line).fill = fillNotSetBill
			worksheet.getCell('B' + line).fill = fillNotSetBill
			worksheet.getCell('C' + line).fill = fillNotSetBill
			worksheet.getCell('D' + line).fill = fillNotSetBill
			worksheet.getCell('E' + line).fill = fillNotSetBill
			worksheet.getCell('F' + line).fill = fillNotSetBill
			worksheet.getCell('G' + line).fill = fillNotSetBill
			worksheet.getCell('H' + line).fill = fillNotSetBill
			worksheet.getCell('I' + line).fill = fillNotSetBill
			worksheet.getCell('J' + line).fill = fillNotSetBill
			worksheet.getCell('K' + line).fill = fillNotSetBill
		}
        else if(Math.abs(row.total_hour_facturation - row.total_hour_agent) > 24) {
            if(row.total_hour_facturation > row.total_hour_agent) {
                worksheet.getCell('A' + line).font = fontGreen
                worksheet.getCell('B' + line).font = fontGreen
                worksheet.getCell('C' + line).font = fontGreen
                worksheet.getCell('D' + line).font = fontGreen
                worksheet.getCell('E' + line).font = fontGreen
                worksheet.getCell('F' + line).font = fontGreen
                worksheet.getCell('G' + line).font = fontGreen
                worksheet.getCell('H' + line).font = fontGreen
                worksheet.getCell('I' + line).font = fontGreen
                worksheet.getCell('J' + line).font = fontGreen
                worksheet.getCell('K' + line).font = fontGreen
            }
            else {
                worksheet.getCell('A' + line).font = fontRed
                worksheet.getCell('B' + line).font = fontRed
                worksheet.getCell('C' + line).font = fontRed
                worksheet.getCell('D' + line).font = fontRed
                worksheet.getCell('E' + line).font = fontRed
                worksheet.getCell('F' + line).font = fontRed
                worksheet.getCell('G' + line).font = fontRed
                worksheet.getCell('H' + line).font = fontRed
                worksheet.getCell('I' + line).font = fontRed
                worksheet.getCell('J' + line).font = fontRed
                worksheet.getCell('K' + line).font = fontRed
            }
        }
		worksheet.getCell('A' + line).value = row.idsite
		worksheet.getCell('A' + line).border = borderStyle
		worksheet.getCell('A' + line).alignment = alignmentStyle
		worksheet.getCell('B' + line).value = capitalizeFirstLetter(row.nom)
		worksheet.getCell('B' + line).border = borderStyle
		worksheet.getCell('C' + line).value = row.horaire
		worksheet.getCell('C' + line).border = borderStyle
		worksheet.getCell('C' + line).alignment = alignmentStyle
		worksheet.getCell('D' + line).value = row.nb_agent_day
		worksheet.getCell('D' + line).border = borderStyle
		worksheet.getCell('D' + line).alignment = alignmentStyle
		worksheet.getCell('E' + line).value = row.nb_agent_night
		worksheet.getCell('E' + line).border = borderStyle
		worksheet.getCell('E' + line).alignment = alignmentStyle
		worksheet.getCell('F' + line).value = row.total_hour_facturation
		worksheet.getCell('F' + line).border = borderStyle
		worksheet.getCell('G' + line).value = row.total_hour_agent
		worksheet.getCell('G' + line).border = borderStyle
		worksheet.getCell('H' + line).value = row.total_hour_facturation - row.total_hour_agent
		worksheet.getCell('H' + line).border = borderStyle
		worksheet.getCell('I' + line).value = row.nb_agent_facturation.toFixed(2)
		worksheet.getCell('I' + line).alignment = alignRight
		worksheet.getCell('I' + line).border = borderStyle
		worksheet.getCell('J' + line).value = row.nb_agent_pointage.toFixed(2)
		worksheet.getCell('J' + line).alignment = alignRight
		worksheet.getCell('J' + line).border = borderStyle
		worksheet.getCell('K' + line).value = row.nb_agent
		worksheet.getCell('K' + line).border = borderStyle
		line++
	})

	worksheet.getCell('F' + line).value = secteur.total_hour_facture
	worksheet.getCell('F' + line).border = borderStyle
	worksheet.getCell('F' + line).fill = fillHeader
	worksheet.getCell('G' + line).value = secteur.total_hour_agent
	worksheet.getCell('G' + line).border = borderStyle
	worksheet.getCell('G' + line).fill = fillHeader
	let diff = secteur.total_hour_facture - secteur.total_hour_agent
	worksheet.getCell('H' + line).value = diff
	worksheet.getCell('H' + line).border = borderStyle
	worksheet.getCell('H' + line).fill = fillHeader
	worksheet.getCell('I' + line).value = secteur.total_hour_facture > 0 ? ((diff/secteur.total_hour_facture*100).toFixed(2) + "%") :'X'
	worksheet.getCell('I' + line).border = borderStyle
	worksheet.getCell('I' + line).fill = diff >= 0 ? fillGreen : fillRed
	worksheet.getCell('I' + line).alignment = alignmentStyle
	line++

	line++
	worksheet.getCell('A' + line).value = 'Agents'
	worksheet.mergeCells('A' + line + ':J' + line)
	worksheet.getCell('A' + line).font = fontHeader
	
	line ++
	worksheet.getCell('A' + line).value = 'Nom'
	worksheet.getCell('A' + line).border = borderStyle
	worksheet.getCell('A' + line).fill = fillHeader
	worksheet.getCell('A' + line).font = fontBold
	worksheet.mergeCells('A' + line + ':B' + line)
	worksheet.getCell('C' + line).value = 'Site'
	worksheet.getCell('C' + line).border = borderStyle
	worksheet.getCell('C' + line).fill = fillHeader
	worksheet.getCell('C' + line).font = fontBold
	worksheet.mergeCells('C' + line + ':D' + line)

	line++
	agents.forEach((row) => {
		worksheet.getCell('A' + line).value = '[' + (
			row.societe_id == 1 ? 'DGM-' + row.numero_employe :
			row.societe_id == 2 ? 'SOIT-' + row.num_emp_soit : 
			row.societe_id == 3 ? 'ST-' + row.numero_stagiaire :
			row.societe_id == 4 ? 'SM' :
			row.numero_employe ? row.numero_employe :
			row.numero_stagiaire ? row.numero_stagiaire : 'Ndf')
			+ '] ' + row.nom
		worksheet.getCell('A' + line).border = borderStyle
		worksheet.mergeCells('A' + line + ':B' + line)
		
		worksheet.getCell('C' + line).value = row.site
		worksheet.getCell('C' + line).border = borderStyle
		worksheet.mergeCells('C' + line + ':D' + line)
		line++
	})
}

function generateGlobalSecteurExcelFile(worksheet, secteurs){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
	const fontHeader = { size: 16, bold: true }
	const fontBold = { bold: true }
	const fontRed = { color: { argb: 'FFe91e63' } }
	const alignmentStyle = { horizontal: 'center' }
	
	const fillHeader = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'77888888'}
	}
	worksheet.getColumn('A').width = 40
	worksheet.getColumn('B').width = 15
	worksheet.getColumn('C').width = 15
	worksheet.getColumn('D').width = 15
	worksheet.getColumn('E').width = 15
	worksheet.getColumn('F').width = 10

	worksheet.mergeCells('A1:F1')
	worksheet.getCell('A1').value = "SECTEURS"
	worksheet.getCell('A1').font = fontHeader
	
	let line = 3
	worksheet.getCell('A' + line).value = "Secteur"
	worksheet.getCell('A' + line).border = borderStyle
	worksheet.getCell('A' + line).fill = fillHeader
	worksheet.getCell('A' + line).font = fontBold
	worksheet.getCell('A' + line).alignment = alignmentStyle
	worksheet.getCell('B' + line).value = "Nb site"
	worksheet.getCell('B' + line).border = borderStyle
	worksheet.getCell('B' + line).fill = fillHeader
	worksheet.getCell('B' + line).font = fontBold
	worksheet.getCell('B' + line).alignment = alignmentStyle
	worksheet.getCell('C' + line).value = "Tot H. Facturé"
	worksheet.getCell('C' + line).border = borderStyle
	worksheet.getCell('C' + line).fill = fillHeader
	worksheet.getCell('C' + line).font = fontBold
	worksheet.getCell('C' + line).alignment = alignmentStyle
	worksheet.getCell('D' + line).value = "Tot H. Travaillé"
	worksheet.getCell('D' + line).border = borderStyle
	worksheet.getCell('D' + line).fill = fillHeader
	worksheet.getCell('D' + line).font = fontBold
	worksheet.getCell('D' + line).alignment = alignmentStyle
	worksheet.getCell('E' + line).value = "Différence"
	worksheet.getCell('E' + line).border = borderStyle
	worksheet.getCell('E' + line).fill = fillHeader
	worksheet.getCell('E' + line).font = fontBold
	worksheet.getCell('E' + line).alignment = alignmentStyle
	worksheet.getCell('F' + line).value = "Pourcentage"
	worksheet.getCell('F' + line).border = borderStyle
	worksheet.getCell('F' + line).fill = fillHeader
	worksheet.getCell('F' + line).font = fontBold
	worksheet.getCell('F' + line).alignment = alignmentStyle
	line++

	secteurs.forEach(row => {
		if(row.total_hour_facture && row.total_hour_agent){
			if(row.total_hour_facture < row.total_hour_agent){
				worksheet.getCell('A' + line).font = fontRed
				worksheet.getCell('B' + line).font = fontRed
				worksheet.getCell('C' + line).font = fontRed
				worksheet.getCell('D' + line).font = fontRed
				worksheet.getCell('E' + line).font = fontRed
				worksheet.getCell('F' + line).font = fontRed
			}
			worksheet.getCell('A' + line).value = capitalizeFirstLetter(row.nom)
			worksheet.getCell('A' + line).border = borderStyle
			worksheet.getCell('B' + line).value = row.nb_site
			worksheet.getCell('B' + line).border = borderStyle
			worksheet.getCell('B' + line).alignment = alignmentStyle
			worksheet.getCell('C' + line).value = row.total_hour_facture
			worksheet.getCell('C' + line).border = borderStyle
			worksheet.getCell('D' + line).value = row.total_hour_agent
			worksheet.getCell('D' + line).border = borderStyle
			const diff = row.total_hour_facture - row.total_hour_agent
			worksheet.getCell('E' + line).value = diff
			worksheet.getCell('E' + line).border = borderStyle
			worksheet.getCell('F' + line).value = row.total_hour_facture > 0 ? ((diff/row.total_hour_facture*100).toFixed(2) + "%") : "X"
			worksheet.getCell('F' + line).border = borderStyle
			worksheet.getCell('F' + line).alignment = alignmentStyle
			line++
		}
	})
}

const sqlSelectSecteur = "select id, nom, heure_contrat from secteurs order by nom"
function sqlSelectSite(id, year, month){
	console.log(id + ": " + year + "-" + ("0" + month).slice(-2) + "-01 00:00:00")
	const begin = moment(year + "-" + ("0" + month).slice(-2) + "-01 00:00:00")
	const end = begin.clone().add({month: 1}).subtract({day: 1})
	return "select s.idsite, s.nom, s.nb_agent_day, s.nb_agent_night, h.nom as 'horaire', " +
		"COALESCE(s.total_hour, 0) as 'total_hour_facturation' from pointages p " +
		"LEFT JOIN sites s ON s.idsite = p.site_id " +
		"LEFT JOIN horaires h ON h.id = s.horaire_pointage_id " +
		"where p.date_pointage > '"+ begin.format("YYYY-MM-DD HH:mm:ss") +"' and date_pointage < '"+ end.format("YYYY-MM-DD") +" 23:00:00'" +
		"and (p.soft_delete is null or p.soft_delete = 0) " +
		"and s.secteur_id = " + id + " " +
		"group by p.site_id " +
		"order by nom "
}
function sqlSelectSiteNullSecteur(year, month){
	console.log(year + "-" + ("0" + month).slice(-2) + "-01 00:00:00")
	const begin = moment(year + "-" + ("0" + month).slice(-2) + "-01 00:00:00")
	const end = begin.clone().add({month: 1}).subtract({day: 1})
	return "select s.nom from pointages p " +
		"LEFT JOIN sites s ON s.idsite = p.site_id " +
		"where p.date_pointage > '"+ begin.format("YYYY-MM-DD HH:mm:ss") +"' and p.date_pointage < '"+ end.format("YYYY-MM-DD") +" 23:00:00'" +
		"and (p.soft_delete is null or p.soft_delete = 0) and (s.secteur_id is null or s.secteur_id = 0) " +
		"group by p.site_id " +
		"order by nom "
}
function sqlSelectAgent(site_ids){
	return "select a.id, a.nom, s.nom as 'site', a.real_site_id as 'site_id', " +
		"a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit from agents a " +
		"left join sites s ON s.idsite = a.real_site_id " +
		"where (a.soft_delete is null or a.soft_delete = 0) and " +
		"a.real_site_id in (" + site_ids.join(',') + ") " +
		"order by s.nom"
}
function sqlSelectPointage(year, month){
	const begin = moment(year + "-" + ("0" + month).slice(-2) + "-01 00:00:00")
	const end = begin.clone().add({month: 1}).subtract({day: 1})
	return "select id, site_id from pointages p " +
    	"where (p.soft_delete is null or p.soft_delete = 0) " +
		"and date_pointage > '"+ begin.format("YYYY-MM-DD HH:mm:ss") +"' and date_pointage < '"+ end.format("YYYY-MM-DD") +" 23:00:00'"
}
function generateSecteurSheet(secteurs, countSecteur, workbook, firstWorksheet){
	pool.query(sqlSelectSite(secteurs[countSecteur].id, year, month), [], async (err, sites) => {
		if(err) console.error(err)
		else {
			console.log("Nb site: " + sites.length)
			pool.query(sqlSelectPointage(year, month), [], async (err, pointages) => {
				if(err)
					console.error(err)
				else {
					if(sites.length > 0){
						console.log("Nb pointage: " + pointages.length)
						pool.query(sqlSelectAgent(sites.map(s => s.idsite)), [], async (err, agents) => {
							console.log("Nb agent: " + agents.length)
							sites.map( st => {
								st.count_agent = 0
								st.nb_agent = 0
							})
							sites.map(st => {
								for(let i=0; i<pointages.length; i++){
									if(st.idsite == pointages[i].site_id){
										st.count_agent = st.count_agent + 1
										pointages.splice(i, 1)
										i--
									}
								}
								agents.map(ag => {
									if(st.idsite == ag.site_id){
										st.nb_agent = st.nb_agent + 1
									}
								})
							})
							let total_hour_facture = 0
							let total_hour_agent = 0
							sites.map( st => {
								st.total_hour_agent = st.count_agent * 12
								st.nb_agent_pointage = st.count_agent * 12 / secteurs[countSecteur].heure_contrat
								st.nb_agent_facturation = st.total_hour_facturation / secteurs[countSecteur].heure_contrat
								total_hour_agent += st.total_hour_agent
								total_hour_facture += st.total_hour_facturation
							})
							secteurs[countSecteur].nb_site = sites.length
							secteurs[countSecteur].total_hour_agent = total_hour_agent
							secteurs[countSecteur].total_hour_facture = total_hour_facture
							generateHourAgentSiteExcelFile(workbook, secteurs[countSecteur], sites, agents)
							if(countSecteur == secteurs.length - 1){
								generateGlobalSecteurExcelFile(firstWorksheet, secteurs)
							}
							
							countSecteur++
							if(countSecteur < secteurs.length)
								generateSecteurSheet(secteurs, countSecteur, workbook, firstWorksheet)
							else{
								const siteBuffer = await workbook.xlsx.writeBuffer()
								const monthName = moment(year + "-" + ("00" + month).slice(-2) + "-01").format("MMMM YYYY").toUpperCase()
								sendMail(
									destination_test,
									"Comparatif Facturation Pointage " + monthName, 
									"Veuillez trouver ci-joint joint les rapports de comparatif du nombre d'heure (et nombre d'agent) facturé par rapport aux services fournis.",
									[
										{
											filename: "Comparatif Facturation Pointage " + monthName + ".xlsx",
											content: siteBuffer
										},
									], 
									() => {
										process.exit(1)
									}
								)
							}
						})
					}
					else {
						countSecteur++
						if(countSecteur < secteurs.length)
							generateSecteurSheet(secteurs, countSecteur, workbook, firstWorksheet)
						else {
							const siteBuffer = await workbook.xlsx.writeBuffer()
							const monthName = moment(year + "-" + ("00" + month).slice(-2) + "-01").format("MMMM YYYY").toUpperCase()
							sendMail(
								destination_test,
								"Comparatif Facturation Pointage " + monthName, 
								"Veuillez trouver ci-joint joint les rapports de comparatif du nombre d'heure (et nombre d'agent) facturé par rapport aux services fournis.",
								[
									{
										filename: "Comparatif Facturation Pointage " + monthName + ".xlsx",
										content: siteBuffer
									},
								]
							)
						}
					}
				}
			
			})
		}
	})
}
function doExport(){
	pool.query(sqlSelectSiteNullSecteur(year, month), [], async (err, nullSites) => {
		if(err) console.error(err)
		else {
			console.log("Nb site null: " + nullSites.length)
			if(nullSites.length > 0){
				console.log("-------\n")
				nullSites.map(s => {
					console.log(s.nom)
				})
				console.log("\n\nClear this list to continue...")
			}
			else {
				pool.query(sqlSelectSecteur, [], async (err, secteurs) => {
					if(err) console.error(err)
					else {
						console.log("Nb secteurs: " + secteurs.length)
						let workbook = new Excel.Workbook()
						workbook.views = [
							{
							x: 0, y: 0, width: 10000, height: 20000,
							firstSheet: secteurs.length -1, activeTab: secteurs.length, visibility: 'visible'
							}
						]
						let countSecteur = 0
						const firstWorksheet = workbook.addWorksheet("SECTEURS")
						generateSecteurSheet(secteurs, countSecteur, workbook, firstWorksheet)
					}
				})
			}
		}
	})
}
	
if(process.argv[2] != "task"){
	if(!process.argv[2])
		console.log("year required")
	else if(!/^\d{4}$/.test(process.argv[2]))
		console.log("year bad format")
	else if(!process.argv[3])
		console.log("month required")
	else if(!/^\d{1,2}$/.test(process.argv[3]))
		console.log("month bad format")
	else {
		year = process.argv[2]
		month = process.argv[3]
		doExport()
	}
}
