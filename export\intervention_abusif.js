const moment = require('moment')
const mysql = require('mysql')
const Excel = require("exceljs")
const nodemailer = require("nodemailer")

moment.locale('fr')


const {db_config_ovh, auth_mail_tls} = require("../auth")
const pool = mysql.createPool(db_config_ovh)

let transporter = nodemailer.createTransport({
	host: "ssl0.ovh.net",
	port: 465,
	secure: true, // upgrade later with STARTTLS
	auth: auth_mail_tls,
	tls: {
		rejectUnauthorized: false
	}
  })

const sqlSelectDateDiagExport = "SELECT value FROM params p WHERE p.key = 'last_export_interv_client'"
const sqlUpdateLastDiagExport = "UPDATE params p SET p.value = ? WHERE p.key = 'last_export_interv_client'"

const destination_diag = {
	to: "og<PERSON><PERSON><EMAIL>,de",
    cc: "<EMAIL>"
}
const destination_test = {
	to: "<EMAIL>",
}

function sendMail(destination, subject, text, attachements, callback){
	const message = {
		from: "<EMAIL>",
		to: destination.to,
		cc: destination.cc,
		subject: subject,
		html: "<p>Bonjour,</p>" + 
			"<p>" + text + "</p>" +
			"<p>Cordialement,</p>",
		attachments: attachements
	};
	transporter.sendMail(message , (err, info) => {
		if(err)
			console.error(err)
		else console.log(info)
		callback()
	})
}

function capitalizeFirstLetter(string) {
	const  arrayString = string.split(' ').map((s) => (
		s.trim().charAt(0).toUpperCase() + s.trim().slice(1).toLowerCase()
	))
	return arrayString.join(' ')
}
function generateInterventionClientExcelFile(workbook, header, sites){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
	const fontHeader = { size: 16, bold: true }
	const fontBold = { bold: true }
	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	
	const fillHeader = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'88888888'}
	}
	const fillVg = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'88aaaaaa'}
	}
	const worksheet = workbook.addWorksheet("INTERVENTION")
	worksheet.getColumn('A').width = 70
	worksheet.getColumn('B').width = 15
	worksheet.getColumn('C').width = 50

	worksheet.mergeCells('A1:C1')
	worksheet.getCell('A1').value = header + " (" + sites.length + " sites)"
	worksheet.getCell('A1').font = fontHeader
	worksheet.getCell('A1').alignment = alignmentStyle
	worksheet.getCell('A2').value = "Site"
	worksheet.getCell('A2').border = borderStyle
	worksheet.getCell('A2').fill = fillHeader
	worksheet.getCell('A2').font = fontBold
	worksheet.getCell('B2').value = "Nb intervention"
	worksheet.getCell('B2').border = borderStyle
	worksheet.getCell('B2').fill = fillHeader
	worksheet.getCell('B2').font = fontBold
	worksheet.getCell('B2').alignment = alignmentStyle
	worksheet.getCell('C2').value = "Vigilance"
	worksheet.getCell('C2').border = borderStyle
	worksheet.getCell('C2').fill = fillHeader
	worksheet.getCell('C2').font = fontBold
	worksheet.getCell('C2').alignment = alignmentStyle
	
    function addDetailFile(site, index){
        const worksheet = workbook.addWorksheet(index + " " + site.nom)
        worksheet.getColumn('A').width = 30
        worksheet.getColumn('B').width = 12
        worksheet.getColumn('C').width = 12
        worksheet.getColumn('D').width = 8
        worksheet.getColumn('E').width = 12
        worksheet.getColumn('F').width = 70
    
        worksheet.mergeCells('A1:F1')
        worksheet.getCell('A1').value = site.nom + " (" + site.interventions.length + " interventions)"
        worksheet.getCell('A1').font = fontHeader
        worksheet.getCell('A1').alignment = alignmentStyle
        worksheet.getCell('A2').value = "Du"
        worksheet.getCell('A2').border = borderStyle
        worksheet.getCell('A2').fill = fillHeader
        worksheet.getCell('A2').font = fontBold
        worksheet.getCell('B2').value = "Départ"
        worksheet.getCell('B2').border = borderStyle
        worksheet.getCell('B2').fill = fillHeader
        worksheet.getCell('B2').font = fontBold
        worksheet.getCell('B2').alignment = alignmentStyle
        worksheet.getCell('C2').value = "Arrivée"
        worksheet.getCell('C2').border = borderStyle
        worksheet.getCell('C2').fill = fillHeader
        worksheet.getCell('C2').font = fontBold
        worksheet.getCell('C2').alignment = alignmentStyle
        worksheet.getCell('D2').value = "Durée"
        worksheet.getCell('D2').border = borderStyle
        worksheet.getCell('D2').fill = fillHeader
        worksheet.getCell('D2').font = fontBold
        worksheet.getCell('D2').alignment = alignmentStyle
        worksheet.getCell('E2').value = "Fin"
        worksheet.getCell('E2').border = borderStyle
        worksheet.getCell('E2').fill = fillHeader
        worksheet.getCell('E2').font = fontBold
        worksheet.getCell('E2').alignment = alignmentStyle
        worksheet.getCell('F2').value = "Commentaire"
        worksheet.getCell('F2').border = borderStyle
        worksheet.getCell('F2').fill = fillHeader
        worksheet.getCell('F2').font = fontBold
        worksheet.getCell('F2').alignment = alignmentStyle

        let line = 3
        site.interventions.forEach(row => {
            worksheet.getCell('A' + line).value = moment(row.created_at).format("ddd DD MMM YYYY, HH:mm")
            worksheet.getCell('A' + line).border = borderStyle
            worksheet.getCell('B' + line).value = moment(row.depart).format("HH:mm")
            worksheet.getCell('B' + line).border = borderStyle
            worksheet.getCell('B' + line).alignment = alignmentStyle
            worksheet.getCell('C' + line).value = moment(row.arrivee).format("HH:mm")
            worksheet.getCell('C' + line).border = borderStyle
            worksheet.getCell('C' + line).alignment = alignmentStyle
            worksheet.getCell('D' + line).value = Number.parseInt(moment.duration(moment(row.arrivee).diff(moment(row.depart))).asMinutes()) + " min"
            worksheet.getCell('D' + line).border = borderStyle
            worksheet.getCell('D' + line).alignment = alignmentStyle
            worksheet.getCell('E' + line).value = moment(row.fin).format("HH:mm")
            worksheet.getCell('E' + line).border = borderStyle
            worksheet.getCell('E' + line).alignment = alignmentStyle
            worksheet.getCell('F' + line).value = row.commentaire.substring(0, 70) + "..."
            worksheet.getCell('F' + line).border = borderStyle
            line++
        })
    }
	let line = 3
    sites = sites.sort((a, b) => (b.interventions.length - a.interventions.length))
	sites.forEach((row, index) => {
        /*if(row.horaire){
            worksheet.getCell('A' + line).fill = fillVg
            worksheet.getCell('B' + line).fill = fillVg
            worksheet.getCell('C' + line).fill = fillVg
        }*/
		worksheet.getCell('A' + line).value = capitalizeFirstLetter(row.nom)
		worksheet.getCell('A' + line).border = borderStyle
		worksheet.getCell('B' + line).value = row.interventions.length
		worksheet.getCell('B' + line).border = borderStyle
		worksheet.getCell('B' + line).alignment = alignmentStyle
		worksheet.getCell('C' + line).value = row.horaire
		worksheet.getCell('C' + line).border = borderStyle
        addDetailFile(row, index + 1)
		line++
	})

}

const sqlSelectSiteWithBadTransmitter = "SELECT r.id, r.site_id, s.nom as 'site', r.depart, r.arrivee, r.fin, r.created_at, " + 
    "h.nom as 'horaire', r.commentaire, s.vigilance, s.pointage, s.pointeuse " +
	"FROM rapports r " +
	"LEFT JOIN sites s ON s.idsite = r.site_id " +
	"LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id " +
	"WHERE (r.soft_delete is null or r.soft_delete = 0) and r.type_rapport_id = 1 " +
    "and  r.created_at >= ? and r.created_at < ?"

function doIntereventionClient(year, month){
	console.log("doIntereventionClient")
    const begin = year + "-" + month + "-01 00:00:00"
    const end = moment(year + "-" + month + "-01 00:00:00").add(1, "month").format("YYYY-MM-DD HH:mm:ss")
    console.log(begin, end)
	pool.query(sqlSelectSiteWithBadTransmitter, [begin, end], async (err, interventions) => {
		console.log("after query")
		if(err)
			console.error(err)
		else {
			console.log("Nb intervention : " + interventions.length)
			let sites = []
            interventions.forEach(interv => {
                if(!sites.map(site => site.id).includes(interv.site_id))
                    sites.push({
                        id: interv.site_id,
                        nom: interv.site,
                        horaire: interv.horaire,
                        interventions: []
                    })
            })
            sites.forEach(site => {
                interventions.forEach(interv => {
                    if(site.id == interv.site_id)
                        site.interventions.push(interv)
                })
            })
            console.log(sites.length)
			const workbookTana = new Excel.Workbook()
			generateInterventionClientExcelFile(workbookTana, "Nombre d'intervention par client " + moment().format("MMMM YYYY").toUpperCase(), sites)
			const tanaSiteBuffer = await workbookTana.xlsx.writeBuffer()

			sendMail(
				(process.argv[2] != 'task') ? destination_test : destination_diag,
				"Nombre d'intervention par client mois de " + moment(begin).format('MMMM YYYY').toUpperCase(), 
				"Veuillez trouver ci-joint joint la liste des interventions effectuées durant le mois de " + moment(begin).format('MMMM YYYY').toUpperCase() + " par ordre des plus abusifs.",
				[
					{
						filename: "Nombre d'intervention par client " + moment(begin).format("MMMM YYYY").toUpperCase() + ".xlsx",
						content: tanaSiteBuffer
					},
				], 
				() => {
					if(process.argv[2] == 'task'){
                        pool.query(sqlUpdateLastDiagExport, [year + " " + month], (e, r) =>{
                            if(e)
                                console.error(e)
                            else
                                console.log("update last diag export: " + r)
							process.exit(1)
                        })
					}
					else
						process.exit(1)
				}
			)
		}
	})
}

if(/^[0-9]{4}$/g.exec(process.argv[2]) && /^[0-9]{2}$/g.exec(process.argv[3])){
	console.log("send test...")
	doIntereventionClient(process.argv[2], process.argv[3])
}
else if(process.argv[2] == 'task'){
	if(moment().format("D") == 1 && moment().isAfter(moment().set({hour: 7, minute: 0}))){
		pool.query(sqlSelectDateDiagExport, [], (err, result) => {
			if(err)
				console.error(err)
			else if(result && moment().subtract(1, "month").format("YYYY MM") == result[0].value){
				console.log("export diag already done!")
				process.exit(1)
			}
			else doIntereventionClient(moment().subtract(1, "month").format("YYYY"), moment().subtract(1, "month").format("MM"))
		})
	}
	else 
		console.log("skip nb interv...")
}
else
	console.log("please specify command!")
