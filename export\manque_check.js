const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")

moment.locale('fr')

const {db_config_zo, db_config_admin, sendMail} = require("../auth")
const poolOvh = mysql.createPool(db_config_zo)
const poolAdmin = mysql.createPool(db_config_admin)

function getDayOrNightExport(){
	let beginDay = moment().set({hour:6, minute:50, second:0})
	let endDay = moment().set({hour:18, minute:50, second:0})
	if(moment().isAfter(beginDay) && moment().isBefore(endDay))
		return moment().subtract(1, 'day').format("YYYY-MM-DD") + " 18:00:00"
	else {
		if(moment().isBefore(beginDay))
			return moment().subtract(1, 'day').format("YYYY-MM-DD") + " 07:00:00"
		return moment().format("YYYY-MM-DD") + " 07:00:00"
	}
}

const sqlSelectExportBouton = "SELECT value FROM params p WHERE p.key = 'last_export_manque_vigilance'"
function sqlUpdateLastExport(dateString){
	return "UPDATE params p SET p.value = '" + dateString + "' " +
		"WHERE p.key = 'last_export_manque_vigilance'"
}

const isTask = (process.argv[2] == 'task')
const destination_test = ["<EMAIL>", "<EMAIL>"]

const destination_task = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
	"<EMAIL>", "<EMAIL>", "<EMAIL>,<EMAIL>", "<EMAIL>", 
	"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", 
	"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
	"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>" , "<EMAIL>", 
	"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
	"<EMAIL>", "<EMAIL>","<EMAIL>", "<EMAIL>",
	"<EMAIL>", "<EMAIL>", "<EMAIL>"]

function generateVgExcelFile(workbook, header, sites){
	const cols = ['B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 
	'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 
	'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ',]
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
	const fontHeader = { size: 16, bold: true }
	const fontBold = { bold: true }
	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	const fillRed = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'fff44336'}
	}
	const fillHeader = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'ffbbbbbb'}
	}

	let	line = 3
	const worksheet = workbook.addWorksheet("MANQUE")
	worksheet.getColumn('A').width = 20
	cols.forEach(col => {
		worksheet.getColumn(col).width = 10
	})

	console.log(header)
	
	worksheet.mergeCells('A1:' + cols[cols.length - 1] + '1')
	worksheet.getCell('A1').value = header + " (" + sites.length + " sites)"
	worksheet.getCell('A1').font = fontHeader
	
	sites.forEach(s => {
		worksheet.mergeCells('A'+ line +':' + cols[cols.length - 1] + line)
		worksheet.getCell('A' + line).value = s.nom + (s.commentaire ? ' - ' + s.commentaire : '')
		worksheet.getCell('A' + line).font = fontBold
		
		line++
		worksheet.getCell('A' + line).value = s.prom
		worksheet.mergeCells('B'+ line +':D' + line)
		worksheet.getCell('B' + line).value = s.centrale
		if(s.last_sms_check){
			worksheet.mergeCells('E'+ line +':' + cols[cols.length - 1] + line)
			worksheet.getCell('E' + line).value = moment(s.last_sms_check).format("DD MMM YYYY")
		}

		line++
		worksheet.getCell("A" + line).value = "Vigilance"
		worksheet.getCell("A" + line).border = borderStyle
		worksheet.getCell("A" + line).fill = fillHeader
		worksheet.getCell("A" + (line+1)).value = "Heure"
		worksheet.getCell("A" + (line+1)).border = borderStyle
		s.intervals.forEach((itv, itvIndex) => {
			worksheet.getCell(cols[itvIndex] + line).value = itv.nom
			worksheet.getCell(cols[itvIndex] + line).alignment = alignmentStyle
			worksheet.getCell(cols[itvIndex] + line).border = borderStyle
			worksheet.getCell(cols[itvIndex] + line).fill = fillHeader
			worksheet.getCell(cols[itvIndex] + (line+1)).value = itv.value ? itv.value : 'X'
			worksheet.getCell(cols[itvIndex] + (line+1)).alignment = alignmentStyle
			worksheet.getCell(cols[itvIndex] + (line+1)).border = borderStyle
			if(!s.check || (s.check && itv.has_check))
				worksheet.getCell(cols[itvIndex] + (line+1)).fill = fillRed
			if(itv.commentaire)
				worksheet.getCell(cols[itvIndex] + line).note = itv.commentaire
		})

		line++
		line = line+2
    })

	sites = sites.filter(s => {
		if(s.check && s.last_sms_check && moment(s.last_sms_check).isAfter(moment().subtract(2, "day"))){
			s.intervals = s.intervals.filter(itv => itv.has_check)
			if(s.intervals.length > 2) return true
			else if(s.intervals.length == 2)
				return s.intervals[0].end.format("HH:mm") == s.intervals[1].begin.format("HH:mm")
			return false
		}
		else
			return true
	})
	const worksheet2 = workbook.addWorksheet("CHECK VALIDE")
	worksheet2.getColumn('A').width = 20
	cols.forEach(col => {
		worksheet2.getColumn(col).width = 10
	})
	
	worksheet2.mergeCells('A1:' + cols[cols.length - 1] + '1')
	worksheet2.getCell('A1').value = header + " (" + sites.length + " sites)"
	worksheet2.getCell('A1').font = fontHeader

	line = 3
	sites.forEach(s => {
		worksheet2.mergeCells('A'+ line +':' + cols[cols.length - 1] + line)
		worksheet2.getCell('A' + line).value = s.nom + (s.commentaire ? ' - ' + s.commentaire : '')
		worksheet2.getCell('A' + line).font = fontBold
		
		line++
		worksheet2.getCell('A' + line).value = s.prom
		worksheet2.mergeCells('B'+ line +':D' + line)
		worksheet2.getCell('B' + line).value = s.centrale
		if(s.last_sms_check){
			worksheet2.mergeCells('E'+ line +':' + cols[cols.length - 1] + line)
			worksheet2.getCell('E' + line).value = moment(s.last_sms_check).format("DD MMM YYYY")
		}
		
		s.agents.forEach((ag) => {
			line++
			worksheet2.mergeCells('A'+ line +':' + cols[cols.length - 1] + line)
			worksheet2.getCell('A' + line).value = (
				ag.societe_id == 1 ? 'DGM-' + ag.numero_employe :
				ag.societe_id == 2 ? 'SOIT-' + ag.num_emp_soit :
				ag.societe_id == 3 ? 'ST-' + ag.numero_stagiaire :
				ag.societe_id == 4 ? 'SM' :
				ag.numero_employe ? ag.numero_employe :
				ag.numero_stagiaire ? ag.numero_stagiaire :
				'Ndf'
			) + ' ' + ag.nom
		})

		line++
		worksheet2.getCell("A" + line).value = "Vigilance"
		worksheet2.getCell("A" + line).border = borderStyle
		worksheet2.getCell("A" + line).fill = fillHeader
		worksheet2.getCell("A" + (line+1)).value = "Heure"
		worksheet2.getCell("A" + (line+1)).border = borderStyle
		s.intervals.forEach((itv, itvIndex) => {
			worksheet2.getCell(cols[itvIndex] + line).value = itv.nom
			worksheet2.getCell(cols[itvIndex] + line).alignment = alignmentStyle
			worksheet2.getCell(cols[itvIndex] + line).border = borderStyle
			worksheet2.getCell(cols[itvIndex] + line).fill = fillHeader
			worksheet2.getCell(cols[itvIndex] + (line+1)).value = itv.value ? itv.value : 'X'
			worksheet2.getCell(cols[itvIndex] + (line+1)).alignment = alignmentStyle
			worksheet2.getCell(cols[itvIndex] + (line+1)).border = borderStyle
			if(!s.check || (s.check && itv.has_check))
				worksheet2.getCell(cols[itvIndex] + (line+1)).fill = fillRed
			if(itv.commentaire)
				worksheet2.getCell(cols[itvIndex] + line).note = itv.commentaire
		})

		line++
		line = line+2
    })
}

function sqlSelectJourFerie(date_vigilance){
	return "SELECT id from jour_feries where date = '" + moment(date_vigilance).format("YYYY-MM-DD") + "'"
}

function sqlSelectSite(date_vigilance, ferie) {
	const horaire = (moment(date_vigilance).format('HH:mm:ss') == '07:00:00') ? 'day' : 'night'
	const field = horaire + '_' + moment(date_vigilance).day()
	console.log("isFerie: " + ferie)
	if(ferie)
		return "SELECT s.idsite as 'id', s.nom, s.commentaire, s.phone_agent, s.pointeuse, s.vigilance, s.transmitter, c.check, s.last_sms_check, c.nom as 'centrale', s.prom " +
			"FROM sites s  " +
			"LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id " +
			"LEFT JOIN centrales c ON c.idcentrale = s.idcentrale " +
			"WHERE (s.soft_delete is null or s.soft_delete = 0) " +
			"and s.vigilance = 1 " +
			"and (h.id is null or (h." + field + " is not null and h." + field  + " = 1) or (h." + horaire + "_ferie is not null and h." + horaire + "_ferie = 1)) " +
			"ORDER BY s.group_pointage_id DESC, s.nom"
	else
		return "SELECT s.idsite as 'id', s.nom, s.commentaire, s.phone_agent, s.pointeuse, s.vigilance, s.transmitter, c.check, s.last_sms_check, c.nom as 'centrale', s.prom " +
			"FROM sites s  " +
			"LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id " +
			"LEFT JOIN centrales c ON c.idcentrale = s.idcentrale " +
			"WHERE (s.soft_delete is null or s.soft_delete = 0) " +
			"and s.vigilance = 1 " +
			"and (h.id is null or (h." + field + " is not null and h." + field + " = 1)) " +
			"ORDER BY s.group_pointage_id DESC, s.nom"
}
function sqlSelectPointage(siteIds, date_vigilance) {
	return "SELECT ptg.id, ap.pointeuse_id, ptg.site_id, p.site_id as 'pointeuse_site_id', a.societe_id, a.nom, a.numero_employe, a.num_emp_soit, a.numero_stagiaire, " +
		"ptg.id as 'pointage_id', a.id as agent_id, ptg.dtarrived, s.nom as 'site', ptg.motif, p.transmitter " + 
		"FROM pointages ptg " +
		"LEFT JOIN agents a ON a.id = ptg.agent_id " +
		"LEFT JOIN sites s ON s.idsite = ptg.site_id " +
		"LEFT JOIN pointeuses p ON p.id = ptg.pointeuse_id " +
		"LEFT JOIN agent_pointeuses ap ON ap.agent_id = ptg.agent_id and ap.pointeuse_id = ptg.pointeuse_id " +
		"WHERE (ptg.soft_delete is null or ptg.soft_delete = 0) " +
		"and ptg.vigilance = 1 " +
		"and ptg.site_id in (" + siteIds.join(',') + ") " +
		"and ptg.date_pointage = '" + date_vigilance + "' " +
		"group by ptg.agent_id"
}
function sqlSelectCommentaire(date_vigilance) {
	let begin_date = ''
	let end_date = ''
	if(moment(date_vigilance).format('HH:mm:ss') == '07:00:00'){
		begin_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 05:50:00'
		end_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 17:50:00'
	}
	else {
		begin_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 17:50:00'
		end_date = moment(date_vigilance).add(1, 'day').format('YYYY-MM-DD') + ' 05:50:00'
	}
	return "SELECT c.site_id, c.agent_id, c.pointeuse_id, c.commentaire, c.objet, c.date_vigilance " +
		"FROM v_commentaires c " +
		"where c.date_vigilance >= '" + begin_date + "' " +
		"and c.date_vigilance < '" + end_date + "' " +
		"order by c.date_vigilance"
}
function sqlSelectVigilance(siteIds, date_vigilance) {
	let begin_date = ''
	let end_date = ''
	if(moment(date_vigilance).format('HH:mm:ss') == '07:00:00'){
		begin_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 05:50:00'
		end_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 17:50:00'
	}
	else {
		begin_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 17:50:00'
		end_date = moment(date_vigilance).add(1, 'day').format('YYYY-MM-DD') + ' 05:50:00'
	}
	return "SELECT adm.idademco as 'id', adm.site_id, adm.dtarrived, adm.agent_id, adm.codeTevent " +
		"from ademcotemp adm " +
		"where codeTevent in (1000, 601) and dtarrived >= '" + begin_date + "' " +
		"and dtarrived < '" + end_date + "' " +
        "and adm.site_id in (" + siteIds.join(',') + ") " +
		"order by dtarrived asc"
}
function sqlSelectCoupure(date_vigilance) {
	let currentVigilance = moment(date_vigilance)
	let vigilanceBegin = ''
	let vigilanceEnd = ''
	if(currentVigilance.format('HH:mm:ss') == '07:00:00') {
		vigilanceBegin = moment(currentVigilance).format("YYYY-MM-DD") + " 06:00:00"
		vigilanceEnd = moment(currentVigilance).format("YYYY-MM-DD") + " 18:00:00"
	}
	else {
		vigilanceBegin = moment(currentVigilance).format("YYYY-MM-DD") + " 18:00:00"
		vigilanceEnd = moment(currentVigilance).add(1, "day").format("YYYY-MM-DD") + " 06:00:00"
	}
	console.log(vigilanceBegin, vigilanceEnd)
	return "SELECT id, transmitter, vigilance FROM coupures WHERE vigilance >= '" + vigilanceBegin + "' and vigilance < '" + vigilanceEnd + "'"
}

function getVigilanceInterval(date_vigilance){
	let currentVigilance = moment(date_vigilance)
	let intervals = []
	if(currentVigilance.format('HH:mm:ss') == '07:00:00'){
		let vigilanceJour = moment(currentVigilance.format("YYYY-MM-DD") + " 05:50:00")
		while(vigilanceJour.isBefore(moment(currentVigilance.format("YYYY-MM-DD") + " 17:50:00"))){
			let begin = vigilanceJour.clone()
			let nom = vigilanceJour.clone().add('10', 'minutes').format('HH:mm')
			let end = vigilanceJour.clone().add('1', 'hour').clone()
			intervals.push({
				begin: begin,
				nom: nom,
				end: end
			})
			vigilanceJour.add('1', 'hour')
		}
	}
	else {
		let vigilanceNuit = moment(currentVigilance.format("YYYY-MM-DD") + " 17:50:00")
		let limitVigilance = moment(currentVigilance.clone().add(1, 'day').format("YYYY-MM-DD") + " 05:50:00")
		while(vigilanceNuit.isBefore(limitVigilance)){
			let begin = vigilanceNuit.clone()
			let nom = vigilanceNuit.clone().add('10', 'minutes').format('HH:mm')
			let end = vigilanceNuit.clone().add('30', 'minutes').clone()
			intervals.push({
				begin: begin,
				nom: nom,
				end: end,
			})
			vigilanceNuit.add('30', 'minutes')
		}
	}
	return intervals
}

function doVigilanceBouton(date_vigilance){
	console.log("doVigilanceBouton")
	poolOvh.query(sqlSelectJourFerie(date_vigilance), [], (err, ferie) => {
		if(err)
			console.error(err)
		else {
			poolOvh.query(sqlSelectSite(date_vigilance, (ferie && ferie.length > 0)), [], (err, sites) => {
				if(err)
					console.error(err)
				else if(sites){
					console.log("Nb site: " + sites.length)
					if(sites.length > 0){
						poolOvh.query(sqlSelectPointage(sites.map(s => s.id), date_vigilance), [], (err, pointages) => {
							if(err)
								console.error(err)
							else {
								console.log("Nb pointage: " + pointages.length)
								poolOvh.query(sqlSelectCommentaire(date_vigilance), [], (err, commentaires) => {
									if(err)
										console.error(err)
									else {
										console.log("Nb commentaire: " + commentaires.length)
										poolOvh.query(sqlSelectVigilance(sites.map(s => s.id), date_vigilance), [], async (err, vigilances) => {
											if(err)
												console.error(err)
											else if(vigilances){
												console.log("Nb vigilance: " + vigilances.length)
												poolOvh.query(sqlSelectCoupure(date_vigilance), [], async (err, coupures) => {
													if(err)
														console.error(err)
													else if(coupures){
														console.log("Nb coupure: " + coupures.length)
														const boutonSites = []
														sites.map((s) => {
															s.agents = []
															let pi = 0
															while(pi < pointages.length){
																const p = pointages[pi]
																if(p.site_id == s.id){ 
																	if(s.vigilance)
																		s.agents.push(p)
																	pointages.splice(pi, 1)
																}
																else
																	pi++
															}
															if(s.vigilance && s.agents.length > 0)
																boutonSites.push(s)
														})
		
														console.log("site bouton: " + boutonSites.length)
														boutonSites.map(s => {
															const intervals = [ ...getVigilanceInterval(date_vigilance)]
															intervals.forEach(itv => {
																let vi = 0
																while(vi<vigilances.length){
																	const vg = vigilances[vi]
																	let dtarrived = moment(vg.dtarrived)
																	if( vg.site_id == s.id 
																		&& dtarrived.isAfter(itv.begin) && dtarrived.isBefore(itv.end)){
																			if(vg.codeTevent == 1000) {
                                                                                if(!itv.value)
    																				itv.value = dtarrived.format('HH:mm')
                                                                                vigilances.splice(vi, 1)
                                                                            }
                                                                            else if(vg.codeTevent == 601) {
																				itv.has_check = true
                                                                                vigilances.splice(vi, 1)
                                                                            }
                                                                            else vi++
																	}
																	else vi++
																}
																let ci = 0
																while(ci<commentaires.length){
																	const cm = commentaires[ci]
																	if(!cm.pointeuse_id && cm.site_id == s.id
																		&& moment(cm.date_vigilance).isSame(itv.begin.clone().add(10, "minutes"))){
																			itv.commentaire = cm.objet + (cm.commentaire ? (': ' + cm.commentaire) : '')
																			if(/check-phone \d{2}h\d{2}/.test(cm.objet.toLowerCase())){
																				const hours = /check-phone (\d{2})h(\d{2})/.exec(cm.objet.toLowerCase())
																				itv.value = hours[1] + ':' + hours[2]
																			}
																			commentaires.splice(ci, 1)
																			break;
																	}
																	else ci++
																}
																coupures.forEach(c => {
																	if(moment(c.vigilance).format("HH:mm") == itv.nom && c.transmitter == s.transmitter){
																		itv.removed = true
																	}
																})
															})
															s.intervals = intervals.filter(itv => (!itv.removed && !itv.value))
														})
		
														const clearBoutonSites = boutonSites.filter(s => {
															if(s.intervals.length > 2) return true
															else if(s.intervals.length == 2)
																return s.intervals[0].end.format("HH:mm") == s.intervals[1].begin.format("HH:mm")
															return false
														})
														.sort((a, b) => b.intervals.length - a.intervals.length)
		
														const dateVgString = moment(date_vigilance).format("DD MMMM YYYY")
															+ ' ' + (moment(date_vigilance).format("HH:mm:ss") == '07:00:00' ? 'Jour' : 'Nuit')
														const attachements = []
														const workbookSite = new Excel.Workbook()
														if(clearBoutonSites.length > 0) {
															generateVgExcelFile(workbookSite, "Rapport des manques boutons " + dateVgString
															, clearBoutonSites, getVigilanceInterval(date_vigilance))
															const siteBuffer = await workbookSite.xlsx.writeBuffer()
															attachements.push({
																filename: "Rapport des manques boutons " + dateVgString + ".xlsx",
																content: siteBuffer
															})
														}
														sendMail(
															poolAdmin,
															isTask ? destination_task : destination_test,
															"Rapport des manques de vigilances " + dateVgString, 
															(
																( clearBoutonSites.length > 0) ?
                                                                    "Veuillez trouver ci-joint le rapport des manques avec check transmission : <br/>" + 
                                                                    (clearBoutonSites.length > 0 ? " - Bouton: " + clearBoutonSites.length + " sites<br/>" : "")
																:
																    "Aucune manque de vigilance sanctionnable n'a été constater durant le service."
															),
															attachements, 
															() => {
																if(isTask) {
																	poolOvh.query(sqlUpdateLastExport(date_vigilance), [], (e, r) => {
																		if(e)
																			console.error(e)
																		else
																			console.log("update last diag export: " + r)
																		process.exit(1)
																	})
																}
																else process.exit(1)
															},
															isTask
														)
													}
												})
											}
										})
									}
								})
							}
						})		
					}
				}
			})
		}
	})
}

if(/^\d{4}-\d{2}-\d{2}$/.test(process.argv[2]) && /^\d{2}:\d{2}:\d{2}$/.test(process.argv[3])){
    console.log("send test...")
    doVigilanceBouton(process.argv[2] + ' ' + process.argv[3])
}
else if(isTask){
    poolOvh.query(sqlSelectExportBouton, [], (err, exports) => {
        if(err)
            console.error(err)
        else if(exports && exports[0]){
            const exportInfo = exports[0]
            let date_vigilance = getDayOrNightExport() 
            if(exportInfo.value != date_vigilance)
                doVigilanceBouton(date_vigilance)
            else {
                console.log("export manque vigilance already done!")
                process.exit()
            }
        }
        else 
            console.log("export not found")
    })
}
else
    console.log("please specify command!")