const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")
const nodemailer = require("nodemailer")

moment.locale('fr')


const {db_config_zo, auth_mail_tls} = require("../auth")
const pool = mysql.createPool(db_config_zo)

let transporter = nodemailer.createTransport({
	host: "ssl0.ovh.net",
	port: 465,
	secure: true, // upgrade later with STARTTLS
	auth: auth_mail_tls,
	tls: {
		rejectUnauthorized: false
	}
  })

const sqlSelectDatePointageNewPackExport = "SELECT value FROM params p WHERE p.key = 'last_export_pointage_eclosia'"
function sqlUpdateLastPointageEclosiaExport(dateString){
	return "UPDATE params p SET p.value = '" + dateString + "' " +
		"WHERE p.key = 'last_export_pointage_eclosia'"
}

const destination_vg = {
	to: "<EMAIL>,<EMAIL>",
	cc: "<EMAIL>",
}
const destination_test = {
	to: "<EMAIL>",
}

function sendMail(destination, subject, text, attachements, callback){
	const message = {
		from: "<EMAIL>",
		to: destination.to,
		cc: destination.cc,
		subject: subject,
		html: "<p>Bonjour,</p>" + 
			"<p>" + text + "</p>" +
			"<p>Cordialement,</p>",
		attachments: attachements
	};
	transporter.sendMail(message , (err, info) => {
		if(err)
			console.error(err)
		else console.log(info)
		callback()
	})
}

function generateEclosiaExcelFile(workbook, header, pointages){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
	const fontHeader = { size: 16, bold: true }
	const fontBold = { bold: true }

	const worksheet = workbook.addWorksheet("Pointage")
	worksheet.getColumn('A').width = 15
	worksheet.getColumn('B').width = 40
	worksheet.getColumn('C').width = 40

	
	worksheet.mergeCells('A1:C1')
	worksheet.getCell('A1').value = header + " (" + pointages.length + " pointages)"
	worksheet.getCell('A1').font = fontHeader
	
	let	line = 2
    let current_date = ''
	pointages.forEach(p => {
        if(current_date != moment(p.date_pointage).format('YYYY-MM-DD HH:mm:ss')){
            line++
            current_date = moment(p.date_pointage).format('YYYY-MM-DD HH:mm:ss')
            worksheet.mergeCells('A'+ line +':' + 'C' + line)
            worksheet.getCell('A' + line).value = capitalizeFirstLetter(moment(current_date).format('dddd DD MMMM YYYY'))
             + " " + (moment(current_date).format('HH:mm:ss') == "07:00:00" ? "JOUR" : "NUIT")
            worksheet.getCell('A' + line).font = fontBold
            line++
        }
        worksheet.getCell('A' + line).value = (
            p.societe_id == 1 ? 'DGM-' + p.numero_employe :
            p.societe_id == 2 ? 'SOIT-' + p.num_emp_soit :
            p.societe_id == 3 ? 'ST-' + p.numero_stagiaire :
            p.societe_id == 4 ? 'SM' :
            p.numero_employe ? p.numero_employe :
            p.numero_stagiaire ? p.numero_stagiaire :
            'Ndf'
        )
        worksheet.getCell('A' + line).border = borderStyle
        worksheet.getCell('B' + line).value = p.nom
        worksheet.getCell('B' + line).border = borderStyle
        worksheet.getCell('C' + line).value = p.site
        worksheet.getCell('C' + line).border = borderStyle
        line++
		
        /*
		line++
		worksheet.getCell('A' + line).value = "Agent"
		worksheet.getCell('A' + line).border = borderStyle
		worksheet.getCell('A' + line).fill = fillHeader

		line++
			line++
        */
	})
}
function sqlSelectPointage(begin, end) {
	return "SELECT ptg.id, ptg.site_id, ptg.date_pointage, a.societe_id, a.nom, a.numero_employe, a.num_emp_soit, a.numero_stagiaire, " +
		"ptg.id as 'pointage_id', a.id as agent_id, ptg.dtarrived, ptg.pointeuse_id, s.nom as 'site', a.empreinte, ptg.motif, ptg.soft_delete " + 
		"FROM pointages ptg " +
		"LEFT JOIN agents a ON a.id = ptg.agent_id " +
		"LEFT JOIN sites s ON s.idsite = ptg.site_id " +
		"WHERE (ptg.soft_delete is null or ptg.soft_delete = 0) " +
		"and s.idsite in (1068, 862 ,1320 ,3068 ,1086 ,3352) " +
		"and ptg.date_pointage > '" +  begin + " 00:00:00' " +
		"and ptg.date_pointage < '" + end + " 23:00:00' " +
		"order by ptg.date_pointage, s.idsite"
}
function capitalizeFirstLetter(string) {
	const  arrayString = string.split(' ').map((s) => (
		s.trim().charAt(0).toUpperCase() + s.trim().slice(1).toLowerCase()
	))
	return arrayString.join(' ')
}

function doVigilancePointeuse(date_vigilance){
	console.log("doPointageEclosia")
	const begin = moment(date_vigilance).subtract(1, 'week').format('YYYY-MM-DD')
	const end = moment(date_vigilance).subtract(1, 'day').format('YYYY-MM-DD')
	pool.query(sqlSelectPointage(begin, end), [], async (err, pointages) => {
		if(err)
			console.error(err)
		else {
			console.log("Nb pointage: " + pointages.length)
			const workbookTana = new Excel.Workbook()
			generateEclosiaExcelFile(workbookTana, "Rapport Pointage ECLOSIA du " + begin + " au " + end, pointages)
			const pointageBuffer = await workbookTana.xlsx.writeBuffer()

			sendMail(
				process.argv[2] == 'task' ? destination_vg : destination_test,
				"Rapport Pointage ECLOSIA du " + begin + " au " + end, 
				"Veuillez trouver ci-joint le rapport hebdomadaire du pointage ECLOSIA.",
				[
					{
						filename: "Rapport Pointage ECLOSIA du " + begin + " au " + end + ".xlsx",
						content: pointageBuffer
					},
				], 
				() => {
                    if(process.argv[2] == 'task') {
						pool.query(sqlUpdateLastPointageEclosiaExport(date_vigilance), [], (e, r) =>{
							if(e)
								console.error(e)
							else
								console.log("update last diag export: " + r)
							process.exit(1)
						})
					}
					else process.exit(1)
				}
			)
		}
	})
}

if(/^\d{4}-\d{2}-\d{2}$/.test(process.argv[2])){
	console.log("send test...")
	doVigilancePointeuse(process.argv[2])
}
else if(process.argv[2] == 'task'){
	if(moment().day() == 1 && moment().isAfter(moment().set({hour: 7, minute: 0}))){
		const date_vigilance = moment().format("YYYY-MM-DD")
		pool.query(sqlSelectDatePointageNewPackExport, [], (err, result) => {
			if(err)
				console.error(err)
			else if(result && result[0].value == date_vigilance){
				console.log("export pointage ECLOSIA already done!")
				process.exit(1)
			}
			else
				doVigilancePointeuse(date_vigilance)
		})
	}
	else
		console.log("Not Monday, skip export ECLOSIA.")
}
else
	console.log("please specify command!")