const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")

moment.locale('fr')


const { db_config_zo, db_config_admin, sendMail } = require("../auth")
const poolOvh = mysql.createPool(db_config_zo)
const poolAdmin = mysql.createPool(db_config_admin)

const sqlSelectLastPointageTmvExport = "SELECT value FROM params p WHERE p.key = 'last_export_pointage_tmv'"
const sqlUpdateLastPointageExport = "UPDATE params p SET p.value = ? WHERE p.key = 'last_export_pointage_tmv'"

const isTask = (process.argv[2] == "task")
const nbDay = process.argv[3] ? parseInt(process.argv[3]) : 31
const destination_pointage = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
const destination_test = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]

const sqlSelectEmpreinte = "select ad.idademco, ad.agent_id, ad.dtarrived " +
	"FROM ademcolog ad " +
	"WHERE ad.site_id = 4957 and ad.codeTevent = 1000 and ad.agent_id is not null " +
	"and ad.dtarrived > ? and ad.dtarrived < ? " +
	"order by ad.dtarrived"

function sqlSelectAgent(ids){
	return "select a.id, a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.num_emp_saoi, a.nom " +
		"FROM agents a " +
		"where a.id in (" + ids.join(", ") + ") " +
		"order by a.nom"
}
    
async function generateExcelFile(workbook, agents, pointages, weeks){
	const worksheet = workbook.addWorksheet("TMV")
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
	const fontHeader = { size: 16, bold: true }
	const fillTitle = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'FF607d8b'}
	}
	const fontRow = { color: { argb: 'FFe91e63' } }

	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	const alignLeftStyle = { vertical: 'middle'}

	worksheet.getColumn('A').width = 15
	worksheet.getColumn('B').width = 65
	worksheet.getColumn('C').width = 25
	worksheet.getColumn('D').width = 55

	worksheet.getCell('A1').value = "Rapport pointage Bureau Dirickx TMV du " + weeks[0].begin.format("DD-MM-YYYY") +
		" au " + weeks[weeks.length - 1].end.format("DD-MM-YYYY")
	worksheet.getCell('A1').font = fontHeader
	worksheet.mergeCells('A1:D1')

	worksheet.getCell('A3').value = 'MATRICULE'
	worksheet.getCell('A3').fill = fillTitle
	worksheet.getCell('A3').border = borderStyle
	worksheet.getCell('A3').alignment = alignmentStyle
	worksheet.getCell('B3').value = 'NOM'
	worksheet.getCell('B3').fill = fillTitle
	worksheet.getCell('B3').border = borderStyle
	worksheet.getCell('B3').alignment = alignLeftStyle
	worksheet.getCell('C3').value = 'DATE'
	worksheet.getCell('C3').fill = fillTitle
	worksheet.getCell('C3').border = borderStyle
	worksheet.getCell('C3').alignment = alignmentStyle 
	worksheet.getCell('D3').value = 'POINTAGE'
	worksheet.getCell('D3').fill = fillTitle
	worksheet.getCell('D3').border = borderStyle
	worksheet.getCell('D3').alignment = alignmentStyle

	let line = 4
	
	agents.forEach(a => {
		weeks.slice().forEach(w => {
			const dateEmpreintes = []
			pointages.forEach(p => {
				if(a.id == p.agent_id && moment(p.dtarrived).isAfter(w.begin) && moment(p.dtarrived).isBefore(w.end))
					dateEmpreintes.push(p.dtarrived)
			})
			worksheet.getCell('A' + line).value = (
				a.societe_id == 1 ? 'DGM-' + a.numero_employe :
				a.societe_id == 2 ? 'SOIT-' + a.num_emp_soit :
				a.societe_id == 3 ? 'ST-' + a.numero_stagiaire :
				a.societe_id == 6 ? 'SAOI-' + a.num_emp_saoi :
				a.societe_id == 4 ? 'SM' :
				a.numero_employe ? a.numero_employe :
				a.numero_stagiaire ? a.numero_stagiaire :
				'Ndf'
			)
			worksheet.getCell('A' + line).border = borderStyle
			worksheet.getCell('A' + line).fill = fontRow
			worksheet.getCell('A' + line).alignment = alignmentStyle
			worksheet.getCell('B' + line).value = a.nom
			worksheet.getCell('B' + line).border = borderStyle
			worksheet.getCell('B' + line).fill = fontRow
			worksheet.getCell('B' + line).alignment = alignLeftStyle
			worksheet.getCell('C' + line).value = w.begin.format("ddd DD MMM YYYY")
			worksheet.getCell('C' + line).border = borderStyle
			worksheet.getCell('C' + line).fill = fontRow
			worksheet.getCell('C' + line).alignment = alignmentStyle
			worksheet.getCell('D' + line).value = [... new Set(dateEmpreintes.map(dt => moment(dt).format("HH:mm")))].join(" ")
			worksheet.getCell('D' + line).border = borderStyle
			worksheet.getCell('D' + line).fill = fontRow
			line ++;
		})
		line ++;
	})
}

function doPointage(date){
	console.log("doPointage")
	const weeks = getWeeks(date)
	const begin = weeks[0].begin
	const end = weeks[weeks.length - 1].end
	console.log(begin.format("DD-MM-YYYY") + " -> " + end.format("DD-MM-YYYY"))
	poolOvh.query(sqlSelectEmpreinte, [begin.format("YYYY-MM-DD HH:mm:ss"), end.format("YYYY-MM-DD HH:mm:ss")], async (err, pointages) => {
		if(err)
			console.error(err)
		else {
			console.log("Nb pointage: " + pointages.length)
			poolOvh.query(sqlSelectAgent([...new Set(pointages.map(p => p.agent_id))]), [], async (err, agents) => {
				if(err)
					console.error(err)
				else {
					console.log("Nb agent: " + agents.length)
					const workbook = new Excel.Workbook()
					await generateExcelFile(workbook, agents, pointages, weeks)
					const buffer = await workbook.xlsx.writeBuffer()
					const titleExport = 'Pointage TMV du ' + moment(date).subtract(1, "day").format('DD-MM-YYYY')
					const arrayFile = [{
						filename: titleExport + ".xlsx",
						content: buffer
					}]
					sendMail(
						poolAdmin,
						isTask ? destination_pointage : destination_test,
						"Rapport pointage TMV du " + moment(date).subtract(1, "day").format('ddd DD MMM YYYY'), 
						"Veuillez trouver ci-joint joint le rapport pointage du " + begin.format('DD-MM-YYYY') +
						' au ' + end.format('DD-MM-YYYY') , 
						arrayFile,
						() => {
							if(isTask)
								poolOvh.query(sqlUpdateLastPointageExport, [date], (err, data) => {
									if(err)
										console.error(err)
									console.log("\n\ndone!!!")
									process.exit(1)
								})
							else process.exit(1)
						},
						isTask
					)
				}
			})
		}
	})
}

function getWeeks(date){
    let currentDate = moment(date).subtract(nbDay, "day")
	const arrayDays = []
    for(var i=0; i<nbDay; i++){
		arrayDays.push({
			begin: currentDate.clone(),
			end: currentDate.clone().set({h:23, m:59, s:59})
		})
		currentDate.add(1, "day")
	}
    return arrayDays
}

if(/^\d{4}\-\d{2}\-\d{2}$/.test(process.argv[2])){
	console.log("test...")
	doPointage(process.argv[2] + " 00:00:00")
}
else if(isTask){
	if(moment().day() == 1 && moment().isAfter(moment().set({hour: 9, minute: 0}))){
		const currentDate = moment().format("YYYY-MM-DD") + " 00:00:00"
		poolOvh.query(sqlSelectLastPointageTmvExport, [], (err, result) => {
			if(err)
				console.error(err)
			else if(result && currentDate == result[0].value){
				console.log("export pointage TMV already done!")
				process.exit(1)
			}
			else 
				doPointage(currentDate)
		})
	}
	else {
		console.log("Not monday, skip export pointage TMV.")
	}
}
else {
	console.log("please specify command!")
}
