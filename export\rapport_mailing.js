const moment = require('moment')
const hbs = require('nodemailer-express-handlebars')
const mysql = require('mysql2')
const nodemailer = require("nodemailer")
const path = require('path')

moment.locale('fr')

const {db_config_zo, auth_mail_tls_guard} = require("../auth")
const pool = mysql.createPool(db_config_zo)

let transporter = nodemailer.createTransport({
        host: "ssl0.ovh.net",
        port: 465,
        secure: true,
        auth: auth_mail_tls_guard,
        tls: {
            rejectUnauthorized: false
        }
    })

// point to the template folder
const handlebarOptions = {
    viewEngine: {
        partialsDir: path.resolve('./export/views/'),
        defaultLayout: false,
    },
    viewPath: path.resolve('./export/views/'),
};

// use a template file with nodemailer
transporter.use('compile', hbs(handlebarOptions))

function sendMail(rapports, index, dateString){
    if(index < rapports.length){
        const rapport = rapports[index]
        console.log(rapport.site)
        let message = {
            from: auth_mail_tls_guard.sender,
            to: process.argv[2] == 'task' ? rapport.clients.map(client => client.name + " <" + client.email + ">").join(', ') : "<EMAIL>",
            cc: process.argv[2] == 'task' ? "<EMAIL>, <EMAIL>" : "",
            subject: capitalizeFirstLetter(rapport.site),
            template: "email",
            attachments: [
                {
                    filename: 'logo.png',
                    path: './export/views/img/dirickx.jpg',
                    cid: 'logo@dirickx'
                },
                {
                    filename: 'site-web.png',
                    path: './export/views/img/site-web.png',
                    cid: 'site@dirickx'
                },
                {
                    filename: 'facebook.png',
                    path: './export/views/img/facebook.png',
                    cid: 'facebook@dirickx'
                },
                {
                    filename: 'dirickx-groupe.jpg',
                    path: './export/views/img/dirickx-groupe.jpg',
                    cid: 'dirickx-groupe@dirickx'
                },
                {
                    filename: 'dirickx-guard.png',
                    path: './export/views/img/dirickx-guard.png',
                    cid: 'dirickx-guard@dirickx'
                },
                {
                    filename: 'security-shop.jpg',
                    path: './export/views/img/security-shop.jpg',
                    cid: 'security-shop@dirickx' //same cid value as in the html img src
                },
            ],
            context: {
                ...rapport
            }
        };
        transporter.sendMail(message , (err, info) => {
            if(err)
                console.error(err)
            else
                console.log(info)
            sendMail(rapports, index+1, dateString)
        })
    }
    else {
        if(process.argv[2] == 'task')
            pool.query(sqlUpdateLastRapportExport(dateString), [], async (e, r) => {
                if(e)
                    console.error(e)
                else
                    console.log("update last rapport export: " + r)
                process.exit(1)
            })
        else 
            process.exit(1)
    }
}
function getDayOrNightExport(){
	let beginDay = moment().set({hour:7, minute:20, second:0})
	let endDay = moment().set({hour:19, minute:20, second:0})
	if(moment().isAfter(beginDay) && moment().isBefore(endDay))
		return moment().subtract(1, 'day').format("YYYY-MM-DD") + " 18:00:00"
	else {
		if(moment().isBefore(beginDay))
			return moment().subtract(1, 'day').format("YYYY-MM-DD") + " 06:00:00"
		return moment().format("YYYY-MM-DD") + " 06:00:00"
	}
}
function capitalizeFirstLetter(string) {
    const  arrayString = string.replace(/\s\s+/g, ' ').trim().split(' ').map((s) => (
        s.charAt(0).toUpperCase() + s.slice(1).toLowerCase()
    ))
    return arrayString.join(' ')
}

const sqlSelectLastDatePaniqueExport = "SELECT value FROM params p WHERE p.key = 'last_export_rapport_mailing'"
const sqlUpdateLastRapportExport = (dateString) => ("UPDATE params p SET p.value = '"+ dateString +"' WHERE p.key = 'last_export_rapport_mailing'")

function sqlSelectRapport(dateString){
    const begin = dateString
    const end = moment(dateString).add(12, "hours").format("YYYY-MM-DD HH:mm:ss")
	return "SELECT r.id, r.site_id, r.created_at, r.type_rapport_id, r.soft_delete, t.nom as 'type', " +
        "r.site_id, s.nom as 'site', r.debut, r.fin, r.depart, r.arrivee, r.commentaire, r.tache, r.technicien " +
        "from rapports r " +
        "left join type_rapports t on t.id = r.type_rapport_id " +
        "left join sites s on s.idsite = r.site_id " +
        "where (r.type_rapport_id is not null and r.type_rapport_id != 0) " +
        "and (r.soft_delete is null or r.soft_delete = 0) " +
        "and ((r.type_rapport_id not in (1, 3, 6) and r.created_at >= '" + begin + "' and r.created_at <= '" + end + "') " +
        "or (r.type_rapport_id in (1, 3, 6) and r.fin >= '" + begin + "' and r.fin <= '" + end + "')) " +
        "order by r.type_rapport_id, r.created_at"
}
function sqlSelectClient(ids){
	return "SELECT us.site_id, us.user_id, c.name, c.email " +
        "FROM user_sites us " +
        "LEFT JOIN client_users c on c.id = us.user_id " +
        "WHERE us.site_id in (" + ids.join(', ') + ") "
}

function sqlSelectAlarm(ids){
	return "SELECT a.idademco, a.rapport_id, a.dtarrived, a.codeTevent as 'code', a.eventQualify, ev.Description as 'alarm', " +
        "a.zones as 'numZone', z.nomZone, a.received_at, a.site_id " +
        "FROM ademcotemp a " +
        "LEFT JOIN eventcode ev on ev.code = a.codeTevent " +
        "LEFT JOIN zonesites z on z.idsite = a.site_id and z.numZone = a.zones " +
        "WHERE a.rapport_id in (" + ids.join(', ') + ") " +
        "order by received_at asc"
}
function sqlSelectAction(ids){
	return "SELECT a.id, a.rapport_id, a.created_at, t.nom " +
        "FROM actions a " +
        "LEFT JOIN type_actions t ON t.id = a.type_action_id " +
        "WHERE (a.soft_delete is null or a.soft_delete = 0) and a.rapport_id in (" + ids.join(', ') + ") " +
        "order by a.created_at asc"
}

function doRapportExport(dateString){
	console.log("doRapportExport")
    pool.query(sqlSelectRapport(dateString), [], async (err, rapports) => {
        if(err)
            console.error(err)
        else {
            pool.query(sqlSelectClient(rapports.map(r => r.site_id)), [], async (err, clients) => {
                if(err)
                    console.error(err)
                else {
                    pool.query(sqlSelectAlarm(rapports.map(r => r.id)), [], async (err, alarms) => {
                        if(err)
                            console.error(err)
                        else {
                            pool.query(sqlSelectAction(rapports.map(r => r.id)), [], async (err, actions) => {
                                if(err)
                                    console.error(err)
                                else {
                                    rapports.forEach(r => {
                                        r.site = capitalizeFirstLetter(r.site)
                                        const duree = moment.utc(moment(r.arrivee).diff(moment(r.depart))).format("HH:mm")
                                        const heure = Number.parseInt(duree.split(':')[0])
                                        const minute = Number.parseInt(duree.split(':')[1])
                                        r.date_rapport = moment(r.created_at).format("dddd DD MMM YYYY")
                                         + " à " + moment(r.created_at).format("HH:mm")
                                        r.duree = (heure ? heure + 'h ' : '') + (minute ? minute + (heure ? 'm' : ' min') : '')
                                        r.depart = moment(r.depart).format("HH:mm:ss")
                                        r.arrivee = moment(r.arrivee).format("HH:mm:ss")
                                        r.debut = moment(r.debut).format("HH:mm:ss")
                                        r.fin = moment(r.fin).format("HH:mm:ss")
                                        r.clients = []
                                        r.alarms = []
                                        r.actions = []
                                        r.zones = []
                                        clients.forEach(c => {
                                            if(c.site_id == r.site_id)
                                                r.clients.push(c)
                                        })
                                        alarms.forEach(a => {
                                            if(a.rapport_id == r.id){
                                                a.numZone = ("000" + a.numZone).slice(-3)
                                                a.trigger_at = moment(a.received_at).format("HH:mm:ss")
                                                r.zones.map(z => z.numZone)
                                                if(a.nomZone && !r.zones.map(z => z.numZone).includes(a.numZone))
                                                    r.zones.push({
                                                        numZone: a.numZone,
                                                        nomZone: a.nomZone
                                                    })
                                                r.alarms.push(a)
                                            }
                                        })
                                        actions.forEach(a => {
                                            if(a.rapport_id == r.id){
                                                a.hour = moment(a.created_at).format("HH:mm:ss")
                                                r.actions.push(a)
                                            }
                                        })
                                        if(r.type_rapport_id != 6 && r.alarms.length > 0)
                                            r.hasAlarm = true
                                        if(r.type_rapport_id != 6 && r.zones.length > 0)
                                            r.hasZone = true
                                        if([1, 2, 6].includes(Number.parseInt(r.type_rapport_id)) && r.actions.length > 0)
                                            r.isWithAction = true
                                        if(r.type_rapport_id == 3)
                                            r.isTechnical = true
                                        if([1, 6].includes(Number.parseInt(r.type_rapport_id)))
                                            r.isWithIntervention = true
                                    })
                                    const rapportFilters = rapports.filter(r => r.clients.length)
                                    if(rapportFilters.length > 0)
                                        sendMail(rapportFilters, 0, dateString)
                                    else
                                        process.exit(1)
                                }
                            })
                        }
                    })
                }
            })
        }
    })
}

if(/^\d{4}-\d{2}-\d{2}$/.test(process.argv[2]) && ["06:00:00", "18:00:00"].includes(process.argv[3])){
    console.log("send test...")
    doRapportExport(process.argv[2] + ' ' + process.argv[3])
}
else if(process.argv[2] == 'task'){
    let date_vigilance = getDayOrNightExport()
    pool.query(sqlSelectLastDatePaniqueExport, [], (err, result) => {
        if(err)
            console.error(err)
        else if(result && result[0].value == date_vigilance) {
            console.log("export list rapport mailing already done!")
            process.exit(1)
        }
        else {
            console.log("exporting ...")
            doRapportExport(date_vigilance)
        }
    })
}
else
    console.log("please specify command!")