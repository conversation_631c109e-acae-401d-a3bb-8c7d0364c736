const moment = require('moment')
const mysql = require('mysql2')
const { default: axios } = require('axios')
const { formDataOption, db_config_zo } = require('../../auth')

moment.locale('fr')

const pool = mysql.createPool(db_config_zo)

function getDayOrNightExport(){
	let beginDay = moment().set({hour:6, minute:50, second:0})
	let endDay = moment().set({hour:18, minute:50, second:0})
	if(moment().isAfter(beginDay) && moment().isBefore(endDay))
		return moment().subtract(1, 'day').format("YYYY-MM-DD") + " 18:00:00"
	else {
		if(moment().isBefore(beginDay))
			return moment().subtract(1, 'day').format("YYYY-MM-DD") + " 07:00:00"
		return moment().format("YYYY-MM-DD") + " 07:00:00"
	}
}

const sqlSelectExportBouton = "SELECT value FROM params p WHERE p.key = 'last_sanction_manque'"
function sqlUpdateLastSanctionManque(dateString){
	return "UPDATE params p SET p.value = '" + dateString + "' " +
		"WHERE p.key = 'last_sanction_manque'"
}

function sqlSelectJourFerie(date_vigilance){
	return "SELECT id from jour_feries where date = '" + moment(date_vigilance).format("YYYY-MM-DD") + "'"
}

function sqlSelectSite(date_vigilance, ferie) {
	const horaire = (moment(date_vigilance).format('HH:mm:ss') == '07:00:00') ? 'day' : 'night'
	const field = horaire + '_' + moment(date_vigilance).day()
	console.log("isFerie: " + ferie)
	if(ferie)
		return "SELECT s.idsite as 'id', s.nom, s.commentaire, s.phone_agent, s.pointeuse, s.vigilance, s.transmitter, c.check, s.last_sms_check " +
			"FROM sites s  " +
			"LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id " +
			"LEFT JOIN centrales c ON c.idcentrale = s.idcentrale " +
			"WHERE (s.soft_delete is null or s.soft_delete = 0) " +
			"and (s.vigilance = 1 or s.pointeuse = 1) " +
			"and (h.id is null or (h." + field + " is not null and h." + field  + " = 1) or (h." + horaire + "_ferie is not null and h." + horaire + "_ferie = 1)) " +
			"ORDER BY s.group_pointage_id DESC, s.nom"
	else
		return "SELECT s.idsite as 'id', s.nom, s.commentaire, s.phone_agent, s.pointeuse, s.vigilance, s.transmitter, c.check, s.last_sms_check " +
			"FROM sites s  " +
			"LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id " +
			"LEFT JOIN centrales c ON c.idcentrale = s.idcentrale " +
			"WHERE (s.soft_delete is null or s.soft_delete = 0) " +
			"and (s.vigilance = 1 or s.pointeuse = 1) " +
			"and (h.id is null or (h." + field + " is not null and h." + field + " = 1)) " +
			"ORDER BY s.group_pointage_id DESC, s.nom"
}
function sqlSelectPointage(siteIds, date_vigilance) {
	return "SELECT ptg.id, ptg.pointeuse_id, ptg.site_id, p.site_id as 'pointeuse_site_id', a.societe_id, a.nom, a.numero_employe, a.num_emp_soit, a.numero_stagiaire, " +
		"ptg.id as 'pointage_id', a.id as agent_id, ptg.dtarrived, s.nom as 'site', ptg.motif, p.transmitter, ptg.vigilance " +
		"FROM pointages ptg " +
		"LEFT JOIN agents a ON a.id = ptg.agent_id " +
		"LEFT JOIN sites s ON s.idsite = ptg.site_id " +
		"LEFT JOIN pointeuses p ON p.id = ptg.pointeuse_id " +
		"WHERE (ptg.soft_delete is null or ptg.soft_delete = 0) " +
		"and (ptg.type_pointage_id is null or ptg.type_pointage_id = 1) " +
		"and ((s.pointeuse = 0 or s.pointeuse is null) or (s.pointeuse = 1 and ptg.dtarrived is not null)) " +
		"and ptg.site_id in (" + siteIds.join(',') + ") " +
		"and ptg.date_pointage = '" + date_vigilance + "' " +
		"group by ptg.agent_id, ptg.id"
}
function sqlSelectCommentaire(date_vigilance) {
	let begin_date = ''
	let end_date = ''
	if(moment(date_vigilance).format('HH:mm:ss') == '07:00:00'){
		begin_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 05:40:00'
		end_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 17:50:00'
	}
	else {
		begin_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 17:40:00'
		end_date = moment(date_vigilance).add(1, 'day').format('YYYY-MM-DD') + ' 05:50:00'
	}
	return "SELECT c.site_id, c.agent_id, c.pointeuse_id, c.commentaire, c.objet, c.date_vigilance " +
		"FROM v_commentaires c " +
		"where c.date_vigilance >= '" + begin_date + "' " +
		"and c.date_vigilance < '" + end_date + "' " +
		"order by c.date_vigilance"
}
function sqlSelectVigilance(siteIds, date_vigilance) {
	let begin_date = ''
	let end_date = ''
	if(moment(date_vigilance).format('HH:mm:ss') == '07:00:00'){
		begin_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 05:40:00'
		end_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 17:50:00'
	}
	else {
		begin_date = moment(date_vigilance).format('YYYY-MM-DD') + ' 17:40:00'
		end_date = moment(date_vigilance).add(1, 'day').format('YYYY-MM-DD') + ' 05:50:00'
	}
	return "SELECT adm.idademco as 'id', adm.site_id, adm.dtarrived, adm.agent_id, codeTevent " +
		"from " + (moment(date_vigilance).isBefore(moment().subtract(1, "day")) ? " ademcolog " : " ademcotemp ") + " adm " +
		"where codeTevent in (601, 1000) and dtarrived >= '" + begin_date + "' " +
		"and dtarrived < '" + end_date + "' " +
        "and adm.site_id in (" + siteIds.join(',') + ") " +
		"order by dtarrived asc"
}
function sqlSelectCoupure(date_vigilance) {
	let currentVigilance = moment(date_vigilance)
	let vigilanceBegin = ''
	let vigilanceEnd = ''
	if(currentVigilance.format('HH:mm:ss') == '07:00:00') {
		vigilanceBegin = moment(currentVigilance).format("YYYY-MM-DD") + " 06:00:00"
		vigilanceEnd = moment(currentVigilance).format("YYYY-MM-DD") + " 18:00:00"
	}
	else {
		vigilanceBegin = moment(currentVigilance).format("YYYY-MM-DD") + " 18:00:00"
		vigilanceEnd = moment(currentVigilance).add(1, "day").format("YYYY-MM-DD") + " 06:00:00"
	}
	return "SELECT id, transmitter, vigilance FROM coupures WHERE vigilance >= '" + vigilanceBegin + "' and vigilance < '" + vigilanceEnd + "'"
}

function getVigilanceInterval(date_vigilance){
	let currentVigilance = moment(date_vigilance)
	let intervals = []
	if(currentVigilance.format('HH:mm:ss') == '07:00:00'){
		let vigilanceJour = moment(currentVigilance.format("YYYY-MM-DD") + " 05:40:00")
		while(vigilanceJour.isBefore(moment(currentVigilance.format("YYYY-MM-DD") + " 17:50:00"))){
			let nom, end
			let begin = vigilanceJour.clone()
			if(intervals.length == 0 ){
				nom = vigilanceJour.clone().add('20', 'minutes').format('HH:mm')
				end = vigilanceJour.clone().add('1', 'hour').add('10', 'minutes').clone()
			}
			else{
				nom = vigilanceJour.clone().add('10', 'minutes').format('HH:mm')
				end = vigilanceJour.clone().add('1', 'hour').clone()
			}
			intervals.push({
				begin: begin,
				nom: nom,
				end: end
			})
			if(intervals.length == 1)
				vigilanceJour.add('10', 'minutes')
			vigilanceJour.add('1', 'hour')
		}
	}
	else {
		let vigilanceNuit = moment(currentVigilance.format("YYYY-MM-DD") + " 17:40:00")
		let limitVigilance = moment(currentVigilance.clone().add(1, 'day').format("YYYY-MM-DD") + " 05:50:00")
		while(vigilanceNuit.isBefore(limitVigilance)){
			let begin = vigilanceNuit.clone()
			let nom, end
			if(intervals.length == 0 ){
				nom = vigilanceNuit.clone().add('20', 'minutes').format('HH:mm')
				end = vigilanceNuit.clone().add('40', 'minutes').clone()
			}
			else{
				nom = vigilanceNuit.clone().add('10', 'minutes').format('HH:mm')
				end = vigilanceNuit.clone().add('30', 'minutes').clone()
			}
			intervals.push({
				begin: begin,
				nom: nom,
				end: end,
			})
			if(intervals.length == 1)
				vigilanceNuit.add('10', 'minutes')
			vigilanceNuit.add('30', 'minutes')
		}
	}
	return intervals
}

function doVigilanceBouton(date_vigilance){
	console.log("doVigilanceBouton")
	pool.query(sqlSelectJourFerie(date_vigilance), [], (err, ferie) => {
		if(err)
			console.error(err)
		else {
			pool.query(sqlSelectSite(date_vigilance, (ferie && ferie.length > 0)), [], (err, sites) => {
				if(err)
					console.error(err)
				else if(sites){
					console.log("Nb site: " + sites.length)
					if(sites.length > 0){
						pool.query(sqlSelectPointage(sites.map(s => s.id), date_vigilance), [], (err, pointages) => {
							if(err)
								console.error(err)
							else {
								console.log("Nb pointage: " + pointages.length)
								pool.query(sqlSelectCommentaire(date_vigilance), [], (err, commentaires) => {
									if(err)
										console.error(err)
									else {
										console.log("Nb commentaire: " + commentaires.length)
										pool.query(sqlSelectVigilance(sites.map(s => s.id), date_vigilance), [], async (err, vigilances) => {
											if(err)
												console.error(err)
											else if(vigilances){
												console.log("Nb vigilance: " + vigilances.length)
												pool.query(sqlSelectCoupure(date_vigilance), [], async (err, coupures) => {
													if(err)
														console.error(err)
													else if(coupures){
														console.log("Nb coupure: " + coupures.length)
														const boutonSites = []
														const biometriqueAgents = []
														sites.map((s) => {
															s.agents = []
															let pi = 0
															while(pi < pointages.length){
																const p = pointages[pi]
																if(p.site_id == s.id){
																	if(s.pointeuse && p.pointeuse_id && p.pointeuse_site_id == p.site_id){
																		biometriqueAgents.push(p)
																	}	
																	else if(s.vigilance)
																		s.agents.push(p)
																	pointages.splice(pi, 1)
																}
																else
																	pi++
															}
															if(s.vigilance && s.agents.length > 0)
																boutonSites.push(s)
														})
		
														console.log("site bouton: " + boutonSites.length)
														boutonSites.map(s => {
															const intervals = [ ...getVigilanceInterval(date_vigilance)]
															intervals.forEach(itv => {
																let vi = 0
																while(vi<vigilances.length){
																	const vg = vigilances[vi]
																	let dtarrived = moment(vg.dtarrived)
																	if( vg.site_id == s.id 
																		&& dtarrived.isAfter(itv.begin) && dtarrived.isBefore(itv.end)){
																			if(vg.codeTevent == 1000) {
                                                                                if(!itv.value)
    																				itv.value = dtarrived.format('HH:mm')
                                                                                vigilances.splice(vi, 1)
                                                                            }
                                                                            else if(vg.codeTevent == 601) {
																				itv.has_check = true
                                                                                vigilances.splice(vi, 1)
                                                                            }
                                                                            else vi++
																	}
																	else vi++
																}
																let ci = 0
																while(ci<commentaires.length){
																	const cm = commentaires[ci]
																	if(!cm.pointeuse_id && cm.site_id == s.id 
																			&& (moment(cm.date_vigilance).isSame(itv.begin.clone().add(10, "minutes")) 
																				|| (ci == 0 && moment(cm.date_vigilance).isSame(itv.begin.clone().add(20, "minutes")))
																			)
																	){
																			itv.commentaire = cm.objet + (cm.commentaire ? (': ' + cm.commentaire) : '')
																			if(/check-phone \d{2}h\d{2}/.test(cm.objet.toLowerCase())){
																				const hours = /check-phone (\d{2})h(\d{2})/.exec(cm.objet.toLowerCase())
																				itv.value = hours[1] + ':' + hours[2]
																			}
																			commentaires.splice(ci, 1)
																			break;
																	}
																	else ci++
																}
																coupures.forEach(c => {
																	if(moment(c.vigilance).format("HH:mm") == itv.nom && c.transmitter == s.transmitter){
																		itv.removed = true
																	}
																})
															})
															s.intervals = intervals.filter(itv => (!itv.removed && !itv.value))
														})
		
														const clearBoutonSites = boutonSites.filter(s => {
															if(s.check && s.last_sms_check && moment(s.last_sms_check).isAfter(moment().subtract(1, "day")))
																s.intervals = s.intervals.filter(itv => itv.has_check)

															if(s.intervals.length > 2) 
																return true
															else if(s.intervals.length == 2)
																return s.intervals[0].end.format("HH:mm") == s.intervals[1].begin.format("HH:mm")
															return false
														})
														.sort((a, b) => b.intervals.length - a.intervals.length)

														console.log("manque bouton: " + clearBoutonSites.length)
														biometriqueAgents.map(a => {
															const intervals = [ ...getVigilanceInterval(date_vigilance)]
															intervals.forEach(itv => {
																let vi = 0
																while(vi<vigilances.length){
																	const vg = vigilances[vi]
																	let dtarrived = moment(vg.dtarrived)
																	if(vg.agent_id == a.agent_id
																		&& dtarrived.isAfter(itv.begin) && dtarrived.isBefore(itv.end)){
																			if(!itv.value){
																				itv.value = dtarrived.format('HH:mm')
																			}
																			vigilances.splice(vi, 1)
																	}
																	else vi++
																}
																let ci = 0
																while(ci<commentaires.length){
																	const cm = commentaires[ci]
																	if(moment(cm.date_vigilance).isSame(itv.begin.clone().add(10, "minutes"))){
																		if(cm.agent_id == a.agent_id){
																			itv.commentaire = cm.objet + (cm.commentaire ? (': ' + cm.commentaire) : '')
																			if(/check-phone \d{2}h\d{2}/.test(cm.objet.toLowerCase())){
																				const hours = /check-phone (\d{2})h(\d{2})/.exec(cm.objet.toLowerCase())
																				itv.value = hours[1] + ':' + hours[2]
																			}
																			commentaires.splice(ci, 1)
																			break;
																		}
																		else if(cm.pointeuse_id == a.pointeuse_id){
																			itv.commentaire = cm.objet + (cm.commentaire ? (': ' + cm.commentaire) : '')
																			if(/check-phone \d{2}h\d{2}/.test(cm.objet.toLowerCase())){
																				const hours = /check-phone (\d{2})h(\d{2})/.exec(cm.objet.toLowerCase())
																				itv.value = hours[1] + ':' + hours[2]
																			}
																			ci++
																		}
																		else ci++
																	}
																	else ci++
																}
																coupures.forEach(c => {
																	if(moment(c.vigilance).format("HH:mm") == itv.nom && c.transmitter == a.transmitter)
																		itv.removed = true
																})
															})
															a.intervals = intervals.filter(itv => (!itv.removed && !itv.value))
														})
														const clearBiometriqueAgents = biometriqueAgents.filter(a => {
																if(a.intervals.length > 2) return true
																else if(a.intervals.length == 2)
																	return a.intervals[0].end.format("HH:mm") == a.intervals[1].begin.format("HH:mm")
																return false
															})
															.sort((a, b) => b.intervals.length - a.intervals.length)
                                                        console.log("manque biometrique: " + clearBiometriqueAgents.length)

                                                        const agentList = [].concat(clearBiometriqueAgents)
                                                        clearBoutonSites.forEach(site => {
                                                            site.agents.forEach(ag => {
																ag.intervals = site.intervals
                                                                agentList.push(ag)
                                                            })
                                                        })
                                                        console.log("total: " + agentList.length)
														sendSanction(date_vigilance, agentList, 0)
													}
												})
											}
										})
									}
								})
							}
						})		
					}
				}
			})
		}
	})
}

function sendSanction(date_vigilance, pointages, index){
	if(index < pointages.length){
		const pointage = pointages[index]
		const data = {
			employe_id: pointage.agent_id,
			pointage_id: pointage.id,
			date_pointage: pointage.date_pointage,
			site_id: pointage.site_id,
			status: (moment(date_vigilance).format("HH:mm:ss") == "18:00:00" ? 
				pointage.intervals.length >= 20 ? "draft" : "demande"
			:
				pointage.intervals.length >= 10 ? "draft" : "demande"),
			motif: "Manque de vigilance : " + pointage.intervals.length + "\n" +
				((pointage.pointeuse_id && pointage.motif) ? (pointage.motif + "\n") : "") +
				(pointage.intervals.filter(itv => itv.commentaire).map(itv => (itv.nom + " : " + itv.commentaire)).join('\n')),
			confirmation: true,
		}
		axios.post("https://app.dirickx.mg:8001/api/sanction/add", data, formDataOption)
		.then(({data}) => {
			if(data.error){
				console.log(data.error)
				setTimeout(() => {
					sendSanction(date_vigilance, pointages, index)
				}, 1000);
			}
			else 
				setTimeout(() => {
					sendSanction(date_vigilance, pointages, index+1)
				}, 1000);
		})
		.catch((e) => {
			console.log(e)
			setTimeout(() => {
				sendSanction(date_vigilance, pointages, index)
			}, 3000);
		})
	}
	else {
		if(process.argv[2] == 'task')
			pool.query(sqlUpdateLastSanctionManque(date_vigilance), [], (err) => {
				if(err)
					console.error(err)
				else {
					console.log("Succefully updated")
					process.exit()
				}
			})
		else
			process.exit()
	}
}

if(/^\d{4}-\d{2}-\d{2}$/.test(process.argv[2]) && /^\d{2}:\d{2}:\d{2}$/.test(process.argv[3])){
    console.log("send test...")
    doVigilanceBouton(process.argv[2] + ' ' + process.argv[3])
}
else if(process.argv[2] == 'task'){
    pool.query(sqlSelectExportBouton, [], (err, exports) => {
        if(err)
            console.error(err)
        else if(exports && exports[0]){
            const exportInfo = exports[0]
            let date_vigilance = getDayOrNightExport() 
            if(exportInfo.value != date_vigilance)
                doVigilanceBouton(date_vigilance)
            else {
                console.log("export manque vigilance already done!")
                process.exit()
            }
        }
        else 
            console.log("export not found")
    })
}
else
    console.log("please specify command!")
