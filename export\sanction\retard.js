const { default: axios } = require('axios')
const moment = require('moment')
const mysql = require('mysql2')
const { db_config_zo, formDataOption } = require('../../auth')

moment.locale('fr')

const pool = mysql.createPool(db_config_zo)

function getDayOrNightExport(){
	let beginDay = moment().set({hour:5, minute:50, second:0})
	let endDay = moment().set({hour:17, minute:50, second:0})
	if(moment().isAfter(beginDay) && moment().isBefore(endDay))
		return moment().subtract(1, 'day').format("YYYY-MM-DD") + " 18:00:00"
	else {
		if(moment().isBefore(beginDay))
			return moment().subtract(1, 'day').format("YYYY-MM-DD") + " 07:00:00"
		return moment().format("YYYY-MM-DD") + " 07:00:00"
	}
}

const sqlSelectSanctionLateExport = "SELECT value FROM params p WHERE p.key = 'last_sanction_late'"
function sqlUpdateSanctionLateExport(dateString){
	return "UPDATE params p SET p.value = '" + dateString + "' " +
		"WHERE p.key = 'last_sanction_late'"
}

function sqlSelectPointage(date_vigilance) {
	return "SELECT ptg.id, ptg.site_id, a.societe_id, a.nom, a.numero_employe, a.num_emp_soit, a.numero_stagiaire, y.id as 'agent_pointeuse_id', " +
		"ptg.id as 'pointage_id', a.id as 'agent_id', ptg.dtarrived, ptg.pointeuse_id, s.nom as 'site', p.optic, a.empreinte, a.empreinte_optic, ptg.motif " + 
		"FROM pointages ptg " +
		"LEFT JOIN agents a ON a.id = ptg.agent_id " +
		"LEFT JOIN sites s ON s.idsite = ptg.site_id " +
		"LEFT JOIN pointeuses p ON p.id = ptg.pointeuse_id " +
		"LEFT JOIN (select ap.id, ap.agent_id, ap.pointeuse_id from agent_pointeuses ap) y ON y.agent_id = ptg.agent_id and y.pointeuse_id = ptg.pointeuse_id " +
		"WHERE (s.soft_delete is null or s.soft_delete = 0)  " +
		"and ptg.type_pointage_id = 1 " +
		"and (ptg.soft_delete is null or ptg.soft_delete = 0)  " +
		"and (p.soft_delete is null or p.soft_delete = 0)  " +
		"and (ptg.pointeuse_id is not null or ptg.dtarrived is not null) " +
		"and ptg.date_pointage= '" +  date_vigilance + "' " +
		"group by ptg.agent_id, ptg.id"

}

function doExportLateSanction(date_vigilance){
	console.log("doExportLateSanction")
    pool.query(sqlSelectPointage(date_vigilance), [], (err, pointages) => {
        if(err)
            console.error(err)
        else {
            console.log(pointages.length)
			sendSanction(date_vigilance, pointages, 0)
        }
    })
}

function sendSanction(date_vigilance, pointages, index){
	if(index < pointages.length){
		const pointage = pointages[index]
		const data = {
			employe_id: pointage.agent_id,
			pointage_id: pointage.id,
			date_pointage: pointage.date_pointage,
			site_id: pointage.site_id,
			motif: "Agent en retard : " + pointage.motif,
		}
		axios.post("https://app.dirickx.mg:8001/api/sanction/add", data, formDataOption)
		.then(({data}) => {
			if(data.error){
				console.log(data.error)
				setTimeout(() => {
					sendSanction(date_vigilance, pointages, index)
				}, 1000);
			}
			else 
				setTimeout(() => {
					sendSanction(date_vigilance, pointages, index+1)
				}, 500);
		})
		.catch((e) => {
			console.log(e)
			setTimeout(() => {
				sendSanction(date_vigilance, pointages, index)
			}, 1000);
		})
	}
	else {
		if(process.argv[2] == 'task')
			pool.query(sqlUpdateSanctionLateExport(date_vigilance), [], (err) => {
				if(err)
					console.error(err)
				else {
					console.log("Succefully updated")
					process.exit()
				}
			})
		else
			process.exit()
	}
}

if(/^\d{4}-\d{2}-\d{2}$/.test(process.argv[2]) && /^\d{2}:\d{2}:\d{2}$/.test(process.argv[3])){
	console.log("send test...")
	doExportLateSanction(process.argv[2] + ' ' + process.argv[3])
}
else if(process.argv[2] == 'task'){
	let date_vigilance = getDayOrNightExport()
	pool.query(sqlSelectSanctionLateExport, [], (err, result) => {
		if(err)
			console.error(err)
		else if(result && result[0].value == date_vigilance){
			console.log("export sanction late already done!")
			process.exit(1)
		}
		else 
			doExportLateSanction(date_vigilance)
	})
}
else
	console.log("please specify command!")
