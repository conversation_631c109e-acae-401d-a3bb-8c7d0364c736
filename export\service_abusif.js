const moment = require('moment')
const mysql = require('mysql')
const Excel = require("exceljs")
const nodemailer = require("nodemailer")

moment.locale('fr')


const db_config = require("../auth").db_config_ovh
const pool = mysql.createPool(db_config)

let transporter = nodemailer.createTransport({
	host: "ssl0.ovh.net",
	port: 465,
	secure: true, // upgrade later with STARTTLS
	auth: {
	  user: "<EMAIL>",
	  pass: "ArTl$DrXP4$21"
	},
	tls: {
		rejectUnauthorized: false
	}
  })

const sqlSelectDatePointageExport = "SELECT value FROM params p WHERE p.key = 'last_export_pointage'"
function sqlUpdateLastPointageExport(date) {
	console.log("date: " + date)
	return "UPDATE params p SET p.value = '" + date + "' WHERE p.key = 'last_export_pointage'"
} 

const destination_pointage = {
	to: "<EMAIL>, <EMAIL>, <EMAIL>, " +
		"<EMAIL>, <EMAIL>, <EMAIL>, " +
        "<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, " +
		"<EMAIL>, <EMAIL>, <EMAIL>, " +
		"<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>",
	cc: "<EMAIL>",
}
const destination_test = {
	to: "<EMAIL>",
}

function sendMail(destination, subject, text, attachements, callback){
	const message = {
		from: "<EMAIL>",
		to: destination.to,
		cc: destination.cc,
		subject: subject,
		html: "<p>Bonjour,</p>" + 
			"<p>" + text + "</p>" +
			"<p>Cordialement,</p>",
		attachments: attachements
	};
	transporter.sendMail(message , (err, info) => {
		if(err)
			console.error(err)
		else console.log(info)
		callback()
	})
}

function sqlSelectPointage(interval){
	console.log(moment(interval.begin).set({hour: 7, minute: 0, second: 0}).format('YYYY-MM-DD HH:mm:ss'), interval.end)
	return "select p.id, p.agent_id, p.site_id, p.date_pointage, s.nom as 'site' FROM pointages p " +
	"left join sites s on s.idsite = p.site_id " +
	"WHERE (p.soft_delete is null or p.soft_delete = 0) " +
	"and p.date_pointage >= '" + moment(interval.begin).set({hour: 7, minute: 0, second: 0}).format('YYYY-MM-DD HH:mm:ss') + "' and p.date_pointage <= '" + interval.end + "'" 
}
function sqlSelectAgent(ids){
	return "select a.id, a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, a.nom, s.nom as 'site', " +
		"a.real_site_id as 'site_id', s.phone_agent FROM agents a " +
		"left join sites s on s.idsite = a.real_site_id " +
		"where a.id in (" + ids.join(',') + ") " +
		"order by s.nom, a.nom"
}

function getDayExport(){
	let beginDay = moment().set({hour:6, minute:0, second:0})
	let endDay = moment().set({hour:23, minute:59, second:59})
	if(moment().isAfter(beginDay) && moment().isBefore(endDay))
		return moment().format("YYYY-MM-DD")
	else
		return moment().subtract(1, 'day').format("YYYY-MM-DD")
}

function generateColumnExcel(interval){
	let currentCol = "E"
	let colsByDate = {}
	let currentDate = moment(interval.begin).set({hour: 7, minute: 0, second: 0})
	while(moment(currentDate).isBefore(moment(interval.end).set({hour: 23}))){
		colsByDate[currentDate.format('YYYY-MM-DD HH:mm:ss')] = {
			title : currentDate.format('DD') + ' ' + 
				(currentDate.format('HH:mm:ss') == '07:00:00' ? 'J' : 
				currentDate.format('HH:mm:ss') == '18:00:00' ? 'N' : ''),
			col: currentCol
		}
		let currentArray = currentCol.split('')
		if(currentArray[currentArray.length - 1] != "Z")
			currentArray[currentArray.length - 1] = String.fromCharCode(currentArray[currentArray.length - 1].charCodeAt(0) + 1)
		else if(currentArray.length == 1)
			currentArray = ['A', 'A']
		else{ 
			currentArray[0] = String.fromCharCode(currentArray[0].charCodeAt(0) + 1)
			currentArray[1] = 'A'
		}
		currentCol = currentArray.join('')
		if(currentDate.format('HH:mm:ss') == '07:00:00')
			currentDate.set({hour: 18, minute: 0, second: 0})
		else currentDate.add(1, 'day').set({hour: 7, minute: 0, second: 0})
	}
	colsByDate.total = {
		title: "Total",
		col: currentCol
	}
	return colsByDate
}
    
async function handleExport(workbook, interval, agents, pointages){
	const colsByDate = generateColumnExcel(interval)
	console.log(colsByDate)
	const worksheet = workbook.addWorksheet("POINTAGE")
	worksheet.properties.defaultRowHeight = 24;
	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	const alignLeftStyle = { vertical: 'middle'}

	const colors = [
		{
			site: '9c27b0',
			line1: 'f3e5f5',
			line2: 'e1bee7'
		},
		{
			site: '2196f3',
			line1: 'e3f2fd',
			line2: 'bbdefb'
		},
		{
			site: 'cddc39',
			line1: 'f9fbe7',
			line2: 'f0f4c3'
		},
		{
			site: '009688',
			line1: 'e0f2f1',
			line2: 'b2dfdb'
		}
	]
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
	const fillTitle = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'FF607d8b'}
	}
	const fillHoraire = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'FFb0bec5'}
	}

	worksheet.getColumn('A').width = 15
	worksheet.getColumn('B').width = 65
	worksheet.getColumn('C').width = 55
	worksheet.getColumn('D').width = 30

	worksheet.getRow(1).height = 20
	worksheet.getCell('A1').value = 'MATRICULE'
	worksheet.getCell('A1').fill = fillTitle
	worksheet.getCell('A1').border = borderStyle
	worksheet.getCell('A1').alignment = alignmentStyle
	worksheet.getCell('B1').value = 'NOM'
	worksheet.getCell('B1').fill = fillTitle
	worksheet.getCell('B1').border = borderStyle
	worksheet.getCell('B1').alignment = alignLeftStyle
	worksheet.getCell('C1').value = 'SITE'
	worksheet.getCell('C1').fill = fillTitle
	worksheet.getCell('C1').border = borderStyle
	worksheet.getCell('C1').alignment = alignmentStyle 
	worksheet.getCell('D1').value = 'CONTACT'
	worksheet.getCell('D1').fill = fillTitle
	worksheet.getCell('D1').border = borderStyle
	worksheet.getCell('D1').alignment = alignmentStyle

	Object.keys(colsByDate).map((key) => {
		worksheet.getColumn(colsByDate[key].col).width = 5
		worksheet.getCell(colsByDate[key].col + '1').value = colsByDate[key].title
		worksheet.getCell(colsByDate[key].col + '1').fill = fillHoraire
		worksheet.getCell(colsByDate[key].col + '1').border = borderStyle
		worksheet.getCell(colsByDate[key].col + '1').alignment = alignmentStyle
	})

	//Total
	worksheet.getColumn(colsByDate.total.col).width = 8
	worksheet.getCell(colsByDate.total.col + '1').value = colsByDate.total.title
	worksheet.getCell(colsByDate.total.col + '1').fill = fillHoraire
	worksheet.getCell(colsByDate.total.col + '1').border = borderStyle
	worksheet.getCell(colsByDate.total.col + '1').alignment = alignmentStyle
	agents.map((a) => {
		let pointageByAgents = [] 
		let newPointages = []
		pointages.map((p) => {
			if(p.agent_id == a.id)
				pointageByAgents.push(p)
			else newPointages.push(p)
		})
		pointages = newPointages
		a['pointages'] = pointageByAgents
		return a
	})

	let line = 2
	let colorIndex = 0
	let lastSiteId = 0
	agents.map((a) => {
		if(a.pointages.length > 0){
			if(lastSiteId && lastSiteId != a.site_id){
				if(colorIndex < colors.length -1)
					colorIndex++
				else colorIndex = 0
			}
			const fillLine = (level) => ({
				type: 'pattern',
				pattern:'solid',
				fgColor:{argb:'FF' + colors[level]['line2']}
			})
			const fillSite = {
				type: 'pattern',
				pattern:'solid',
				fgColor:{argb:'FF' + colors[colorIndex].site}
			}

			Object.keys(colsByDate).map((key) => {
				//worksheet.getCell(colsByDate[key].col + line).fill = fillLine
				worksheet.getCell(colsByDate[key].col + line).border = borderStyle
			})
			worksheet.getRow(line).height = 20
			worksheet.getCell('A' + line).value = (
				a.societe_id == 1 ? a.numero_employe :
				a.societe_id == 2 ? a.num_emp_soit :
				a.societe_id == 3 ? a.numero_stagiaire :
				a.societe_id == 4 ? 'SM' :
				a.numero_employe ? a.numero_employe :
				a.numero_stagiaire ? a.numero_stagiaire :
				'Non définie'
			)
			worksheet.getCell('A' + line).border = borderStyle
			//worksheet.getCell('A' + line).fill = fillLine
			worksheet.getCell('A' + line).alignment = alignmentStyle
			worksheet.getCell('B' + line).value = a.nom
			worksheet.getCell('B' + line).border = borderStyle
			//worksheet.getCell('B' + line).fill = fillLine
			worksheet.getCell('B' + line).alignment = alignLeftStyle
			worksheet.getCell('C' + line).value = a.site
			worksheet.getCell('C' + line).border = borderStyle
			//worksheet.getCell('C' + line).fill = fillSite
			worksheet.getCell('C' + line).alignment = alignmentStyle
			worksheet.getCell('D' + line).value = a.phone_agent
			worksheet.getCell('D' + line).border = borderStyle
			//worksheet.getCell('D' + line).fill = fillLine
			worksheet.getCell('D' + line).alignment = alignmentStyle
			let total = 0;
			a.pointages.map((p) => {
				const datePointage = moment(p.date_pointage).format("YYYY-MM-DD HH:mm:ss")
				total += 12
				worksheet.getCell(colsByDate[datePointage].col + line).value = 12
				//worksheet.getCell(colsByDate[datePointage].col + line).fill = fillSite
				worksheet.getCell(colsByDate[datePointage].col + line).alignment = alignmentStyle
				if(a.site_id != p.site_id)
					worksheet.getCell(colsByDate[datePointage].col + line).note = p.site
			})
			
			worksheet.getCell(colsByDate.total.col + line).value = total
			worksheet.getCell(colsByDate.total.col + line).fill = fillLine(total == 48 ? 3 : total == 36 ? 2 : total == 24 ? 1 : 0)
			worksheet.getCell(colsByDate.total.col + line).alignment = alignmentStyle

			lastSiteId = a.site_id
			line ++;
		}
	})
}

function doPointage(date){
	console.log("doPointage")
	const interval = {
		begin: moment(date).subtract(1, "days").format('YYYY-MM-DD') + " 18:00:00",
		end: moment(date).format('YYYY-MM-DD')  + " 18:00:00"
	}
	pool.query(sqlSelectPointage(interval), [], async (err, pointages) => {
		if(err)
			console.error(err)
		else {
			console.log("Nb pointage: " + pointages.length)
			pool.query(sqlSelectAgent(pointages.map(p => p.agent_id)), [], async (err, agents) => {
				if(err)
					console.error(err)
				else {
					console.log("Nb agent: " + agents.length)
					const workbook = new Excel.Workbook();
					const agentList = []
					agents.forEach(ag => {
						ag.total = 0
						const services = {}
						Object.keys(generateColumnExcel(interval)).map((key) => {if(key != 'total') services[key] = false})
						pointages.forEach(p => {
							if(ag.id == p.agent_id){
								services[moment(p.date_pointage).format("YYYY-MM-DD HH:mm:ss")] = true
								ag.total += 1
							}
						})
						const values = Object.keys(services).map(key => services[key])

						if((values[0] && values[1]) || (values[1] && values[2]) || (values[2] && values[3])){
							agentList.push(ag)
						}

					})
					console.log(agents.length, agentList.length)

					agentList.sort((a, b) => b.total - a.total)
					
					handleExport(workbook, interval, agentList, pointages)
					const pointageBuffer = await workbook.xlsx.writeBuffer()
					sendMail(
						process.argv[2] == "task" ? destination_pointage : destination_test,
						"Service 24H du " + moment(date).subtract(1, "day").format('dddd DD MMM YYYY'), 
						agentList.length ? 
							"Veuillez trouver ci-joint la liste des agents qui font du service 24H et plus "
							+ " <br/> Total : " + agentList.length + " agents"
						:
							"Aucune service 24H n'a été trouvé."
						, 
						[
							{
								filename: 'Pointage du ' + moment(interval.begin).format('DD-MM-YYYY') + 
									(moment(interval.begin).format('DD-MM-YYYY') != moment(interval.end).format('DD-MM-YYYY') ? ' au ' + moment(interval.end).format('DD-MM-YYYY'): '')  + ".xlsx",
								content: pointageBuffer
							}
						],
						() => {
							if(process.argv[2] == "task")
								pool.query(sqlUpdateLastPointageExport(date), [], (err, data) => {
									if(err)
										console.error(err)
									process.exit(1)
								})
							else process.exit(1)
						}
					)
					
				}
			})
		}
	})
}

if(/^\d{4}\-\d{2}\-\d{2}$/.test(process.argv[2])){
    console.log("test...")
    doPointage(process.argv[2])
}
else if(process.argv[2] == 'task'){
    const date = getDayExport()
    pool.query(sqlSelectDatePointageExport, [], (err, result) => {
        if(err)
            console.error(err)
        else if(result && date == result[0].value){
            console.log("export diag already done!")
            process.exit(1)
        }
        else 
            doPointage(date)
    })
}
else {
    console.log("please specify command!")
}