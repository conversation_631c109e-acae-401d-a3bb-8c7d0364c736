const moment = require('moment')
const mysql = require('mysql2')
const Excel = require("exceljs")

moment.locale('fr')


const {db_config_zo, db_config_admin, sendMail} = require("../auth")
const poolOvh = mysql.createPool(db_config_zo)
const poolAdmin = mysql.createPool(db_config_admin)

const sqlSelectDateDiagExport = "SELECT value FROM params p WHERE p.key = 'last_export_transmitter'"
const sqlUpdateLastDiagExport = "UPDATE params p SET p.value = DATE_FORMAT(NOW(), '%Y-%m-%d') WHERE p.key = 'last_export_transmitter'"

const isTask = (process.argv[2] == "task")
const destination_diag = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
	"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
	"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
const destination_test = ["<EMAIL>", "<EMAIL>"]

function capitalizeFirstLetter(string) {
	const  arrayString = string.split(' ').map((s) => (
		s.trim().charAt(0).toUpperCase() + s.trim().slice(1).toLowerCase()
	))
	return arrayString.join(' ')
}
function generateTransmitterExcelFile(workbook, header, group){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
	const fontHeader = { size: 16, bold: true }
	const fontBold = { bold: true }
	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	
	const fillHeader = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'88888888'}
	}
	const fillTitleFade = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'77fce4ec'}
	}
	const fillRed = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'77e91e63'}
	}
	const worksheet = workbook.addWorksheet(group.name)
	worksheet.getColumn('A').width = 50
	worksheet.getColumn('B').width = 15
	worksheet.getColumn('C').width = 40
	worksheet.getColumn('D').width = 15
	worksheet.getColumn('E').width = 15

	worksheet.mergeCells('A1:E1')
	worksheet.getCell('A1').value = header + " (" + group.sites.length + " sites)"
	worksheet.getCell('A1').font = fontHeader
	worksheet.getCell('A1').alignment = alignmentStyle
	worksheet.getCell('A2').value = "Site"
	worksheet.getCell('A2').border = borderStyle
	worksheet.getCell('A2').fill = fillHeader
	worksheet.getCell('A2').font = fontBold
	worksheet.mergeCells('A2:A3')
	worksheet.getCell('B2').value = "Prom"
	worksheet.getCell('B2').border = borderStyle
	worksheet.getCell('B2').fill = fillHeader
	worksheet.getCell('B2').font = fontBold
	worksheet.getCell('B2').alignment = alignmentStyle
	worksheet.mergeCells('B2:B3')
	worksheet.getCell('C2').value = "Vigilance"
	worksheet.getCell('C2').border = borderStyle
	worksheet.getCell('C2').fill = fillHeader
	worksheet.getCell('C2').font = fontBold
	worksheet.getCell('C2').alignment = alignmentStyle
	worksheet.mergeCells('C2:C3')
	worksheet.getCell('D2').value = "Récépteur"
	worksheet.getCell('D2').border = borderStyle
	worksheet.getCell('D2').fill = fillHeader
	worksheet.getCell('D2').font = fontBold
	worksheet.getCell('D2').alignment = alignmentStyle
	worksheet.mergeCells('D2:E2')
	worksheet.getCell('D3').value = "Actuel"
	worksheet.getCell('D3').border = borderStyle
	worksheet.getCell('D3').fill = fillRed
	worksheet.getCell('D3').font = fontBold
	worksheet.getCell('D3').alignment = alignmentStyle
	worksheet.getCell('E3').value = "Correcte"
	worksheet.getCell('E3').border = borderStyle
	worksheet.getCell('E3').fill = fillHeader
	worksheet.getCell('E3').font = fontBold
	worksheet.getCell('E3').alignment = alignmentStyle
	
	let line = 4
	group.sites.forEach(row => {
		worksheet.getCell('A' + line).value = capitalizeFirstLetter(row.nom)
		worksheet.getCell('A' + line).border = borderStyle
		worksheet.getCell('B' + line).value = row.prom
		worksheet.getCell('B' + line).border = borderStyle
		worksheet.getCell('B' + line).alignment = alignmentStyle
		worksheet.getCell('C' + line).value = (row.vigilance ? row.horaire : '')
		worksheet.getCell('C' + line).border = borderStyle
		worksheet.getCell('C' + line).alignment = alignmentStyle
		worksheet.getCell('D' + line).value = row.transmitter
		worksheet.getCell('D' + line).border = borderStyle
		worksheet.getCell('D' + line).alignment = alignmentStyle
		worksheet.getCell('D' + line).fill = fillTitleFade
		worksheet.getCell('E' + line).value = row.correct_transmitter
		worksheet.getCell('E' + line).border = borderStyle
		worksheet.getCell('E' + line).alignment = alignmentStyle
		line++
	})
}

const sqlSelectSiteWithBadTransmitter = `SELECT s.nom, s.prom, s.vigilance, h.nom as 'horaire', s.correct_transmitter, s.transmitter_sms as 'transmitter', s.group_diag_id
	FROM sites s
	LEFT JOIN horaires h ON h.id = s.horaire_vigilance_id
	WHERE (s.soft_delete is null or s.soft_delete = 0) 
	and prom is not null
	and transmitter_sms is not null and correct_transmitter is not null 
	and correct_transmitter != transmitter_sms`

function doTransmitter(){
	console.log("doTransmitter")
	poolOvh.query(sqlSelectSiteWithBadTransmitter, [], async (err, sites) => {
		console.log("after query")
		if(err)
			console.error(err)
		else if(sites.length == 0){
			console.error("no data fetch")
            poolOvh.query(sqlUpdateLastDiagExport, [], (e, r) =>{
                if(e)
                    console.error(e)
                else
                    console.log("update last diag export: " + r)
            })
		}
		else {
			console.log("Nb site: " + sites.length)
			let groups = [
				{
					id: 1, 
					name: "Tana",
				},
				{
					id: 2, 
					name: "Tamatave",
				},
				{
					id: 3, 
					name: "Province",
				}
			]
			groups.forEach(g => {
				g.sites = []
				sites.forEach(site => {
					if(g.id == site.group_diag_id)
						g.sites.push(site)
				})
			})
			const workbook = new Excel.Workbook()
			generateTransmitterExcelFile(workbook, "TANA", groups[0])
			generateTransmitterExcelFile(workbook, "TAMATAVE", groups[1])
			generateTransmitterExcelFile(workbook, "PROVINCE", groups[2])
			const siteBuffer = await workbook.xlsx.writeBuffer()

			sendMail(
				poolAdmin,
				isTask ? destination_diag : destination_test,
				"Récépteur incorrecte " + moment().format('DD-MM-YY'), 
				"Veuillez trouver ci-joint joint la liste des sites dont les recepteurs sont incorrectes.<ul>" +
				(groups[2].sites.length && "<li>Tana : " + groups[0].sites.length + "</li>")+
				(groups[2].sites.length && "<li>Tamatave : " + groups[1].sites.length  + "</li>")+
				(groups[2].sites.length && "<li>Province : " + groups[2].sites.length  + "</li>")+
				"</ul>",
				[
					{
						filename: "Récépteur incorrecte " + moment().format("DD-MM-YYYY") + ".xlsx",
						content: siteBuffer
					},
				], 
				() => {
					if(isTask){
                        poolOvh.query(sqlUpdateLastDiagExport, [], (e, r) =>{
                            if(e)
                                console.error(e)
                            else
                                console.log("update last diag export: " + r)
							process.exit(1)
                        })
					}
					else
						process.exit(1)
				},
				isTask
			)
		}
	})
}

if(process.argv[2] == 'test'){
	console.log("send test...")
	doTransmitter()
}
else if(isTask){
	if(moment().day() == 2 && moment().isAfter(moment().set({hour: 7, minute: 0}))){
		poolOvh.query(sqlSelectDateDiagExport, [], (err, result) => {
			if(err)
				console.error(err)
			else if(result && moment().format("YYYY-MM-DD") == result[0].value){
				console.log("export diag already done!")
				process.exit(1)
			}
			else doTransmitter()
		})
	}
	else 
		console.log("skip diag...")
}
else
	console.log("please specify command!")
