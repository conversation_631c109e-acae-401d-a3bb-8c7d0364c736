const drachtio = require('drachtio');
const { SIPServer } = drachtio;

const sipConfig = {
  host: '************',   // The IP or hostname of your SIP server
  port: 5060,          // The port your SIP server listens on (default 5060)
  username: '801',  // Username for authentication
  password: 'AdmDir2025',  // Password for authentication
};

const app = new SIPServer(sipConfig);

// Start the SIP server and listen for incoming calls
app.on('connect', (err, res) => {
  if (err) {
    console.error('Failed to connect to SIP server:', err);
  } else {
    console.log('Connected to SIP server');
  }
});

// Handle incoming calls
app.on('invite', (req) => {
  console.log('Incoming call from: ', req.getFrom());

  // Accept the call
  req.accept();

  // Send a response (like answering the call)
  req.on('accept', (res) => {
    console.log('Call answered');
  });

  // Handle DTMF signals
  req.on('dtmf', (dtmf) => {
    // DTMF event: dtmf contains the digit pressed
    console.log('DTMF received:', dtmf.digit);

    // Here you can add your custom logic based on DTMF digit
    if (dtmf.digit === '1') {
      console.log('User pressed 1, perform action A');
    } else if (dtmf.digit === '2') {
      console.log('User pressed 2, perform action B');
    }
  });

  req.on('bye', (res) => {
    console.log('Call ended');
  });
});

// Send a call to a specific extension or number
function makeCall(destination) {
  const inviteOptions = {
    to: destination,  // The phone number or extension to dial
    from: sipConfig.username,
  };

  app.invite(inviteOptions, (err, res) => {
    if (err) {
      console.error('Error initiating call:', err);
    } else {
      console.log('Call initiated');
    }
  });
}

// Listen for incoming calls indefinitely
app.listen(() => {
  console.log('SIP Client is listening for incoming calls...');
});

// For testing, you can initiate a call to a destination
// makeCall('1001');

