const net = require('net')
const moment = require('moment')
const mysql = require('mysql')
const fs = require('fs')
const recoveryPath = 'recovery/sip/'

let lastReception = {}

const { db_config_ovh, db_config_maroho } = require("./auth")
const pool = mysql.createPool(db_config_ovh)
const pool_tls = mysql.createPool(db_config_maroho)
const sqlSelectCheckphoneStatus = "SELECT do_checkphone FROM sites WHERE checkphone = ? limit 1"
const sqlInsertLog = "INSERT INTO ademcomemlog(checkphone, dtarrived, codeevent, sip, istraite) VALUES(?, ?, ?, ?, 1)"
const sqlInsertLogProm = "INSERT INTO ademcomemlog(prom, dtarrived, codeevent, sip, istraite) VALUES(?, ?, ?, ?, 1)"

var sockets = {}
var server = net.createServer(function (socket) {
  socket.on('error', (err) => {
    //if(err ==='ERCONNRESET')
    Object.keys(sockets).map((key) => {
      if (sockets[key] == socket) delete sockets[key]
    })
    socket.end()
    console.log("*** erreur reset ***")
  })
  socket.on('timeout', () => {
    console.log("TIMEOUT close")
    socket.end()
  })
  socket.on('data', (data) => {
    const message = data.toString()
    const rows = message.split('\n')
    let call = {}
    rows.forEach(r => {
      if (/^INVITE sip:\d+@.+/.test(r)) {
        const group = /^INVITE sip:(\d+)@.+/.exec(r)
        call.sip = group[1]
      }
      else if (/^Contact: <sip:\+261\d+@.+/.test(r)) {
        const group = /^Contact: <sip:\+261(\d+)@.+/.exec(r)
        call.number = "0" + group[1]
        call.number261 = "+261" + group[1]
      }
      else if (/^Call-ID: \d+@.+/.test(r)) {
        const group = /^Call-ID: (\d+)@.+/.exec(r)
        call.id = group[1]
      }
      else if (/^CSeq: \d+@.+/.test(r)) {
        const group = /^CSeq: (\d+).+/.exec(r)
        call.seq = group[1]
      }
    })

    // 3. Wait for ACK message from the client (handled by next data event)
    if (/^INFO /.test(message) || /^NOTIFY /.test(message)) {
      if (/application\/dtmf-relay/.test(message)) {
        console.log("DTMF INFO received");

        // Extract DTMF digits from the INFO message
        const dtmfDigits = message.match(/Signal=([0-9A-D#*])/g);
        if (dtmfDigits) {
          dtmfDigits.forEach(dtmf => {
            const digit = dtmf.split('=')[1]; // Get the DTMF digit (A-D, #, *, 0-9)
            console.log(`Received DTMF digit: ${digit}`);
            // Process the digit (e.g., update some state or trigger an action)
          });
        }
      }
    }

    if (call.sip && call.number == "0321132522") {
      console.log("Ringing call 032 11 325 22")
    }

    if (call.sip && call.number == "0321300321") {
      console.log(message)
      console.log(`Incoming call from SIP ${call.sip}, number: ${call.number}`);

      // 1. Send 180 Ringing response
      const ringingResponse = `SIP/2.0 180 Ringing\r\n` +
        `To: <sip:${call.sip}@************>\r\n` + // Replace with the appropriate domain
        `From: <sip:${call.number261}@************>\r\n` + // Replace with the appropriate domain
        `Call-ID: ${call.id}\r\n` + // Replace with the correct Call-ID
        `CSeq: ${call.seq} INVITE\r\n` +
        `Contact: <sip:${call.number261}@************>\r\n` + // Replace with the correct contact
        `Content-Length: 0\r\n\r\n`;

      socket.write(ringingResponse);
      console.log("Sent 180 Ringing response");

      // 2. Send 200 OK response (after deciding to accept the call)
      const okResponse = `SIP/2.0 200 OK\r\n` +
        `To: <sip:${call.sip}@************>;tag=accepted\r\n` + // Replace with the appropriate domain and tag
        `From: <sip:${call.number261}@************>;tag=calling\r\n` + // Replace with the appropriate domain and tag
        `Call-ID: ${call.id}\r\n` + // Replace with the correct Call-ID
        `CSeq: ${call.seq} INVITE\r\n` +
        `Contact: <sip:${call.number261}@************>\r\n` + // Replace with the correct contact
        `Content-Type: application/sdp\r\n` +
        `Content-Length: 0\r\n\r\n` + // Assuming an empty SDP; you can provide the SDP if necessary

        setTimeout(() => {
          socket.write(okResponse);
        }, 200)
      console.log("Sent 200 OK response");

      console.log("\n-----------------")
      console.log(call.number + " " + moment().format("DD-MM-YY HH:mm:ss"))
      lastReception[call.number] = moment()
      call.datetime = lastReception[call.number].format("YYYY-MM-DD HH:mm:ss")
      const queryValue = [call.number, call.datetime, 120, call.sip]

      pool_tls.query(sqlInsertLogProm, queryValue, (err) => {
        if (err) {
          console.error(err)
          fs.writeFile(
            recoveryPath + moment().format('YYMMDDHHmmss') + '_' + queryValue[0] + '.json',
            JSON.stringify(queryValue),
            (err) => {
              console.error(err)
            }
          )
        }
        else {
          console.log("Insert succefully")
        }
      })

    }

    if (call.sip && call.number && (!lastReception[call.number] || moment().isAfter(moment(lastReception[call.number]).add(1, 'minute')))) {
      console.log("\n-----------------")
      console.log(call.number + " " + moment().format("DD-MM-YY HH:mm:ss"))
      lastReception[call.number] = moment()
      call.datetime = lastReception[call.number].format("YYYY-MM-DD HH:mm:ss")
      const queryValue = [call.number, call.datetime, 1000, call.sip]
      pool.query(sqlSelectCheckphoneStatus, [call.number], (err, result) => {
        if (err) {
          console.error(err)
          fs.writeFile(
            recoveryPath + moment().format('YYMMDDHHmmss') + '_' + queryValue[0] + '.json',
            JSON.stringify(queryValue),
            (err) => {
              console.error(err)
            }
          )
        }
        else if (result.length > 0 && result[0]['do_checkphone']) {
          pool_tls.query(sqlInsertLog, queryValue, (err) => {
            if (err) {
              console.error(err)
              fs.writeFile(
                recoveryPath + moment().format('YYMMDDHHmmss') + '_' + queryValue[0] + '.json',
                JSON.stringify(queryValue),
                (err) => {
                  console.error(err)
                }
              )
            }
            else {
              console.log("Insert succefully")
            }
          })
        }
        else if (result.length > 0)
          console.log("checkphone not allow...")
        else
          console.log("site not found, number: " + call.number)
      })
    }
  });
})


setTimeout(() => {
  server.listen(5060, "0.0.0.0", () => {
    const address = server.address();
    const port = address.port;
    const family = address.family;
    const ipaddr = address.address;
    console.log('Server ' + family + ' is listening at ' + ipaddr + ':' + port);
  });
}, 2000)
