const net = require('net')

var sockets = {}
var server = net.createServer(function(socket) {
    socket.on('error', (err) => {
        //if(err ==='ERCONNRESET')
        Object.keys(sockets).map((key) => {
            if(sockets[key] == socket) delete sockets[key]
        })
        socket.end()
        console.log("*** erreur reset ***")
    })
    socket.on('timeout', () => {
        console.log("TIMEOUT close")
        socket.end()
    })
    socket.on('data', (data) => {
        const message = data.toString()
        console.log(message)
        const rows = message.split('\n')
        let call = {}
        rows.forEach(r => {
          if(/^INVITE sip:\d+@.+/.test(r)){
            const group = /^INVITE sip:(\d+)@.+/.exec(r)
            call.sip = group[1]
          }
          else if(/^Contact: <sip:\+261\d+@.+/.test(r)){
            const group = /^Contact: <sip:\+261(\d+)@.+/.exec(r)
            call.number = "0" + group[1]
          }
        })
      });
})


setTimeout(() => {
    server.listen(5060, "0.0.0.0", ()=>{
      const address = server.address();
      const port = address.port;
      const family = address.family;
      const ipaddr = address.address;
      console.log('Server '+ family + ' is listening at ' + ipaddr + ':' + port);
    });
}, 2000)
