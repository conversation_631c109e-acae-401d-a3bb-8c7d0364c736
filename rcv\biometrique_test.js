const moment = require('moment')
const mysql = require('mysql2')
const fs = require('fs')

const recoveryPath = '/opt/app/tls/recovery/biometrique_test/'

const db_config = require("../auth").db_config_zo
const pool = mysql.createPool(db_config);

const sqlInsert = "INSERT INTO test_periodiques(pointeuse_id, dtarrived ) values ?"

function readDirRecovery(){
    console.log("-----------\n" + moment().format("YYYY-MM-DD HH:mm:ss"))
    const dir = fs.readdirSync(recoveryPath)
    console.log("nb dir : " + dir.length)
    
    function isOutOfInterval() {
        if(moment().isAfter(moment().set({minute: 0, second: 0})) && moment().isBefore(moment().set({minute: 5, second: 0})))
            return false
        else if(moment().isAfter(moment().set({minute: 57, second: 0})) && moment().isBefore(moment().add(1, "hour").set({minute: 0, second: 0})))
            return false
        else if(!(moment().isAfter(moment().set({hour: 7, minute: 0, second: 0})) && moment().isBefore(moment().set({hour: 17, minute: 0, second: 0}))) 
        && moment().isAfter(moment().set({minute: 27, second: 0})) && moment().isBefore(moment().set({minute: 35, second: 0})))
            return false
        return true
    }

    if(isOutOfInterval()){
        if(dir.length){
            const dirToTraite = dir.slice(0, 50)
            const queryValues = []
            dirToTraite.forEach(fileName => {
                const data = fs.readFileSync(recoveryPath + fileName, {encoding: 'utf-8'})
                JSON.parse(data).forEach(qv => {queryValues.push(qv)})
            })
            if(queryValues.length){
                pool.query(sqlInsert, [queryValues], (err, result) => {
                    if(err) {
                        console.log(err)
                        console.error("NOT INSERT FROM FILE :X")
                    }
                    else {
                        dirToTraite.forEach(fileName => {
                            fs.unlink(recoveryPath + fileName, (err) => {
                                if (err) console.error(err)
                                //else console.log('successfully deleted ' + recoveryPath + fileName)
                            })
                        })
                        console.log('Succefully insert SQL: ' + result.affectedRows + ' rows')
                    }
                    setTimeout(() => {
                        readDirRecovery()
                    }, dir.length > 50 ? 3000 : 30000)
                })
            }
            else {
                console.log("empty value to insert dir : ")
                console.log(dirToTraite)
                /*
                dirToTraite.forEach(fileName => {
                    fs.unlink(recoveryPath + fileName, (err) => {
                        if (err) console.error(err)
                        else console.log('successfully deleted ' + recoveryPath + fileName)
                    })
                    setTimeout(() => {
                            readDirRecovery()
                    }, 30000)
                })
                */
            }
        }
        else {
            console.log("empty dir, wait...")
            setTimeout(() => {
                readDirRecovery()
            }, 30000)
        }
    }
    else {
        console.log("moment not in interval")
        setTimeout(() => {
            readDirRecovery()
        }, 60000)
    }
}

readDirRecovery()
