const moment = require('moment')
const mysql = require('mysql')
const fs = require('fs')
const recoveryCentralePath = '/opt/app/tls/recovery/centrale/'

const auth = require("../auth")
const {db_config_maroho, db_config_306} = auth
const pool = mysql.createPool(process.argv[2] == "306" ? db_config_306 : db_config_maroho);

const sqlInsertCentrale = "INSERT INTO type_centrales(prom, centrale_id, last_transmission) values (?, ?, ?)"

function readDirCentraleRecovery(){
    console.log("-------\n" + moment().format("YYYY-MM-DD HH:mm:ss"))
    const dirCentrale = fs.readdirSync(recoveryCentralePath)
    
    if(dirCentrale.length > 0){
        const fileName = dirCentrale[0]
        const data = fs.readFileSync(recoveryCentralePath + fileName, {encoding: 'utf-8'})
        if(data){
            const rows = data.toString().split(', ')
            pool.query(sqlInsertCentrale, [rows[0], rows[1], moment(rows[2], "YYYYMMDDHHmmss").format("YYYY-MM-DD HH:mm:ss")], (err) => {
                if(err) {
                    console.error(err)
                    console.log("NOT INSERT FROM FILE :X")
                    setTimeout(() => {
                        readDirCentraleRecovery()
                    }, 500)
                }
                else {
                    fs.unlink(recoveryCentralePath + fileName, (err) => {
                        if (err) console.error(err)
                        else console.log('successfully deleted ' + recoveryCentralePath + fileName)
                    })
                    console.log('Succefully insert SQL')
                    setTimeout(() => {
                        readDirCentraleRecovery()
                    }, 200)
                }
            })
        }
        else {
            console.error("error read file data")
            setTimeout(() => {
                readDirCentraleRecovery()
            }, 500)
        }
    }
    else 
        setTimeout(() => {
            readDirCentraleRecovery()
        }, 2000)
}

setTimeout(() => {
    readDirCentraleRecovery()
}, 2000)
