const moment = require('moment')
const mysql = require('mysql2')
const fs = require('fs')
const { exec } = require("child_process")
const { argv } = require('process');

const recoveryPath = '/opt/app/tls/recovery/sms/virtual/'

const auth = require("../auth")
const {db_config_maroho, db_config_306} = auth
const pool = mysql.createPool(process.argv[2] == "306" ? db_config_306 : db_config_maroho);

const sqlInsert = "INSERT INTO ademcomemlog(dtarrived, prom, pointeuse_id, pointeuse_user_id, codeevent, eventQualify, zones, " +
    "transmitter, istraite, panel_area, panel_user) values ?"

const numeros = ['0321134413', '0321154685', '0321154686']
let transmissions = []
numeros.forEach(num => {
    transmissions.push({
        numero: num,
        last_transmission: null
    })
})

function deleteArrayFile(files, index){
    if(index < files.length){
        const fileName = files[index]
        fs.unlink(recoveryPath + fileName, (err) => {
            if (err) console.error(err)
            else console.log('successfully deleted ' + recoveryPath + fileName)
            deleteArrayFile(files, index+1)
        })
    }
    else
        readDirRecoveryTimeout()
}

function readDirRecovery(){
    console.log("---------\n" + moment().format("YYYY-MM-DD HH:mm:ss"))
    const dir = fs.readdirSync(recoveryPath)
    
    if(dir.length > 0){
        const rows = []
        const currentDir = (argv[2] == 'reverse' ? dir.slice(-25) : dir.slice(0, 25))
        const fileToDelete = []
        currentDir.forEach(fileName => {
            if(moment(fileName.split('_')[0], "YYYYMMDDHHmmss").isBefore(moment().subtract(5, 'seconds'))){
                const data = fs.readFileSync(recoveryPath + fileName, {encoding: 'utf-8'})
                if(data){
                    const dataJson = JSON.parse(data)
                    if(dataJson.length > 0){
                        dataJson.forEach(r => {
                            rows.push(r)
                            transmissions.forEach(sim => {
                                if(sim.numero == r[r.length - 2] && moment(r[0], 'YYYY/MM/DD HH:mm:ss').isValid()
                                    && (!sim.last_transmission || (sim.last_transmission && moment(r[0], "YYYY/MM/DD HH:mm:ss").isAfter(moment(sim.last_transmission, "YYYY/MM/DD HH:mm:ss"))))
                                ) {
                                    sim.last_transmission = r[0]
                                }
            
                            })
                        })
                    }
                }
                else {
                    console.log("Empty data on file to insert : " + fileName)
                    const row = fileName.split('.')[0].split('_')
                    //row.dtarrived + row.prom + row.codeevent + row.eventQualify + row.zones
                    //dtarrived, prom, pointeuse_id, pointeuse_user_id, codeevent, eventQualify, zones, transmitter, istraite
                    rows.push([moment(row[0], "YYYYMMDDHHmmss").format("YYYY/MM/DD HH:mm:ss"), row[1], null, null, row[2], row[3], row[4], null, 1])
                }
                fileToDelete.push(fileName)
            }
            else {
                console.log("File wait 5s : " + fileName)
            }
        })
        if(rows.length > 0) {
            pool.query(sqlInsert, [rows], (err) => {
                if(err) {
                    console.log(err)
                    console.log("NOT INSERT FROM FILE :X")
                    readDirRecoveryTimeout()
                }
                else {
                    deleteArrayFile(fileToDelete, 0)
                    console.log('Succefully insert SQL')
                }
            })
        }
        else {
            console.log("empty file")
            deleteArrayFile(fileToDelete, 0)
        }
    }
    else {
        const sims = transmissions.filter(t => (
            (t.last_transmission && moment(t.last_transmission, "YYYY/MM/DD HH:mm:ss").isBefore(moment().subtract(8, "minutes")))
            //|| (!t.last_transmission && startDate.isBefore(moment().subtract(2, "minutes")))
        ))
        sims.forEach(sim => {
            let cmd = "systemctl restart tls_smpp.service"
            if(sim.numero == "0321154685")
                cmd = "systemctl restart tls_smpp2.service"
            else if(sim.numero == "0321154686")
                cmd = "systemctl restart tls_smpp3.service"
            exec(cmd, (err, stdout, stderr) => {
                console.log("command send")
                if(err)
                    console.error("error : " + err)
                if(stderr)
                    console.error("stderr : " + stderr)
                if(stdout)
                    console.log(stdout)
            })
        })
        readDirRecoveryTimeout()
    }
}

function readDirRecoveryTimeout(){
    setTimeout(() => {
        readDirRecovery()
    }, 1000)
}

readDirRecoveryTimeout()
