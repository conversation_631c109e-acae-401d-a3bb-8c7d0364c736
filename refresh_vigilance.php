<?php
$servername = "localhost";
$username = "servertls";
$password = "123456";
/*
$servername = "localhost";
$username = "root";
$password = "";
*/

try{
	$conn = new PDO("mysql:host=$servername;dbname=tls_alarm", $username, $password);
	$conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
	$sql = "SELECT a.idademco, a.prom, a.messageType, a.eventQualify, a.codeevent, a.partition, a.zones, a.dtarrived, 
		a.transmitter, a.status, clientTLS, lastupdate, Lat, Lon, gps, COALESCE(s.emit_code, a.codeevent) as code 
		FROM ademcomemlog a left join signales s on a.prom = s.prom and a.codeevent = s.received_code 
		WHERE COALESCE(s.emit_code, a.codeevent) = 1000";
	$data = $conn->query($sql)->fetchAll();
	#$response = $q->execute([]);
	$sql_insert = "insert into ademcolog
		(prom, messageType, eventQualify, codeevent, ademcolog.partition, zones, istraite, dtarrived, dttraite, transmitter, codeTevent, IdUser, lastupdate, Lat, Lon, gps)
		values";
	$insert_values = [];
	$delete_ids = array();
	$values = array();
	$ids = array();
	$sql_delete = "delete from ademcomemlog where idademco in (";
	if(count($data) > 0){
		foreach ($data as $row) {
			array_push($insert_values, "(?, ?, ?, ?, ?, ?, 2, ?, now(), ?, ?, 1, now(), ?, ?, ?)");
			array_push($delete_ids, "?");
			array_push($values, $row['prom'], $row['messageType'], $row['eventQualify'], $row['codeevent'], $row['partition'], $row['zones'], $row['dtarrived'], 
				$row['transmitter'], $row['code'], $row['Lat'], $row['Lon'], $row['gps']);
			array_push($ids, $row['idademco']);
		}
		$q1 = $conn->prepare($sql_insert . implode(", ", $insert_values));
		$q1->execute($values);
		$q2 = $conn->prepare($sql_delete . implode(',', $delete_ids). ')');
		$q2->execute($ids);
	}
}
catch(PDOException $e){
	echo "Connection failed: ". $e->getMessage();
}