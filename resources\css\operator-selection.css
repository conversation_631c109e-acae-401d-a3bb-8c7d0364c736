.operator-selection {
    padding: 10px 0;
}

.operator-selection h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.operator-grid {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.operator-card {
    background: white;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100px;
}

.operator-card:hover {
    background-color: whitesmoke;
    border-color: rgba(0, 0, 0, 0.2);
}

.operator-logo {
    width: 45px;
    height: 45px;
    margin: 0 auto 10px;
    display: block;
}

.operator-name {
    text-align: center;
    color: #333;
    font-size: 0.9rem;
}