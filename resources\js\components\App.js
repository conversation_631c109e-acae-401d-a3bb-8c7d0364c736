import React, { Component, lazy, Suspense } from 'react';
import ReactDOM from 'react-dom';

import './login.css'
import './app.css'
import './panic/panic.css'
import './agent/agent.css'
import './site/site.css'
import './agent/notification/notification.css'
import './modal/modal.css'
import './vigilance/vigilance.css'
import './pointeuse/pointeuse.css'
import './site/pointeuse/pointeuse.css'
import './pointage/pointage.css'
import './site/signale/signale.css'
import './site/zone/zone.css'
import './report/select/select.css'
import './alarm/alarm.css'
import './alarm/checkbox.css'
import './vigilance/commentaire/commentaire.css'
import './site/client/client.css'
import './pointage/site/site.css'

import axios from 'axios'

import Panic from './panic/Panic'
import Nav from './nav/Nav'
import Loading from './loading/Loading'
import moment from 'moment';
import Appelle from './appelle/Appelle';
import Contact from './appelle/contact/Contact';

const Reclamation = lazy(() => import('./reclamation/Reclamation'))
const Service24 = lazy(() => import('./service24/Service24'))
const Alarm = lazy(() => import('./alarm/Alarm'))
const Site = lazy(() => import('./site/Site'))
const Vigilance = lazy(() => import('./vigilance/Vigilance'))
const Monitor = lazy(() => import('./monitor/Monitor'))
const Agent = lazy(() => import('./agent/Agent'))
const Report = lazy(() => import('./report/Report'))
const Pointage = lazy(() => import("./pointage/Pointage"))
const PointageLate = lazy(() => import("./pointage_late/PointageLate"))
const RepartitionModal = lazy(() => import("./modal/RepartitionModal"))
const VigilancePointeuse = lazy(() => import('./vigilance_pointeuse/VigilancePointeuse'))
const Pointeuse = lazy(() => import('./pointeuse/Pointeuse'))
const Client = lazy(() => import('./client/Client'))
const Rappeler = lazy(() => import('./rappeler/Rappeler'))
// const Anomalie = lazy(() => import('./anomalie/Anomalie'))

export default class App extends Component {
    constructor(props) {
        super(props)
        this.state = {
            user: null,
            menu: "",
            extension: "",
            username: "",
            password: "",
            loading: false,
            onLogin: false,
            filterCount: 0,
            allowedData: 0,
        }
        this.handleClearLogin = this.handleClearLogin.bind(this)
        this.handleLogin = this.handleLogin.bind(this)
        this.handleExtension = this.handleExtension.bind(this)
        this.handleUsername = this.handleUsername.bind(this)
        this.handlePassword = this.handlePassword.bind(this)
        this.handleChangeMenu = this.handleChangeMenu.bind(this)
        this.toggleLoading = this.toggleLoading.bind(this)
        this.playAudio = this.playAudio.bind(this)
        this.pauseAudio = this.pauseAudio.bind(this)
        this.handleClearMenu = this.handleClearMenu.bind(this)
        this.updateFilterCount = this.updateFilterCount.bind(this)
        this.updateAllowedData = this.updateAllowedData.bind(this)
    }
    updateAllowedData(data) {
        this.setState({
            allowedData: data
        })
    }
    updateFilterCount(count) {
        this.setState({
            filterCount: count
        })
    }
    handleClearMenu() {
        this.setState({
            menu: ''
        })
    }
    handleClearLogin() {
        localStorage.setItem("username", "")
        localStorage.setItem("secret", "")
        this.setState({
            username: "",
            password: "",
            menu: "login",
        })
    }
    handleChangeMenu(value) {
        this.setState({
            menu: value
        })
    }
    handleLogin() {
        const { extension, username, password, onLogin } = this.state
        this.setState({
            onLogin: true,
            menu: ''
        })
        let data = new FormData()
        data.append("extension", extension)
        data.append("email", username)
        data.append("password", password)
        axios.post("/api/login", data)
            .then(({ data }) => {
                console.log(data)
                if (data) {
                    this.setState({
                        user: {
                            id: data.id,
                            name: data.name,
                            username: data.email,
                            role: data.role,
                        },
                        menu: 'alarm',
                        onLogin: false
                    })
                    localStorage.setItem("id", data.id)
                    localStorage.setItem("name", data.name)
                    localStorage.setItem("username", data.email)
                    localStorage.setItem("secret", data.secret)
                    localStorage.setItem("role", data.role)
                    localStorage.setItem("local", data.local)
                    localStorage.setItem("extension", data.extension)
                    localStorage.setItem("datetime", data.datetime)
                }
                else {
                    this.setState({
                        menu: 'login'
                    })
                }
            })
            .catch((response) => {
                console.log(response);
                this.setState({
                    onLogin: false,
                    menu: 'login'
                })
            });
    }
    handleUsername(event) {
        this.setState({
            username: event.target.value
        })
    }
    handleExtension(event) {
        this.setState({
            extension: event.target.value
        })
    }
    handlePassword(event) {
        this.setState({
            password: event.target.value
        })
    }
    toggleLoading(load) {
        this.setState({
            loading: load
        })
    }
    updateAuthUser() {
        const { menu } = this.state
        const username = localStorage.getItem('username')
        const secret = localStorage.getItem('secret')
        const lastUpdateCount = localStorage.getItem('last_update_count')
        if (username) {
            if (!lastUpdateCount || moment(lastUpdateCount, "YYYY-MM-DD HH:mm:ss").isBefore(moment().subtract(10, 'minute'))) {
                console.log("getAuthUser get by axios")
                axios.get('/api/getAuthUser?username=' + username + '&secret=' + secret)
                    .then(({ data }) => {
                        if (data) {
                            localStorage.setItem("id", data.id)
                            localStorage.setItem("name", data.name)
                            localStorage.setItem("username", data.email)
                            localStorage.setItem("role", data.role)
                            localStorage.setItem("local", data.local)
                            localStorage.setItem("extension", data.extension)
                            localStorage.setItem("datetime", data.datetime)
                            localStorage.setItem("last_update_count", data.last_update_count)
                            localStorage.setItem("call_reminders_count", data.call_reminders_count)

                            try {
                                const formattedReminders = this.fixMalformedRemindersData(data.call_reminders);

                                localStorage.setItem('call_reminders', JSON.stringify(formattedReminders));
                                console.log('Successfully stored formatted call_reminders data');
                            } catch (error) {
                                console.error('Error formatting call_reminders data:', error);
                                localStorage.setItem('call_reminders', JSON.stringify([]));
                            }

                            localStorage.setItem('alarm', data.alarm)
                            localStorage.setItem('site', data.site)
                            localStorage.setItem('pointage_error', data.pointage_error)
                            localStorage.setItem('reclamation', data.reclamation)
                            localStorage.setItem('service24', data.service24)

                            this.setState({
                                user: {
                                    id: data.id,
                                    name: data.name,
                                    username: data.email,
                                    role: data.role,
                                    local: data.local,
                                    extension: data.extension,
                                    datetime: data.datetime,
                                },
                            })
                            if (!menu || menu == "login")
                                this.setState({
                                    menu: 'alarm'
                                })
                        }
                        else {
                            this.setState({
                                user: null,
                                menu: "login",
                            })
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching user data:', error);
                        this.setState({
                            user: null,
                            menu: "login",
                        });
                    });
            }
            else {
                console.log("getAuthUser get by localStorage")
                const role = localStorage.getItem("role")
                const local = localStorage.getItem("local")
                this.setState({
                    user: {
                        id: localStorage.getItem("id"),
                        name: localStorage.getItem("name"),
                        username: localStorage.getItem("username"),
                        role: role,
                        local: local,
                        extension: localStorage.getItem("extension"),
                        datetime: localStorage.getItem("datetime"),
                    },
                })
                if (!menu || menu == "login")
                    this.setState({
                        menu: 'alarm'
                    })
            }
            // console.table(localStorage.getItem("call_reminders"))
            setTimeout(() => {
                this.updateAuthUser()
            }, 10000)
        }
        else {
            console.log("logout status ...")
            this.setState({
                onLogin: true,
                menu: 'login'
            })
            setTimeout(() => {
                this.updateAuthUser()
            }, 3000)
        }
    }
    ensureProperRemindersFormat() {
        try {
            const remindersData = localStorage.getItem('call_reminders');

            // If data exists but isn't in proper JSON format
            if (remindersData &&
                (remindersData.includes('[object Object]') ||
                    (!remindersData.startsWith('[') && !remindersData.startsWith('{')))) {

                console.warn('Found malformed call_reminders data in localStorage. Resetting to empty array.');
                localStorage.setItem('call_reminders', JSON.stringify([]));
            }
        } catch (error) {
            console.error('Error in ensureProperRemindersFormat:', error);
            // Set to empty array as fallback
            localStorage.setItem('call_reminders', JSON.stringify([]));
        }
    }

    fixMalformedRemindersData(data) {
        if (typeof data === 'string') {
            if (data.includes('[object Object]')) {
                console.warn('Detected malformed call_reminders data. Attempting to fix...');

                const objectCount = (data.match(/\[object Object\]/g) || []).length;

                return Array(objectCount).fill().map(() => ({}));
            }

            try {
                return JSON.parse(data);
            } catch (e) {
                console.error('Error parsing call_reminders string:', e);
                return [];
            }
        }

        if (Array.isArray(data)) {
            return data;
        }

        if (typeof data === 'object' && data !== null) {
            return [data];
        }

        return [];
    }
    componentDidMount() {
        this.audio = new Audio('/sound/siren_noise.wav')
        this.audio.volume = .3
        this.audio.load()

        this.ensureProperRemindersFormat();

        this.updateAuthUser()
    }
    playAudio() {
        this.audio.currentTime = 0
        const audioPromise = this.audio.play()

        if (audioPromise !== undefined) {

            audioPromise
                .then(_ => {
                    // autoplay started
                })
                .catch(err => {
                    // catch dom exception
                    console.info(err)
                })
        }
    }
    pauseAudio() {
        this.audio.pause()
    }
    render() {
        const { extension, username, password, user, menu, loading, filterCount, allowedData } = this.state
        const datetime = localStorage.getItem("datetime")
        return (
            <Suspense fallback={<Loading />}>
                {
                    !menu ?
                        <Loading />
                        :
                        menu != 'login' ?
                            <div id="mainContainer">
                                {
                                    loading &&
                                    <Loading />
                                }
                                {
                                    (['room', 'root'].includes(user.role)) &&
                                    <Panic playPanicSound={this.playAudio}
                                        pauseAudio={this.pauseAudio}
                                        user={user}
                                        userId={user.id}
                                        menu={menu}
                                        updateFilterCount={this.updateFilterCount}
                                        allowedData={allowedData}
                                        filterCount={filterCount} />
                                }
                                {
                                    (
                                        ['room', 'root'].includes(user.role)
                                        && menu == "alarm"
                                        && (
                                            (moment(datetime).isAfter(moment(datetime).set({ hour: 9, minute: 0 })) && moment(datetime).isBefore(moment(datetime).set({ hour: 11, minute: 59 })))
                                            || (moment(datetime).isAfter(moment(datetime).set({ hour: 21, minute: 0 })) && moment(datetime).isBefore(moment(datetime).set({ hour: 23, minute: 59 })))
                                        )
                                    ) && <RepartitionModal />
                                }
                                <Nav
                                    user={user}
                                    updateAuthUser={this.updateAuthUser}
                                    toggleLoading={this.toggleLoading}
                                    changeMenu={this.handleChangeMenu}
                                    clearLogin={this.handleClearLogin} />
                                {menu == 'alarm' && <Alarm toggleLoading={this.toggleLoading} user={user} filterCount={filterCount} updateFilterCount={this.updateFilterCount} updateAllowedData={this.updateAllowedData} />}
                                {/* {menu == 'anomalie' && <Anomalie toggleLoading={this.toggleLoading} user={user} />} */}
                                {/* {menu == 'rappeler' && <Rappeler toggleLoading={this.toggleLoading} user={user} />} */}
                                {menu == 'contact' && <Contact toCall={true} toggleLoading={this.toggleLoading} user={user} />}
                                {menu == "vigilance" && <Vigilance user={user} toggleLoading={this.toggleLoading} />}
                                {menu == "site" && <Site toggleLoading={this.toggleLoading} user={user} />}
                                {menu == "archive_site" && <Site archive={true} toggleLoading={this.toggleLoading} user={user} />}
                                {menu == "monitor" && <Monitor toggleLoading={this.toggleLoading} />}
                                {menu == "agent" && <Agent toggleLoading={this.toggleLoading} user={user} />}
                                {menu == "report" && <Report user={user} toggleLoading={this.toggleLoading} />}
                                {menu == "pointage" && <Pointage toggleLoading={this.toggleLoading} user={user} />}
                                {menu == "vigilance_pointeuse" && <VigilancePointeuse toggleLoading={this.toggleLoading} user={user} />}
                                {menu == "archive" && <Agent archive={true} toggleLoading={this.toggleLoading} user={user} />}
                                {menu == "pointeuse" && <Pointeuse toggleLoading={this.toggleLoading} user={user} />}
                                {menu == "pointage_late" && <PointageLate toggleLoading={this.toggleLoading} user={user} />}
                                {menu == "client" && <Client toggleLoading={this.toggleLoading} user={user} />}
                                {menu == "reclamation" && <Reclamation toggleLoading={this.toggleLoading} user={user} />}
                                {menu == "service24" && <Service24 toggleLoading={this.toggleLoading} user={user} />}
                                {menu == "a_rappeler" && <Appelle toCall={true} toggleLoading={this.toggleLoading} user={user} />}
                                {menu == "appelle" && <Appelle all={true} toggleLoading={this.toggleLoading} user={user} />}
                            </div>
                            :
                            <div id="loginContainer">
                                <div id="loginContent">
                                    <div id="logoContainer">
                                        <img id="loginLogo" src="/img/tls_login.svg" />
                                    </div>
                                    <div id="loginForm">
                                        <input onChange={this.handleExtension} value={extension} placeholder="Extension" type="text" />
                                        <input onChange={this.handleUsername} value={username} placeholder="Utilisateur" type="text" />
                                        <input onChange={this.handlePassword} value={password} placeholder="Mot de passe" type="password" />
                                        <button onClick={this.handleLogin}> Connecter </button>
                                    </div>
                                </div>
                            </div>
                }
            </Suspense>
        );
    }
}

if (document.getElementById('app')) {
    ReactDOM.render(<App />, document.getElementById('app'));
}


