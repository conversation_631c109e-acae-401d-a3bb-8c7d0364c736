import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'
import 'moment/locale/fr'
import AgentDetail from './AgentDetail'
import Notification from './notification/Notification'

import EditAgentModal from './EditAgentModal'
import InfiniteScroll from 'react-infinite-scroll-component'
import LoadingData from '../loading/LoadingData'

export default class Agent extends Component {
    constructor(props) {
        super(props)
        this.state = {
            currentDate: '',
            timeoutId: '',
            searchTimeoutId: '',
            inputSearch: '',
            searchAgent: '',
            currentAgent: null,
            fonctions: [],
            agents: [],
            notifications: [],
            heightWindow: 0,
            widthWindow: 0,
            activeTab: 'pj',
            showEditAgentModal: false,
            showAddAgentModal: false,
            showArchiveAgentModal: false,
            showDeleteAgentModal: false,
            showEditAgentMenu: false,
            allDataLoaded: false,
        }
        this.fetchMoreData = this.fetchMoreData.bind(this)
        this.updateNotification = this.updateNotification.bind(this)
        this.updateData = this.updateData.bind(this)
        this.updateAgent = this.updateAgent.bind(this)
        this.handleClickAgent = this.handleClickAgent.bind(this)
        this.handleClickAddAgent = this.handleClickAddAgent.bind(this)
        this.closeAgentModal = this.closeAgentModal.bind(this)
        this.handleChangeSearchAgent = this.handleChangeSearchAgent.bind(this)
        this.toggleLoading = this.toggleLoading.bind(this)
        this.toggleEditAgentMenu = this.toggleEditAgentMenu.bind(this)
        this.getColor = this.getColor.bind(this)
        this.handleChangeTab = this.handleChangeTab.bind(this)
        this.handleEnterPress = this.handleEnterPress.bind(this)
    }
    handleEnterPress(event) {
        if (event.key === 'Enter') {
            this.updateData(true)
        }
    }
    handleChangeTab(value) {
        this.setState({
            activeTab: value
        })
    }
    toggleEditAgentMenu(value) {
        this.setState({
            showEditAgentMenu: value
        })
    }
    toggleLoading(load) {
        this.props.toggleLoading(load)
    }
    handleChangeSearchAgent(event) {
        this.setState({
            inputSearch: event.target.value
        })
    }
    closeAgentModal() {
        this.setState({
            showAddAgentModal: false,
        })
    }
    handleClickAddAgent() {
        this.setState({
            showAddAgentModal: true
        })
    }
    updateData(loading, clearSearch) {
        const { archive } = this.props
        const { agents, inputSearch } = this.state
        this.setState({
            currentAgent: null
        })
        if (loading)
            this.toggleLoading(true)
        if (clearSearch)
            this.setState({
                inputSearch: ''
            })

        axios.get('/api/agents' + (archive ? '/archive' : '')
            + '?username=' + localStorage.getItem("username") + '&secret=' + localStorage.getItem("secret")
            + '&offset=' + (loading ? 0 : agents.length) + ((!clearSearch && inputSearch) ? '&search=' + inputSearch : ''))
            .then(({ data }) => {
                if (data) {
                    if (loading) {
                        this.container.scroll(0, 0)
                        this.setState({
                            agents: data.agents
                        }, () => {
                            this.toggleLoading(false)
                        })
                    }
                    else {
                        const list = agents ? agents.slice().concat(data.agents) : data.agents
                        this.setState({
                            agents: list
                        })
                    }
                    this.setState({
                        fonctions: data.fonctions,
                        agences: data.agences,
                        allDataLoaded: (data.agents.length < 50)
                    })
                }
            })
            .catch(() => {
                setTimeout(() => {
                    this.updateData()
                }, 10000)
            })
    }

    fetchMoreData() {
        setTimeout(() => {
            this.updateData()
        }, 300);
    }

    updateAgent(id, begin, end, isAfterSave) {
        const { currentDate } = this.state
        if (!begin)
            begin = moment(currentDate).subtract(1, 'month').format('YYYY-MM-DD')
        if (!end)
            end = moment(currentDate).format('YYYY-MM-DD')
        this.toggleLoading(true)
        axios.get('/api/agents/show/' + id + '?begin=' + begin + '&end=' + end
            + '&username=' + localStorage.getItem("username") + '&secret=' + localStorage.getItem("secret"))
            .then(({ data }) => {
                if (data) {
                    let agents = this.state.agents
                    for (let i = 0; i < agents.length; i++) {
                        if (agents[i].id == data.agent.id) {
                            if (isAfterSave)
                                agents.splice(i, 1)
                            else
                                agents[i] = data.agent
                        }
                    }
                    if (isAfterSave)
                        agents.unshift(data.agent)

                    this.setState({
                        currentAgent: data.agent,
                        showAddAgentModal: false,
                        showEditAgentModal: false,
                        showDeleteAgentModal: false,
                        activeTab: 'pj'
                    }, () => {
                        console.log(data.agent)
                        this.toggleLoading(false)
                    })
                }
            })
            .catch(() => {
                this.toggleLoading(false)
            })
    }
    handleClickAgent(id) {
        this.updateAgent(id)
    }
    setTimeoutUpdateNotification() {
        if (this.state.timeoutId)
            clearTimeout(this.state.timeoutId)
        let timeoutId = setTimeout(this.updateNotification, 9000)
        this.setState({
            timeoutId: timeoutId
        })
    }
    updateNotification() {
        axios.get('/api/agents/notification')
            .then(({ data }) => {
                console.log('updateNotification')
                this.setState({
                    notifications: data.agents,
                    currentDate: data.now
                }, () => {
                    this.setTimeoutUpdateNotification()
                })
            })
            .catch(() => {
                this.setTimeoutUpdateNotification()
            })
    }
    getColor(agent) {
        const { currentDate } = this.state
        const { archive } = this.props
        if (!archive && currentDate) {
            if ((!agent.last_date_pointage || moment(agent.last_date_pointage).isBefore(moment(currentDate).subtract(30, 'days')))
                && (!agent.created_at || moment(agent.created_at).isBefore(moment(currentDate).subtract(30, 'days'))))
                return 'red'
            else if (!agent.numero_employe && !agent.num_emp_soit && agent.date_embauche && moment(agent.date_embauche).isBefore(moment(currentDate).subtract(6, 'months')))
                return 'green'
        }
        return ''
    }
    componentDidMount() {
        const { archive } = this.props
        this.updateData(true)
        this.updateNotification()
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
        document.title = archive ? "Archive - TLS" : "Agent - TLS"
    }
    resize() {
        this.setState({
            heightWindow: window.innerHeight,
            widthWindow: window.innerWidth
        });
    }
    render() {
        const { fonctions, agences, currentDate, notifications, agents, currentAgent, inputSearch, heightWindow, widthWindow, activeTab,
            showEditAgentMenu, showAddAgentModal, allDataLoaded } = this.state
        const { archive, user } = this.props
        return (
            <div className="table" onClick={() => { if (showEditAgentMenu) this.toggleEditAgentMenu(false) }}>
                {
                    (['rh', 'root'].includes(user.role) && !archive && notifications.length > 0) &&
                    <Notification now={currentDate} data={notifications} clickItem={this.handleClickAgent} />
                }
                <div id="tableContainer">
                    <div className="table">
                        <div className="row-header">
                            <h3 className="h3-table">
                                <span className="cell">
                                    {archive ? 'Archives' : 'Agents'}
                                </span>
                                <span className="cell center">
                                    <div id="searchSite">
                                        <div>
                                            <input onKeyDown={this.handleEnterPress} onChange={this.handleChangeSearchAgent} value={inputSearch} type="text" />
                                            <img onClick={() => { this.updateData(true) }} src="/img/search.svg" />
                                        </div>
                                    </div>
                                </span>
                            </h3>
                        </div>
                        <div className="row-table">

                            <table className="fixed_header visible-scroll layout-fixed">
                                <thead>
                                    <tr>
                                        <th className="cellNum">Num.</th>
                                        <th className="cellAgent">Nom</th>
                                        <th className="cellSiteAgent">
                                            {archive ? 'Observation' : 'Site'}
                                            <img src="/img/refresh_table.svg" onClick={() => { this.updateData(true, true) }} />
                                        </th>
                                    </tr>
                                </thead>
                                <tbody id="scrollableDiv" ref={el => (this.container = el)} style={{ 'height': (heightWindow - 160) + "px" }}>
                                    <InfiniteScroll
                                        scrollableTarget="scrollableDiv"
                                        dataLength={agents ? agents.length : 0}
                                        next={this.fetchMoreData}
                                        hasMore={!allDataLoaded}
                                        loader={<LoadingData />}
                                    >{
                                            agents.map((row) => {
                                                return (
                                                    <tr
                                                        key={row.id}
                                                        onDoubleClick={() => { this.handleClickAgent(row.id) }}
                                                        className={this.getColor(row) + " " + ((currentAgent != null && currentAgent.id == row.id) ? "selected-row" : "")}
                                                    >
                                                        <td className="cellNum">
                                                            {
                                                                row.societe_id == 1 ? 'DGM-' + row.numero_employe :
                                                                    row.societe_id == 2 ? 'SOIT-' + row.num_emp_soit :
                                                                        row.societe_id == 3 ? 'ST-' + row.numero_stagiaire :
                                                                            row.societe_id == 4 ? 'SM' :
                                                                                row.numero_employe ? row.numero_employe :
                                                                                    row.numero_stagiaire ? row.numero_stagiaire :
                                                                                        <span className="purple">Ndf</span>
                                                            }
                                                        </td>
                                                        <td className="cellAgent" title={row.nom}>{row.nom}</td>
                                                        <td className="cellSiteAgent" title={archive ? row.observation : row.site}>
                                                            {archive ? row.observation : row.site}
                                                        </td>
                                                    </tr>
                                                )
                                            })
                                        }

                                        {
                                            (allDataLoaded && agents.length == 0) &&
                                            <tr>
                                                <td className='center secondary'>Aucun données trouvé</td>
                                            </tr>
                                        }
                                    </InfiniteScroll>
                                </tbody>
                            </table>
                            {/* {!allDataLoaded && <LoadingData />} */}

                        </div>
                    </div>
                </div>
                {showAddAgentModal && <EditAgentModal
                    action={'/api/agents/store'}
                    closeModal={this.closeAgentModal}
                    updateAgent={this.updateAgent}
                    fonctions={fonctions}
                    agences={agences}
                />}
                <div className={currentAgent ? "box-shadow-left" : ""} style={{ width: (widthWindow / 2.5) + 'px', maxWidth: (widthWindow / 2.5) + 'px', minWidth: (widthWindow / 2.5) + 'px' }} id="overviewContainer">
                    {
                        currentAgent ?
                            <AgentDetail
                                currentAgent={currentAgent}
                                currentDate={currentDate}
                                user={user}
                                archive={archive}
                                updateData={this.updateData}
                                updateAgent={this.updateAgent}
                                getColor={this.getColor}
                                fonctions={fonctions}
                                agences={agences}
                                showEditAgentMenu={showEditAgentMenu}
                                toggleEditAgentMenu={this.toggleEditAgentMenu}
                                heightWindow={heightWindow}
                                closeAgentModal={this.closeAgentModal}
                                activeTab={activeTab}
                                handleChangeTab={this.handleChangeTab}
                            />
                            :
                            <div className="img-bg-container">
                                <img className="img-bg-overview" src="/img/tls_background.svg" />
                            </div>
                    }
                </div>
            </div>
        )
    }
}
