import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'
import Pointage from './pointage/Pointage'
import Operation from './operation/Operation'

import 'react-datepicker/dist/react-datepicker.css'
import ResetDigitModal from './ResetDigitModal'

export default class AgentDetail extends Component {
    constructor(props) {
        super(props)
        this.state = {
            showSiteModal: false,
            showEditAgentModal: false,
            showArchiveAgentModal: false,
            showDeleteAgentModal: false,
            showResetDigitModal: false,
            optic: false,
        }
        this.handleChangeTab = this.handleChangeTab.bind(this)
        this.handleClickResetDigitAgent = this.handleClickResetDigitAgent.bind(this)
        this.closeAgentModal = this.closeAgentModal.bind(this)
        this.updateAgent = this.updateAgent.bind(this)
        this.updateData = this.updateData.bind(this)
        this.handleShowSite = this.handleShowSite.bind(this)
    }
    handleShowSite() {
        this.setState({
            showSiteModal: true
        })
    }
    closeAgentModal() {
        this.setState({
            showSiteModal: false,
            showEditAgentModal: false,
            showDeleteAgentModal: false,
            showArchiveAgentModal: false,
            showResetDigitModal: false,
        })
    }
    updateData() {
        this.props.updateData(true)
    }
    updateAgent() {
        const { currentAgent } = this.props
        this.props.updateAgent(currentAgent.id)
    }
    handleClickResetDigitAgent() {
        this.setState({
            showResetDigitModal: true,
            showEditAgentMenu: false,
        })
    }
    handleChangeTab(event) {
        if (event.target.id != this.props.activeTab) {
            this.props.handleChangeTab(event.target.id)
        }
    }
    render() {
        const { showResetDigitModal } = this.state
        const { activeTab, user, archive, currentDate, heightWindow, showEditAgentMenu, currentAgent } = this.props
        return (
            <div>
                {
                    showResetDigitModal &&
                    <ResetDigitModal
                        updateAgent={this.updateAgent}
                        closeModal={this.closeAgentModal}
                        agent={currentAgent} />
                }
                <div className="overview-container">
                    <div className="head-title-overview" title={currentAgent.nom}>
                        <div style={{ height: "40px", lineHeight: "40px" }}>
                            <div className="title-overview">
                                <span className={this.props.getColor(currentAgent)} style={{ opacity: .9 }}>
                                    {
                                        currentAgent.societe_id == 1 ? 'DGM-' + currentAgent.numero_employe :
                                            currentAgent.societe_id == 2 ? 'SOIT-' + currentAgent.num_emp_soit :
                                                currentAgent.societe_id == 3 ? 'ST-' + currentAgent.numero_stagiaire :
                                                    currentAgent.societe_id == 4 ? 'SM' :
                                                        currentAgent.numero_employe ? currentAgent.numero_employe :
                                                            currentAgent.numero_stagiaire ? currentAgent.numero_stagiaire :
                                                                <span className="purple">Non définie</span>
                                    }
                                </span>
                            </div>
                            <div className="overview-edit-icon">
                                {
                                    (currentAgent.empreinte || currentAgent.empreinte_optic) &&
                                    <img onClick={() => { this.props.toggleEditAgentMenu(!showEditAgentMenu) }} className="overview-edit-img" src="/img/parametre.svg" />
                                }
                                {
                                    showEditAgentMenu &&
                                    <div className="dropdown-overview-edit">
                                        <span onClick={this.handleClickResetDigitAgent}>Réinitialiser l'empreinte</span>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                    <span className="overview-break-overflow" title={currentAgent.nom}>
                        <b>Nom : </b>{currentAgent.nom}
                    </span>
                    <span title={currentAgent.phone_agent}><b>Site : </b> {currentAgent.site}</span><br />
                    <span><b>Fonction : </b> {currentAgent.fonction}</span><br />
                    <div className="table">
                        <span className="cell">
                            {
                                !archive ?
                                    (
                                        currentAgent.societe_id == 1 ?
                                            <span>
                                                <b>Date de confirmation : </b>
                                                {currentAgent.date_confirmation && moment(currentAgent.date_confirmation).format('DD MMM YYYY')}
                                            </span>
                                            :
                                            currentAgent.societe_id == 2 ?
                                                <span>
                                                    <b>Date de confirmation : </b>
                                                    {currentAgent.date_conf_soit && moment(currentAgent.date_conf_soit).format('DD MMM YYYY')}
                                                </span>
                                                :
                                                <span>
                                                    <b>Date d'embauche : </b>
                                                    {currentAgent.date_embauche && moment(currentAgent.date_embauche).format('DD MMM YYYY')}
                                                </span>
                                    )
                                    :
                                    <span>
                                        <b>Date de sortie : </b>
                                        {currentAgent.date_sortie && moment(currentAgent.date_sortie).format('DD MMM YYYY')}
                                    </span>
                            }
                        </span>
                        {
                            (currentAgent.empreinte && !currentAgent.empreinte_optic) &&
                            <span className="cell right" style={{ height: "30px" }}>
                                <span className="badge bg-purple">Empreinte capacitif</span>
                            </span>
                        }
                        {
                            (!currentAgent.empreinte && currentAgent.empreinte_optic) &&
                            <span className="cell right" style={{ height: "30px" }}>
                                <span className="badge bg-purple">Empreinte optique</span>
                            </span>
                        }
                        {
                            (currentAgent.empreinte && currentAgent.empreinte_optic) &&
                            <span className="cell right" style={{ height: "30px" }}>
                                <span className="badge bg-primary">Empreinte complet</span>
                            </span>
                        }
                    </div>
                </div>
                <div style={{ position: 'relative', top: '2px' }}>
                    <div className="table">
                        <div className="cell">
                            <div id="tabHeaderOverview">
                                <ul>
                                    <li id="pj" className={activeTab == 'pj' ? "active-tab" : ""} onClick={this.handleChangeTab}>Document</li>
                                    {/*
                                        (['rh', 'root'].includes(user.role) && currentAgent.site) &&
                                        <li id="sanction" className={activeTab == 'sanction' ? "active-tab" : ""} onClick={this.handleChangeTab}>Sanction</li>
                                    */}
                                    {/*
                                        (['rh', 'root'].includes(user.role) && currentAgent.site) &&
                                        <li id="prime" className={activeTab == 'prime' ? "active-tab" : ""} onClick={this.handleChangeTab}>Prime</li>
                                    */}
                                    {
                                        currentAgent.site &&
                                        <li id="pointage" className={activeTab == 'pointage' ? "active-tab" : ""} onClick={this.handleChangeTab}>Pointage</li>
                                    }
                                    {/*
                                        ['rh', 'root'].includes(user.role) &&
                                        <li id="conge" className={activeTab == 'conge' ? "active-tab" : ""} onClick={this.handleChangeTab}>Congé</li>
                                    */}
                                    <li id="operation" className={activeTab == 'operation' ? "active-tab" : ""} onClick={this.handleChangeTab}>Traçabilité</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="tabContentOverview">
                    <div id="tabContainer">
                        {
                            activeTab == 'pointage' &&
                            <Pointage currentDate={currentDate} agentId={currentAgent.id} nomAgent={currentAgent.nom} heightWindow={heightWindow} updateAgent={this.updateAgent} />
                        }
                        {
                            activeTab == 'operation' &&
                            <Operation
                                currentDate={currentDate}
                                action={'/api/agents/operation/' + currentAgent.id}
                                heightTable={heightWindow - 450} />
                        }
                    </div>
                </div>
            </div>
        )
    }
}
