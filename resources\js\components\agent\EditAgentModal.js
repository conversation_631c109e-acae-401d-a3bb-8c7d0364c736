import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'
import DatePicker  from 'react-datepicker'

import Modal from '../modal/Modal'
import Site from './site/Site'

import 'react-datepicker/dist/react-datepicker.css'

export default class EditAgentModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            nom: '',
            numero_employe: '',
            num_emp_soit: '',
            numero_stagiaire: '',
            date_embauche: '',
            date_confirmation: '',
            date_conf_soit: '',
            nb_heure_contrat: '',
            site: null,
            agence: null,
            societe_id: '',
            fonction_id: '',
            sal_base: '',
            begin: '',
            showSite: false,
            disableSave: false,
            error: null,
            activeTab: 'contrat',
            showAgence: false,
            showSalBase: false,
            agenceSearch: '',
            cin: false,
            cv: false,
            photo: false,
            residence: false,
            plan_reperage: false,
            bulletin_n3: false,
            bonne_conduite: false,
            sal_forfait: true,
            idm_depl: 0,
            part_variable: 0,
            perdiem: 0,
            prime_anc: 0,
        }
        this.handleChangeSalaire = this.handleChangeSalaire.bind(this)
        this.handleChangeNom = this.handleChangeNom.bind(this)
        this.handleChangeNumEmp = this.handleChangeNumEmp.bind(this)
        this.handleChangeNumEmpSoit = this.handleChangeNumEmpSoit.bind(this)
        this.handleChangeNumStg = this.handleChangeNumStg.bind(this)
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.toggleSite = this.toggleSite.bind(this)
        this.handleChangeSite = this.handleChangeSite.bind(this)
        this.handleChangeSociete = this.handleChangeSociete.bind(this)
        this.handleChangeFonction = this.handleChangeFonction.bind(this)
        this.handleBeginDate = this.handleBeginDate.bind(this)
        this.handleConfirmationDate = this.handleConfirmationDate.bind(this)
        this.handleConfirmationDateSOIT = this.handleConfirmationDateSOIT.bind(this)
        this.handleChangeTab = this.handleChangeTab.bind(this)
        this.showAgence = this.showAgence.bind(this)
        this.handleAgenceCLick = this.handleAgenceCLick.bind(this)
        this.handleAgenceChange = this.handleAgenceChange.bind(this)
        this.handleNbHeureContratChange = this.handleNbHeureContratChange.bind(this)
        this.handleCheckDocument = this.handleCheckDocument.bind(this)
        this.handleChangePartVariable = this.handleChangePartVariable.bind(this)
        this.handleChangeIdmDepl = this.handleChangeIdmDepl.bind(this)
        this.handleChangePerdiem = this.handleChangePerdiem.bind(this)
        this.handleSalForfaitChange = this.handleSalForfaitChange.bind(this)
        this.handleChangePrimeAncChange = this.handleChangePrimeAncChange.bind(this)
        this.handleNbHeureConvenuChange = this.handleNbHeureConvenuChange.bind(this)
        this.handleSalBaseChange = this.handleSalBaseChange.bind(this)
        this.handleSalBaseCLick = this.handleSalBaseCLick.bind(this)
        this.showSalBase = this.showSalBase.bind(this)
        
    }
    handleClickSalBaseItem(e){
        e.stopPropagation()
        this.setState({
            sal_base: e.target.value,
            showSalBase: false
        })
    }
    showSalBase(salbase){
        const {sal_base} = this.state
        if(sal_base){
            const search = sal_base.toLocaleLowerCase().replace(/[.*+?^{}()|[\]\\]/g, '\\$&')
            var patt = new RegExp(search)
            if(salbase.libelle && patt.test(salbase.libelle.toLocaleLowerCase()))
                return true
            return false
        }
        return true
    }
    handleSalBaseChange(e){
        this.setState({
            sal_base: e.target.value,
            showSalbase: true
        })
    }
    handleSalBaseCLick(e){
        console.log("click... must be display")
        e.stopPropagation()
        this.setState({
            showSalBase: !this.state.showSalBase,
        }, () => {
            console.log(this.state.showSalBase)
        })
    }
    handleChangePrimeAncChange(e){
        this.setState({
            prime_anc: e.target.value
        })
    }
    handleNbHeureConvenuChange(e){
        this.setState({
            nb_heure_convenu: e.target.value
        })
    }
    handleSalForfaitChange(e){
        this.setState({
            sal_forfait: e.target.checked
        })
    }
    handleChangePerdiem(e){
        this.setState({
            perdiem: e.target.value
        })
    }
    handleChangeIdmDepl(e){
        this.setState({
            idm_depl: e.target.value
        })
    }
    handleChangePartVariable(e){
        this.setState({
            part_variable: e.target.value
        })
    }
    handleChangeSalaire(e){
        this.setState({
            sal_base: e.target.value
        })
    }
    handleCheckDocument(pj){
        const {cin, cv, photo, residence, plan_reperage, bulletin_n3, bonne_conduite} = this.state
        if(pj == 'cin'){
            this.setState({
                cin: !cin
            })
        }
        else if(pj == 'cv'){
            this.setState({
                cv: !cv
            })
        }
        else if(pj == 'photo'){
            this.setState({
                photo: !photo
            })
        }
        else if(pj == 'residence'){
            this.setState({
                residence: !residence
            })
        }
        else if(pj == 'plan_reperage'){
            this.setState({
                plan_reperage: !plan_reperage
            })
        }
        else if(pj == 'bulletin_n3'){
            this.setState({
                bulletin_n3: !bulletin_n3
            })
        }
        else if(pj == 'bonne_conduite'){
            this.setState({
                bonne_conduite: !bonne_conduite
            })
        }
    }
    handleNbHeureContratChange(e){
        this.setState({
            nb_heure_contrat: e.target.value
        })
    }
    handleClickAgenceItem(e, agence){
        e.stopPropagation()
        this.setState({
            agence: agence,
            agenceSearch: agence.libelle,
            showAgence: false
        })
    }
    handleAgenceChange(e){
        this.setState({
            agenceSearch: e.target.value,
            showAgence: true
        })
    }
    handleAgenceCLick(e){
        e.stopPropagation()
        this.setState({
            showAgence: !this.state.showAgence,
            agenceSearch: (!this.state.showAgence ? '' : this.state.agenceSearch)
        })
    }
    showAgence(agence){
        const {agenceSearch} = this.state
        if(agenceSearch){
            const search = agenceSearch.toLocaleLowerCase().replace(/[.*+?^{}()|[\]\\]/g, '\\$&')
            var patt = new RegExp(search)
            if(agence.libelle && patt.test(agence.libelle.toLocaleLowerCase()))
                return true
            return false
        }
        return true
    }
    handleConfirmationDateSOIT(date){
        this.setState({
            date_conf_soit: date
        })
    }
    handleConfirmationDate(date){
        this.setState({
            date_confirmation: date
        })
    }
    handleChangeTab(event){
        this.setState({
            activeTab: event.target.id
        })
    }
    newNumberDgm(){
        axios.get('/api/agents/new_num_dgm')
        .then(({data}) =>{
            this.setState({
                numero_employe: data.toString()
            })
        })
    }
    newNumberSoit(){
        axios.get('/api/agents/new_num_soit')
        .then(({data}) =>{
            this.setState({
                num_emp_soit: data.toString()
            })
        })
    }
    newNumberStagiaire(){
        axios.get('/api/agents/new_num_stg')
        .then(({data}) =>{
            this.setState({
                numero_stagiaire: data.toString()
            })
        })
    }
    handleBeginDate(date){
        this.setState({
            begin: date
        })
    }
    handleChangeSociete(e){
        this.setState({
            societe_id: e.target.value,
            activeTab: e.target.value == 1 ? 'dgm' : e.target.value == 2 ? 'soit' : e.target.value == 3 ? 'stagiaire' : 'contrat'
        })
    }
    handleChangeFonction(e){
        this.setState({
            fonction_id: e.target.value
        })
    }
    handleChangeSite(site){
        this.setState({
            site: site,
            showSite: false
        })
    }
    toggleSite(value){
        this.setState({
            showSite: value
        })
    }
    handleChangeNom(event){
        this.setState({
            nom: event.target.value
        })
    }
    handleChangeNumEmp(event){
        this.setState({
            numero_employe: event.target.value
        })
    }
    handleChangeNumEmpSoit(event){
        this.setState({
            num_emp_soit: event.target.value
        })
    }
    handleChangeNumStg(event){
        this.setState({
            numero_stagiaire: event.target.value
        })
    }
    componentDidMount(){
        const {agent} = this.props
        if(agent)
            this.setState({
                nom:  agent.nom ? agent.nom : '',
                numero_stagiaire: agent.numero_stagiaire ? agent.numero_stagiaire : '',
                numero_employe: agent.numero_employe ? agent.numero_employe : '',
                site: {
                    idsite: agent.real_site_id,
                    nom: agent.site,
                },
                agence: {
                    id: agent.agence_id,
                    libelle: agent.agence,
                },
                agenceSearch: agent ? agent.agence : '',
                nb_heure_contrat: agent.nb_heure_contrat,
                nb_heure_convenu: agent.nb_heure_convenu,
                societe_id: agent.societe_id ? agent.societe_id : '',
                fonction_id: agent.fonction_id ? agent.fonction_id : '',
                sal_base: agent.sal_base ? agent.sal_base : '',
                begin: agent.date_embauche ? moment(agent.date_embauche).toDate() : '',
                date_confirmation: agent.date_confirmation ? moment(agent.date_confirmation).toDate() : '',
                num_emp_soit: agent.num_emp_soit ? agent.num_emp_soit : '',
                date_conf_soit: agent.date_conf_soit ? moment(agent.date_conf_soit).toDate() : '',
                sal_forfait: agent.sal_forfait ? agent.sal_forfait : false,
                cin: agent.cin ? agent.cin : false,
                cv: agent.cv ? agent.cv : false,
                photo: agent.photo ? agent.photo : false,
                residence: agent.residence ? agent.residence : false,
                plan_reperage: agent.plan_reperage ? agent.plan_reperage : false,
                bulletin_n3: agent.bulletin_n3 ? agent.bulletin_n3 : false,
                bonne_conduite: agent.bonne_conduite ? agent.bonne_conduite : false,
                idm_depl: agent.idm_depl,
                part_variable: agent.part_variable,
                perdiem: agent.perdiem,
                prime_anc: agent.prime_anc,
            })
    }
    handleSave(){
        const {nom, numero_stagiaire, numero_employe, num_emp_soit, site, societe_id, fonction_id, begin, 
            date_confirmation, date_conf_soit, nb_heure_contrat, nb_heure_convenu, agence,
            cin, cv, photo, residence, plan_reperage, bulletin_n3, bonne_conduite,
            sal_forfait, sal_base, idm_depl, part_variable, perdiem, prime_anc} = this.state
        if(!societe_id){
            this.setState({
                error: {
                    key: "societe_id",
                    value: "L'immatriculation doit être remplie"
                },
            })
        }
        this.setState({
            disableSave: true,
            error: null,
        })
        let data = new FormData()
        data.append("cin", cin ? 1 : 0)
        data.append("cv", cv ? 1 : 0)
        data.append("photo", photo ? 1 : 0)
        data.append("residence", residence ? 1 : 0)
        data.append("plan_reperage", plan_reperage ? 1 : 0)
        data.append("bulletin_n3", bulletin_n3 ? 1 : 0)
        data.append("bonne_conduite", bonne_conduite ? 1 : 0)
        data.append("sal_forfait", sal_forfait ? 1 : 0)
        if(nom && nom.trim())
            data.append("nom", nom.replace(/ +/g, " ").trim())
        if(site)
            data.append("real_site_id", site.idsite)
        if(nb_heure_contrat)
            data.append("nb_heure_contrat", nb_heure_contrat)
        if(nb_heure_convenu)
            data.append("nb_heure_convenu", nb_heure_convenu)
        if(agence)
            data.append("agence_id", agence.id)
        if(fonction_id)
            data.append("fonction_id", fonction_id)
        if(sal_base)
            data.append("sal_base", sal_base)
        if(societe_id)
            data.append("societe_id", societe_id)
        if(numero_stagiaire && numero_stagiaire.trim())
            data.append("numero_stagiaire", numero_stagiaire.trim())
        if(numero_employe && numero_employe.trim())
            data.append("numero_employe", numero_employe.trim())
            if(num_emp_soit && num_emp_soit.trim())
                data.append("num_emp_soit", num_emp_soit.trim())
        if(begin)
            data.append("date_embauche", moment(begin).format('YYYY-MM-DD'))
        if(date_confirmation)
            data.append("date_confirmation", moment(date_confirmation).format('YYYY-MM-DD'))
        if(begin)
            data.append("date_embauche", moment(begin).format('YYYY-MM-DD'))
        if(date_conf_soit)
            data.append("date_conf_soit", moment(date_conf_soit).format('YYYY-MM-DD'))


        if(idm_depl)
            data.append("idm_depl", idm_depl)
        if(part_variable)
            data.append("part_variable", part_variable)
        if(perdiem)
            data.append("perdiem", perdiem)
        if(prime_anc)
            data.append("prime_anc", prime_anc)

        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        axios.post(this.props.action, data)
        .then(({data}) => {
            if(data.error){
                console.log(data.error)
                const firstKey = Object.keys(data.error)[0]
                const firstValue = data.error[firstKey][0]
                this.setState({
                    error: {
                        key: firstKey,
                        value: firstValue
                    },
                    disableSave: false,
                }, () => {
                    console.log(this.state.error)
                })
            }
            else if(data){
                this.props.closeModal()
                this.props.updateAgent(data.id, null, null, true)
            }
        })
        .finally(()=>{
            this.setState({
                disableSave: false
            })
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        const {showAgence, showSalBase, agenceSearch, 
            activeTab, nom, numero_employe, num_emp_soit, numero_stagiaire, societe_id, fonction_id, showSite, 
            site, agence, date_confirmation, begin, date_conf_soit, nb_heure_contrat, nb_heure_convenu, disableSave, error,
            cin, cv, photo, residence, plan_reperage, bulletin_n3, bonne_conduite,
            sal_forfait, sal_base, part_variable, idm_depl, perdiem, prime_anc} = this.state
        const {fonctions, agences, agent} = this.props
        return (
            <div onClick={() => {this.setState({showSalBase: false, showAgence: false, agenceSearch: agence ? agence.libelle : ''})}}>
                <Modal 
                        disableSave={disableSave} 
                        width="lg" 
                        handleSave={this.handleSave} 
                        handleCancel={this.handleCancel}
                        error={error}
                    >
                    <h3>Agent</h3>
                    <div className="table">
                        <div className="row">
                            <div className="cell-50">
                                <div className="input-container">
                                    <label>Nom *</label>
                                    <input onChange={this.handleChangeNom} value={nom} disabled={agent && agent.ignore_name}/>
                                </div>
                            </div>
                            <div className="cell-50">
                                <div className="input-container">
                                    <label>Site occupé</label>
                                    <div className="table">
                                        <div className="cell">
                                            <input 
                                                disabled={true} 
                                                value={site ? site.nom : ''}/>
                                        </div>
                                        {/*
                                        <div id="cellClientBtn" onClick={()=>{this.toggleSite(true)}}>
                                            <img id="clientImg" src="/img/site.svg"/>
                                        </div>
                                        */}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="row">
                            <div className="cell-50">
                                <div className="input-container">
                                    <label>Immatriculation *</label>
                                    <select onChange={this.handleChangeSociete} value={societe_id}>
                                        <option></option>
                                        <option value="4">Sans matricule</option>
                                        <option value="3">Stagiaire</option>
                                        <option value="2">SOIT</option>
                                        <option value="1">Dirickx Guard</option>
                                    </select>
                                </div>
                            </div>
                            <div className="cell-50">
                                <div className="input-select-relative">
                                    <div className="input-container">
                                        <label>Agence *</label>
                                        <input autoComplete="off" onClick={this.handleAgenceCLick} onChange={this.handleAgenceChange} type="text" value={agenceSearch}/>
                                    </div>
                                    <div>
                                        {
                                            showAgence &&
                                            <ul id="agenceListContainer" className="comment-vg-list">
                                                {
                                                    agences && (
                                                        agenceSearch ? agences.map((ag, index) =>{
                                                            if(this.showAgence(ag))
                                                                return <li key={'key_' + ag.id + index} onClick={(e) => {this.handleClickAgenceItem(e, ag)}}>
                                                                    { ag.libelle }
                                                                </li>
                                                        })
                                                        :  
                                                        agences.slice(0, 9).map((ag, index) =>{
                                                            if(this.showAgence(ag))
                                                                return <li key={'key_' + ag.id + index} onClick={(e) => {this.handleClickAgenceItem(e, ag)}}>
                                                                    { ag.libelle }
                                                                </li>
                                                        })
                                                    )
                                                }
                                                {
                                                    (agences && !agenceSearch) && 
                                                    <li>...</li>
                                                }
                                            </ul>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style={{position: 'relative', top: '2px'}}>
                        <div className="table">
                            <div className="cell">
                                <div id="tabHeaderOverview">
                                    <ul>
                                        <li id="contrat" className={activeTab == 'contrat' ? "active-tab" : ""} onClick={this.handleChangeTab}>Contrat</li>
                                        {
                                            [3].includes(Number.parseInt(societe_id)) && 
                                            <li id="stagiaire" className={activeTab == 'stagiaire' ? "active-tab" : ""} onClick={this.handleChangeTab}>Stagiaire</li>
                                        }
                                        {
                                            [1, 2].includes(Number.parseInt(societe_id)) && 
                                            <li id="soit" className={activeTab == 'soit' ? "active-tab" : ""} onClick={this.handleChangeTab}>SOIT</li>
                                        }
                                        {
                                            [1, 2].includes(Number.parseInt(societe_id)) && 
                                            <li id="dgm" className={activeTab == 'dgm' ? "active-tab" : ""} onClick={this.handleChangeTab}>Dirickx Guard</li>
                                        }
                                        <li id="pj" className={activeTab == 'pj' ? "active-tab" : ""} onClick={this.handleChangeTab}>Document</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="border-padding">
                        {
                            activeTab == 'contrat' &&
                            <div className="table">
                                <div className="row">
                                    <br/>
                                    <label className="checkbox-container">
                                        Salaire forfaitisé
                                        <input onChange={this.handleSalForfaitChange} checked={sal_forfait} name="sal_forfait" type="checkbox"/>
                                        <span class="checkmark"></span>
                                    </label>
                                </div>
                                <div className="row">
                                    <div className="cell-50">
                                        <div className="input-container">
                                            <label>Fonction *</label>
                                            <select onChange={this.handleChangeFonction} value={fonction_id}>
                                                <option></option>
                                                {fonctions.map((f) => (
                                                    <option key={f.id} value={f.id}>
                                                        {f.libelle}
                                                    </option>
                                                ))}
                                            </select>
                                        </div>
                                    </div>
                                    <div className="cell-50">
                                        <div className="input-select-relative">
                                            <div className="input-container">
                                                <label>Salaire de base *</label>
                                                <input type="number" autoComplete="off" onClick={this.handleSalBaseCLick} onChange={this.handleSalBaseChange} value={sal_base}/>
                                            </div>
                                            <div>
                                                {
                                                    showSalBase &&
                                                    <ul id="agenceListContainer" className="comment-vg-list">
                                                        <li value="155000" onClick={(e) => {this.handleClickSalBaseItem(e)}}>
                                                            155 000
                                                        </li>
                                                        <li value="200000" onClick={(e) => {this.handleClickSalBaseItem(e)}}>
                                                            200 000
                                                        </li>
                                                        <li value="170000" onClick={(e) => {this.handleClickSalBaseItem(e)}}>
                                                            170 000
                                                        </li>
                                                        <li value="180000" onClick={(e) => {this.handleClickSalBaseItem(e)}}>
                                                            180 000
                                                        </li>
                                                    </ul>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="row">
                                    <div className="cell-50">
                                        <div className="input-container">
                                            <label>Nb d'heure contrat *</label>
                                            <select value={nb_heure_contrat} onChange={this.handleNbHeureContratChange}>
                                                <option></option>
                                                <option value="240">240</option>
                                                <option value="312">312</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div className="cell-50">
                                        <div className="input-container">
                                            <label>Nb d'heure convenu</label>
                                            <select value={nb_heure_convenu} onChange={this.handleNbHeureConvenuChange}>
                                                <option></option>
                                                <option value="240">240</option>
                                                <option value="312">312</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div className="row">
                                    <div className="cell-50">
                                        <div className="input-container">
                                            <label>Part variable</label>
                                            <input type="number" onChange={this.handleChangePartVariable} value={part_variable}/>
                                        </div>
                                    </div>
                                    <div className="cell-50">
                                        <div className="input-container">
                                            <label>Perdiem</label>
                                            <input type="number" onChange={this.handleChangePerdiem} value={perdiem}/>
                                        </div>
                                    </div>
                                </div>
                                <div className="row">
                                    <div className="cell-50">
                                        <div className="input-container">
                                            <label>Indemnité de déplacement</label>
                                            <input type="number" onChange={this.handleChangeIdmDepl} value={idm_depl}/>
                                        </div>
                                    </div>
                                    <div className="cell-50">
                                        <div className="input-container">
                                            <label>Prime d'ancienneté</label>
                                            <input type="number" onChange={this.handleChangePrimeAncChange} value={prime_anc}/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                        {
                            activeTab == 'stagiaire' &&
                            <div className="table">
                                <div className="row">
                                    <div className="cell-50">
                                        <div className="input-container">
                                            <label>Numéro Stagiaire {societe_id == 3 && '*'}</label>
                                            <div className="table">
                                                <div className="cell">
                                                    <input onChange={this.handleChangeNumStg} value={numero_stagiaire}/>
                                                </div>
                                                { !numero_stagiaire &&
                                                    <div id="cellClientBtn" onClick={()=>{this.newNumberStagiaire()}}>
                                                        <img id="clientImg" src="/img/new_num.svg"/>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                    <div className="cell-50">
                                        <div className="input-container">
                                            <label>Date d'embauche  {societe_id == 3 && '*'}</label>
                                            <DatePicker className="datepicker" dateFormat="dd-MM-yyyy" selected={begin} onChange={this.handleBeginDate}/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                        {
                            activeTab == 'dgm' &&
                            <div className="table">
                                <div className="row">
                                    <div className="cell-50">
                                        <div className="input-container">
                                            <label>Numéro Employé {societe_id == 1 && '*'}</label>
                                            <div className="table">
                                                <div className="cell">
                                                    <input onChange={this.handleChangeNumEmp} value={numero_employe}/>
                                                </div>
                                                { !numero_employe &&
                                                    <div id="cellClientBtn" onClick={()=>{this.newNumberDgm(true)}}>
                                                        <img id="clientImg" src="/img/new_num.svg"/>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                    <div className="cell-50">
                                        <div className="input-container">
                                            <label>Date de confirmation {societe_id == 1 && '*'}</label>
                                            <DatePicker className="datepicker" dateFormat="dd-MM-yyyy" selected={date_confirmation} onChange={this.handleConfirmationDate}/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                        {
                            activeTab == 'soit' &&
                            <div className="table">
                                <div className="row">
                                    <div className="cell-50">
                                        <div className="input-container">
                                            <label>Numéro Employé {societe_id == 2 && '*'}</label>
                                            <div className="table">
                                                <div className="cell">
                                                    <input onChange={this.handleChangeNumEmpSoit} value={num_emp_soit}/>
                                                </div>
                                                { !num_emp_soit &&
                                                    <div id="cellClientBtn" onClick={()=>{this.newNumberSoit(true)}}>
                                                        <img id="clientImg" src="/img/new_num.svg"/>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                    <div className="cell-50">
                                        <div className="input-container">
                                            <label>Date de confirmation {societe_id == 2 && '*'}</label>
                                            <DatePicker className="datepicker" dateFormat="dd-MM-yyyy" selected={date_conf_soit} onChange={this.handleConfirmationDateSOIT}/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                        {
                            activeTab == 'pj' &&
                            <table className="fixed_header default layout-fixed">
                                <tbody style={{display:'block', width: '100%', overflowY: 'auto'}}>
                                    <tr onClick={(e) => {this.handleCheckDocument('cin')}}>
                                        <td className="cellSiteRadio">
                                            <label className="checkbox-container checkbox-container-lg">
                                                <input disabled checked={cin} name="siteRadio" type="checkbox"/>
                                                <span className="checkmark-lg"></span>
                                            </label>
                                        </td>
                                        <td>CIN</td>
                                    </tr>
                                    <tr onClick={(e) => {this.handleCheckDocument('cv')}}>
                                        <td className="cellSiteRadio">
                                            <label className="checkbox-container checkbox-container-lg">
                                                <input disabled checked={cv}  name="siteRadio" type="checkbox"/>
                                                <span className="checkmark-lg"></span>
                                            </label>
                                        </td>
                                        <td>CV</td>
                                    </tr>
                                    <tr onClick={(e) => {this.handleCheckDocument('photo')}}>
                                        <td className="cellSiteRadio">
                                            <label className="checkbox-container checkbox-container-lg">
                                                <input disabled checked={photo} name="siteRadio" type="checkbox"/>
                                                <span className="checkmark-lg"></span>
                                            </label>
                                        </td>
                                        <td>Photo</td>
                                    </tr>
                                    <tr onClick={(e) => {this.handleCheckDocument('residence')}}>
                                        <td className="cellSiteRadio">
                                            <label className="checkbox-container checkbox-container-lg">
                                                <input disabled checked={residence} name="siteRadio" type="checkbox"/>
                                                <span className="checkmark-lg"></span>
                                            </label>
                                        </td>
                                        <td>Certificat de résidence</td>
                                    </tr>
                                    <tr onClick={(e) => {this.handleCheckDocument('plan_reperage')}}>
                                        <td className="cellSiteRadio">
                                            <label className="checkbox-container checkbox-container-lg">
                                                <input disabled checked={plan_reperage} name="siteRadio" type="checkbox"/>
                                                <span className="checkmark-lg"></span>
                                            </label>
                                        </td>
                                        <td>Plan de repérage</td>
                                    </tr>
                                    <tr onClick={(e) => {this.handleCheckDocument('bulletin_n3')}}>
                                        <td className="cellSiteRadio">
                                            <label className="checkbox-container checkbox-container-lg">
                                                <input disabled checked={bulletin_n3} name="siteRadio" type="checkbox"/>
                                                <span className="checkmark-lg"></span>
                                            </label>
                                        </td>
                                        <td>Bulletin N°3</td>
                                    </tr>
                                    <tr onClick={(e) => {this.handleCheckDocument('bonne_conduite')}}>
                                        <td className="cellSiteRadio">
                                            <label className="checkbox-container checkbox-container-lg">
                                                <input disabled checked={bonne_conduite} name="siteRadio" type="checkbox"/>
                                                <span className="checkmark-lg"></span>
                                            </label>
                                        </td>
                                        <td>Certificat de bonne conduite</td>
                                    </tr>
                                </tbody>
                            </table>
                        }
                        <br/>
                    </div>
                </Modal>
                {
                    showSite &&
                    <Site defaultSite={site} closeModal={()=>{this.toggleSite(false)}} changeSite={this.handleChangeSite}/>
                }
            </div>
        )
    }
}