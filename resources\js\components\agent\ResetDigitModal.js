import React, { Component } from 'react'
import Modal from '../modal/Modal'
import axios from 'axios'

export default class ResetDigitModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            typeCapteur: ''
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.handleChangeTypeCapteur = this.handleChangeTypeCapteur.bind(this)
    }
    handleChangeTypeCapteur(event){
        this.setState({
            typeCapteur: event.target.value
        })
    }
    handleSave(){
        const data = new FormData()
        const {agent} = this.props
        const {typeCapteur} = this.state
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        data.append("type_capteur", typeCapteur)
        axios.post('/api/agents/reset_digit/' + agent.id, data)
        .then(({data}) => {
            if(data){
                this.props.updateAgent()
                this.props.closeModal()
            }
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        const {agent} = this.props
        const {typeCapteur} = this.state
        return (
            <Modal disableSave={!typeCapteur} handleSave={this.handleSave} handleCancel={this.handleCancel}>
                <div>
                    <h3>Réinitialiser l'empreinte</h3>
                    <div>{agent.nom}</div>
                    <div className="table">
                        <div className="row">
                            <div className="input-container">
                                <label>Type de capteur *</label>
                                <select onChange={this.handleChangeTypeCapteur} value={typeCapteur}>
                                    <option></option>
                                    {
                                        agent.empreinte &&
                                        <option value="capacitif">Capacitif</option>
                                    }
                                    {
                                        agent.empreinte_optic &&
                                        <option value="optic">Optique</option>
                                    }
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </Modal>)
    }
}