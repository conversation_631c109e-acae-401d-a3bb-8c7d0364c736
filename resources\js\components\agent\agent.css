tr.confirmed > :first-child{
    background-color: #336666;
}
.cellNum{
    width: 110px;
    max-width: 110px;
    min-width: 110px;
    overflow: hidden;
}
#searchAgent{
    box-sizing: border-box; 
    width: 250px;
    padding: 10px;
    vertical-align: middle;
    text-align: center;
    border: solid .5px rgba(0, 0, 0, .1);
}
#newAgentBtn{
    vertical-align: middle;
    font-size: 13pt;
    font-weight: normal;
}
.fix-cell-agent{
    width: 160px;
    min-width: 160px;
    max-width: 160px;
}
.cellAgent{
    width: 280px;
    max-width: 280px;
    min-width: 280px;
    overflow: hidden;
}
.cellAgentPointage{
    width: 380px;
    max-width: 380px;
    min-width: 380px;
    overflow: hidden;
}
.cellDatePointage{
    width: 180px;
    max-width: 180px;
    min-width: 180px;
    text-align: center;
    overflow: hidden;
}
#rowHeaderAgent{
    display: table-row;
    height: 50px;
}
#filterAgent{
    width: 150px;
}
#agentOverviewEditImg{
    padding: 5px;
    width: 30px;
    vertical-align: middle;
    box-shadow: 1px 1px 1px rgba(24, 42, 42, .1);
}
/*#agentOverviewEditImg:hover{
    background-color: rgba(24, 42, 42, .1);
}*/
ul#agenceListContainer{
    width:100%;
    background-color: white;
    z-index: 2000;
}
.cellSiteAgent > img {
    width: 15px;
    float: right;
    cursor: pointer;
}