import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'

import './conge.css'
import EditCongeModal from './EditCongeModal'
import DeleteCongeModal from './DeleteCongeModal'
import LoadingData from '../../loading/LoadingData'
import IconButton from '../../button/IconButton'

export default class Conge extends Component {
    constructor(props){
        super(props)
        this.state = {
            loading: false,
            dateEmbauche: null,
            conge: null,
            conges: [],
            showAddModal: false,
            showEditModal: false,
            showDeleteModal: false,
            editableMenu: false,
            widthPx: ''
        }
        this.closeModal = this.closeModal.bind(this)
        this.addModal = this.addModal.bind(this)
        this.editModal = this.editModal.bind(this)
        this.updateData = this.updateData.bind(this)
    }
    numberWithSpace(x) {
        if(x)
            return Number.parseInt(x).toString().replace(/\B(?=(\d{3})+(?!\d))/g, " ")
        return ""
    }
    closeModal(){
        this.setState({
            showAddModal: false,
            showEditModal: false,
            showDeleteModal: false,
        })
    }
    addModal(){
        this.setState({
            showAddModal: true
        })
    }
    editModal(h){
        this.setState({
            conge: h,
            showEditModal: true
        })
    }
    deleteModal(h){
        this.setState({
            conge: h,
            showDeleteModal: true
        })
    }
    updateData(){
        this.setState({
            loading: true
        })
        const {agentId} = this.props
        axios.get('/api/conges/' + agentId)
        .then(({data}) => {
            let dateEmbauche = null
            if(data.date_embauche)
                dateEmbauche = data.date_embauche
            if(!dateEmbauche || (dateEmbauche && data.date_dgm && moment(data.date_dgm).isBefore(dateEmbauche)))
                dateEmbauche = data.date_dgm
            if(!dateEmbauche || (dateEmbauche && data.date_soit && moment(data.date_soit).isBefore(dateEmbauche)))
                dateEmbauche = data.date_soit
            this.setState({
                conges: data.conges,
                dateEmbauche: dateEmbauche
            })
            this.setState({
                loading: false
            })
            this.closeModal()
        })
    }
    componentDidMount() {
        this.updateData()
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
    }
    resize() {
        if(this.container)
            this.setState({
                widthPx : this.container.offsetWidth - 370
            })
    }
    render(){
        const {heightWindow, agentId, paieDate, confirmed} = this.props
        const {loading, conge, conges, showAddModal, showEditModal, showDeleteModal, widthPx} = this.state
        return (
            <div ref={el => (this.container = el)}>
            {showAddModal && <EditCongeModal 
                action={"/api/conges/store"}
                paieDate={paieDate}
                agentId={agentId}
                closeModal={this.closeModal}
                updateData={this.updateData}/>}

            {(conge && showEditModal) && <EditCongeModal 
                action={"/api/conges/update/"+ conge.id}
                paieDate={paieDate}
                agentId={agentId}
                conge={conge}
                closeModal={this.closeModal}
                updateData={this.updateData}/>}

            {(conge && showDeleteModal) && <DeleteCongeModal 
                action={"/api/conges/delete/"+ conge.id}
                conge={conge}
                closeModal={this.closeModal}
                updateData={this.updateData}/>}
                
                
                <div className="btn-label-container">
                    <div className="table">
                        <div className="cell">
                            {
                                !paieDate && 
                                <span className="secondary">Tout</span>
                            }
                        </div>
                        <div className="cell right">
                            {
                                !confirmed &&
                                <IconButton onClick={this.addModal} label="Ajouter une conge" src="/img/add.svg"/>
                            }
                        </div>
                    </div>
                </div>
                {
                    loading ?
                        <LoadingData/>
                    :
                    (   widthPx && 
                        <div>
                            <table className="fixed_header default layout-fixed">
                                <thead>
                                    <tr>
                                        <th className="cellDateConge">Date</th>
                                        <th className="cellNbDay">Nb. Jour</th>
                                        <th style={{width: widthPx, minWidth: widthPx, maxWidth: widthPx}}>Motif</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody style={{height: (heightWindow - 450) + "px"}}>
                                    {
                                        conges && conges.map((row) => {
                                            return (
                                                <tr key={row.id}>
                                                    <td className="cellDateConge" title={moment(row.begin_date).format('ddd DD MMM YYYY')}>
                                                        {moment(row.begin_date).format('ddd DD MMM')}
                                                    </td>
                                                    <td className="cellNbDay"title={'Retour ' + moment(row.begin_date).add(parseInt(row.nb_day), 'days').format('dddd DD MMM YYYY')}>
                                                        {row.nb_day}
                                                    </td>
                                                    <td style={{width: widthPx, minWidth: widthPx, maxWidth: widthPx}} title={row.contact ? row.contact.phone : ''}>
                                                        <span title={row.motif}>{row.motif}</span>
                                                    </td>
                                                    <td>
                                                        <img onClick={() => {this.editModal(row)}} className="edit-icon-cell" title="Modifier" src="/img/edit.svg"/>
                                                        <img onClick={() => {this.deleteModal(row)}} className="edit-icon-cell" title="Supprimer" src="/img/delete.svg"/>
                                                    </td>
                                                </tr>)
                                        })
                                    }
                                </tbody>
                            </table>
                        </div>
                    )
                }
            </div>
        )
    } 
}