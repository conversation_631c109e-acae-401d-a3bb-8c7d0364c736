import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'
import DatePicker  from 'react-datepicker'

import Modal from '../../modal/Modal'
import 'react-datepicker/dist/react-datepicker.css'

export default class EditCongeModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            begin_date: '',
            nb_day: 0,
            motif: '',
            currentYear: 0,
            currentMonth: 0,
            selectedDate: '',
            error: null,
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.handleChangeBeginDate = this.handleChangeBeginDate.bind(this)
        this.handleChangeNbDay = this.handleChangeNbDay.bind(this)
        this.handleChangeMotif = this.handleChangeMotif.bind(this)
    }
    handleChangeBeginDate(date){
        this.setState({
            begin_date: date
        })
    }
    handleChangeNbDay(event){
        this.setState({
            nb_day: event.target.value
        })
    }
    handleChangeMotif(event){
        this.setState({
            motif: event.target.value
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    componentDidMount(){
        const {conge} = this.props
        this.setState({
            begin_date: conge ? moment(conge.begin_date).toDate() : '',
            nb_day: conge ? conge.nb_day : 0,
            motif: conge ? conge.motif : '',
        })
    }
    handleSave(){
        this.setState({
            error: null
        })
        const {agentId} = this.props
        const {begin_date, nb_day, motif} = this.state
        let data = new FormData()
        if(agentId)
            data.append("agent_id", agentId)
        if(begin_date)
            data.append("begin_date", begin_date)
        if(nb_day)
            data.append("nb_day", nb_day)
        if(motif)
            data.append("motif", motif)
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        axios.post(this.props.action, data)
        .then(({data}) => {
            if(data){
                if(data.error){
                    const firstKey = Object.keys(data.error)[0]
                    const firstValue = data.error[firstKey][0]
                    this.setState({
                        error: {
                            key: firstKey,
                            value: firstValue
                        },
                    })
                }
                else {
                    this.props.updateData()
                }
            }
        })
        .catch((e) => {
            console.error(e)
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        const {begin_date, motif, nb_day, error} = this.state
        return (
            <div>
                <Modal handleSave={this.handleSave} handleCancel={this.handleCancel}>
                    <h3>Conge</h3>
                    <div className="input-container">
                        <label>Motif *</label>
                        <input onChange={this.handleChangeMotif} value={motif}/>
                    </div>
                    <div className="input-container">
                        <label>Date de départ *</label>
                        <DatePicker className="datepicker" dateFormat="dd-MM-yyyy" selected={begin_date} onChange={this.handleChangeBeginDate}/>
                    </div>
                    <div className="input-container">
                        <label>Nombre de jour *</label>
                        <input onChange={this.handleChangeNbDay} value={nb_day} type="number"/>
                    </div>
                    <div className="input-container">
                        <label>Date de retour</label>
                        <DatePicker 
                            disabled 
                            className="datepicker" 
                            dateFormat="dd-MM-yyyy" 
                            selected={begin_date ? moment(begin_date).add(parseInt(nb_day), "days").toDate(): ''} 
                            onChange={this.handleChangeBeginDate}/>
                    </div>
                    <div>
                        <span className="pink">{error && error.value}</span>
                    </div>
                </Modal>
            </div>
        )
    }
}