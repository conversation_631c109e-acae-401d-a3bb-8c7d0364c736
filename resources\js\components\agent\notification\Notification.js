import React, { Component } from 'react'
import moment from 'moment'
import 'moment/locale/fr'


export default class Notification extends Component {
    constructor(props){
        super(props)
    }
    render(){
        const {now, data} = this.props
        return (
            <div id="notificationContainer">
                <div className="table">
                    <div className="cell"><h4>Notification </h4></div>
                    <div className="cell right"><span className="badge bg-light">{data.length > 9 ? '+' : ''} {data.length}</span></div>
                </div>
                <ul id="notificationList">
                    {
                        data.map((agent) => {
                            if(agent.confirmation)
                                return <li key={agent.id} className="lb-green" onClick={() => {this.props.clickItem(agent.id)}}>
                                    L'agent {agent.nom.substr(0, 20) + (agent.nom.length > 20 ? '.' : '')} doit être confirmé.
                                </li>
                            else
                                return <li key={agent.id} className="lb-red" onClick={() => {this.props.clickItem(agent.id)}}>
                                    L'agent {agent.nom.substr(0, 20)} est inactif {agent.last_date_pointage && moment(agent.last_date_pointage).from(moment(now))}.
                                </li>
                        })
                    }
                </ul>
            </div>
        )
    }
}