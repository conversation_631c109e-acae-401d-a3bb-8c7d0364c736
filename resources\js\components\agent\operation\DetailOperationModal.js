import React, { Component } from 'react'
import moment from 'moment'

import 'moment/locale/fr'

export default class Modal extends Component {
    constructor(props){
        super(props)
        this.state = {
            confirm: '',
            operation: ''
        }
        this.handleCancel = this.handleCancel.bind(this)
    }
    handleCancel(){
        this.props.handleCancel()
    }
    render(){
        const {operation} = this.props
        return (
            <div style={{zIndex: 200}} className="fixed-front">
                <div className="table">
                    <div className="modal-container">
                        <div className="modal md">
                            <div className="modal-content">
                                <h4>{operation.objet}</h4>
                                <textarea rows="20" disabled style={{fontFamily: "CallingCode"}}>
                                    {
                                        (operation.detail && operation.detail.split("\\n").length > 1) ? 
                                        operation.detail.split("\\n").join("\n") : 
                                        operation.detail.split("<br/>").join("\n")
                                    }
                                </textarea>
                                <div id="authorOperation">
                                    {operation.user && operation.user.email.toUpperCase()}, 
                                    {moment(operation.created_at).format('dddd DD MMM YYYY')}
                                </div>
                            </div>
                            <div className="right modal-footer">
                                <button onClick={this.handleCancel} className="btn-default fix-width">OK</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>)
    }
}