import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'
import DatePicker from 'react-datepicker'
import { saveAs } from "file-saver";
const Excel = require("exceljs")

import './operation.css'
import 'react-datepicker/dist/react-datepicker.css'
import LoadingData from '../../loading/LoadingData'
import DetailOperationModal from './DetailOperationModal'

export default class Operation extends Component {
    constructor(props) {
        super(props)
        this.state = {
            operation: null,
            showDetailModal: false,
            widthAction: '',
            begin: null,
            end: null,
            loading: false
        }
        this.handleCloseDetailModal = this.handleCloseDetailModal.bind(this)
        this.handleClickSearch = this.handleClickSearch.bind(this)
        this.handleClickExport = this.handleClickExport.bind(this)
        this.handleBeginDate = this.handleBeginDate.bind(this)
        this.handleEndDate = this.handleEndDate.bind(this)
    }
    handleCloseDetailModal() {
        this.setState({
            showDetailModal: false
        })
    }
    handleShowDetailModal(operation) {
        this.setState({
            operation: operation,
            showDetailModal: true
        })
    }

    async handleClickExport() {
        console.log('click export')
        const { begin, end, operations } = this.state
        const { nomSite } = this.props
        const titleExport = 'Historique ' + nomSite + ' du ' + moment(begin).format('DD-MM-YYYY')
            + (moment(begin).format('DD-MM-YYYY') != moment(end).format('DD-MM-YYYY') ? (' au ' + moment(end).format('DD-MM-YYYY')) : '')
        const workbook = new Excel.Workbook()
        const worksheet = workbook.addWorksheet(nomSite)

        worksheet.getCell('A1').value = nomSite
        worksheet.getCell('A1').font = {
            size: 20,
            bold: true
        }
        worksheet.getCell('A2').value = 'Historique du ' + moment(begin).format('DD-MM-YYYY')
            + (moment(begin).format('DD-MM-YYYY') != moment(end).format('DD-MM-YYYY') ? (' au ' + moment(end).format('DD-MM-YYYY')) : '')
        worksheet.getCell('A2').font = {
            size: 14
        }

        worksheet.mergeCells('A1:C1')
        worksheet.mergeCells('A2:C2')
        worksheet.mergeCells('A3:C3')

        worksheet.getColumn('A').width = 20
        worksheet.getColumn('B').width = 50
        worksheet.getColumn('C').width = 40

        const headerFont = { bold: true }
        const borderStyle = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
        }
        worksheet.getCell('A4').value = 'Date'
        worksheet.getCell('A4').border = borderStyle
        worksheet.getCell('A4').font = headerFont
        worksheet.getCell('B4').value = 'Alarme'
        worksheet.getCell('B4').border = borderStyle
        worksheet.getCell('B4').font = headerFont
        worksheet.getCell('C4').value = 'Zone'
        worksheet.getCell('C4').border = borderStyle
        worksheet.getCell('C4').font = headerFont

        let line = 5
        operations.map((row) => {
            worksheet.getCell('A' + line).value = moment(row.dateArrived).format('DD/MM/YYYY HH:mm')
            worksheet.getCell('A' + line).border = borderStyle
            worksheet.getCell('B' + line).value = '[' + row.codeTevent + '] '
            worksheet.getCell('B' + line).border = borderStyle
            worksheet.getCell('C' + line).value = (row.numZone ? ('000' + row.numZone).slice(-3) : '') + (row.nomZone ? ' (' + row.nomZone + ')' : '')
            worksheet.getCell('C' + line).border = borderStyle
            line++
        })

        const buffer = await workbook.xlsx.writeBuffer();
        const fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        const fileExtension = '.xlsx'
        const blob = new Blob([buffer], { type: fileType });
        saveAs(blob, titleExport + fileExtension);

    }
    handleEndDate(date) {
        this.setState({
            end: date
        })
    }
    handleBeginDate(date) {
        this.setState({
            begin: date
        })
    }
    getOperation() {
        this.setState({
            loading: true
        })
        const { begin, end } = this.state
        axios.get(this.props.action + '/' +
            moment(begin).format('YYYY-MM-DD') + '/' +
            moment(end).format('YYYY-MM-DD')
        )
            .then(({ data }) => {
                console.log(data)
                this.setState({
                    operations: data,
                    loading: false
                })
            })
            .catch(() => {
                this.setState({
                    loading: false
                })
            })
    }
    handleClickSearch(event) {
        this.getOperation()
    }
    componentDidMount() {
        const { currentDate } = this.props
        this.setState({
            begin: moment(currentDate).subtract(1, 'month').toDate(),
            end: moment(currentDate).toDate()
        }, () => {
            this.getOperation()
        })
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
    }
    resize() {
        if (this.container)
            this.setState({
                widthAction: (this.container.offsetWidth - 352)
            })
    }
    render() {
        const { begin, end, loading, operations, widthAction, showDetailModal, operation } = this.state
        const { heightTable } = this.props
        return (
            <div>
                {(showDetailModal && operation) && <DetailOperationModal handleCancel={this.handleCloseDetailModal} operation={operation} />}
                <div id="searchBarHistorique" ref={el => (this.container = el)}>
                    <div className="table">
                        <div className="cell">
                            <DatePicker className="datepicker" dateFormat="dd-MM-yyyy" selected={begin} onChange={this.handleBeginDate} />
                        </div>
                        <div className="cell">
                            <DatePicker className="datepicker" dateFormat="dd-MM-yyyy" selected={end} onChange={this.handleEndDate} />
                        </div>
                        <div className="cell right">
                            <button disabled={loading} onClick={this.handleClickSearch} id="searchHistoriqueBtn">Rechercher</button>
                            <button disabled={loading || !operations || operations.length == 0} onClick={this.handleClickExport} id="exportHistoriqueBtn">Exporter</button>
                        </div>
                    </div>
                </div>
                {
                    loading ?
                        <LoadingData />
                        :
                        <table className="fixed_header default layout-fixed">
                            <thead>
                                <tr>
                                    <th className="cellDateOperation">Date</th>
                                    <th className="cellUser">Utilisateur</th>
                                    <th style={{ width: widthAction, minWidth: widthAction, maxWidth: widthAction }}>Action</th>
                                    <th className="cellDetailIcon"></th>
                                </tr>
                            </thead>
                            <tbody style={{ height: (heightTable) + "px" }}>
                                {
                                    operations && operations.map((row) => {
                                        return (
                                            <tr key={row.id}>
                                                <td className="cellDateOperation">
                                                    {row.created_at && moment(row.created_at).format('DD/MM/YYYY HH:mm')}
                                                </td>
                                                <td className="cellUser">
                                                    {row.user && row.user.email.toUpperCase()}
                                                </td>
                                                <td style={{ width: widthAction, minWidth: widthAction, maxWidth: widthAction }} title={row.detail ? row.detail.split("\\n").join("\n") : ''}>
                                                    {row.objet + ' '}
                                                    <span className="secondary">
                                                        {
                                                            row.detail &&
                                                            ': ' +
                                                            (
                                                                (row.detail && row.detail.split("\\n").length > 1) ?
                                                                    row.detail.split("\\n")[0] + (row.detail.split("\\n").length > 1 ? "..." : "") :
                                                                    row.detail.split("<br/>")[0] + (row.detail.split("<br/>").length > 1 ? "..." : "")
                                                            )
                                                        }
                                                    </span>
                                                </td>
                                                <td className="cellDetailIcon">
                                                    <img onClick={() => { this.handleShowDetailModal(row) }} className="img-btn" title="Détail" src="/img/detail.svg" />
                                                </td>
                                            </tr>)
                                    })
                                }
                            </tbody>
                        </table>
                }
            </div>
        )
    }
}