#navHistorique{
    margin-bottom: 0px;
}
#navHistorique li{
    display: inline-block;
    cursor: pointer;
    padding: 10px;
}
#navHistorique li:hover{
    background-color: #e3e3e3;
}
#navHistorique li.active{
    color: white;
    background-color: #366666;
}
.datepicker{
    display: inline-block;
    padding: 10px;
    border: solid .5px rgba(0, 0, 0, .1);
    width: 95%;
}
#searchBarHistorique{
    padding: 10px 0px;
}
#searchHistoriqueBtn{
    padding: 10px;
    background-color: #366666;
    color: white;
    border: none;
}
#exportHistoriqueBtn{
    display: inline-block;
    margin-left: 5px;
    padding: 10px;
    background-color: #aaa;
    color: white;
    border: none;
}
#searchHistoriqueBtn:disabled, #exportHistoriqueBtn:disabled{
    opacity: .5;
}
.cellDateOperation{
    width: 170px;
    min-width: 170px;
    max-width: 170px;
}
td.cellDateOperation{
    font-family: "CallingCode";
}
.cellUser{
    text-align: center;
    width: 120px;
    min-width: 120px;
    max-width: 120px;
}
.cellDetailIcon{
    text-align: center;
    width: 70px;
    min-width: 70px;
    max-width: 70px;
}
#authorOperation{
    padding: 10px 0px;
    color: rgba(0, 0, 0, .7);
    font-size: 14px;
    font-weight: bold;
}