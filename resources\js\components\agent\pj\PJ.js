import React, { Component } from 'react'

export default class PJ extends Component {
    constructor(props){
        super(props)
    }
    componentDidMount(){
        console.log(this.props.agent)
    }
    render(){
        const {agent, heightWindow} = this.props
        return (
            <table className="fixed_header default layout-fixed">
                <tbody style={{display:'block', width: '100%', height: (heightWindow - 340) + "px", overflowY: 'auto'}}>
                    <tr className={agent.cin ? "confirmed" : ""}>
                        <td className="cellSiteRadio">
                            {
                                agent.cin == 1 &&
                                <span className="checkmark-label"></span>
                            }
                        </td>
                        <td>CIN</td>
                    </tr>
                    <tr className={agent.cv ? "confirmed" : ""}>
                        <td className="cellSiteRadio">
                            {
                                agent.cv == 1 &&
                                <span className="checkmark-label"></span>
                            }
                        </td>
                        <td>CV</td>
                    </tr>
                    <tr className={agent.photo ? "confirmed" : ""}>
                        <td className="cellSiteRadio">
                            {
                                agent.photo == 1 &&
                                <span className="checkmark-label"></span>
                            }
                        </td>
                        <td>Photo</td>
                    </tr>
                    <tr className={agent.residence ? "confirmed" : ""}>
                        <td className="cellSiteRadio">
                            {
                                agent.residence == 1 &&
                                <span className="checkmark-label"></span>
                            }
                        </td>
                        <td>Certificat de résidence</td>
                    </tr>
                    <tr className={agent.plan_reperage ? "confirmed" : ""}>
                        <td className="cellSiteRadio">
                            {
                                agent.plan_reperage == 1 &&
                                <span className="checkmark-label"></span>
                            }
                        </td>
                        <td>Plan de repérage</td>
                    </tr>
                    <tr className={agent.bulletin_n3 ? "confirmed" : ""}>
                        <td className="cellSiteRadio">
                            {
                                agent.bulletin_n3 == 1 &&
                                <span className="checkmark-label"></span>
                            }
                        </td>
                        <td>Bulletin N°3</td>
                    </tr>
                    <tr className={agent.bonne_conduite ? "confirmed" : ""}>
                        <td className="cellSiteRadio">
                            {
                                agent.bonne_conduite == 1 &&
                                <span className="checkmark-label"></span>
                            }
                        </td>
                        <td>Certificat de bonne conduite</td>
                    </tr>
                </tbody>
            </table>
        )
    } 
}