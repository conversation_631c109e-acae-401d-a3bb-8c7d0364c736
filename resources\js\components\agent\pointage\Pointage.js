import React, { Component } from 'react'
import moment from 'moment'
import axios from 'axios'
import {saveAs} from "file-saver";
const Excel = require("exceljs")

import './pointage.css'
import 'react-datepicker/dist/react-datepicker.css'
import LoadingData from '../../loading/LoadingData'

export default class Pointage extends Component {
    constructor(props){
        super(props)
        this.state = {
            errorMessage: '',
            begin: null,
            end: null,
            month: 0,
            year: 0,
            pointages: [],
            loading: false
        }
        this.handleClickSearch = this.handleClickSearch.bind(this)
        this.handleClickExport = this.handleClickExport.bind(this)
        this.handleBeginDate = this.handleBeginDate.bind(this)
        this.handleEndDate = this.handleEndDate.bind(this)
        this.handleChangeMonth = this.handleChangeMonth.bind(this)
        this.handleChangeYear = this.handleChangeYear.bind(this)
    }
    handleChangeMonth(e){
        this.setState({
            month: e.target.value
        })
    }
    handleChangeYear(e){
        this.setState({
            year: e.target.value
        })
    }
    async handleClickExport(){
        console.log('click export')
        const {begin, end, pointages} = this.state
        const {nomAgent} = this.props
        const titleExport = 'Historique ' + nomAgent + ' du ' + moment(begin).format('DD-MM-YYYY')
         + (moment(begin).format('DD-MM-YYYY') != moment(end).format('DD-MM-YYYY') ? (' au ' + moment(end).format('DD-MM-YYYY')): '')
        const workbook = new Excel.Workbook()
        const worksheet = workbook.addWorksheet(nomAgent)

        worksheet.getCell('A1').value = nomAgent
        worksheet.getCell('A1').font = {
            size: 20,
            bold: true
        }
        worksheet.getCell('A2').value = 'Historique du ' + moment(begin).format('DD-MM-YYYY')
        + (moment(begin).format('DD-MM-YYYY') != moment(end).format('DD-MM-YYYY') ? (' au ' + moment(end).format('DD-MM-YYYY')): '')
        worksheet.getCell('A2').font = {
            size: 14
        }
        
        worksheet.mergeCells('A1:C1')
        worksheet.mergeCells('A2:C2')
        worksheet.mergeCells('A3:C3')

        worksheet.getColumn('A').width = 20
        worksheet.getColumn('B').width = 50
        worksheet.getColumn('C').width = 40

        const headerFont = { bold: true }
        const borderStyle = {
            top: {style:'thin'},
            left: {style:'thin'},
            bottom: {style:'thin'},
            right: {style:'thin'}
        }
        worksheet.getCell('A4').value = 'Date'
        worksheet.getCell('A4').border = borderStyle
        worksheet.getCell('A4').font = headerFont
        worksheet.getCell('B4').value = 'Alarme'
        worksheet.getCell('B4').border = borderStyle
        worksheet.getCell('B4').font = headerFont
        worksheet.getCell('C4').value = 'Zone'
        worksheet.getCell('C4').border = borderStyle
        worksheet.getCell('C4').font = headerFont

        let line = 5
        pointages.map((row) =>{
            worksheet.getCell('A' + line).value = moment(row.dateArrived).format('DD/MM/YYYY HH:mm')
            worksheet.getCell('A' + line).border = borderStyle
            worksheet.getCell('B' + line).value = '['  + row.codeTevent + '] '
            worksheet.getCell('B' + line).border = borderStyle
            worksheet.getCell('C' + line).value = (row.numZone ? ('000' + row.numZone).slice(-3) : '') + (row.nomZone ? ' (' + row.nomZone + ')' : '')
            worksheet.getCell('C' + line).border = borderStyle
            line++
        })

        const buffer = await workbook.xlsx.writeBuffer();
        const fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        const fileExtension = '.xlsx'
        const blob = new Blob([buffer], {type: fileType});
        saveAs(blob, titleExport + fileExtension);
        
    }
    handleEndDate(date){
        this.setState({
            end: date
        })
    }
    handleBeginDate(date){
        this.setState({
            begin: date
        })
    }
    handleClickSearch(event){
        this.getPointage()
    }
    getPointage(){
        this.setState({
            loading: true
        })
        const {month, year} = this.state
        axios.get('/api/agents/pointage/' + 
            this.props.agentId + '?month=' + 
            month + '&year=' + year
        )
        .then(({data})=> {
            if(data.message == 'set_site'){
                console.log(data.message)
                this.setState({
                    errorMessage: "Veuiller remplir le site occupé par l'agent",
                    pointages: [],
                    month: 0,
                    year: 0,
                    begin: null,
                    end: null,
                    loading: false
                })
            }
            else
                this.setState({
                    errorMessage: '',
                    pointages: data.pointages,
                    month: data.month,
                    year: data.year,
                    begin: data.begin,
                    end: data.end,
                    loading: false
                })
        })
        .catch((e) => {
            console.log(e)
            this.setState({
                loading: false
            })
        })
    }
    componentDidMount(){
        this.getPointage()
    }
    render(){
        const {errorMessage, begin, end, month, year, pointages, loading} = this.state
        const {heightWindow} = this.props
        const currentYear = Number.parseInt(moment().format('YYYY'))
        return (
            <div>
                <div id="searchBarHistorique">
                    <div className="table">
                        <div className="cell">
                            <select className="custom-input" value={month} onChange={this.handleChangeMonth}>
                                <option value="0"></option>
                                <option value="1">Janvier</option>
                                <option value="2">Fevrier</option>
                                <option value="3">Mars</option>
                                <option value="4">Avril</option>
                                <option value="5">Mai</option>
                                <option value="6">Juin</option>
                                <option value="7">Juillet</option>
                                <option value="8">Août</option>
                                <option value="9">Septembre</option>
                                <option value="10">Octobre</option>
                                <option value="11">Novembre</option>
                                <option value="12">Décembre</option>
                            </select>
                        </div>
                        <div className="cell">
                            <select className="custom-input" value={year} onChange={this.handleChangeYear}>
                                <option value={currentYear-2}>
                                    {currentYear-2}
                                </option>
                                <option value={currentYear-1}>
                                    {currentYear-1}
                                </option>
                                <option value={currentYear}>
                                    {currentYear}
                                </option>
                            </select>
                        </div>
                        <div className="cell right">
                            <button disabled={loading} onClick={this.handleClickSearch} id="searchHistoriqueBtn">Rechercher</button>
                            <button disabled={loading || !pointages || pointages.length == 0} onClick={this.handleClickExport} id="exportHistoriqueBtn">Exporter</button>
                        </div>
                    </div>
                </div>
                {
                    loading ?
                        <LoadingData/>
                    :
                    errorMessage ?
                        <h3 className="default center">{errorMessage}</h3>
                    :
                    <div>
                        {
                            (begin && end) &&
                            <h3>
                                Du {moment(begin).format('DD MMM YYYY')} au {moment(end).format('DD MMM YYYY')}
                            </h3>
                        }
                        <table className="fixed_header default layout-fixed">
                            <thead>
                                <tr>
                                    <th className="cellDatePointage">Date</th>
                                    <th className="cellHoraire">Horaire</th>
                                    <th>Site</th>
                                </tr>
                            </thead>
                            <tbody style={{height: (heightWindow - 500) + "px"}}>
                                {
                                    pointages && pointages.map((row) =>{
                                        return (
                                            <tr key={row.id} onDoubleClick={() => {this.handleClickRow(row)}}>
                                                <td className="cellDatePointage">
                                                    {
                                                        moment(row.date_pointage).format('ddd DD MMM YY').substr(0, 3).toUpperCase() + 
                                                        moment(row.date_pointage).format('ddd DD MMM YY').substr(3)
                                                    }
                                                </td>
                                                <td className="cellHoraire">
                                                    {moment(row.date_pointage).format('HH:mm') == '18:00' ? 'NUIT': 'JOUR'}
                                                </td>
                                                <td>
                                                    {row.site}
                                                </td>
                                            </tr>)
                                    })
                                }
                            </tbody>
                        </table>
                    </div>
                }
            </div>
        )
    } 
}