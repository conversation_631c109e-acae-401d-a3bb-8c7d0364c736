import React, { Component } from 'react'
import axios from 'axios'

import Modal from '../../modal/Modal'
import moment from 'moment'

export default class EditPrimeModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            montant: 0,
            typeId: 0,
            currentYear: 0,
            currentMonth: 0,
            selectedDate: '',
            error: null,
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.handleChangeMontant = this.handleChangeMontant.bind(this)
        this.handleChangeType = this.handleChangeType.bind(this)
        this.handleChangeMonth = this.handleChangeMonth.bind(this)
    }
    handleChangeMonth(event){
        this.setState({
            selectedDate: event.target.value,
        })
    }
    handleChangeMontant(event){
        this.setState({
            montant: event.target.value
        })
    }
    handleChangeType(event){
        this.setState({
            typeId: event.target.value
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    getMonth(){
        const {agentId} = this.props
        axios.get('/api/primes/month/' + agentId)
        .then(({data}) =>{
            this.setState({
                currentYear: data.year,
                currentMonth: data.month,
            })
        })
        .catch(() => {
            setTimeout(() => {
                this.getMonth()
            }, 300)
        })
    }
    componentDidMount(){
        this.getMonth()
        const {prime, defaultDate} = this.props
        if(defaultDate){
            this.setState({
                selectedDate: defaultDate,
            })
        }
        else{
            this.setState({
                selectedDate: prime ? moment({month: prime.month - 1, year: prime.year}).format('YYYY-MM-DD') : '',
                montant: prime ? prime.montant : 0,
                motif: prime ? prime.motif : '',
                typeId: prime ? prime.type_id: 0
            })
        }
    }
    handleSave(){
        this.setState({
            error: null
        })
        const {agentId, paieDate} = this.props
        const {selectedDate, montant, typeId} = this.state
        let data = new FormData()
        if(paieDate){
            data.append("month", moment(paieDate, "YYYY-MM-DD").format('MM'))
            data.append("year", moment(paieDate, "YYYY-MM-DD").format('YYYY'))
        }
        else if(selectedDate){
            data.append("month", moment(selectedDate, "YYYY-MM-DD").format('MM'))
            data.append("year", moment(selectedDate, "YYYY-MM-DD").format('YYYY'))
        }
        if(agentId)
            data.append("agent_id", agentId)
        if(montant)
            data.append("montant", montant)
        if(typeId)
            data.append("type_id", typeId)
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        axios.post(this.props.action, data)
        .then(({data}) => {
            console.log('data: ', data)
            if(data){
                if(data.error){
                    const firstKey = Object.keys(data.error)[0]
                    const firstValue = data.error[firstKey][0]
                    this.setState({
                        error: {
                            key: firstKey,
                            value: firstValue
                        },
                    })
                }
                else {
                    this.props.updateData()
                }
            }
        })
        .catch((e) => {
            console.error(e)
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        const {typeId, montant, selectedDate, currentMonth, currentYear, error} = this.state
        const {types, paieDate} = this.props
        return (
            <div>
                <Modal handleSave={this.handleSave} handleCancel={this.handleCancel}>
                    <h3>Prime</h3>
                    {
                        !paieDate &&
                        <div className="input-container">
                            <label>Mois *</label>
                            <select value={selectedDate} onChange={this.handleChangeMonth}>
                                <option></option>
                                {
                                    currentMonth &&
                                    <option value={moment().set({month: currentMonth - 1, year: currentYear}).subtract(1, 'month').format('YYYY-MM-DD')}>
                                        {moment().set({month: currentMonth - 1, year: currentYear}).subtract(1, 'month').format('MMMM').toUpperCase()}
                                    </option>
                                }
                                {
                                    currentYear &&
                                    <option value={moment().set({month: currentMonth - 1, year: currentYear}).format('YYYY-MM-DD')}>
                                        {moment().set({month: currentMonth - 1, year: currentYear}).format('MMMM').toUpperCase()}
                                    </option>
                                }
                            </select>
                        </div>
                    }
                    <div className="input-container">
                        <label>Montant *</label>
                        <input onChange={this.handleChangeMontant} value={montant} type="number"/>
                    </div>
                    <div className="input-container">
                        <label>Type *</label>
                        <select onChange={this.handleChangeType} value={typeId}>
                            <option value="0"></option>
                            {
                                types.map((tp) => {
                                    return <option key={tp.id} value={tp.id}>{tp.libelle}</option>
                                })
                            }
                        </select>
                    </div>
                    <div>
                        <span className="pink">{error && error.value}</span>
                    </div>
                </Modal>
            </div>
        )
    }
}