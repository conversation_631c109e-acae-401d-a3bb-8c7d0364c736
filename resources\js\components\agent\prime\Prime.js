import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'

import './prime.css'
import EditPrimeModal from './EditPrimeModal'
import DeletePrimeModal from './DeletePrimeModal'
import LoadingData from '../../loading/LoadingData'
import Icon<PERSON>utton from '../../button/IconButton'

export default class Prime extends Component {
    constructor(props){
        super(props)
        this.state = {
            month: 0,
            year: 0,
            loading: false,
            prime: null,
            primes: [],
            typePrimes: [],
            showAddModal: false,
            showEditModal: false,
            showDeleteModal: false,
            showAll: false,
            editableMenu: false,
            widthPx: ''
        }
        this.closeModal = this.closeModal.bind(this)
        this.addModal = this.addModal.bind(this)
        this.editModal = this.editModal.bind(this)
        this.updateData = this.updateData.bind(this)
        this.handleChangeMonth = this.handleChangeMonth.bind(this)
        this.handleChangeYear = this.handleChangeYear.bind(this)
        this.toggleAll = this.toggleAll.bind(this)
    }
    toggleAll(){
        this.setState({
            showAll: !this.state.showAll
        }, () => {
            this.updateData()
        })
    }
    handleChangeMonth(e){
        this.setState({
            month: e.target.value
        })
    }
    handleChangeYear(e){
        this.setState({
            year: e.target.value
        })
    }
    numberWithSpace(x) {
        if(x)
            return Number.parseInt(x).toString().replace(/\B(?=(\d{3})+(?!\d))/g, " ")
        return ""
    }
    closeModal(){
        this.setState({
            showAddModal: false,
            showEditModal: false,
            showDeleteModal: false,
        })
    }
    addModal(){
        this.setState({
            showAddModal: true
        })
    }
    editModal(h){
        this.setState({
            prime: h,
            showEditModal: true
        })
    }
    deleteModal(h){
        this.setState({
            prime: h,
            showDeleteModal: true
        })
    }
    updateData(){
        this.setState({
            loading: true
        })
        const {agentId, paieDate} = this.props
        const {showAll} = this.state
        let month = 0
        let year = 0
        if(paieDate){
            month = moment(paieDate).format("MM")
            year = moment(paieDate).format("YYYY")
        }
        axios.get('/api/primes/' + (showAll ? 'all/': '') + agentId + '?month=' + month + '&year=' + year)
        .then(({data}) => {
            this.setState({
                primes: data.primes,
                typePrimes: data.type_primes,
            })
            this.setState({
                loading: false
            })
            this.closeModal()
        })
    }
    componentDidMount() {
        this.updateData()
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
    }
    resize() {
        if(this.container)
            this.setState({
                widthPx : this.container.offsetWidth - 370
            })
    }
    render(){
        const {heightWindow, agentId, paieDate, confirmed} = this.props
        const {showAll, loading, prime, primes, typePrimes, showAddModal, showEditModal, showDeleteModal, widthPx} = this.state
        return (
            <div ref={el => (this.container = el)}>
            {showAddModal && <EditPrimeModal 
                action={"/api/primes/store"}
                paieDate={paieDate}
                agentId={agentId}
                closeModal={this.closeModal}
                updateData={this.updateData}
                types={typePrimes}/>}

            {(prime && showEditModal) && <EditPrimeModal 
                action={"/api/primes/update/"+ prime.id}
                paieDate={paieDate}
                agentId={agentId}
                prime={prime}
                closeModal={this.closeModal}
                updateData={this.updateData}
                types={typePrimes}/>}

            {(prime && showDeleteModal) && <DeletePrimeModal 
                action={"/api/primes/delete/"+ prime.id}
                prime={prime}
                closeModal={this.closeModal}
                updateData={this.updateData}/>}
                
                <div className="btn-label-container">
                    <div className="table">
                        <div className="cell">
                            {
                                !paieDate && 
                                <span className={"link" + (showAll ? "primary": "secondary")} onClick={this.toggleAll}>
                                    {showAll ? "Retour": "Tout"}
                                </span>
                            }
                        </div>
                        <div className="cell right">
                            {
                                (!showAll && !confirmed) &&
                                <IconButton onClick={this.addModal} label="Ajouter une prime" src="/img/add.svg"/>
                            }
                        </div>
                    </div>
                </div>
                {
                    loading ?
                        <LoadingData/>
                    :
                    (   widthPx && 
                        <div>
                            <table className="fixed_header default layout-fixed">
                                <thead>
                                    <tr>
                                        { !paieDate && <th className="cellService">Mois</th> }
                                        <th className="cellMontant center">Montant</th>
                                        <th style={{width: widthPx, minWidth: widthPx, maxWidth: widthPx}}>Type</th>
                                        { (!showAll && !confirmed) && <th></th> }
                                    </tr>
                                </thead>
                                <tbody style={{height: (heightWindow - 450) + "px"}}>
                                    {
                                        primes && primes.map((row) => {
                                            return (
                                                <tr key={row.id}>
                                                    {
                                                        !paieDate &&
                                                        <td className="cellService" title={moment().set({month: row.month - 1, year: row.year}).format('MMM YYYY')}>
                                                            {moment().set({month: row.month - 1, year: row.year}).format('MMMM').toUpperCase()}
                                                        </td>
                                                    }
                                                    <td className="cellMontant">
                                                        {this.numberWithSpace(row.montant)}
                                                    </td>
                                                    <td style={{width: widthPx, minWidth: widthPx, maxWidth: widthPx}} title={row.contact ? row.contact.phone : ''}>
                                                        <span title={row.type}>{row.type}</span>
                                                    </td>
                                                    {
                                                        (!showAll && !confirmed) &&
                                                        <td>
                                                            <img onClick={() => {this.editModal(row)}} className="edit-icon-cell" title="Modifier" src="/img/edit.svg"/>
                                                            <img onClick={() => {this.deleteModal(row)}} className="edit-icon-cell" title="Supprimer" src="/img/delete.svg"/>
                                                        </td>
                                                    }
                                                </tr>)
                                        })
                                    }
                                </tbody>
                            </table>
                        </div>
                    )
                }
            </div>
        )
    } 
}