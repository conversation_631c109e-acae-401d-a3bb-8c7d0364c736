import React, { Component } from 'react'
import Modal from '../../modal/Modal'
import axios from 'axios'

export default class DeleteSanctionModal extends Component {
    constructor(props){
        super(props)
        this.state = {
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }
    handleSave(){
        const data = new FormData()
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        axios.post(this.props.action, data)
        .then(({data}) => {
            this.props.updateData()
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        return (
            <Modal handleSave={this.handleSave} handleCancel={this.handleCancel}>
                <h3>Sanction</h3>
                <div>Voulez-vous vraiment supprimer cette sanction ?</div>
            </Modal>)
    }
}