import React, { Component } from 'react'
import axios from 'axios'

import Modal from '../../modal/Modal'
import moment from 'moment'

export default class EditSanctionModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            montant: 0,
            motif: '',
            currentYear: 0,
            currentMonth: 0,
            selectedDate: '',
            error: null,
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.handleChangeMontant = this.handleChangeMontant.bind(this)
        this.handleChangeMotif = this.handleChangeMotif.bind(this)
        this.handleChangeMonth = this.handleChangeMonth.bind(this)
    }
    handleChangeMonth(event){
        this.setState({
            selectedDate: event.target.value,
        })
    }
    handleChangeMontant(event){
        this.setState({
            montant: event.target.value
        })
    }
    handleChangeMotif(event){
        this.setState({
            motif: event.target.value
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    getMonth(){
        const {agentId} = this.props
        axios.get('/api/primes/month/' + agentId)
        .then(({data}) =>{
            this.setState({
                currentYear: data.year,
                currentMonth: data.month,
            })
        })
        .catch(() => {
            setTimeout(() => {
                this.getMonth()
            }, 300)
        })
    }
    componentDidMount(){
        this.getMonth()
        const {sanction} = this.props
        this.setState({
            selectedDate: sanction ? moment({month: sanction.month - 1, year: sanction.year}).format('YYYY-MM-DD') : '',
            montant: sanction ? sanction.montant : 0,
            motif: sanction ? sanction.motif : '',
        })
    }
    handleSave(){
        this.setState({
            error: null
        })
        const {agentId, paieDate} = this.props
        const {selectedDate, montant, motif} = this.state
        let data = new FormData()
        if(paieDate){
            data.append("month", moment(paieDate, "YYYY-MM-DD").format('MM'))
            data.append("year", moment(paieDate, "YYYY-MM-DD").format('YYYY'))
        }
        else if(selectedDate){
            data.append("month", moment(selectedDate, "YYYY-MM-DD").format('MM'))
            data.append("year", moment(selectedDate, "YYYY-MM-DD").format('YYYY'))
        }
        if(agentId)
            data.append("agent_id", agentId)
        if(motif)
            data.append("motif", motif)
        if(montant)
            data.append("montant", montant)
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        axios.post(this.props.action, data)
        .then(({data}) => {
            if(data){
                if(data.error){
                    const firstKey = Object.keys(data.error)[0]
                    const firstValue = data.error[firstKey][0]
                    this.setState({
                        error: {
                            key: firstKey,
                            value: firstValue
                        },
                    })
                }
                else {
                    this.props.updateData()
                }
            }
        })
        .catch((e) => {
            console.error(e)
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        const {motif, montant, selectedDate, currentMonth, currentYear, error} = this.state
        const {paieDate} = this.props
        return (
            <div>
                <Modal handleSave={this.handleSave} handleCancel={this.handleCancel}>
                    <h3>Sanction</h3>
                    {
                        !paieDate &&
                        <div className="input-container">
                            <label>Mois *</label>
                            <select value={selectedDate} onChange={this.handleChangeMonth}>
                                <option></option>
                                {
                                    currentMonth &&
                                    <option value={moment().set({month: currentMonth - 1, year: currentYear}).subtract(1, 'month').format('YYYY-MM-DD')}>
                                        {moment().set({month: currentMonth - 1, year: currentYear}).subtract(1, 'month').format('MMMM').toUpperCase()}
                                    </option>
                                }
                                {
                                    currentYear &&
                                    <option value={moment().set({month: currentMonth - 1, year: currentYear}).format('YYYY-MM-DD')}>
                                        {moment().set({month: currentMonth - 1, year: currentYear}).format('MMMM').toUpperCase()}
                                    </option>
                                }
                            </select>
                        </div>
                    }
                    <div className="input-container">
                        <label>Montant *</label>
                        <input onChange={this.handleChangeMontant} value={montant} type="number"/>
                    </div>
                    <div className="input-container">
                        <label>Motif *</label>
                        <input onChange={this.handleChangeMotif} value={motif}/>
                    </div>
                    <div>
                        <span className="pink">{error && error.value}</span>
                    </div>
                </Modal>
            </div>
        )
    }
}