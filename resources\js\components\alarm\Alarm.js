import React, { Component } from 'react'
import axios from 'axios'

import Historique from '../site/historique/Historique'
import Rapport from './rapport/Rapport'
import AquitAlarmModal from './AquitAlarmModal'
import Filtre from './Filtre'
import LoadingCallModal from '../vigilance/LoadingCallModal'
import { isEmpty } from 'lodash'
import { formDataOption } from '../../../../auth'

export default class Alarm extends Component {
    constructor(props) {
        super(props)
        this.state = {
            timeoutId: null,
            currentLog: null,
            dataReceived: [],
            dataTraite: [],
            widthWindow: 0,
            heightWindow: 0,
            widthPx: 0,
            activeTab: 'habilite',
            showEditAlarmMenu: false,
            showRapport: false,
            showAquitModal: false,
            showLoadingCallModal: false,
            idAdemcoList: [],
            allowedData: 0,
            option: 'aucun'
        }
        this.updateData = this.updateData.bind(this)
        this.handleAcquittement = this.handleAcquittement.bind(this)
        this.handleAcquitLog = this.handleAcquitLog.bind(this)
        this.handleClickRow = this.handleClickRow.bind(this)
        this.handleChangeTab = this.handleChangeTab.bind(this)
        this.handleShowRapport = this.handleShowRapport.bind(this)
        this.handleCloseRapport = this.handleCloseRapport.bind(this)
        this.clearTimeoutUpdate = this.clearTimeoutUpdate.bind(this)
        this.toggleLoading = this.toggleLoading.bind(this)
        this.setAllowedData = this.setAllowedData.bind(this)
        this.makeCall = this.makeCall.bind(this)
        this.toggleLoadingCallModal = this.toggleLoadingCallModal.bind(this)
    }

    makeCall(to) {
        to = to.trim()
        if (/^0[0-9]{9}$/.test(to)) {
            this.toggleLoadingCallModal(true)
            const { user } = this.props
            console.log('from: ' + user.extension + ', to: ' + to)
            let data = new FormData()
            data.append("user_id", user.id)
            data.append("from", user.extension)
            data.append("to", to)
            axios.post("/api/call/make_call", data, formDataOption)
                .then(({ data }) => {
                    console.log(data)
                    this.toggleLoadingCallModal(false)
                })
                .catch((e) => {
                    console.error(e)
                    this.toggleLoadingCallModal(false)
                })
        }
        else
            console.log("erreur de format")
    }

    toggleLoadingCallModal(value) {
        this.setState({
            showLoadingCallModal: value
        })
    }
    setAllowedData(option) {
        let allowedData = 0

        if (option == 'Alarme')
            allowedData = 100;
        else if (option == 'Armement / Désarmement')
            allowedData = 400;
        else if (option == 'Coupure / Rétablissement')
            allowedData = 300;
        else
            allowedData = 0;

        this.setState({
            allowedData: allowedData,
            option: option
        })
    }
    handleCloseRapport() {
        this.setState({
            showRapport: false
        })
    }
    handleShowRapport() {
        this.setState({
            showRapport: true
        })
    }
    toggleEditAlarmMenu(value) {
        this.setState({
            showEditAlarmMenu: value
        })
    }
    handleChangeTab(e) {
        this.setState({
            activeTab: e.target.id
        })
    }
    toggleLoading(load) {
        this.props.toggleLoading(load)
    }
    clearTimeoutUpdate() {
        clearTimeout(this.state.timeoutId)
        this.setState({
            timeoutId: null
        })
    }
    handleClickRow(row) {
        this.toggleLoading(true)
        axios.get("/api/logs/traite_alarme/" + row.id + "?user_id=" + this.props.user.id)
            .then(res => {
                if (res.data) {
                    let log = res.data
                    console.log(log)
                    let currentLog = {
                        id: log.idademco,
                        rapport_id: log.rapport_id,
                        site: log.site && log.site.nom,
                        commentaire: log.site && log.site.commentaire,
                        transmitter: log.transmitter,
                        correct_transmitter: log.site && log.site.correct_transmitter,
                        adresse: log.site.adresse,
                        puce: log.site && log.site.numeropuces,
                        transmission: log.site && (log.site.gps ? 'GPS' : log.site.gprs ? 'GPRS' : log.site.sms ? 'SMS' : ''),
                        alarm: log.alarm && log.alarm.Description,
                        code: log.codeTevent,
                        zone: log.zones,
                        eventQualify: log.eventQualify,
                        prom: log.prom,
                        site_id: log.site_id,
                        pointeuse_id: log.pointeuse_id,
                        date: log.dtarrived,
                        partition: log.partition,
                        habilites: log.site && log.site.habilites.sort((a, b) => (a.idordre - b.idordre)), //log.site.habilites,
                        arm: log.site.arm && log.site.arm,
                        panel_user: log.panel_user,
                        panel_area: log.panel_area,
                    }
                    this.setState({
                        activeTab: 'habilite',
                        currentLog: currentLog
                    }, () => {
                        this.updateData()
                    })
                }
                else
                    this.updateData()
            })
            .catch(() => {
                this.updateData()
            })
    }
    handleAcquittement(id) {
        if (id)
            this.setState({
                showAquitModal: true,
                idAdemcoList: [id]
            })
        else {
            const { dataTraite } = this.state
            console.log("dataTraite", dataTraite.map(({ id }) => id).slice(0, 50))
            this.setState({
                showAquitModal: true,
                idAdemcoList: dataTraite.map(({ id }) => id).slice(0, 50)
            })
        }
    }
    handleAcquitLog() {
        const data = new FormData()
        data.append("ids", JSON.stringify([this.state.currentLog.id]))
        data.append("user_id", this.props.user.id)
        axios.post("/api/logs/aquiter", data)
            .then(res => {
                this.updateData()
                this.setState({
                    currentLog: null
                })
            })
            .catch((response) => {
                console.log(response);
            });
    }
    updateData(loading) {
        if (this.state.timeoutId)
            clearTimeout(this.state.timeoutId)
        console.log('updateData')
        if (loading)
            this.toggleLoading(true)
        axios.get("/api/logs/" + this.props.user.id, { params: { allowedData: this.state.allowedData } })
            .then(res => {
                if (res.data) {
                    let { alarm_received, alarm_traite } = res.data
                    let dataReceived = []
                    let dataTraite = []
                    alarm_received.forEach(row => {
                        dataReceived.push({
                            id: row.idademco,
                            rapport_id: row.rapport_id,
                            prom: row.prom,
                            code: row.codeTevent,
                            partition: row.partition,
                            zone: row.zones,
                            dateArrived: row.dtarrived,
                            dateReceived: row.received_at,
                            alarm: row.Description,
                            site: row.nom,
                            numZone: row.zones,
                            nomZone: row.nomZone,
                            eventQualify: row.eventQualify,
                            pointeuse_id: row.pointeuse_id,
                            transmitter: row.transmitter,
                            correct_transmitter: row.correct_transmitter,
                        })
                    })
                    alarm_traite.forEach(row => {
                        dataTraite.push({
                            id: row.idademco,
                            rapport_id: row.rapport_id,
                            prom: row.prom,
                            code: row.codeTevent,
                            partition: row.partition,
                            zone: row.zones,
                            dateArrived: row.dtarrived,
                            dateReceived: row.received_at,
                            alarm: row.Description,
                            site: row.nom,
                            numZone: row.zones,
                            nomZone: row.nomZone,
                            eventQualify: row.eventQualify,
                            pointeuse_id: row.pointeuse_id,
                            transmitter: row.transmitter,
                            correct_transmitter: row.correct_transmitter,
                        })
                    })
                    this.setState({
                        dataReceived: dataReceived,
                        dataTraite: dataTraite
                    }, () => {
                        this.toggleLoading(false)
                        //this.setTimeoutUpdateData()
                    })
                }
                else {
                    this.setState({
                        dataReceived: [],
                        dataTraite: []
                    }, () => {
                        this.toggleLoading(false)
                        //this.setTimeoutUpdateData()
                    })
                }
            })
            .catch(() => {
                setTimeout(() => {
                    this.updateData()
                }, 10000)
            })
    }
    setTimeoutUpdateData() {
        const timeoutId = setTimeout(this.updateData, 60000)
        this.setState({
            timeoutId: timeoutId
        })
    }
    componentDidMount() {
        this.updateData(true)
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
        document.title = "Alarme - TLS"
    }
    componentDidUpdate(prevProps, prevState) {

        if (prevState.allowedData !== this.state.allowedData) {
            this.updateData(true)
            this.props.updateAllowedData(this.state.allowedData)
        }

        if (this.props.filterCount == 10) {
            if (this.state.allowedData > 0) {
                this.setState({
                    option: '',
                })
                this.setAllowedData('')
            }
            this.props.updateFilterCount(0)
        }
    }
    resize() {
        this.setState({
            heightWindow: window.innerHeight,
            widthWindow: window.innerWidth,
            widthPx: ((window.innerWidth / 2.5) / 3) + "px"
        });
    }
    componentWillUnmount() {
        if (this.state.timeoutId)
            clearTimeout(this.state.timeoutId)
    }
    getColor(code) {
        let color = (
            120 == code ? 'd50000' :
                132 == code ? 'c62828' :
                    133 == code ? 'c62828' :
                        134 == code ? 'c62828' :
                            130 == code ? 'f44336' :
                                131 == code ? 'c51162' :
                                    137 == code ? 'ad1457' :
                                        140 == code ? 'd500f9' :
                                            100 == code ? 'e91e63' :
                                                101 == code ? 'ff6d00' :
                                                    110 == code ? 'ef6c00' :
                                                        151 == code ? '8d6e63' :
                                                            111 == code ? 'ff9800' :
                                                                117 == code ? 'ffab00' :
                                                                    112 == code ? 'ff8f00' :
                                                                        113 == code ? 'ffb300' :
                                                                            102 == code ? 'dd2c00' :
                                                                                139 == code ? 'd84315' :
                                                                                    384 == code ? '673ab7' :
                                                                                        301 == code ? '6200ea' :
                                                                                            302 == code ? '6200ea' :
                                                                                                350 == code ? '4527a0' :
                                                                                                    1000 == code ? '78909c' :
                                                                                                        [400, 401, 402, 403, 404, 405, 407, 406, 408, 409, 441, 442, 456, 454].includes(parseInt(code)) ? '7cb342' : '444'
        )
        return '#' + color
    }
    getAlarm(row) {
        if (row.code == '301')
            if (row.eventQualify == 3)
                return 'Courant rétablit'
            else return 'Coupure de courant JIRAMA 220V'
        else if (row.alarm)
            if (row.eventQualify == 3)
                return row.alarm.replace("Armement/Désarmement", "Armement")
            else
                return row.alarm.replace("Armement/Désarmement", "Désarmement")
        else
            return 'Alarme non définie'
    }
    render() {
        const { idAdemcoList, showAquitModal, showRapport, showEditAlarmMenu, activeTab, dataReceived, dataTraite, currentLog, heightWindow, widthWindow, widthPx, option, showLoadingCallModal } = this.state
        const panics = [100, 101, 120, 151, 110, 111, 130, 131, 132, 133, 134, 137, 140]
        return (
            <div className="table" onClick={() => { if (showEditAlarmMenu) this.toggleEditAlarmMenu(false) }}>
                {
                    showAquitModal &&
                    <AquitAlarmModal
                        ids={idAdemcoList}
                        userId={this.props.user.id}
                        closeModal={() => { this.setState({ showAquitModal: false }) }}
                        clearTimeoutUpdate={this.clearTimeoutUpdate}
                        clearCurrentLog={() => { this.setState({ currentLog: null }) }}
                        updateData={this.updateData} />
                }
                {
                    showLoadingCallModal &&
                    <LoadingCallModal loading={showLoadingCallModal} />
                }
                <div id="tableContainer">
                    <div className="table">
                        <div className="row-header">
                            <h3 className="h3-table">
                                <span className="cell">
                                    Alarme reçu
                                </span>
                                <span className='cell right'>
                                    <Filtre onFilterChange={(option) => this.setAllowedData(option)} selectedOption={option} options={['aucun', 'Alarme', 'Armement / Désarmement', 'Coupure / Rétablissement']} />
                                </span>
                                <span className="cell right">
                                    <img src="/img/refresh.svg" onClick={() => { this.updateData(true, true) }} width="20" />
                                </span>
                            </h3>
                        </div>

                        <div className="row-table">
                            <table className="fixed_header visible-scroll layout-fixed">
                                <thead>
                                    <tr>
                                        <th className="cellHour">Déclenché à</th>
                                        <th className="cellHour">Reçu à</th>
                                        <th className="cellIconTransmitter"></th>
                                        <th className="cellAlarm">Alarme</th>
                                        <th className="cellZone">Zone</th>
                                        <th>Site</th>
                                    </tr>
                                </thead>
                                <tbody style={{ 'height': (heightWindow / 2 - 150) + "px" }}>
                                    {
                                        dataReceived/*.filter((item) => {
                                            const group = item.code < 1000 ? Math.floor(item.code / 100) * 100 : Math.floor(item.code / 1000) * 1000; // Group the code (100, 200, etc.)
                                            return allowedData.includes(group);
                                        })*/.map((row, index) => {
                                            return (
                                                <tr style={{ color: this.getColor(row.code) }}
                                                    className={(row.correct_transmitter != null && row.correct_transmitter != row.transmitter) ? "correct-transmitter" : ""}
                                                    key={'received_' + row.id + '_' + index}
                                                    onDoubleClick={() => { this.handleClickRow(row) }}
                                                >
                                                    <td className="cellHour">{row.dateArrived.split(" ")[1]}</td>
                                                    <td className="cellHour">{row.dateReceived && row.dateReceived.split(" ")[1]}</td>
                                                    <td className="cellIconTransmitter">
                                                        {
                                                            /0[2-3][0-9]{8}/.test(row.transmitter) ?
                                                                <img src="/img/sms.svg" height={15} />
                                                                :
                                                                <img src="/img/gprs.svg" height={15} />
                                                        }
                                                    </td>
                                                    <td className="cellAlarm" title={this.getAlarm(row)}>
                                                        [{row.code}] {this.getAlarm(row)}
                                                    </td>
                                                    <td className="cellZone" title={row.nomZone}>
                                                        <span style={row.pointeuse_id ? { backgroundColor: this.getColor(row.code), color: 'whitesmoke' } : null}>
                                                            {
                                                                ("000" + row.numZone).slice(-3)
                                                            }
                                                        </span>
                                                    </td>
                                                    <td>
                                                        {row.site ? row.site : 'Site non défini (' + row.prom + ')'}
                                                    </td>
                                                </tr>)
                                        })
                                    }
                                </tbody>
                            </table>
                        </div>

                        <div className="row-header">
                            <h3 className="h3-table">
                                <span className="cell">Alarme en cours</span>
                                <span className="cell right">
                                    <button disabled={dataTraite.length == 0} className="btn-primary" onClick={() => this.handleAcquittement()}>Tout acquitter</button>
                                </span>
                            </h3>
                        </div>

                        <div className="row-table">
                            <table className="fixed_header visible-scroll">
                                <thead>
                                    <tr>
                                        <th className="cellHour">Déclenché à</th>
                                        <th className="cellHour">Reçu à</th>
                                        <th className="cellIconTransmitter"></th>
                                        <th className="cellAlarm">Alarme</th>
                                        <th className="cellZone">Zone</th>
                                        <th>Site</th>
                                    </tr>
                                </thead>
                                <tbody style={{ 'height': (heightWindow / 2 - 150) + "px" }}>
                                    {
                                        dataTraite.map((row, index) => {
                                            return (
                                                <tr style={{ color: this.getColor(row.code) }}
                                                    className={(row.correct_transmitter != null && row.correct_transmitter != row.transmitter) ? "correct-transmitter" : ""}
                                                    key={'traite_' + row.id + '_' + index}
                                                    onDoubleClick={() => { this.handleClickRow(row) }}
                                                >
                                                    <td className="cellHour">{row.dateArrived.split(" ")[1]}</td>
                                                    <td className="cellHour">{row.dateReceived && row.dateReceived.split(" ")[1]}</td>
                                                    <td className="cellIconTransmitter">
                                                        {
                                                            /0[2-3][0-9]{8}/.test(row.transmitter) ?
                                                                <img src="/img/sms.svg" height={15} />
                                                                :
                                                                <img src="/img/gprs.svg" height={15} />
                                                        }
                                                    </td>
                                                    <td className="cellAlarm" title={this.getAlarm(row)}>
                                                        <span style={(!row.rapport_id && panics.includes(Number.parseInt(row.code))) ? { backgroundColor: this.getColor(row.code), color: 'whitesmoke' } : null}>
                                                            [{row.code}] {this.getAlarm(row)}
                                                        </span>
                                                    </td>
                                                    <td className="cellZone" title={row.nomZone}>
                                                        <span style={row.pointeuse_id ? { backgroundColor: this.getColor(row.code), color: 'whitesmoke' } : null}>
                                                            {
                                                                ("000" + row.numZone).slice(-3)
                                                            }
                                                        </span>
                                                    </td>
                                                    <td>
                                                        {row.site ? row.site : 'Site non défini (' + row.prom + ')'}
                                                    </td>
                                                </tr>)
                                        })
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div className={currentLog ? "box-shadow-left" : ""} style={{ height: heightWindow + 'px', width: (widthWindow / 2.5) + 'px', maxWidth: (widthWindow / 2.5) + 'px', minWidth: (widthWindow / 2.5) + 'px' }} id="overviewContainer">
                    {
                        currentLog ?
                            <div>
                                {
                                    showRapport &&
                                    <Rapport alarm={currentLog} updateCurrentAlarm={() => this.handleClickRow(currentLog)} closeModal={this.handleCloseRapport} />
                                }
                                <div className="overview-container">
                                    <div className="head-title-overview">
                                        <div style={{ height: "40px", lineHeight: "40px" }}>
                                            <div
                                                className={"title-overview " + (currentLog.arm ? "green" : "")} >
                                                {currentLog.site}
                                            </div>
                                            <div className="overview-edit-icon">
                                                <img onClick={() => { this.toggleEditAlarmMenu(!showEditAlarmMenu) }} className="overview-edit-img" src="/img/parametre.svg" />
                                                {
                                                    showEditAlarmMenu &&
                                                    <div className="dropdown-overview-edit">
                                                        {panics.includes(Number.parseInt(currentLog.code)) && <span onClick={this.handleShowRapport}>Faire le rapport</span>}
                                                        <span onClick={() => this.handleAcquittement(currentLog.id)}>Acquitter l'alarme</span>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                    <span className="overview-break-overflow" title={currentLog.adresse}>
                                        <b>Adresse : </b> {currentLog.adresse}
                                    </span>
                                    {
                                        currentLog.prom &&
                                        <>
                                            <b>Prom : </b> {currentLog.prom}<br />
                                        </>
                                    }
                                    {
                                        currentLog.pointeuse_id &&
                                        <>
                                            <b>Pointeuse : </b> {String(currentLog.pointeuse_id).padStart(4, "0")}<br />
                                        </>
                                    }
                                    <b>Récépteur : </b>{currentLog.transmitter}
                                    
                                        {
                                            (
                                                /0[2-3][0-9]{8}/.test(currentLog.transmitter) 
                                                && currentLog.correct_transmitter 
                                                && currentLog.correct_transmitter != currentLog.transmitter
                                            ) &&
                                            <>
                                                <span className="pink"> 
                                                    {" -> " + currentLog.correct_transmitter}
                                                </span><br />
                                            </>
                                        }
                                    
                                </div>
                                <div className="overview-container">
                                    <h3 style={{ color: this.getColor(currentLog.code) }}>
                                        {this.getAlarm(currentLog)}
                                    </h3>
                                    <b>Code : </b> {currentLog.code}<br />
                                    <b>Date : </b> {currentLog.date}<br />
                                    {
                                        currentLog.panel_area &&
                                        <>
                                            <b>Area : </b> {currentLog.panel_area}<br />
                                        </>
                                    }
                                    {
                                        currentLog.panel_user &&
                                        <>
                                            <b>User : </b> {currentLog.panel_user}<br />
                                        </>
                                    }
                                </div>
                                <div id="habiliteLabel">
                                    <div style={{ position: 'relative', top: '2px' }}>
                                        <div className="table">
                                            <div className="cell">
                                                <div id="tabHeaderOverview">
                                                    <ul>
                                                        <li id="habilite" className={activeTab == 'habilite' ? "active-tab" : ""} onClick={this.handleChangeTab}>Habilité</li>
                                                        <li id="historique" className={activeTab == 'historique' ? "active-tab" : ""} onClick={this.handleChangeTab}>Historique</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="tabContentOverview">
                                        <div id="tabContainer">
                                            {
                                                activeTab == 'habilite' &&
                                                <table className="fixed_header default layout-fixed">
                                                    <thead>
                                                        <tr>
                                                            <th style={{ width: widthPx, minWidth: widthPx, maxWidth: widthPx }}>Nom</th>
                                                            <th style={{ width: widthPx, minWidth: widthPx, maxWidth: widthPx }}>Téléphone</th>
                                                            <th>Code vocal</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody style={{ height: (heightWindow - 560) + "px" }}>
                                                        {
                                                            currentLog.habilites && currentLog.habilites.map((row) => {
                                                                return (
                                                                    <tr
                                                                        key={row.idhabilite}
                                                                        onDoubleClick={() => { this.handleClickRow(row) }}
                                                                    >
                                                                        <td title={row.contact && ((row.contact.nom != null ? row.contact.nom : '') + ' ' + (row.contact.prenom != null ? row.contact.prenom : '') + (row.quality ? (' (' + row.quality + ')') : ''))} style={{ width: widthPx, minWidth: widthPx, maxWidth: widthPx }}>
                                                                            {row.contact && ((row.contact.nom != null ? row.contact.nom : '') + ' ' + (row.contact.prenom != null ? row.contact.prenom : '') + (row.quality ? (' (' + row.quality + ')') : ''))}
                                                                        </td>
                                                                        <td style={{ width: widthPx, minWidth: widthPx, maxWidth: widthPx }} title={row.contact ? row.contact.phones.map(phone => phone.numero).join(", ") : ''}>
                                                                            {row.contact ? row.contact.phones.map((phone, idx) => (
                                                                                <span key={idx} className='phone-call' onClick={() => this.makeCall(phone.numero)}>
                                                                                    {phone.numero}
                                                                                </span>
                                                                            )) : ''}
                                                                        </td>
                                                                        <td title={row.password}>
                                                                            {row.password}
                                                                        </td>
                                                                    </tr>)
                                                            })
                                                        }
                                                    </tbody>
                                                </table>
                                            }
                                            {
                                                activeTab == 'historique' &&
                                                <Historique
                                                    data={currentLog.pointeuse_id ? "pointeuse" : "site"}
                                                    nomSite={currentLog.site}
                                                    id={currentLog.pointeuse_id ? currentLog.pointeuse_id : currentLog.site_id}
                                                    heightWindow={heightWindow - 600} />
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>
                            :
                            <div className="img-bg-container">
                                <img className="img-bg-overview" src="/img/tls_background.svg" />
                            </div>
                    }
                </div>
            </div>
        )
    }
}
