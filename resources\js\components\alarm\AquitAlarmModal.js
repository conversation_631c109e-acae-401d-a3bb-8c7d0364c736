import React, { Component } from 'react'
import Modal from '../modal/Modal'
import axios from 'axios'

export default class AquitAlarmModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            error: ''
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }
    handleSave(){
        this.setState({error: '', disableSave: true})
        const data = new FormData()
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        this.props.ids.forEach(id => {
            data.append("ids[]", id)
        });
        axios.post("/api/logs/aquiter", data)
        .then(({data}) => {
            console.log("acquitter", data)
            if(data.error)
                this.setState({error: data.error})
            else {
                this.props.clearTimeoutUpdate()
                this.props.updateData(true)
                this.props.clearCurrentLog()
                this.props.closeModal()
            }
        })
        .catch( (err) => {
            console.error(err);
            this.setState({
                disableSave: false
            })
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        const {disableSave, error} = this.state
        const {ids} = this.props
        return (
            <Modal disableSave={disableSave} handleSave={this.handleSave} handleCancel={this.handleCancel}>
                <h3>Acquittement</h3>
                {
                    error ? <p className="red">{error}</p> 
                    : ids.length > 1 ? <div>Voulez-vous vraiment tout acquitter?</div>
                    : <div>Voulez-vous vraiment acquitter cet alarme?</div>
                }
            </Modal>
        )
    }
}