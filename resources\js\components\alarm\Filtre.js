import React, { useState, useEffect } from 'react';

const Filtre = ({ onFilterChange, options, selectedOption }) => {
    const [isOpen, setIsOpen] = useState(false);
    const [option, setOption] = useState('aucun');

    const toggleDropdown = () => {
        setIsOpen(!isOpen);
    };
    const handleOptionClick = (option) => {
        setOption(option);
        setIsOpen(false);

        if (onFilterChange) {
            onFilterChange(option);
        }
    };
    useEffect(() => {
        setOption(selectedOption)
    }, [selectedOption])

    return (
        <div style={{ position: 'relative', display: 'inline-block' }}>
            <button
                onClick={toggleDropdown}
                style={{
                    padding: '10px',
                    backgroundColor: '#fff',
                    color: '#333',
                    border: '1px solid #ccc',
                    borderRadius: '5px',
                    cursor: 'pointer',
                    minWidth: '200px',
                    textAlign: 'left',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                }}
            >
                <span>{option}</span>
                <span style={{ marginLeft: '10px' }}>▼</span>
            </button>
            {isOpen && (
                <ul
                    style={{
                        position: 'absolute',
                        top: '100%',
                        left: '0',
                        margin: '0',
                        padding: '10px',
                        listStyle: 'none',
                        backgroundColor: '#fff',
                        border: '1px solid #ccc',
                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                        borderRadius: '5px',
                        zIndex: '1000',
                        width: '200px',
                    }}
                >
                    {options.map((option, index) => {
                        return (<>
                            <li
                                key={index}
                                onClick={() => handleOptionClick(option)}
                                style={{
                                    padding: '8px',
                                    cursor: 'pointer',
                                    borderRadius: '4px',
                                }}
                                onMouseEnter={(e) => (e.target.style.backgroundColor = '#f0f0f0')}
                                onMouseLeave={(e) => (e.target.style.backgroundColor = 'transparent')}
                            >
                                {option}
                            </li>
                            {index < options.length - 1 && <hr style={{ margin: '8px 0', borderColor: '#ccc' }} />}
                        </>
                        );
                    })}
                    {/* <li
                        onClick={() => handleOptionClick('Aucun')}
                        style={{
                            padding: '8px',
                            cursor: 'pointer',
                            borderRadius: '4px',
                        }}
                        onMouseEnter={(e) => (e.target.style.backgroundColor = '#f0f0f0')}
                        onMouseLeave={(e) => (e.target.style.backgroundColor = 'transparent')}
                    >
                        Aucun
                    </li>
                    <hr style={{ margin: '8px 0', borderColor: '#ccc' }} /> {/* Separator
                    <li
                        onClick={() => handleOptionClick('Alarme')}
                        style={{
                            padding: '8px',
                            cursor: 'pointer',
                            borderRadius: '4px',
                        }}
                        onMouseEnter={(e) => (e.target.style.backgroundColor = '#f0f0f0')}
                        onMouseLeave={(e) => (e.target.style.backgroundColor = 'transparent')}
                    >
                        Panique
                    </li>
                    <hr style={{ margin: '8px 0', borderColor: '#ccc' }} /> {/* Separator
                    <li
                        onClick={() => handleOptionClick('Armement / Désarmement')}
                        style={{
                            padding: '8px',
                            cursor: 'pointer',
                            borderRadius: '4px',
                        }}
                        onMouseEnter={(e) => (e.target.style.backgroundColor = '#f0f0f0')}
                        onMouseLeave={(e) => (e.target.style.backgroundColor = 'transparent')}
                    >
                        Armement / Désarmement
                    </li>
                    <hr style={{ margin: '8px 0', borderColor: '#ccc' }} /> {/* Separator
                    <li
                        onClick={() => handleOptionClick('Coupure / Rétablissement')}
                        style={{
                            padding: '8px',
                            cursor: 'pointer',
                            borderRadius: '4px',
                        }}
                        onMouseEnter={(e) => (e.target.style.backgroundColor = '#f0f0f0')}
                        onMouseLeave={(e) => (e.target.style.backgroundColor = 'transparent')}
                    >
                        Coupure / Rétablissement
                    </li> */}
                </ul>
            )}
        </div>
    );
};

export default Filtre;
