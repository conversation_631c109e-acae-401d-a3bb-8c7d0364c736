html,
body {
    width: 100%;
    height: 100%;
}

html,
a,
input,
textarea,
select,
option,
button,
ul,
li {
    font-family: "Bahnschrift";
    color: #444;
}

hr {
    /*border: solid #aaa .5px;*/
    opacity: .3;
}

body,
#app {
    margin: 0;
    width: 100%;
    height: 100%;
}

.primary {
    color: #366666;
}

.red {
    color: #f44336;
}

.green {
    color: #7cb342
}

.pink {
    color: #e91e63;
}

.amber {
    color: #ffc107;
}

.purple {
    color: #9c27b0;
}

.indigo {
    color: #3f51b5;
}

.blue {
    color: #2196f3;
}

.default {
    color: #444;
    ;
}

.secondary {
    color: #888;
}

.grey-light {
    color: #a5a3a3;
}

.link {
    cursor: pointer;
}

.link:hover {
    opacity: .7;
}

.bg-primary {
    background-color: #366666 !important;
    color: white !important;
}

.bg-red {
    background-color: #f44336 !important;
    color: white !important;
}

.bg-pink {
    background-color: #e91e63 !important;
    color: white !important;
}

.bg-amber {
    background-color: #ffc107 !important;
    color: white !important;
}

.bg-purple {
    background-color: #9c27b0 !important;
    color: white !important;
}

.bg-dark {
    background-color: #444 !important;
    color: white !important;
}

.bg-light {
    background-color: whitesmoke !important;
    border: solid .5px #aaa;
    color: #444 !important;
}

.bg-secondary {
    background-color: #aaa !important;
    color: white !important;
}

.bg-grey-light {
    background-color: #f2f2f2 !important;
    color: #a5a3a3;
}

#logoTls {
    margin: 20px 0px;
    width: 80px;
    height: 80px;
}

.table-container {
    display: table;
    width: 100%;
    height: 100%;
}

#mainContainer {
    position: absolute;
    display: table;
    width: 100%;
    height: 100%;
}

nav {
    display: table-cell;
    padding: 10px;
    width: 220px;
    min-width: 220px;
    max-width: 220px;
    height: 100%;
    color: white;
    background-color: #366666;
    vertical-align: top;
}

#tableContainer,
#overviewContainer,
#filterContainer {
    display: table-cell;
    padding: 10px 20px;
    vertical-align: top;

}

.img-bg-container {
    display: inline-block;
    width: 100%;
    height: 100%;
    background-color: #efefef;
}

img.img-bg-overview {
    display: inline-block;
    vertical-align: middle;
    width: 100%;
    height: 100%;
}

#sideContainer {
    display: inline-block;
    width: 100%;
    height: 100%;
}

#loginContainer {
    height: 50px;
    background-color: white;
}

table {
    display: table;
    border-collapse: collapse;
    width: 100%;
    height: 100%;
}

table td,
table th {
    border: 1px solid #ddd;
    padding: 8px;
}

table tr {
    background-color: white;
}

table tr:nth-child(even) {
    background-color: #f2f2f2;
}

table tr:hover {
    background-color: #d0dddd;
}


table tr.selected-row {
    background-color: #666;
    color: white;
}

table tr.selected-row:hover {
    background-color: #888;
    color: white;
}

table tr {
    user-select: none;
    /* standard syntax */
    -webkit-user-select: none;
    /* webkit (safari, chrome) browsers */
    -moz-user-select: none;
    /* mozilla browsers */
    -khtml-user-select: none;
    /* webkit (konqueror) browsers */
    -ms-user-select: none;
    /* IE10+ */
}

table th {
    padding-top: 12px;
    padding-bottom: 12px;
    text-align: left;
    background-color: #366666;
    color: white;
}

.fixed_header tbody {
    display: block;
    overflow-x: hidden;
    border-left: solid 1px rgba(24, 42, 42, .1);
    border-right: solid 1px rgba(24, 42, 42, .1);
    border-bottom: solid 1px rgba(24, 42, 42, .3);
    width: 100%;
}

.visible-scroll {
    overflow: scroll;
    overflow-x: hidden;
}

.fixed_header thead {
    border-left: solid 1px rgba(24, 42, 42, .1);
    border-right: solid 1px rgba(24, 42, 42, .1);
    display: block;
    height: 49px;
}

.fixed_header thead tr {
    display: block;
    width: 100%;
    height: 49px;
}

.fixed_header thead tr,
.fixed_header tbody tr {
    display: table;
    width: 100%;
}

.fixed_header thead tr th,
.fixed_header tbody tr td {
    display: table-cell;
}

tbody tr td {
    cursor: default;
}

tr td,
tr th {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.cellCheckbox {
    width: 30px;
    min-width: 30px;
    max-width: 30px;
}

.cellDate {
    width: 170px;
    min-width: 170px;
    max-width: 170px;
}

td.cellDate {
    font-family: "CallingCode";
}

.cellHour {
    width: 100px;
    min-width: 100px;
    max-width: 100px;
}

.cellIconTransmitter {
    width: 50px;
    min-width: 50px;
    max-width: 50px;
    text-align: center;
}

.cellIconTransmitter>img {
    color: #888;
    opacity: .6;
}

.cellCode {
    width: 70px;
    min-width: 70px;
    max-width: 70px;
}

.cellAlarm {
    width: 200px;
    min-width: 200px;
    max-width: 200px;
}

.cellZone {
    width: 90px;
    min-width: 90px;
    max-width: 90px;
    text-align: center;
}

.btn-default {
    display: inline-block;
    padding: 10px;
    border: none;
    background-color: #aaa;
    border: .5px solid #aaa;
    color: white;
    cursor: pointer;
}

.btn-sm-default {
    display: inline-block;
    padding: 5px;
    border: none;
    background-color: #aaa;
    border: .5px solid #aaa;
    color: white;
}

.btn-white {
    display: inline-block;
    padding: 10px;
    border: .5px solid #ddd;
    background-color: white;
    color: #444;
    cursor: pointer;
}

.btn-primary {
    display: inline-block;
    padding: 10px;
    border: none;
    background-color: #366666;
    border: .5px solid #366666;
    color: white;
    cursor: pointer;
}

.btn-primary:disabled {
    opacity: .5;
    cursor: not-allowed;
}

.btn-danger {
    display: inline-block;
    padding: 10px;
    border: none;
    background-color: #f44336;
    border: .5px solid #f44336;
    color: white;
    cursor: pointer;
}

.badge {
    padding: 5px 10px;
    border-radius: 20px;
    color: #444;
    background-color: white;
}

.table {
    display: table;
    width: 100%;
    height: 100%;
}

.fixed_header {
    height: 100%;
}

.h3-table {
    display: table;
    width: 100%;
    height: 36px;
    margin: 18px 0px;
}

.cell {
    display: table-cell;
    vertical-align: middle;
}

.row-header {
    display: table-row;
    height: 70px;
    width: 100%;
}

.cell-header {
    display: table-cell;
    height: 70px;
    min-height: 70px;
    max-height: 70px;
    width: 100%;
}

.row-table {
    display: table-row;
}

.row-button {
    display: table-row;
    height: 56px;
}

.left {
    text-align: left;
}

.right {
    text-align: right;
}

.left-container {
    display: inline-block;
    text-align: right;
}

.overview-container {
    background-color: white;
    padding: 10px 20px 20px 20px;
    margin: 20px 0px;
    border: solid rgba(0, 0, 0, .1) .5px;
}

#noteContainer {
    padding: 20px;
    border: solid rgba(0, 0, 0, .1) .5px;
    margin: 20px 0px;
}

#noteContainer:focus-within {
    border: .5px solid rgba(24, 42, 42, .3);
}

#noteContainer:hover {
    border: .5px solid rgba(24, 42, 42, .2);
}

#noteTextarea {
    width: 100%;
    padding: 0px;
    border: none;
    outline: none;
}

.border-padding {
    padding: 10px;
    border: solid .5px rgba(0, 0, 0, .1);
    margin-bottom: 20px;
}

@keyframes pulse_row {
    0% {
        background-color: white;
    }

    100% {
        background-color: #aaaaaa;
    }
}

.correct-transmitter {
    animation: pulse_row 1s infinite;
}


.title-overview {
    display: inline-block;
    width: calc(100% - 170px);
    ;
    font-size: 16pt;
    font-weight: bold;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    overflow-wrap: break-word;
    vertical-align: middle;
}

.head-title-overview {
    display: table;
    width: 100%;
    padding: 10px 0px;
}

.overview-edit-icon {
    display: inline-block;
    width: 170px;
    height: 40px;
    text-align: right;
    font-size: 12pt;
    font-weight: normal;
    vertical-align: middle;
}

.overview-edit-img {
    padding: 5px;
    height: 30px;
    vertical-align: middle;
    box-shadow: 1px 1px 1px rgba(24, 42, 42, .1);
}

.overview-edit-img:hover {
    background-color: rgba(24, 42, 42, .1);
}

.dropdown-overview-edit {
    position: relative;
    z-index: 1000;
    list-style-type: none;
    position: relative;
    padding: 5px;
    border: solid .5px rgba(0, 0, 0, .2);
    text-align: right;
    background-color: white;
    text-align: left;
}

.dropdown-overview-edit>span {
    display: block;
    font-size: 12px;
    border-top: solid .5px rgba(0, 0, 0, .2);
    padding: 15px 10px 10px 10px;
    line-height: 12px;
    cursor: pointer;
}

.dropdown-overview-edit>span:first-child {
    border-top: none;
    padding: 10px 10px 15px 10px;
}

.overview-break-overflow {
    display: inline-block;
    width: 99%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.overview-container div span {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;

}

.phone-call {
    cursor: pointer;
    padding: 5px;
    border: #aaaaaa solid 1px;
    margin-right: 5px;
    display: inline-block;
}

.phone-call:hover {
    background-color: #aaa;
}