

.radio-inline{
    display: inline-block;
    margin: 0px;
    padding: 10px;
    width: 100%;
}

/* Custom radio */
.checkbox-container{
    display: inline-block;
    position: relative;
    padding-left: 25px;
    margin: 0px 10px 10px 10px;
    cursor: pointer;
    font-size: 16px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.checkbox-container-lg{
    margin: 0px 20px 20px 20px;
}
.checkbox-container > input{
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}
.radiomark{
    position: absolute;
    top: 0;
    left: 0;
    height: 16px;
    width: 16px;
    background-color: #eee;
    border-radius: 50%;
}
.radiomark-lg{
    position: absolute;
    top: 0;
    left: 0;
    height: 25px;
    width: 25px;
    background-color: #eee;
    border-radius: 50%;
}

/* On mouse-over, and a grey background color */
.checkbox-container:hover input ~ .checkmark, 
.checkbox-container:hover input ~ .checkmark-lg, 
.checkbox-container:hover input ~ .radiomark, 
.checkbox-container:hover input ~ .radiomark-lg{
    background-color: #ccc;
}

/* When the radion button is checked, and a blue background */
.checkbox-container input:checked ~ .checkmark, 
.checkbox-container input:checked ~ .checkmark-lg, 
.checkbox-container input:checked ~ .radiomark,  
.checkbox-container input:checked ~ .radiomark-lg{
    background-color: #336666;
}

/* Create the indicator (the dot/circle - hidden when not checked) */
.checkmark:after, .checkmark-lg:after, .radiomark:after, .radiomark-lg:after{
    content: "";
    position: absolute;
    display: none;
}
/* Show indocator when checked */
.checkbox-container input:checked  ~ .radiomark:after, 
.checkbox-container input:checked  ~ .radiomark-lg:after,
.checkbox-container input:checked  ~ .checkmark:after, 
.checkbox-container input:checked  ~ .checkmark-lg:after{
    display: block;
}
.checkbox-container .radiomark:after{
    top: 5px;
    left: 5px;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background-color: white;
}
.checkbox-container .radiomark-lg:after{
    top: 9px;
    left: 9px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: white;
}


/* Checkmark */

.checkmark{    
  position: absolute;
  top: 0;
  left: 0;
  height: 16px;
  width: 16px;
  background-color: #eee;
}
.checkmark-lg{
    position: absolute;
    top: 0;
    left: 0;
    height: 25px;
    width: 25px;
    background-color: #eee;
}

  /* Style the checkmark/indicator */
  .checkbox-container .checkmark:after {
    left: 5px;
    top: 2px;
    width: 3px;
    height: 8px;
    border: solid white;
    border-width: 0 3px 3px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
  }
  .checkbox-container .checkmark-lg:after{
        left: 9px;
        top: 3px;
        width: 5px;
        height: 11px;
        border: solid white;
        border-width: 0 3px 3px 0;
        -webkit-transform: rotate(45deg);
        -ms-transform: rotate(45deg);
        transform: rotate(45deg);
  }