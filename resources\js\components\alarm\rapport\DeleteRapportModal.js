import React, { Component } from 'react'
import Modal from '../../modal/Modal'
import axios from 'axios'
import moment from 'moment'

export default class DeleteRapportModal extends Component {
    constructor(props) {
        super(props)
        this.state = {
            error: ''
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }

    handleSave(){
        this.setState({
            error: ''
        })
        const {rapport} = this.props
        const data = new FormData()
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        axios.post("/api/rapports/" + (rapport.soft_delete ? "restaure" : "soft_delete") + "/" + rapport.id, data)
        .then(({data}) => {
            if(data.error){
                this.setState({
                    error: data.error
                })
            }
            else if(data){
                this.props.updateData(true)
                this.props.closeModal()
                this.props.setSelectedRapport(null)
            }
        })
    }

    handleCancel(){
        this.props.closeModal()
    }

    render(){
        const {error} = this.state
        const {rapport, site} = this.props
        return (
            <Modal confirm={true} handleSave={this.handleSave} handleCancel={this.handleCancel}>
                <div>
                    <h3>{rapport.soft_delete ? 'Restaurer le rapport' : 'Suppression du rapport'}</h3>
                    <div>
                        <b>Site: </b>{site}<br/>
                        <b>Rapport: </b>{rapport.type}<br/>
                        <b>Créé à: </b>{moment(rapport.created_at).format("HH:mm:ss")}<br/>
                    </div>
                    <hr/>
                    <p className="red">
                        {error}
                    </p>
                </div>
            </Modal>)
    }
}