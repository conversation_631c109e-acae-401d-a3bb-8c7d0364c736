import React, { Component } from 'react'
import axios from 'axios'

import Modal from '../../modal/Modal'

import LoadingData from '../../loading/LoadingData'
import Action from './action/Action'
import Alarm from './alarm/Alarm'
import DatePicker from 'react-datepicker'
import 'react-datepicker/dist/react-datepicker.css'
import moment from 'moment'
import Site from './site/Site'
import Habilite from '../../site/habilite/Habilite'
import SeachInput from '../../input/SearchInput'

export default class EditRapportModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            disableSave: false,
            error: null,
            activeTab: 'habilite',
            type_rapport_id: '',
            intervention: null,
            commentaire: '',
            typeRapports: [],
            typeActions: [],
            interventions: [],
            depart: null,
            arrivee: null,
            debut: null,
            fin: null,
            technicien: '',
            tache: '',
            site: null,
            showSite: false,
        }
        this.handleChangeSite = this.handleChangeSite.bind(this)
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.handleChangeTypeRapport = this.handleChangeTypeRapport.bind(this)
        this.handleChangeTab = this.handleChangeTab.bind(this)
        this.handleChangeCommentaire = this.handleChangeCommentaire.bind(this)
        this.handleChangeDateDepart = this.handleChangeDateDepart.bind(this)
        this.handleChangeDateArrivee = this.handleChangeDateArrivee.bind(this)
        this.handleChangeDateDebut = this.handleChangeDateDebut.bind(this)
        this.handleChangeDateFin = this.handleChangeDateFin.bind(this)
        this.handleChangeTechnicien = this.handleChangeTechnicien.bind(this)
        this.handleChangeTache = this.handleChangeTache.bind(this)
        this.handleShowSite = this.handleShowSite.bind(this)
        this.handleCloseSite = this.handleCloseSite.bind(this)
        this.handleInterventionChange = this.handleInterventionChange.bind(this)
    }
    handleInterventionChange(itv){
        console.log("handleInterventionChange")
        console.log(itv)
        this.setState({
            intervention: itv
        })
    }
    handleChangeSite(s){
        this.setState({
            site: s,
            showSite: false
        })
    }
    handleCloseSite(){
        this.setState({
            showSite: false
        })
    }
    handleShowSite(){
        this.setState({
            showSite: true
        })
    }
    handleChangeTache(e){
        this.setState({
            tache: e.target.value
        })
    }
    handleChangeTechnicien(e){
        this.setState({
            technicien: e.target.value
        })
    }
    handleChangeDateDebut(date){
        this.setState({
            debut: date
        })
    }
    handleChangeDateFin(date){
        this.setState({
            fin: date
        })
    }
    handleChangeDateArrivee(date){
        this.setState({
            arrivee: date,
        })
    }
    handleChangeDateDepart(date){
        this.setState({
            depart: date,
        })
    }
    handleChangeCommentaire(e){
        this.setState({
            commentaire: e.target.value
        })
    }
    handleChangeTab(e){
        this.setState({
            activeTab: e.target.id
        })
    }
    handleChangeTypeRapport(e){
        const {type_rapport_id} = this.state
        if(!type_rapport_id || type_rapport_id != e.target.value)
            this.setState({
                type_rapport_id: e.target.value,
                activeTab: ([1, 2].includes(Number.parseInt(e.target.value)) ? "action" : "alarm")
            })
    }
    getFieldSelections(){
        axios.get("/api/rapports/field_selection")
        .then(({data}) => {
            this.setState({
                typeRapports: data.type_rapports,
                typeActions: data.type_actions,
                interventions: data.interventions,
            })
        })
        .catch((e) => {
            console.error(e)
            setTimeout(() => {
                this.getFieldSelections()
            }, 10000)
        })
    }
    componentDidMount(){
        this.getFieldSelections();
        const {rapport} = this.props
        if(rapport){
            this.setState({
                type_rapport_id: rapport.type_rapport_id,
                intervention: (
                    rapport.intervention ? 
                    {
                        id: rapport.intervention_id, 
                        libelle: rapport.intervention
                    }
                    : null),
                commentaire: rapport.commentaire,
                depart: rapport.depart ? moment(rapport.depart).toDate() : null,
                arrivee: rapport.arrivee ? moment(rapport.arrivee).toDate() : null,
                debut: rapport.debut ? moment(rapport.debut).toDate() : null,
                fin: rapport.fin ? moment(rapport.fin).toDate() : null,
                tache: rapport.tache,
                technicien: rapport.technicien,
            })
        }
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
    }
    resize() {
        this.setState({
            heightPx: (window.innerHeight - 220) + "px"
        });
    }
    handleSave(){
        const {site, type_rapport_id, commentaire, intervention, technicien, depart, arrivee, debut, fin, tache} = this.state
        const {rapport} = this.props
        this.setState({
            disableSave: true,
            error: null,
        })
        let data = new FormData()
        data.append("type_rapport_id", type_rapport_id)
        if(intervention && intervention.id)
            data.append("intervention_id", intervention.id)
        data.append("site_id", site ? site.idsite: '')
        if(commentaire && commentaire.trim())
            data.append("commentaire", commentaire.trim())
        if(tache && tache.trim())
            data.append("tache", tache.trim())
        data.append("technicien", technicien ? technicien : '')
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        if(depart) data.append("depart", moment(depart).format("YYYY-MM-DD HH:mm:ss"))
        if(arrivee) data.append("arrivee", moment(arrivee).format("YYYY-MM-DD HH:mm:ss"))
        if(debut) data.append("debut", moment(debut).format("YYYY-MM-DD HH:mm:ss"))
        if(fin) data.append("fin", moment(fin).format("YYYY-MM-DD HH:mm:ss"))
        axios.post("/api/rapports/" + (rapport ? "update/" + rapport.id : "store"), data)
        .then(({data}) => {
            if(data.error){
                const firstKey = Object.keys(data.error)[0]
                const firstValue = data.error[firstKey][0]
                this.setState({
                    error: {
                        key: firstKey,
                        value: firstValue
                    },
                    disableSave: false,
                })
            }
            else if(data){
                this.props.closeModal()
                this.props.updateData()
            }
        })
        .finally(()=>{
            this.setState({
                disableSave: false
            })
        })
    }
    handleCancel(){
        const {rapport} = this.props
        let data = new FormData()
        if(rapport){
            data.append("username", localStorage.getItem("username"))
            data.append("secret", localStorage.getItem("secret"))
            axios.post("/api/rapports/cancel/" + rapport.id, data)
            .then(({data}) => {
                if(data) {
                    this.props.closeModal()
                    this.props.updateData()
                }
            })
        }
        else this.props.closeModal()
    }
    render(){
        const {heightPx, showSite, site, debut, fin, tache, depart, arrivee, intervention, typeRapports, typeActions, interventions, 
            activeTab, type_rapport_id, commentaire, technicien, disableSave, error} = this.state
        const {rapport} = this.props
        const typeRapportSansAlarmes = [
            {
                id: 3,
                nom: "Intervention technique"
            },
            {
                id: 6,
                nom: "Intervention sans déclenchement"
            }
        ]
        return (
            <div>
                <Modal 
                        disableSave={disableSave} 
                        width="lg" 
                        handleSave={this.handleSave} 
                        handleCancel={this.handleCancel}
                        error={error} 
                        readOnly={rapport && rapport.operateur != localStorage.getItem("username")}
                    >
                    {
                        showSite &&
                        <Site 
                            changeSite={this.handleChangeSite}
                            closeModal={this.handleCloseSite}/>
                    }
                    <h3>Rapport</h3>
                    {
                        !typeRapports.length ? <LoadingData/>
                        :
                        <div style={{maxHeight: heightPx, overflowY: "auto", padding:"10px"}}>
                            <div className="table">
                                {
                                    rapport ?
                                    <div className="row">
                                        <div className="cell-50">
                                            <div className="input-container">
                                                <label>Site</label>
                                                <input 
                                                    disabled={true} 
                                                    value={rapport.site}/>
                                            </div>
                                        </div>
                                        <div className="cell-50">
                                            <div className="input-container">
                                                <label>Groupe</label>
                                                <input 
                                                    disabled={true} 
                                                    value={rapport.group}/>
                                            </div>
                                        </div>
                                    </div>
                                    :
                                    <div className="row">
                                        <div className="cell-50">
                                            <div className="input-container">
                                                <label>Site *</label>
                                                <div className="table">
                                                    <div className="cell">
                                                        <input 
                                                            disabled={true} 
                                                            value={site ? site.nom : ''}/>
                                                    </div>
                                                    <div id="cellClientBtn" onClick={this.handleShowSite}>
                                                        <img id="clientImg" src="/img/site.svg"/>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="cell-50">
                                            <div className="input-container">
                                                <label>Région</label>
                                                <input 
                                                    disabled={true} 
                                                    value={site ? site.group : ''}/>
                                            </div>
                                        </div>
                                    </div>
                                }
                                <div className="row">
                                    <div className="cell-50">
                                        <div className="input-container">
                                            <label>Type de rapport *</label>
                                            {
                                                rapport ?
                                                <select className="custom-input" 
                                                    onChange={this.handleChangeTypeRapport} 
                                                    value={type_rapport_id} 
                                                    disabled={!rapport.dtarrived || rapport.operateur != localStorage.getItem("username")}
                                                >
                                                    <option></option>
                                                    {
                                                        (
                                                            rapport.dtarrived ? typeRapports.filter(t => t.id != 6)
                                                            : typeRapportSansAlarmes
                                                        ).map(t => <option key={t.id} value={t.id}>{t.nom}</option>)
                                                    }
                                                </select>
                                                :
                                                <select className="custom-input" 
                                                    onChange={this.handleChangeTypeRapport} 
                                                    value={type_rapport_id} 
                                                >
                                                    <option></option>
                                                    {
                                                        typeRapportSansAlarmes.map(t => <option key={t.id} value={t.id}>{t.nom}</option>)
                                                    }
                                                </select>
                                            }
                                        </div>
                                    </div>
                                    <div className="cell-50">
                                        {
                                            type_rapport_id == 3 &&
                                            <div className="input-container">
                                                <label>Technicien *</label>
                                                <input 
                                                    onChange={this.handleChangeTechnicien} 
                                                    disabled={rapport && rapport.operateur != localStorage.getItem("username")}
                                                    value={technicien}/>
                                            </div>
                                        }
                                        {
                                            [1, 6].includes(Number.parseInt(type_rapport_id)) &&
                                            <SeachInput
                                                label="Intervention *" 
                                                selectedItem={intervention}
                                                items={interventions}
                                                disabled={rapport && rapport.operateur != localStorage.getItem("username")}
                                                handleChange={this.handleInterventionChange}/>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div className="container">
                                {
                                    type_rapport_id != 3 ?
                                    <div className="input-container">
                                        <label>Commentaire {[1, 2].includes(Number.parseInt(type_rapport_id)) ? "*" : ""}</label>
                                        <textarea onChange={this.handleChangeCommentaire} rows="5" disabled={rapport && rapport.operateur != localStorage.getItem("username")}>
                                            {commentaire ? commentaire : ''}
                                        </textarea>
                                    </div>
                                    :
                                    <div className="input-container">
                                        <label>Tâche effectué *</label>
                                        <input 
                                            onChange={this.handleChangeTache} 
                                            disabled={rapport && rapport.operateur != localStorage.getItem("username")}
                                            value={tache}/>
                                    </div>
                                }
                            </div>
                            <div className="table">
                                {
                                    [1, 6].includes(Number.parseInt(type_rapport_id)) &&
                                    <div className="row">
                                        <div className="cell-50">
                                            <div className="input-container">
                                                <label>Départ *</label>
                                                <DatePicker  
                                                    showTimeSelect
                                                    className="datepicker" 
                                                    dateFormat="dd-MM-yyyy HH:mm:ss" 
                                                    timeFormat="HH:mm:ss"
                                                    selected={depart} 
                                                    onChange={this.handleChangeDateDepart}
                                                    disabled={rapport && rapport.operateur != localStorage.getItem("username")}/> 
                                            </div>
                                        </div>
                                        <div className="cell-50">
                                            <div className="input-container">
                                                <label>Arrivée *</label>
                                                <DatePicker  
                                                    showTimeSelect
                                                    className="datepicker" 
                                                    dateFormat="dd-MM-yyyy HH:mm:ss" 
                                                    timeFormat="HH:mm:ss"
                                                    selected={arrivee} 
                                                    onChange={this.handleChangeDateArrivee}
                                                    disabled={rapport && rapport.operateur != localStorage.getItem("username")}/>
                                            </div>
                                        </div>
                                    </div>
                                }
                                {
                                    [1, 3, 6].includes(Number.parseInt(type_rapport_id)) &&
                                    <div className="row">
                                        {
                                            type_rapport_id == 3 &&
                                            <div className="cell-50">
                                                <div className="input-container">
                                                    <label>Début *</label>
                                                    <DatePicker  
                                                        showTimeSelect
                                                        className="datepicker" 
                                                        dateFormat="dd-MM-yyyy HH:mm:ss" 
                                                        timeFormat="HH:mm:ss"
                                                        selected={debut} 
                                                        onChange={this.handleChangeDateDebut}
                                                        disabled={rapport && rapport.operateur != localStorage.getItem("username")}/>
                                                </div>
                                            </div>
                                        }
                                        <div className="cell-50">
                                            <div className="input-container">
                                                <label>Fin *</label>
                                                <DatePicker  
                                                    showTimeSelect
                                                    className="datepicker" 
                                                    dateFormat="dd-MM-yyyy HH:mm:ss" 
                                                    timeFormat="HH:mm:ss"
                                                    selected={fin} 
                                                    onChange={this.handleChangeDateFin}
                                                    disabled={rapport && rapport.operateur != localStorage.getItem("username")}/>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                            {
                                rapport &&
                                <div className="container">
                                    <div style={{position: 'relative', top: '2px'}}>
                                        <div className="table">
                                            <div className="cell">
                                                <div id="tabHeaderOverview">
                                                    <ul>
                                                        <li id="habilite" className={activeTab == 'habilite' ? "active-tab": ""} onClick={this.handleChangeTab}>Habilité</li>
                                                        {    
                                                            [1, 2].includes(Number.parseInt(type_rapport_id)) && 
                                                            <li id="action" className={activeTab == 'action' ? "active-tab" : ""} onClick={this.handleChangeTab}>Action</li>
                                                        }   
                                                        {
                                                            type_rapport_id != 6 &&
                                                            <li id="alarm" className={activeTab == 'alarm' ? "active-tab" : (rapport.alarms.length == 0 ? "red" : "")} onClick={this.handleChangeTab}>
                                                                Alarme {rapport.alarms.length > 0 ? "[" + rapport.alarms.length + "]" : ""}
                                                            </li>
                                                        }
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="tabContentOverview">
                                        <div id="tabContainer">
                                            {
                                                activeTab == 'action' && 
                                                <Action 
                                                    rapportId={rapport.id} 
                                                    data={rapport.actions}
                                                    listSelection={typeActions} 
                                                    readOnly={rapport.operateur != localStorage.getItem("username")}/>
                                            }
                                            {
                                                activeTab == 'alarm' && 
                                                <Alarm data={rapport.alarms}/>
                                            }
                                            {
                                                activeTab == 'habilite' && 
                                                <Habilite 
                                                    archive={true} 
                                                    habilites={rapport.habilites} />
                                            }
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                </Modal>
            </div>
        )
    }
}