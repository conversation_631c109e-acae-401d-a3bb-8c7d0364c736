import React, { Component } from 'react'
import axios from 'axios'

import './rapport.css'
import LoadingData from '../../loading/LoadingData'
import IconButton from '../../button/IconButton'
import EditRapportModal from './EditRapportModal'
import moment from 'moment'
import DeleteRapportModal from './DeleteRapportModal'

export default class Rapport extends Component {
    constructor(props){
        super(props)
        this.state = {
            currentRapport: null,
            searchValue: '',
            loading: true,
            rapports: null,
            showEditRapport: false,
            showDeleteRapport: false,
        }
        this.handleSaveSelect = this.handleSaveSelect.bind(this)
        this.handleChangeSelected = this.handleChangeSelected.bind(this)
        this.setSelectedRapport = this.setSelectedRapport.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.handleSeachRapport = this.handleSeachRapport.bind(this)
        this.updateData = this.updateData.bind(this)
        this.handleCloseEditRapportModal = this.handleCloseEditRapportModal.bind(this)
        this.handleShowEditRapportModal = this.handleShowEditRapportModal.bind(this)
        this.handleShowDeleteRapportModal = this.handleShowDeleteRapportModal.bind(this)
    }
    handleCloseEditRapportModal(){
        this.setState({
            showEditRapport: false,
            showDeleteRapport: false,
        })
    }
    handleShowDeleteRapportModal(){
        this.setState({
            showDeleteRapport: true
        })
    }
    handleShowEditRapportModal(){
        const {currentRapport} = this.state
        if(currentRapport)
            axios.get("/api/rapports/show/" + currentRapport.id)
            .then(({data}) => {
                this.setState({
                    showEditRapport: true,
                    currentRapport: data.rapport,
                    currentDate: data.current_date,
                })
            })
        else {
            const {alarm} = this.props
            let data = new FormData()
            data.append("idademco", alarm.id)
            data.append("site_id", alarm.site_id)
            data.append("eventcode", alarm.code)
            data.append("zone", alarm.zone)
            data.append("dtarrived", alarm.date)
            data.append("username", localStorage.getItem("username"))
            data.append("secret", localStorage.getItem("secret"))
            axios.post("/api/rapports/store", data)
            .then(({data}) => {
                if(data.error){
                    console.error(data.error)
                }
                else {
                    this.setState({
                        showEditRapport: true,
                        currentRapport: data.rapport
                    })
                }
            })
        }
    }
    handleChangeSelected(rapport){
        this.setState({
            currentRapport: rapport
        })
    }
    handleSaveSelect(){
        const {currentRapport} = this.state
        const {alarm} = this.props
        let data = new FormData()
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        data.append("idademco", alarm.id)
        axios.post("/api/rapports/attach_alarm/" + currentRapport.id, data)
        .then(({data}) => {
            if(data.error)
                console.error(error)
            else if(data){
                console.log(data)
                this.props.updateCurrentAlarm()
                this.props.closeModal()
            }
        })
        .catch
    }
    handleCancel(){
        this.props.closeModal()
    }
    handleSeachRapport(event){
        this.setState({
            searchValue: event.target.value
        })
    }
    updateData(){
        const {alarm} = this.props
        this.setState({     
            loading: true
        })
        const username = localStorage.getItem('username')
        const secret = localStorage.getItem('secret')
        axios.get('/api/rapports/show_by_site/' + alarm.site_id + '?username=' + username + '&secret=' + secret)
        .then(({data}) => {
            let currentRapport = null
            data.map(row => {
                if(row.id == alarm.rapport_id)
                    currentRapport = row
            })
            this.setState({
                rapports: data,
                currentRapport: currentRapport,
                loading: false
            })
        })
        .catch((e) => {
            console.error(e)
            setTimeout(() => {
                this.updateData()
            }, 10000)
        })
    }
    setSelectedRapport(value){
        this.setState({
            currentRapport: value
        })
    }
    componentDidMount(){
        this.updateData()
    }
    showRapport(rapport){
        const {searchValue} = this.state
        if(searchValue){
            var patt = new RegExp(searchValue.toLocaleLowerCase())
            if(rapport.nom && patt.test(rapport.nom.toLocaleLowerCase()))
                return true
            return false
        }
        return true
    }
    getColor(rapport){
        let code = (
            (!rapport.type_rapport_id) ? 'aaa' :
            (!rapport.nb_alarm) ? '9c27b0' : '444'
        )
        return '#' + code
    }
    render(){
        const {showDeleteRapport, showEditRapport, loading, rapports, currentRapport} = this.state
        const {alarm} = this.props
        return (
            <div style={{zIndex: 200}} className="fixed-front">
                {
                    showDeleteRapport &&
                    <DeleteRapportModal 
                        rapport = {currentRapport}
                        site = {alarm.site}
                        setSelectedRapport={this.setSelectedRapport} 
                        updateData={this.updateData} 
                        closeModal={this.handleCloseEditRapportModal} />
                }
                {
                    showEditRapport &&
                    <EditRapportModal rapport={currentRapport} updateData={this.updateData} closeModal={this.handleCloseEditRapportModal}/>
                }
                <div className="table">
                    <div className="modal-container">
                        <div className="modal lg">
                            {
                                (alarm.rapport_id && !currentRapport) ? 
                                    <div className="modal-content">
                                        <h3 className="center secondary">L'alarme est déjà rattaché à un rapport antérieur</h3>
                                    </div>
                                :
                                    <div className="modal-content">
                                        <div className="table">
                                            <div className="cell">
                                                <h3>Rapports</h3>
                                            </div>
                                            <div className="cell right">
                                                {/*<input style={{width: "300px"}} value={searchValue} onChange={this.handleSeachRapport}/>*/}
                                                <IconButton onClick={this.handleShowEditRapportModal} label="Nouveau rapport" src="/img/edit.svg"/>
                                            </div>
                                        </div>
                                        <table className="fixed_header default layout-fixed">
                                            <thead>
                                                <tr>
                                                    <th className="cellRapportRadio"></th>
                                                    <th className="cellRapportCreer">Créé à</th>
                                                    <th className="cellRapportType">Type d'alarme</th>
                                                    <th className='center'>Opérateur</th>
                                                </tr>
                                            </thead>
                                            <tbody style={{height: "400px"}}>
                                                {
                                                    loading ?
                                                        <LoadingData/>
                                                    :
                                                        (!rapports || rapports.length == 0) ?
                                                            <h3 className="secondary center">Aucun rapport déjà existant pour ce site</h3>
                                                        :
                                                            rapports.map((r) => {
                                                                if(this.showRapport(r))
                                                                    return (
                                                                        <tr key={r.id} 
                                                                            onClick={() => {this.handleChangeSelected(r)}}
                                                                            style={{color: this.getColor(r)}}
                                                                        >
                                                                        <td className="cellRapportRadio">
                                                                            <label className="checkbox-container">
                                                                                <input checked={(currentRapport && currentRapport.id == r.id)} name="rapportRadio" type="radio"/>
                                                                                <span className="radiomark-lg"></span>
                                                                            </label>
                                                                        </td>
                                                                            <td className="cellRapportCreer">{moment(r.created_at).format("HH:mm:ss")}</td>
                                                                            <td className="cellRapportType">{r.type} {/*r.nb_alarm ? ' (' + r.nb_alarm + ')' : ''*/}</td>
                                                                            <td className='center'>{r.operateur.toUpperCase()}</td>
                                                                        </tr>
                                                                    )
                                                            })
                                                        
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                            }
                            <div className="modal-footer">
                                <div className="table">
                                    {
                                        currentRapport &&
                                        <div className="cell left">
                                            <button onClick={this.handleShowEditRapportModal} className="btn-default fix-width">
                                                {localStorage.getItem("username") == currentRapport.operateur ? "Modifier": "Détail"}
                                            </button>
                                            {
                                                (localStorage.getItem("username") == currentRapport.operateur && currentRapport.nb_alarm == 0) &&
                                                <button onClick={this.handleShowDeleteRapportModal} className="btn-default fix-width">Supprimer</button>
                                            }
                                        </div>
                                    }
                                    <div className="cell right">
                                        {
                                            (currentRapport && currentRapport.type_rapport_id) &&
                                            <button disabled={currentRapport == null} onClick={this.handleSaveSelect} className="fix-width btn-primary">Attacher</button>
                                        }
                                        <button onClick={this.handleCancel} className="btn-default fix-width">Fermer</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }
}