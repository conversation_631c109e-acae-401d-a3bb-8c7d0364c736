import axios from 'axios'
import moment from 'moment'
import React, { Component } from 'react'
import IconButton from '../../../button/IconButton'

import './action.css'
import DeleteActionModal from './DeleteActionModal'
import EditActionModal from './EditActionModal'

export default class Action extends Component {
    constructor(props) {
        super(props)
        this.state = {
            showRemoveAction: false,
            showEditAction: false,
            actions: [],
        }
        this.handleCloseModal = this.handleCloseModal.bind(this)
        this.updateData = this.updateData.bind(this)
    }
    showRemoveModal(action){
        this.setState({
            showRemoveAction: true,
            currentAction: action,
        })
    }
    updateData(){
        const {rapportId} = this.props
        axios.get("/api/actions/by_rapport_id/" + rapportId)
        .then(({data}) => {
            this.setState({
                actions: data
            })
        })
        .catch(() => {
            setTimeout(() => {
                this.updateData()
            }, 10000)
        })
    }
    handleCloseModal(){
        this.setState({
            showEditAction: false,
            showRemoveAction: false,
        })
    }
    toggleEnrollModal(v){
        this.setState({
            showEditAction: v
        })
    }
    componentDidMount(){
        const {data} = this.props
        this.setState({
            actions: data
        })
    }

    render(){
        const {actions, currentAction, showRemoveAction, showEditAction} = this.state
        const {rapportId, readOnly, listSelection} = this.props
        return (
            <div>
                <div className="table">
                    {
                        !readOnly &&
                        <div className="row-header">
                            <div className="cell right fix-cell-empreinte">
                                <span id="newAgentBtn">
                                    <IconButton onClick={() => this.toggleEnrollModal(true)} label="Ajouter une action" src="/img/add.svg"/>
                                </span>
                            </div>
                        </div>
                    }
                </div>
                <table className="fixed_header default layout-fixed">
                    <thead>
                        <tr>
                            <th className="cellAction">Action</th>
                            <th className="cellHeureAction">Heure</th>
                            {!readOnly && <th></th>}
                        </tr>
                    </thead>
                    <tbody style={{height: "250px"}}>
                        {
                            actions.map((row) => {
                                return (
                                    <tr key={row.id}>
                                        <td className="cellAction">
                                            {row.action}
                                        </td>
                                        <td className="cellHeureAction">
                                            {moment(row.created_at).format("HH:mm:ss")}
                                        </td>
                                        {
                                            !readOnly && 
                                            <td className="center">
                                                <img onClick={() => this.showRemoveModal(row)} className="img-btn" title="Supprimer" src="/img/delete.svg"/>
                                            </td>
                                        }
                                    </tr>
                                )
                            })
                        }
                    </tbody>
                </table>
                {
                    showRemoveAction &&
                    <DeleteActionModal action={currentAction} updateData={this.updateData} closeModal={this.handleCloseModal}/>
                }
                {
                    showEditAction &&
                    <EditActionModal rapportId={rapportId} data={listSelection} updateData={this.updateData} closeModal={this.handleCloseModal}/>
                }

            </div>
        )
    }
}