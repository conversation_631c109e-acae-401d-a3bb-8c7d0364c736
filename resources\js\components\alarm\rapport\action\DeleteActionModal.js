import React, { Component } from 'react'
import Modal from '../../../modal/Modal'
import axios from 'axios'
import moment from 'moment'

export default class DeleteActionModal extends Component {
    constructor(props) {
        super(props)
        this.state = {
            error: ''
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }

    handleSave(){
        this.setState({
            error: ''
        })
        const {action} = this.props
        const data = new FormData()
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        axios.post("/api/actions/delete/" + action.id, data)
        .then(({data}) => {
            if(data.error){
                this.setState({
                    error: data.error
                })
            }
            else if(data){
                this.props.updateData(true)
                this.props.closeModal()
            }
        })
    }

    handleCancel(){
        this.props.closeModal()
    }

    render(){
        const {error} = this.state
        const {action, site} = this.props
        return (
            <Modal confirm={true} handleSave={this.handleSave} handleCancel={this.handleCancel}>
                <div>
                    <h3>Suppression de l' action</h3>
                    <div>
                        <b>Action: </b>{action.action}<br/>
                        <b>Créé à: </b>{moment(action.created_at).format("HH:mm:ss")}<br/>
                    </div>
                    <hr/>
                    <p className="red">
                        {error}
                    </p>
                </div>
            </Modal>)
    }
}