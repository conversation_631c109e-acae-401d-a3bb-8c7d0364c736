import React, { Component } from 'react'
import axios from 'axios'

import Modal from '../../../modal/Modal'

import 'react-datepicker/dist/react-datepicker.css'

export default class EditActionModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            disableSave: false,
            error: null,
            activeTab: 'action',
            type_action_id: '',
            commentaire: '',
            typeRapports: [],
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.handleChangeTypeAction = this.handleChangeTypeAction.bind(this)
        this.handleChangeTab = this.handleChangeTab.bind(this)
        this.handleChangeCommentaire = this.handleChangeCommentaire.bind(this)
    }
    handleChangeCommentaire(e){
        this.setState({
            commentaire: e.target.value
        })
    }
    handleChangeTab(e){
        this.setState({
            activeTab: e.target.id
        })
    }
    handleChangeTypeAction(e){
        this.setState({
            type_action_id: e.target.value,
        })
    }
    handleSave(){
        const {type_action_id} = this.state
        const {rapportId} = this.props
        this.setState({
            disableSave: true,
            error: null,
        })
        let data = new FormData()
        data.append("rapport_id", rapportId)
        data.append("type_action_id", type_action_id)
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        axios.post("/api/actions/store", data)
        .then(({data}) => {
            console.log(data)
            this.props.closeModal()
            this.props.updateData()
        })
        .finally(()=>{
            this.setState({
                disableSave: false
            })
        })
    }
    handleCancel(){
        this.props.updateData()
        this.props.closeModal()
    }
    render(){
        const {type_action_id} = this.state
        const {data} = this.props
        return (
            <div>
                <Modal 
                    disableSave={!type_action_id} 
                    width="md" 
                    handleSave={this.handleSave} 
                    handleCancel={this.handleCancel}
                >
                    <h3>Ajouter une action</h3>
                    <div className="input-container">
                        <label>Type d'action *</label>
                        <select className="custom-input" 
                            onChange={this.handleChangeTypeAction} 
                            value={type_action_id}
                        >
                            <option></option>
                            {
                                data.map(t => <option key={t.id} value={t.id}>{t.nom}</option>)
                            }
                        </select>
                    </div>
                </Modal>
            </div>
        )
    }
}