import moment from 'moment'
import React, { Component } from 'react'
import IconButton from '../../../button/IconButton'


export default class Alarm extends Component {
    constructor(props) {
        super(props)
        this.state = {}
    }

    getColor(code){
        let color = (
            120 == code ? 'd50000':
            132 == code ? 'c62828':
            133 == code ? 'c62828':
            134 == code ? 'c62828':
            130 == code ? 'f44336':
            131 == code ? 'c51162':
            137 == code ? 'ad1457':
            140 == code ? 'd500f9':
            100 == code ? 'e91e63':
            101 == code ? 'ff6d00':
            110 == code ? 'ef6c00':
            151 == code ? '8d6e63':
            111 == code ? 'ff9800':
            117 == code ? 'ffab00':
            112 == code ? 'ff8f00':
            113 == code ? 'ffb300':
            102 == code ? 'dd2c00':
            139 == code ? 'd84315':
            384 == code ? '673ab7':
            301 == code ? '6200ea':
            302 == code ? '6200ea':
            350 == code ? '4527a0':
            1000 == code ? '78909c':
            [400, 401, 402, 403, 404, 405, 407, 406, 408, 409, 441, 442, 456, 454].includes(parseInt(code)) ? '7cb342': '444'
        )
        return '#' + color
    }

    render(){
        return (
            <div>
                <table className="fixed_header default layout-fixed">
                    <thead>
                        <tr>
                            <th className="cellAction">Alarme</th>
                            <th className="cellZone">Zone</th>
                            <th className="center">Déclenché à</th>
                        </tr>
                    </thead>
                    <tbody style={{height: "250px"}}>
                        {
                            this.props.data.map((row) => {
                                return (
                                    <tr key={row.idademco} style={{color: this.getColor(row.codeTevent)}}>
                                        <td className="cellAction">
                                            [{row.codeTevent}] {row.alarm}
                                        </td>
                                        <td className="cellZone">
                                            {("000" + row.zones).slice(-3)}
                                        </td>
                                        <td className="center" style={{fontFamily: "CallingCode"}}>
                                            {moment(row.dtarrived).format("HH:mm:ss")}
                                        </td>
                                    </tr>
                                )
                            })
                        }
                    </tbody>
                </table>
            </div>
        )
    }
}