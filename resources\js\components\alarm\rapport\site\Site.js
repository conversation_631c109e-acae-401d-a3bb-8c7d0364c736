import React, { Component } from 'react'
import axios from 'axios'

import LoadingData from '../../../loading/LoadingData'

export default class Site extends Component {
    constructor(props){
        super(props)
        this.state = {
            selectedSite: null,
            searchValue: '',
            loading: true,
            sites: null
        }
        this.handleSaveSelect = this.handleSaveSelect.bind(this)
        this.handleChangeSelected = this.handleChangeSelected.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.handleSeachSite = this.handleSeachSite.bind(this)
        this.updateData = this.updateData.bind(this)
    }
    handleChangeSelected(site){
        this.setState({
            selectedSite: site
        })
    }
    handleSaveSelect(){
        this.props.changeSite(this.state.selectedSite)
    }
    handleCancel(){
        this.props.closeModal()
    }
    handleSeachSite(event){
        this.setState({
            searchValue: event.target.value
        })
    }
    updateData(){
        this.setState({
            loading: true
        })
        axios.get('/api/select_diag_sites')
        .then(({data}) => {
            this.setState({
                sites: data,
                loading: false
            })
        })
    }
    componentDidMount(){
        this.updateData()
    }
    showSite(site){
        const {searchValue} = this.state
        if(searchValue){
            var patt = new RegExp(searchValue.toLocaleLowerCase())
            if(site.nom && patt.test(site.nom.toLocaleLowerCase()))
                return true
            return false
        }
        return true
    }
    render(){
        const {loading, sites, selectedSite, searchValue} = this.state
        return (
            <div style={{zIndex: 200}} className="fixed-front">
                <div className="table">
                    <div className="modal-container">
                        <div className="modal md">
                            <div className="modal-content">
                                <div className="table">
                                    <div className="cell">
                                        <h3>Sites</h3>
                                    </div>
                                    <div className="cell right">
                                        <input id="siteSearch" value={searchValue} onChange={this.handleSeachSite}/>
                                    </div>
                                </div>
                                <table className="fixed_header default layout-fixed">
                                    <tbody style={{height: "400px"}}>
                                        {
                                            loading ?
                                                <LoadingData/>
                                            :
                                                sites && sites.map((site) => {
                                                    if(this.showSite(site))
                                                        return (
                                                            <tr key={site.idsite} onClick={() => {this.handleChangeSelected(site)}}>
                                                                <td className="cellSiteRadio">
                                                                    <label className="checkbox-container">
                                                                        <input checked={(selectedSite && selectedSite.idsite == site.idsite)} name="siteRadio" type="checkbox"/>
                                                                        <span className="radiomark-lg"></span>
                                                                    </label>
                                                                </td>
                                                                <td className="cellSiteNom">{site.nom}</td>
                                                            </tr>)
                                                })
                                                
                                        }
                                    </tbody>
                                </table>
                            </div>
                            <div className="modal-footer">
                                <div className="table">
                                    <div className="cell right">
                                        <button disabled={selectedSite == null} onClick={this.handleSaveSelect} className="btn-primary fix-width">Selectionner</button>
                                        <button onClick={this.handleCancel} className="btn-default fix-width">Annuler</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }
}