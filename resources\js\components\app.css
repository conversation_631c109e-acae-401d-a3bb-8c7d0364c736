*{
    box-sizing: border-box;
}
.modal-footer{
    padding-top: 20px;
}
.modal-content{
    text-align: left;
}
.modal-content select,.modal-content input, .modal-content textarea{
    display: inline-block;
    width: 100%;
    padding: 10px;
    border: solid .5px rgba(0, 0, 0, .2);
}
select.custom-input{
    display: inline-block;
    width: calc(100% - 10px);
    padding: 10px;
    border: solid .5px rgba(0, 0, 0, .2);
}

.input-container{
    padding: 10px 0px;
}
.container{
    padding: 10px;
}
.overflow-auto{
    padding: 10px;
    overflow: auto;
}
.center{
    text-align: center;
}
.nowrap{
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.row{
    display: table-row;
    width: 100%;
}
select.input-fixed, input.input-fixed{
    width: 250px;
    padding: 10px;
    border: solid .5px rgba(0, 0, 0, .1);
}
.btn-label-container{
    height: 45px;
    padding: 4px;
}
.padding-side{
    padding: 0px 10px;
}
.padding-bottom{
    padding: 0px 0px 20px 0px;
}
.cell-50, .cell-30, .cell-40, .cell-33, .cell-45, .cell-10, .cell-padding{
    display: table-cell;
    padding: 10px;
}
.cell-50{
    max-width: 50%;
    width: 50%;
    min-width: 50%;
}
.cell-30{
    max-width: 30%;
    width: 30%;
    min-width: 30%;
}
.cell-40{
    max-width: 40%;
    width: 40%;
    min-width: 40%;
}
.cell-45{
    max-width: 45%;
    width: 45%;
    min-width: 45%;
}
.cell-10{
    max-width: 10%;
    width: 10%;
    min-width: 10%;
    vertical-align: middle;
}
.cell-33{
    max-width: 33%;
    width: 33%;
    min-width: 33%;
}
.full-width{
    display: table;
    width: 100%;
}
.box-shadow-left{
    box-shadow: -3px 0px 3px rgba(0, 0, 0, .1);
}
.add-icon, .edit-icon-cell{
    cursor: pointer;
}
.add-icon{
    width: 20px;
}
.edit-icon-cell{
    display: inline-block;
    width: 18px;
    margin-left: 10px;
}
.add-icon:hover, .edit-icon-cell:hover{
    background-color: rgba(0, 0, 0, .1);
}
.chevron-separator{
    padding: 0px 5px;
}