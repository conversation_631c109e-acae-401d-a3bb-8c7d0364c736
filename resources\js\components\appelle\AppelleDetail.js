import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'

import 'react-datepicker/dist/react-datepicker.css'

export default class AppelleDetail extends Component {
    constructor(props) {
        super(props)
        this.state = {
            showSiteModal: false,
            showEditAppelleModal: false,
            showArchiveAppelleModal: false,
            showDeleteAppelleModal: false,
            optic: false,
        }
        this.handleChangeTab = this.handleChangeTab.bind(this)
        this.closeAppelleModal = this.closeAppelleModal.bind(this)
        this.updateAppelle = this.updateAppelle.bind(this)
        this.updateData = this.updateData.bind(this)
        this.handleShowSite = this.handleShowSite.bind(this)
    }
    handleShowSite() {
        this.setState({
            showSiteModal: true
        })
    }
    closeAppelleModal() {
        this.setState({
            showSiteModal: false,
            showEditAppelleModal: false,
            showDeleteAppelleModal: false,
            showArchiveAppelleModal: false,
        })
    }
    updateData() {
        this.props.updateData(true)
    }
    updateAppelle() {
        const { currentAppelle } = this.props
        this.props.updateAppelle(currentAppelle.id)
    }
    handleChangeTab(event) {
        if (event.target.id != this.props.activeTab) {
            this.props.handleChangeTab(event.target.id)
        }
    }
    render() {
        const { activeTab, user, archive, currentDate, heightWindow, showEditAppelleMenu, currentAppelle } = this.props
        return (
            <div>
                <div className="overview-container">
                    <div className="head-title-overview" title={currentAppelle.nom}>
                        <div style={{ height: "40px", lineHeight: "40px" }}>
                            <div className="title-overview">
                                <span className={this.props.getColor(currentAppelle)} style={{ opacity: .9 }}>
                                    {
                                        currentAppelle.societe_id == 1 ? 'DGM-' + currentAppelle.numero_employe :
                                            currentAppelle.societe_id == 2 ? 'SOIT-' + currentAppelle.num_emp_soit :
                                                currentAppelle.societe_id == 3 ? 'ST-' + currentAppelle.numero_stagiaire :
                                                    currentAppelle.societe_id == 4 ? 'SM' :
                                                        currentAppelle.numero_employe ? currentAppelle.numero_employe :
                                                            currentAppelle.numero_stagiaire ? currentAppelle.numero_stagiaire :
                                                                <span className="purple">Non définie</span>
                                    }
                                </span>
                            </div>
                            <div className="overview-edit-icon">
                                {
                                    (currentAppelle.empreinte || currentAppelle.empreinte_optic) &&
                                    <img onClick={() => { this.props.toggleEditAppelleMenu(!showEditAppelleMenu) }} className="overview-edit-img" src="/img/parametre.svg" />
                                }
                            </div>
                        </div>
                    </div>
                    <span className="overview-break-overflow" title={currentAppelle.nom}>
                        <b>Nom : </b>{currentAppelle.nom}
                    </span>
                    <span title={currentAppelle.phone_appelle}><b>Site : </b> {currentAppelle.site}</span><br />
                    <span><b>Fonction : </b> {currentAppelle.fonction}</span><br />
                    <div className="table">
                        <span className="cell">
                            {
                                !archive ?
                                    (
                                        currentAppelle.societe_id == 1 ?
                                            <span>
                                                <b>Date de confirmation : </b>
                                                {currentAppelle.date_confirmation && moment(currentAppelle.date_confirmation).format('DD MMM YYYY')}
                                            </span>
                                            :
                                            currentAppelle.societe_id == 2 ?
                                                <span>
                                                    <b>Date de confirmation : </b>
                                                    {currentAppelle.date_conf_soit && moment(currentAppelle.date_conf_soit).format('DD MMM YYYY')}
                                                </span>
                                                :
                                                <span>
                                                    <b>Date d'embauche : </b>
                                                    {currentAppelle.date_embauche && moment(currentAppelle.date_embauche).format('DD MMM YYYY')}
                                                </span>
                                    )
                                    :
                                    <span>
                                        <b>Date de sortie : </b>
                                        {currentAppelle.date_sortie && moment(currentAppelle.date_sortie).format('DD MMM YYYY')}
                                    </span>
                            }
                        </span>
                        {
                            (currentAppelle.empreinte && !currentAppelle.empreinte_optic) &&
                            <span className="cell right" style={{ height: "30px" }}>
                                <span className="badge bg-purple">Empreinte capacitif</span>
                            </span>
                        }
                        {
                            (!currentAppelle.empreinte && currentAppelle.empreinte_optic) &&
                            <span className="cell right" style={{ height: "30px" }}>
                                <span className="badge bg-purple">Empreinte optique</span>
                            </span>
                        }
                        {
                            (currentAppelle.empreinte && currentAppelle.empreinte_optic) &&
                            <span className="cell right" style={{ height: "30px" }}>
                                <span className="badge bg-primary">Empreinte complet</span>
                            </span>
                        }
                    </div>
                </div>
                <div style={{ position: 'relative', top: '2px' }}>
                    <div className="table">
                        <div className="cell">
                            <div id="tabHeaderOverview">
                                <ul>
                                    <li id="pj" className={activeTab == 'pj' ? "active-tab" : ""} onClick={this.handleChangeTab}>Document</li>
                                    {/*
                                        (['rh', 'root'].includes(user.role) && currentAppelle.site) &&
                                        <li id="sanction" className={activeTab == 'sanction' ? "active-tab" : ""} onClick={this.handleChangeTab}>Sanction</li>
                                    */}
                                    {/*
                                        (['rh', 'root'].includes(user.role) && currentAppelle.site) &&
                                        <li id="prime" className={activeTab == 'prime' ? "active-tab" : ""} onClick={this.handleChangeTab}>Prime</li>
                                    */}
                                    {
                                        currentAppelle.site &&
                                        <li id="pointage" className={activeTab == 'pointage' ? "active-tab" : ""} onClick={this.handleChangeTab}>Pointage</li>
                                    }
                                    {/*
                                        ['rh', 'root'].includes(user.role) &&
                                        <li id="conge" className={activeTab == 'conge' ? "active-tab" : ""} onClick={this.handleChangeTab}>Congé</li>
                                    */}
                                    <li id="operation" className={activeTab == 'operation' ? "active-tab" : ""} onClick={this.handleChangeTab}>Traçabilité</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="tabContentOverview">
                    <div id="tabContainer">
                        {
                            activeTab == 'pointage' &&
                            <Pointage currentDate={currentDate} appelleId={currentAppelle.id} nomAppelle={currentAppelle.nom} heightWindow={heightWindow} updateAppelle={this.updateAppelle} />
                        }
                        {
                            activeTab == 'operation' &&
                            <Operation
                                currentDate={currentDate}
                                action={'/api/appelles/operation/' + currentAppelle.id}
                                heightTable={heightWindow - 450} />
                        }
                    </div>
                </div>
            </div>
        )
    }
}
