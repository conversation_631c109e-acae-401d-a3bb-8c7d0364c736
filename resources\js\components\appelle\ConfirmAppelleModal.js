import React, { Component } from 'react'
import axios from 'axios'
import DatePicker  from 'react-datepicker'
import moment from 'moment'

import Modal from '../modal/Modal'

import 'react-datepicker/dist/react-datepicker.css'

export default class ConfirmAppelleModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            observation: '',
            date_sortie: '',
            disableSave: true,
            error: null,
        }
        this.handleChangeObservation = this.handleChangeObservation.bind(this)
        this.handleChangeDateSortie = this.handleChangeDateSortie.bind(this)
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)

    }
    handleChangeDateSortie(date){
        this.setState({
            date_sortie: date,
        })
    }
    handleChangeObservation(event){
        this.setState({
            observation: event.target.value,
            disableSave: !event.target.value,
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    handleSave(){
        const {label} = this.props
        if(!label){
            const {observation, date_sortie} = this.state
            this.setState({
                disableSave: true,
                error: null,
            })
            let data = new FormData()
            if(observation && observation.trim())
                data.append("observation", observation.trim())
            if(date_sortie)
                data.append("date_sortie", moment(date_sortie).format('YYYY-MM-DD'))
            data.append("username", localStorage.getItem("username"))
            data.append("secret", localStorage.getItem("secret"))
            axios.post(this.props.action, data)
            .then(({data}) => {
                if(data.error){
                    const firstKey = Object.keys(data.error)[0]
                    const firstValue = data.error[firstKey][0]
                    this.setState({
                        error: {
                            key: firstKey,
                            value: firstValue
                        },
                        disableSave: false,
                    })
                }
                else if(data){
                    this.props.updateData(true)
                    this.props.closeModal()
                }
            })
            .finally(()=>{
                this.setState({
                    disableSave: false
                })
            })
        }
        else {
            let data = new FormData()
            data.append("username", localStorage.getItem("username"))
            data.append("secret", localStorage.getItem("secret"))
            axios.post(this.props.action, data)
            .then(({data}) => {
                if(data) {
                    this.props.updateAppelle(data)
                    this.props.closeModal()
                }
            })
            .finally(()=>{
                this.setState({
                    disableSave: false
                })
            })
        }
    }
    render(){
        const {date_sortie, observation, disableSave, error} = this.state
        const {label} = this.props
        return ( 
            <Modal 
                disableSave={!label && disableSave} 
                width="md" 
                handleSave={this.handleSave} 
                handleCancel={this.handleCancel}
                error={error}
            >
                <h3>{label ? label : "Mis en archive"}</h3>
                {
                    !label &&
                    <div>
                        <div className="input-container">
                            <label>Observation *</label>
                            <input onChange={this.handleChangeObservation} value={observation}/>
                        </div>
                        <div className="input-container">
                            <label>Date de sortie </label>
                            <DatePicker className="datepicker" dateFormat="dd-MM-yyyy" selected={date_sortie} onChange={this.handleChangeDateSortie}/>
                        </div>
                    </div>
                }
            </Modal>
        )
    }
}