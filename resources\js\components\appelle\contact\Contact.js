import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'
import 'moment/locale/fr'

import InfiniteS<PERSON>roll from 'react-infinite-scroll-component'

import '../appelle.css'
import LoadingData from '../../loading/LoadingData'

export default class Contact extends Component {
    constructor(props) {
        super(props)
        this.state = {
            currentDate: '',
            timeoutId: '',
            inputSearch: '',
            searchContact: '',
            currentContact: null,
            contacts: [],
            heightWindow: 0,
            widthWindow: 0,
            allDataLoaded: false,
        }
        this.fetchMoreData = this.fetchMoreData.bind(this)
        this.updateData = this.updateData.bind(this)
        this.toggleLoading = this.toggleLoading.bind(this)
        this.handleEnterPress = this.handleEnterPress.bind(this)
        this.handleChangeSearchContact = this.handleChangeSearchContact.bind(this)
        this.handleChangeSelected = this.handleChangeSelected.bind(this)
        this.searchTimeout = null
    }
    fetchMoreData() {
        this.updateData(true)
    }
    handleEnterPress(event) {
        if (event.key === 'Enter') {
            this.updateData(true)
        }
    }
    toggleLoading(load) {
        this.props.toggleLoading(load)
    }
    handleChangeSearchContact(event) {
        this.setState({
            inputSearch: event.target.value
        })
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
        this.searchTimeout = setTimeout(() => {
            this.updateData(true);
        }, 500);
    }
    handleChangeSelected(numero) {
        this.setState({
            currentContact: numero
        })
    }
    updateData(loading, clearSearch) {
        const { contacts, inputSearch } = this.state
        const params = new URLSearchParams()
        params.append('offset', (loading ? 0 : contacts.length))
        if (loading)
            this.setState({
                allDataLoaded: false,
                contacts: []
            })
        if (clearSearch)
            this.setState({
                inputSearch: ''
            })
        else
            params.append('search', inputSearch)

        // Use the comprehensive numero endpoint instead of contacts
        axios.get('/api/numeros/comprehensive?' + params)
            .then(({ data }) => {
                if (data) {
                    if (loading) {
                        this.container.scroll(0, 0)
                        this.setState({
                            contacts: data
                        })
                    }
                    else {
                        const list = contacts.slice().concat(data)
                        this.setState({
                            contacts: list
                        })
                    }
                    this.setState({
                        allDataLoaded: (data.length < 50)
                    })
                }
            }).catch(() => {
                setTimeout(() => {
                    this.updateData(loading, clearSearch)
                }, 10000)
            })

    }

    fetchMoreData() {
        setTimeout(() => {
            this.updateData()
        }, 300);
    }
    componentDidMount() {
        this.setState({
            currentDate: moment().format("YYYY-MM-DD")
        })
        this.updateData(true)
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
        document.title = "Numéros - TLS"
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
    }
    resize() {
        this.setState({
            heightWindow: window.innerHeight,
            widthWindow: window.innerWidth
        });
    }
    formatContact(numero, row) {
        if (!numero)
            console.log(row)
        return numero.slice(0, 3) + " " + numero.slice(3, 5) + " " + numero.slice(5, 8) + " " + numero.slice(8, 10)
    }
    render() {
        const { currentDate, contacts, currentContact, inputSearch, heightWindow, allDataLoaded, widthWindow } = this.state
        const { archive, user } = this.props
        return (
            <div className="table" onClick={() => { }}>
                {/* {
                    (['rh', 'root'].includes(user.role) && !archive && notifications.length > 0) &&
                    <Notification now={currentDate} data={notifications} clickItem={this.handleClickContact} />
                } */}
                <div id="tableContainer">
                    <div className="table">
                        <div className="row-header">
                            <h3 className="h3-table">
                                <span className="cell">Numéros et Contacts</span>
                                <span className="cell center">
                                    <div id="searchSite">
                                        <div>
                                            <input onKeyDown={this.handleEnterPress} onChange={this.handleChangeSearchContact} value={inputSearch} type="text" />
                                            <img onClick={() => { this.updateData(true) }} src="/img/search.svg" />
                                        </div>
                                    </div>
                                </span>
                            </h3>
                        </div>
                        <div className="row-table">

                            <table className="fixed_header visible-scroll layout-fixed">
                                <thead>
                                    <tr>
                                        <th className="cellContactPhone">Numéro</th>
                                        <th className="cellContactNom">Description</th>
                                        <th className="cellSiteAgent">Site</th>
                                        <th className="cellSiteAgent">Habilités</th>
                                    </tr>
                                </thead>
                                <tbody id="scrollableDiv" ref={el => (this.container = el)} style={{ 'height': (heightWindow - 160) + "px" }}>
                                    <InfiniteScroll
                                        scrollableTarget="scrollableDiv"
                                        dataLength={contacts ? contacts.length : 0}
                                        next={this.fetchMoreData}
                                        hasMore={!allDataLoaded}
                                        loader={<LoadingData />}
                                    >
                                        {
                                            contacts.map((numero, index) => {
                                                return (
                                                    <tr key={numero.id + '_' + index}
                                                        onClick={() => { this.handleChangeSelected(numero) }}
                                                        className={((currentContact != null && currentContact.id == numero.id) ? "selected-row" : "")}
                                                    >
                                                        <td className="cellContactPhone" title={numero.numero}>
                                                            {this.formatContact(numero.numero, numero)}
                                                        </td>
                                                        <td className="cellContactNom" title={numero.info_description}>
                                                            {numero.info_description || 'N/A'}
                                                        </td>
                                                        <td className="cellSiteAgent" title={numero.site_nom}>
                                                            {numero.site_nom || 'N/A'}
                                                        </td>
                                                        <td className="cellSiteAgent">
                                                            {numero.habilites && numero.habilites.length > 0
                                                                ? `${numero.habilites.length} habilité(s)`
                                                                : 'Aucun'
                                                            }
                                                        </td>
                                                    </tr>)
                                            })
                                        }
                                        {
                                            (allDataLoaded && contacts.length == 0) &&
                                            <tr>
                                                <td className='center secondary'>Aucun données trouvé</td>
                                            </tr>
                                        }
                                    </InfiniteScroll>
                                </tbody>
                            </table>

                        </div>
                    </div>
                </div>
                <div className={currentContact ? "box-shadow-left" : ""} style={{ width: (widthWindow / 2.5) + 'px', maxWidth: (widthWindow / 2.5) + 'px', minWidth: (widthWindow / 2.5) + 'px' }} id="overviewContainer">
                    {
                        currentContact ?
                            <div className="overview-container">
                                <div className="head-title-overview" title={currentContact.numero}>
                                    <div style={{ height: "40px", lineHeight: "40px" }}>
                                        <div className="title-overview">
                                            <span style={{ opacity: .9 }}>
                                                {this.formatContact(currentContact.numero, currentContact)}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <span className="overview-break-overflow" title={currentContact.info_description}>
                                    <b>Description : </b>{currentContact.info_description || 'N/A'}
                                </span>
                                <span title={currentContact.site_nom}><b>Site : </b> {currentContact.site_nom || 'N/A'}</span><br />
                                <span><b>Contact : </b> {currentContact.contact_nom || 'N/A'} {currentContact.contact_prenom || ''}</span><br />
                                {currentContact.contact_adresse && (
                                    <>
                                        <span><b>Adresse : </b> {currentContact.contact_adresse}</span><br />
                                    </>
                                )}
                                {currentContact.site_adresse && (
                                    <>
                                        <span><b>Adresse Site : </b> {currentContact.site_adresse}</span><br />
                                    </>
                                )}
                                {currentContact.site_prom && (
                                    <>
                                        <span><b>PROM : </b> {currentContact.site_prom}</span><br />
                                    </>
                                )}

                                {/* Habilites Section */}
                                {currentContact.habilites && currentContact.habilites.length > 0 && (
                                    <div style={{ marginTop: '20px' }}>
                                        <h4>Habilités ({currentContact.habilites.length})</h4>
                                        <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
                                            {currentContact.habilites.map((habilite) => (
                                                <div key={habilite.idhabilite} style={{
                                                    padding: '8px',
                                                    margin: '4px 0',
                                                    backgroundColor: '#f5f5f5',
                                                    borderRadius: '4px'
                                                }}>
                                                    <div><b>Qualité :</b> {habilite.quality || 'N/A'}</div>
                                                    <div><b>Ordre :</b> {habilite.idordre || 'N/A'}</div>
                                                    {habilite.password && (
                                                        <div><b>Mot de passe :</b> {habilite.password}</div>
                                                    )}
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                            :
                            <div className="img-bg-container">
                                <img className="img-bg-overview" src="/img/tls_background.svg" />
                            </div>
                    }
                </div>
            </div>
        )
    }
}
