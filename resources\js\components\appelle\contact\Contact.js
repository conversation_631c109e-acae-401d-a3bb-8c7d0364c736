import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'
import 'moment/locale/fr'

import InfiniteS<PERSON>roll from 'react-infinite-scroll-component'

import '../appelle.css'
import LoadingData from '../../loading/LoadingData'

export default class Contact extends Component {
    constructor(props) {
        super(props)
        this.state = {
            currentDate: '',
            timeoutId: '',
            inputSearch: '',
            searchContact: '',
            currentContact: null,
            contacts: [],
            heightWindow: 0,
            widthWindow: 0,
            allDataLoaded: false,
        }
        this.fetchMoreData = this.fetchMoreData.bind(this)
        this.updateData = this.updateData.bind(this)
        this.toggleLoading = this.toggleLoading.bind(this)
        this.handleEnterPress = this.handleEnterPress.bind(this)
        this.handleChangeSearchContact = this.handleChangeSearchContact.bind(this)
        this.searchTimeout = null
    }
    fetchMoreData() {
        this.updateData(true)
    }
    handleEnterPress(event) {
        if (event.key === 'Enter') {
            this.updateData(true)
        }
    }
    toggleLoading(load) {
        this.props.toggleLoading(load)
    }
    handleChangeSearchContact(event) {
        this.setState({
            inputSearch: event.target.value
        })
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
        this.searchTimeout = setTimeout(() => {
            this.updateData(true);
        }, 500);
    }
    updateData(loading, clearSearch) {
        const { contacts, inputSearch } = this.state
        const params = new URLSearchParams()
        params.append('offset', (loading ? 0 : contacts.length))
        if (loading)
            this.setState({
                allDataLoaded: false,
                contacts: []
            })
        if (clearSearch)
            this.setState({
                inputSearch: ''
            })
        else
            params.append('search', inputSearch)

        params.append('archived', '0')

        axios.get('/api/contacts?' + params)
            .then(({ data }) => {
                if (data) {
                    if (loading) {
                        this.container.scroll(0, 0)
                        this.setState({
                            contacts: data
                        })
                    }
                    else {
                        const list = contacts.slice().concat(data)
                        this.setState({
                            contacts: list
                        })
                    }
                    this.setState({
                        allDataLoaded: (data.length < 50)
                    })
                }
            }).catch(() => {
                setTimeout(() => {
                    this.updateData(loading, clearSearch)
                }, 10000)
            })

    }

    fetchMoreData() {
        setTimeout(() => {
            this.updateData()
        }, 300);
    }
    componentDidMount() {
        this.setState({
            currentDate: moment().format("YYYY-MM-DD")
        })
        this.updateData(true)
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
        document.title = "Contact - TLS"
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
    }
    resize() {
        this.setState({
            heightWindow: window.innerHeight,
            widthWindow: window.innerWidth
        });
    }
    formatContact(numero, row) {
        if (!numero)
            console.log(row)
        return numero.slice(0, 3) + " " + numero.slice(3, 5) + " " + numero.slice(5, 8) + " " + numero.slice(8, 10)
    }
    render() {
        const { currentDate, contacts, currentContact, inputSearch, heightWindow, allDataLoaded, widthWindow } = this.state
        const { archive, user } = this.props
        return (
            <div className="table" onClick={() => { }}>
                {/* {
                    (['rh', 'root'].includes(user.role) && !archive && notifications.length > 0) &&
                    <Notification now={currentDate} data={notifications} clickItem={this.handleClickContact} />
                } */}
                <div id="tableContainer">
                    <div className="table">
                        <div className="row-header">
                            <h3 className="h3-table">
                                <span className="cell">Contacts</span>
                                <span className="cell center">
                                    <div id="searchSite">
                                        <div>
                                            <input onKeyDown={this.handleEnterPress} onChange={this.handleChangeSearchContact} value={inputSearch} type="text" />
                                            <img onClick={() => { this.updateData(true) }} src="/img/search.svg" />
                                        </div>
                                    </div>
                                </span>
                            </h3>
                        </div>
                        <div className="row-table">

                            <table className="fixed_header visible-scroll layout-fixed">
                                <thead>
                                    <tr>
                                        <th className="cellContactNom">Nom</th>
                                        <th className="cellContactPhone">Contact</th>
                                        <th className="cellSiteAgent">Site</th>
                                    </tr>
                                </thead>
                                <tbody id="scrollableDiv" ref={el => (this.container = el)} style={{ 'height': (heightWindow - 160) + "px" }}>
                                    <InfiniteScroll
                                        scrollableTarget="scrollableDiv"
                                        dataLength={contacts ? contacts.length : 0}
                                        next={this.fetchMoreData}
                                        hasMore={!allDataLoaded}
                                        loader={<LoadingData />}
                                    >
                                        {
                                            contacts.map((contact, index) => {
                                                return (
                                                    <tr key={contact.idContact + '_' + index} onClick={() => { this.handleChangeSelected(contact) }}>
                                                        <td className="cellContactNom">{contact.nom && contact.nom + (contact.prenom ? (' ' + contact.prenom) : '')}</td>
                                                        <td className="cellContactPhone">
                                                            {contact.phones.map(phone => phone.numero).join(", ")}
                                                        </td>
                                                        <td>{contact.site}</td>
                                                    </tr>)
                                            })
                                        }
                                        {
                                            (allDataLoaded && contacts.length == 0) &&
                                            <tr>
                                                <td className='center secondary'>Aucun données trouvé</td>
                                            </tr>
                                        }
                                    </InfiniteScroll>
                                </tbody>
                            </table>

                        </div>
                    </div>
                </div>
                <div className={currentContact ? "box-shadow-left" : ""} style={{ width: (widthWindow / 2.5) + 'px', maxWidth: (widthWindow / 2.5) + 'px', minWidth: (widthWindow / 2.5) + 'px' }} id="overviewContainer">
                    <div className="img-bg-container">
                        <img className="img-bg-overview" src="/img/tls_background.svg" />
                    </div>
                </div>
            </div>
        )
    }
}
