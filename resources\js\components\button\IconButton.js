import React, { Component } from 'react'

import './button.css'

export default class IconButton extends Component {
    constructor(props){
        super(props)
        this.handleClick = this.handleClick.bind(this)
    }
    handleClick(e){
        this.props.onClick()
    }
    render(){
        const {label, src, white} = this.props
        return (
            <span onClick={this.handleClick} className={white ? "btn-label-icon-white" : "btn-label-icon"}>
                <img className={label ? "img-icon" : "img-icon-no-padding"} src={src}/>
                {
                    label && 
                    <span className="label-icon">{label}</span>
                }
            </span>)
    }
}