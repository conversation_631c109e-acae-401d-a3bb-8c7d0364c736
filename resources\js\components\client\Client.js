import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'
import 'moment/locale/fr'
import ClientDetail from './ClientDetail'

import './client.css'
import EditClientModal from './EditClientModal'
import IconButton from '../button/IconButton'

export default class Biometrique extends Component {
    constructor(props){
        super(props)
        this.state = {
            currentDate: '',
            timeoutId: '',
            searchTimeoutId: '',
            inputSearch: '',
            searchClient: '',
            currentClient: null,
            sites: [],
            clients: [],
            notifications: [],
            heightWindow: 0,
            widthWindow: 0,
            activeTab: 'site',
            showEditClientModal: false,
            showAddClientModal: false,
            showArchiveClientModal: false,
            showDeleteClientModal: false,
            showEditClientMenu: false,
        }
        this.updateData = this.updateData.bind(this)
        this.updateClient = this.updateClient.bind(this)
        this.handleClickClient = this.handleClickClient.bind(this)
        this.handleClickAddClient = this.handleClickAddClient.bind(this)
        this.closeClientModal = this.closeClientModal.bind(this)
        this.handleChangeSearchClient = this.handleChangeSearchClient.bind(this)
        this.toggleLoading = this.toggleLoading.bind(this)
        this.toggleEditClientMenu = this.toggleEditClientMenu.bind(this)
        this.getColor = this.getColor.bind(this)
        this.handleChangeTab = this.handleChangeTab.bind(this)
    }
    handleChangeTab(value){
        this.setState({
            activeTab: value
        })
    }
    toggleEditClientMenu(value){
        this.setState({
            showEditClientMenu: value
        })
    }
    toggleLoading(load){
        this.props.toggleLoading(load)
    }
    handleChangeSearchClient(event){
        this.setState({
            inputSearch: event.target.value
        }, () => {
            const {searchTimeoutId, inputSearch} = this.state
            if(searchTimeoutId)
                clearTimeout(searchTimeoutId)
            const timeoutId = setTimeout(() => {
                this.setState({
                    searchClient: inputSearch
                })
            }, 1000)
            this.setState({
                searchTimeoutId: timeoutId
            })
        })
    }
    closeClientModal(){
        this.setState({
            showAddClientModal: false,
        })
    }
    handleClickAddClient(){
        this.setState({
            showAddClientModal: true
        })
    }
    updateData(loading){
        this.setState({
            currentClient: null
        })
        if(loading)
            this.toggleLoading(true)

        axios.get('/api/client_users'
             + '?username=' + localStorage.getItem("username") + '&secret=' + localStorage.getItem("secret"))
        .then( ({data}) => {
            if(data){
                console.log(data)
                this.setState({
                    sites: data.sites,
                    clients: data.clients
                }, () => {
                    this.toggleLoading(false)
                })
            }
        })
        .catch(() => {
            setTimeout(() => {
                this.updateData()
            }, 10000)
        })
    }
    showClient(client){
        const {searchClient} = this.state
        if(searchClient){
            const search = searchClient.toLocaleLowerCase().replace(/[.*+?^{}()|[\]\\]/g, '\\$&')
            var patt = new RegExp(search)
            if(client.numero_employe && patt.test(client.numero_employe.toLocaleLowerCase()))
                return true
            else if(client.num_emp_soit && patt.test(client.num_emp_soit.toLocaleLowerCase()))
                return true
            else if(client.numero_stagiaire && patt.test(client.numero_stagiaire.toLocaleLowerCase()))
                return true
            else if(client.nom && patt.test(client.nom.toLocaleLowerCase()))
                return true
            else if(client.site && patt.test(client.site.toLocaleLowerCase()))
                return true
            return false
        }
        return true
    }
    updateClient(id, isUpdate){
        this.toggleLoading(true)
        axios.get('/api/client_users/show/' + id
            + '?username=' + localStorage.getItem("username") + '&secret=' + localStorage.getItem("secret"))
        .then(({data}) => {
            if(data){
                let clients = this.state.clients
                for (let i = 0; i < clients.length; i++) {
                    if(clients[i].id == data.client.id){
                        if(isUpdate)
                            clients.splice(i, 1)
                        else 
                            clients[i] = data.client
                        break
                    }
                }
                if(isUpdate){
                    clients.unshift(data.client)
                }

                this.setState({
                    currentClient: data.client,
                    
                    showAddClientModal: false,
                    showEditClientModal: false,
                    showDeleteClientModal: false,
                    activeTab: 'site'
                }, () => {
                    console.log(data.client)
                    this.toggleLoading(false)
                })
            }
        })
        .catch(() => {
            this.toggleLoading(false)
        })
    }
    handleClickClient(id){
        this.updateClient(id)
    }
    getColor(client){
        const {currentDate} = this.state
        if(currentDate){
            if((!client.last_date_pointage || moment(client.last_date_pointage).isBefore(moment(currentDate).subtract(30, 'days')))
            && (!client.created_at || moment(client.created_at).isBefore(moment(currentDate).subtract(30, 'days'))))
                return 'red'
            else if(!client.numero_employe && !client.num_emp_soit && client.date_embauche && moment(client.date_embauche).isBefore(moment(currentDate).subtract(6, 'months')))
            return 'green'
        }
        return ''
    }
    componentDidMount(){
        this.updateData(true)
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
        document.title = "Client - TLS"
    }
    resize() {
        this.setState({
            heightWindow: window.innerHeight,
            widthWindow: window.innerWidth
        });
    }
    render(){
        const {sites, currentDate, clients, currentClient, inputSearch, heightWindow, widthWindow, activeTab, 
            showEditClientMenu, showAddClientModal} = this.state
        const {user} = this.props
        return  (
            <div className="table"  onClick={() => { if(showEditClientMenu) this.toggleEditClientMenu(false)}}>
                <div id="tableContainer">
                    <div className="table">
                        <div className="row-header">
                            <h3 className="h3-table">
                                <div className="cell fix-cell-client">
                                    Clients
                                </div>
                                <div className="cell center">
                                    <input onChange={this.handleChangeSearchClient} value={inputSearch} id="searchClient" type="text"/>
                                </div>
                                <div className="cell right fix-cell-client">
                                    <span id="newClientBtn">
                                        <IconButton onClick={this.handleClickAddClient} label="Nouveau client" src="/img/edit.svg"/>
                                    </span>
                                </div>
                            </h3>
                        </div>
                        <div className="row-table">
                            <table className="fixed_header visible-scroll layout-fixed">
                                <thead>
                                    <tr>
                                        <th className="cellSite">Nom</th>
                                        <th className="cellSiteClient">
                                            Site
                                            <img src="/img/refresh_table.svg" onClick={() => {this.updateData(true)}}/>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody style={{'height': (heightWindow - 160) + "px"}}>
                                    {
                                        clients.map((row) => {
                                            return (
                                                this.showClient(row) ? 
                                                <tr 
                                                    key={row.id}
                                                    onDoubleClick={() => {this.handleClickClient(row.id)}}
                                                    className={(row.soft_delete ? "red" : "") + " " + ((currentClient!= null && currentClient.id == row.id) ? "selected-row" : "")}
                                                >
                                                    <td className="cellSite" title={row.name}>
                                                        {row.name}
                                                    </td>
                                                    <td className="cellSiteClient" title={row.site}>
                                                        {row.site}
                                                    </td>
                                                </tr>
                                                : 
                                                null)
                                        })
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                {showAddClientModal && <EditClientModal
                    action={'/api/client_users/store'}
                    closeModal={this.closeClientModal}
                    updateClient={this.updateClient}
                    updateData={this.updateData}
                    sites={sites}
                />}
                <div className={currentClient ? "box-shadow-left" : ""} style={{width: (widthWindow/2.5) + 'px', maxWidth: (widthWindow/2.5) + 'px', minWidth: (widthWindow/2.5) + 'px'}} id="overviewContainer">
                    {
                        currentClient ?
                        <ClientDetail
                            currentClient={currentClient}
                            currentDate={currentDate}
                            user={user}
                            updateData={this.updateData}
                            updateClient={this.updateClient}
                            getColor={this.getColor}
                            sites={sites}
                            showEditClientMenu={showEditClientMenu}
                            toggleEditClientMenu = {this.toggleEditClientMenu}
                            heightWindow={heightWindow}
                            closeClientModal={this.closeClientModal}
                            activeTab={activeTab}
                            handleChangeTab={this.handleChangeTab}
                            toggleLoading={this.toggleLoading}
                        />
                        :
                        <div className="img-bg-container">
                            <img className="img-bg-overview" src="/img/tls_background.svg"/>
                        </div>
                    }
                </div>
            </div>
        )
    }
}