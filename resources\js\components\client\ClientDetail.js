import React, { Component } from 'react'
import axios from 'axios'
import EditClientModal from './EditClientModal'
import DeleteClientModal from './DeleteClientModal'

import 'react-datepicker/dist/react-datepicker.css'
import Site from './site/Site'

export default class ClientDetail extends Component {
    constructor(props){
        super(props)
        this.state = {
            showEditClientModal: false, 
            showDeleteClientModal: false,
            returnData: null,
            heightWindow: 0,
        }
        this.handleChangeTab = this.handleChangeTab.bind(this)
        this.handleClickDeleteClient = this.handleClickDeleteClient.bind(this)
        this.handleClickEditClient = this.handleClickEditClient.bind(this)
        this.closeClientModal = this.closeClientModal.bind(this)
        this.updateClient = this.updateClient.bind(this)
        this.updateData = this.updateData.bind(this)
    }
    closeClientModal(){
        this.setState({
            showEditClientModal: false,
            showDeleteClientModal: false,
        })
    }
    updateData(){
        this.props.updateData(true)
    }
    updateClient(id){
        this.closeClientModal()
        this.props.updateClient(id, true)
    }
    handleClickEditClient(){
        this.setState({
            showEditClientModal: true,
            showEditClientMenu: false
        })
    }
    handleClickDeleteClient(){
        this.setState({
            showDeleteClientModal: true,
            showEditClientMenu: false
        })
    }
    handleChangeTab(event){
        if(event.target.id != this.props.activeTab){
            this.props.handleChangeTab(event.target.id)
        }
    }
    componentDidMount(){
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
    }
    resize() {
        this.setState({
            heightWindow: window.innerHeight,
        });
    }
    render(){
        const {showEditClientModal, showDeleteClientModal, heightWindow} = this.state
        const {activeTab, user, showEditClientMenu, currentClient, fonctions, agences} = this.props
        return ( 
            <div>
                {
                    showEditClientModal && 
                    <EditClientModal
                        action={'/api/client_users/update/' + currentClient.id}
                        closeModal={this.closeClientModal}
                        updateClient={this.updateClient}
                        fonctions={fonctions}
                        client={currentClient}
                        agences={agences}/>
                }
                {
                    showDeleteClientModal && 
                    <DeleteClientModal
                        action={'/api/client_users/delete/' + currentClient.id}
                        closeModal={this.closeClientModal}
                        updateData={this.updateData}
                        nom={'#' + ("000"+ currentClient.id).slice(-3) + ' ' + currentClient.site}/>
                }
                <div className="overview-container">
                    <div className="head-title-overview" title={currentClient.nom}>
                        <div style={{height:"40px", lineHeight:"40px" }}>
                            <div className="title-overview">
                                <span className={currentClient.soft_delete ? "red" : ""} style={{opacity: .9}}>
                                    {currentClient.name}
                                </span>
                            </div>
                            <div className="overview-edit-icon">
                                { 
                                    ['root', 'room'].includes(user.role) && 
                                    <img onClick={() => {this.props.toggleEditClientMenu(!showEditClientMenu)}} className="overview-edit-img" src="/img/parametre.svg"/>
                                }    
                                {
                                    showEditClientMenu &&
                                    <div className="dropdown-overview-edit">
                                        <span onClick={this.handleClickEditClient}>Modifier le client</span>
                                        <span onClick={this.handleClickDeleteClient}>Supprimer le client</span>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                    <span>
                        <b>Email : </b> {currentClient.email}
                    </span><br/>
                    <span className="overview-break-overflow" title={currentClient.site}>
                        <b>Site par default: </b> {currentClient.site}
                    </span><br/>
                </div>
                <div style={{position: 'relative', top: '2px'}}>
                    <div className="table">
                        <div className="cell">
                            <div id="tabHeaderOverview">
                                <ul>
                                    <li id="site" className={activeTab == 'site' ? "active-tab" : ""} onClick={this.handleChangeTab}>Site</li>
                                    <li id="operation" className={activeTab == 'operation' ? "active-tab" : ""} onClick={this.handleChangeTab}>Traçabilité</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="tabContentOverview">
                    <div id="tabContainer">
                        {
                            activeTab == 'site' &&
                            <Site data="site" 
                                sites={currentClient.sites} 
                                client_id={currentClient.id} 
                                heightWindow={heightWindow - 390}
                                updateClient={() => this.updateClient(currentClient.id)}/>
                        }
                    </div>
                </div>
            </div>
        )
    }
}