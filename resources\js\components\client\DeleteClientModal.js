import React, { Component } from 'react'
import Modal from '../modal/Modal'
import axios from 'axios'

export default class DeleteClientModal extends Component {
    constructor(props){
        super(props)
        this.state = {
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }
    handleSave(){
        const data = new FormData()
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        axios.post(this.props.action, data)
        .then(({data}) => {
            if(data){
                this.props.updateData(true)
                this.props.closeModal()
                this.props.setSelectedClient(null)
            }
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        return (
            <Modal confirm={true} handleSave={this.handleSave} handleCancel={this.handleCancel}>
                <div>
                    <h3>Suppression de l'agent</h3>
                    <div>{this.props.nom}</div>
                    <hr/>
                </div>
            </Modal>)
    }
}