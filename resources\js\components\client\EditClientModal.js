import React, { Component } from 'react'
import axios from 'axios'

import './client.css'
import Modal from '../modal/Modal'
import SiteModal from './site/SiteModal'

export default class EditClientModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            isUpdate: false,
            name: '',
            email: '',
            error: '',
            site: null
        }
        this.handleEmailChange = this.handleEmailChange.bind(this)
        this.handleSiteChange = this.handleSiteChange.bind(this)
        this.handleNameChange = this.handleNameChange.bind(this)
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.toggleSiteList = this.toggleSiteList.bind(this)
    }
    componentDidMount(){
        const {client} = this.props
        if(client){
            this.setState({
                isUpdate: true,
                email: client.email,
                name: client.name,
                site: {
                    idsite: client.site_id,
                    nom: client.site
                }
            })
        }
    }
    toggleSiteList(value){
        this.setState({
            showPromList: value
        })
    }
    handleEmailChange(e){
        this.setState({
            email: e.target.value
        })
    }
    handleSiteChange(value){
        this.setState({
            site: value,
            showPromList:false
        })
    }
    handleNameChange(event){
        this.setState({
            name: event.target.value
        })
    }
    handleSave(){
        const {email, name, site} = this.state
        const {updateClient, action} = this.props
        let data = new FormData()
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        if(site)
            data.append("site_id", site.idsite)
        data.append("email", email)
        data.append("name", name)
        this.setState({
            error: ''
        })
        axios.post(action, data)
        .then(({data}) => {
            console.log(data)
            if(data.error)
                this.setState({
                    error: data.error[Object.keys(data.error)[0]][0]
                })
            else {
                updateClient(data, true)
            }
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        const {email, name, site, error, showPromList} = this.state
        return (
            <div>
                <Modal handleSave={this.handleSave} handleCancel={this.handleCancel}>
                    <h3>Client</h3>
                    <div className="input-container">
                        <label>Nom *</label>
                        <input value={name} onChange={this.handleNameChange} type="text"/>
                    </div>
                    <div className="input-container">
                        <label>Email *</label>
                        <input value={email} onChange={this.handleEmailChange} type="text"/>
                    </div>
                    <div className="input-container">
                        <label>Site</label>
                        <div id="promInputTable" className="table">
                            <span className="cell">{site && site.nom}</span>
                            {
                                <span id="cellProm" onClick={() => {this.toggleSiteList(true)}}>
                                    <img id="clientImg" src="/img/site.svg"/>
                                </span>
                            }
                        </div>
                    </div>
                    {
                        error && 
                        <div className="pink">{error}</div>
                    }
                </Modal>
                {
                    showPromList && 
                    <SiteModal
                        defaultSite={site}
                        closeModal={() => {this.toggleSiteList(false)}}
                        changeSite={this.handleSiteChange}/>
                }
            </div>
        )
    }
}