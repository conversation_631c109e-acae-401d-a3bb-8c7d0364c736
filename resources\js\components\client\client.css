tr.confirmed > :first-child{
    background-color: #336666;
}
.cellNum{
    width: 110px;
    max-width: 110px;
    min-width: 110px;
    overflow: hidden;
}
#searchClient{
    box-sizing: border-box; 
    width: 250px;
    padding: 10px;
    vertical-align: middle;
    text-align: center;
    border: solid .5px rgba(0, 0, 0, .1);
}
#newClientBtn{
    vertical-align: middle;
    font-size: 13pt;
    font-weight: normal;
}
.fix-cell-client{
    width: 200px;
    min-width: 200px;
    max-width: 200px;
}
.cellSite{
    width: 400px;
    max-width: 400px;
    min-width: 400px;
    overflow: hidden;
}
#rowHeaderClient{
    display: table-row;
    height: 50px;
}
#filterClient{
    width: 150px;
}
#clientOverviewEditImg{
    padding: 5px;
    width: 30px;
    vertical-align: middle;
    box-shadow: 1px 1px 1px rgba(24, 42, 42, .1);
}
/*#clientOverviewEditImg:hover{
    background-color: rgba(24, 42, 42, .1);
}*/
ul#agenceListContainer{
    width:100%;
    background-color: white;
    z-index: 2000;
}
.cellSiteClient > img {
    width: 15px;
    float: right;
    cursor: pointer;
}