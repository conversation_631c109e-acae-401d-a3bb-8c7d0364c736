import React, { Component } from 'react'
import Modal from '../../modal/Modal'
import axios from 'axios'

export default class DeleteUserSiteModal extends Component {
    constructor(props){
        super(props)
        this.state = {
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }
    handleSave(){
        const data = new FormData()
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        axios.post(this.props.action, data)
        .then(({data}) => {
            this.props.updateClient()
            this.props.closeModal()
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        return (
            <Modal confirm={true} handleSave={this.handleSave} handleCancel={this.handleCancel}>
                <div>
                    <h3>Supprimer l'accès à ce site pour ce client</h3>
                    <hr/>
                </div>
            </Modal>)
    }
}