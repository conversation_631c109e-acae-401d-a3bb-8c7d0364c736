import React, { Component } from 'react'

import DeleteUserSiteModal from './DeleteUserSiteModal'
import SiteModal from './SiteModal'
import IconButton from '../../button/IconButton'

export default class Site extends Component {
    constructor(props){
        super(props)
        this.state = {
            user_site: null,
            showAddModal: false,
            showDeleteModal: false,
            widthPx: ''
        }
        this.closeModal = this.closeModal.bind(this)
        this.addModal = this.addModal.bind(this)
        this.updateData = this.updateData.bind(this)
    }
    closeModal(){
        this.setState({
            showAddModal: false,
            showDeleteModal: false,
        })
    }
    addModal(){
        this.setState({
            showAddModal: true
        })
    }
    deleteModal(us){
        this.setState({
            user_site: us,
            showDeleteModal: true
        })
    }
    updateData(){
        this.props.updateCurrentSite()
        this.closeModal()
    }
    componentDidMount() {
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
    }
    resize() {
        if(this.container)
            this.setState({
                widthPx : (this.container.offsetWidth - 100) + "px"
            })
    }
    render(){
        const {heightWindow, sites, archive, client_id} = this.props
        const {user_site, showAddModal, showDeleteModal, widthPx} = this.state
        return (
            <div ref={el => (this.container = el)}>

                {
                    (user_site && showDeleteModal) && <DeleteUserSiteModal 
                        action={"/api/client_users/remove_site/"+ user_site.id}
                        closeModal={this.closeModal}
                        updateClient={this.props.updateClient}/>
                }
                {
                    showAddModal && <SiteModal
                        currentSites={sites}
                        action={"/api/client_users/add_site/" + client_id}
                        updateClient={this.props.updateClient}
                        closeModal={this.closeModal}/>
                }

                { 
                    !archive && 
                    <div className="btn-label-container right">
                        <IconButton onClick={this.addModal} label="Attacher un site" src="/img/add.svg"/>
                    </div>
                }
                {
                    widthPx && 
                    <table className="fixed_header default layout-fixed">
                        <thead>
                            <tr>
                                <th style={{width: widthPx, minWidth: widthPx, maxWidth: widthPx}}>Site</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody style={{height: heightWindow + "px"}}>
                            {
                                sites && sites.map((row) => {
                                    return (
                                        <tr
                                            key={row.idsite}
                                        >
                                            <td title={row.nom} style={{width: widthPx, minWidth: widthPx, maxWidth: widthPx}}>
                                                {row.nom}
                                            </td>
                                            <td>
                                                <img 
                                                    onClick={() => {this.deleteModal(row)}} 
                                                    className="img-btn img-btn-margin" 
                                                    title="Supprimer" 
                                                    src="/img/delete.svg"/>
                                            </td>
                                        </tr>)
                                })
                            }
                        </tbody>
                    </table>
                }
            </div>
        )
    } 
}