import React, { Component } from 'react'
import axios from 'axios'

import Modal from '../modal/Modal'

import 'react-datepicker/dist/react-datepicker.css'

export default class ConfirmHourModal extends Component {
    constructor(props){
        super(props)
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }
    handleSave(){
        const data = new FormData()
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        axios.post(this.props.action, data)
        .then(({data}) => {
            if(data){
                this.props.updateConfirm()
                this.props.closeModal()
            }
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        const {hour, plageDate} = this.props
        return ( 
            <Modal  
                width="md" 
                handleSave={this.handleSave} 
                handleCancel={this.handleCancel}
            >
                {
                    hour &&
                    <div>
                        <h3>Confirmation de la total d'heure</h3>
                        <h2>
                            {hour.heure_trav} H
                        </h2> 
                        <b>Matricule: </b> 
                        {
                            hour.societe_id == 1 ? 'DGM-' + hour.agent.numero_employe :
                            hour.societe_id == 2 ? 'SOIT-' + hour.agent.num_emp_soit :
                            hour.societe_id == 3 ? 'ST-' + hour.agent.numero_stagiaire :
                            hour.societe_id == 4 ? 'SM' :
                            hour.agent.numero_employe ? hour.agent.numero_employe :
                            hour.agent.numero_stagiaire ? hour.agent.numero_stagiaire :
                            <span className="purple">Ndf</span>
                        } <br/>
                        <b>Nom: </b> {hour.agent.nom} <br/>
                        <b>{plageDate}</b>
                    </div>
                }
            </Modal>
        )
    }
}