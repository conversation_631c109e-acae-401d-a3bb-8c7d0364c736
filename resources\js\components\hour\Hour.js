import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'
import SelectGroupSite from './select/SelectGroupSite'

import './hour.css'
import DataTable from './dataTable/DataTable'

export default class Hour extends Component {
    constructor(props){
        super(props)
        this.state = {
            group: null,
            month: null,
            year: null,
            etatHours: [],
            fonctions: [],
            agences: [],
            beginDate: null,
            endDate: null,
            selectedGroup: null,
            selectedMonth: null,
            selectedYear: null,
            currentEtatHour: null,
            enableConfirm: false,
        }
        this.toggleLoading = this.toggleLoading.bind(this)
        this.hideGroupPointage = this.hideGroupPointage.bind(this)
        this.clickItem = this.clickItem.bind(this)
        this.toggleSelectItem = this.toggleSelectItem.bind(this)
        this.handleClickGroup = this.handleClickGroup.bind(this)
        this.handleClickMonth = this.handleClickMonth.bind(this)
        this.handleClickYear = this.handleClickYear.bind(this)
        this.updateData = this.updateData.bind(this)
        this.updateHour = this.updateHour.bind(this)
        this.updateConfirm = this.updateConfirm.bind(this)
        this.closeModal = this.closeModal.bind(this)
        this.handleDoubleClickAgent = this.handleDoubleClickAgent.bind(this)
    }
    updateConfirm(){
        let {etatHours, currentEtatHour} = this.state
        etatHours.map((ep) => {
            if(ep.agent_id == currentEtatHour.agent_id)
                ep.confirm = true
        })
        this.setState({
            etatHours: etatHours,
            currentEtatHour: null,
        })
    }
    closeModal(){
        this.setState({
            currentEtatHour: null
        })
    }
    handleDoubleClickAgent(ep){
        this.setState({
            currentEtatHour: ep
        })
    }
    toggleSelectItem(){
        this.setState({
            currentEtatHour: null,
            showSelectItem: !this.state.showSelectItem
        })
    }
    hideGroupPointage(){
        const {selectedGroup, selectedMonth, selectedYear} = this.state 
        this.setState({
            showSelectItem: false,
            group: selectedGroup,
            month: selectedMonth,
            year: selectedYear,
        })
    }
    toggleLoading(load){
        this.props.toggleLoading(load)

    }
    clickItem(type, value){
        console.log(type)
        this.setState({
            currentItemType: type,
            currentItem: value,
            searchSite: '',
            filterStatus: '',
            showSelectItem: false
        })
    }
    setEtatHour(ep){
        ep.heure_dim = 0
        ep.heure_nuit = 0
        ep.heure_ferie = 0
        ep.heure_reclam = 0
        if(ep.hour && ep.hour.confirm)
            ep.societe_id = ep.hour.societe_id
        else
            ep.societe_id = ep.agent.societe_id
        if(ep.hour && ep.hour.confirm_hour){
            ep.confirm = true
            ep.nb_heure_contrat = ep.hour.nb_heure_contrat
            ep.site_id = ep.hour.site_id
            ep.site = ep.hour.site
            ep.heure_trav = ep.hour.heure_trav
            ep.heure_reclam = ep.hour.heure_reclam
            ep.sal_forfait = ep.hour.sal_forfait
            if(!ep.hour.sal_forfait){
                ep.heure_dim = ep.hour.heure_dim
                ep.heure_nuit = ep.hour.heure_nuit
                ep.heure_ferie = ep.hour.heure_ferie
            }
        }
        else{
            ep.confirm = false
            ep.nb_heure_contrat = ep.agent.nb_heure_contrat
            ep.site_id = ep.agent.site_id
            ep.site = ep.agent.site
            ep.heure_trav = ep.agent.heure_trav
            ep.heure_reclam = ep.agent.heure_reclam
            ep.sal_forfait = ep.agent.sal_forfait
            if(!ep.agent.sal_forfait){
                ep.heure_dim = ep.agent.heure_dim
                ep.heure_nuit = ep.agent.heure_nuit
                ep.heure_ferie = ep.agent.heure_ferie
            }
        }
        ep.numSort = 
            ep.societe_id == 1 ? '1-' + ('0000' + ep.agent.numero_employe).slice(-5) :
            ep.societe_id == 2 ? '2-' + ('0000' + ep.agent.num_emp_soit).slice(-5) : 
            ep.societe_id == 3 ? '3-' + ('0000' + ep.agent.numero_stagiaire).slice(-5) :
            ep.societe_id == 4 ? '4-SM' :
            ep.agent.numero_employe ? '5-' + ('0000' + ep.agent.numero_employe).slice(-5) :
            ep.agent.numero_stagiaire ? '5-' + ('0000' + ep.agent.numero_stagiaire).slice(-5) :
            '7-Ndf'
        ep.diffHCHT = ep.heure_trav - ep.nb_heure_contrat
        return ep
    }
    updateHour(id){
        const {group, year, month} = this.state
        this.toggleLoading(true)
        const url = '/api/hours/show/' + id + '/' + year + '/' + month
        + '?username=' + localStorage.getItem("username") + '&secret=' + localStorage.getItem("secret")
        axios.get(url)
        .then(({data}) => {
            if(data){
                let currentEtatHour = {}
                let etatHours = this.state.etatHours
                let {pointages, reclamations, jour_feries} = data
                for (let i = 0; i < etatHours.length; i++) {
                    if(etatHours[i].agent_id == data.agent.id){
                        if((data.hour && data.hour.confirm_hour && group != data.hour.group_id) 
                         || (!data.hour && group != data.agent.group_id)){
                            etatHours.splice(i, 1)
                            this.setState({
                                etatHours: etatHours,
                                currentEtatHour: null,
                            }, () => {
                                this.toggleLoading(false)
                            })
                        }
                        else{
                            currentEtatHour.reclamable = data.reclamable
                            currentEtatHour.isAfterConfirmable = data.is_after_confirmable
                            currentEtatHour.beginDate = data.begin_date
                            currentEtatHour.endDate = data.end_date
                            currentEtatHour.agent_id = data.agent.id
                            currentEtatHour.agent = data.agent
                            currentEtatHour.pointages = data.pointages
                            currentEtatHour.reclamations = data.reclamations
                            if(data.hour) {
                                currentEtatHour.hour_id = data.hour.id
                                currentEtatHour.hour = data.hour
                            }
                            if(!data.hour || (data.hour && !data.hour.confirm_hour)){
                                currentEtatHour.agent.heure_trav = pointages.length * 12
                                currentEtatHour.agent.heure_reclam = reclamations.length * 12
                                currentEtatHour.agent.heure_nuit = 0
                                currentEtatHour.agent.heure_dim = 0
                                currentEtatHour.agent.heure_ferie = 0
                                for(let j=0; j<pointages.length; j++){
                                    if(moment(pointages[j].date_pointage).format('HH:mm:ss') == '18:00:00')
                                        currentEtatHour.agent.heure_nuit += 12
                                    if(moment(pointages[j].date_pointage).day() == 0)
                                        currentEtatHour.agent.heure_dim += 12
                                    jour_feries.forEach(jr => {
                                        if(moment(pointages[j].date_pointage).format('YYYY-MM-DD') == jr)
                                            currentEtatHour.agent.heure_ferie += 12
                                    })
                                }
                            }
                            currentEtatHour = this.setEtatHour(currentEtatHour)
                            etatHours[i] = currentEtatHour
                            this.setState({
                                etatHours: etatHours,
                                currentEtatHour: currentEtatHour,
                            }, () => {
                                this.toggleLoading(false)
                            })
                        }
                        
                        break;
                    }
                }
            }
        })
        .catch(() => {
            this.toggleLoading(false)
        })
    }
    updateData(){
        const {group, month, year} = this.state
        console.log("updateData, group: " + group + ", month: " + month + " ,year: " + year)
        this.setState({
            currentEtatHour: null,
        })
        this.props.toggleLoading(true)
        axios.get('/api/hours?group=' + group + '&month=' + month + '&year=' + year).then(({data}) => {
            if(data){
                if(data.message){
                    this.setState({
                            message: data.message,
                            group: data.group,
                            month: data.month,
                            year: data.year,
                            selectedGroup: data.group,
                            selectedMonth: data.month,
                            selectedYear: data.year,
                            etatHours: [],
                            beginDate: data.begin_date,
                            endDate: data.end_date,
                        },
                        () => {
                            this.props.toggleLoading(false)
                        }
                    )
                }
                else {
                    let etatHours = []
                    let pointages = data.pointages;
                    let hours = data.hours
                    let reclamations = data.reclamations
                    for(let i=0; i<data.agents.length; i++){
                        let ep = {
                            confirm: false,
                            agent_id: data.agents[i].id,
                            agent: data.agents[i],
                        }
                        ep.agent.heure_trav = 0
                        ep.agent.heure_nuit = 0
                        ep.agent.heure_dim = 0
                        ep.agent.heure_ferie = 0
                        ep.agent.heure_reclam = 0
                        etatHours.push(ep)
                    }
                    etatHours.map((ep) => {
                        for(let i=0; i<pointages.length; i++){
                            if(pointages[i].agent_id == ep.agent_id){
                                ep.agent.heure_trav += 12
                                if(moment(pointages[i].date_pointage).format('HH:mm:ss') == '18:00:00')
                                    ep.agent.heure_nuit += 12
                                if(moment(pointages[i].date_pointage).day() == 0)
                                    ep.agent.heure_dim += 12
                                data.jour_feries.forEach(jr => {
                                    if(moment(pointages[i].date_pointage).format('YYYY-MM-DD') == jr)
                                        ep.agent.heure_ferie += 12
                                })
                                pointages.splice(i, 1)
                                i-=1
                            }
                        }
                        for(let i=0; i<hours.length; i++){
                            if(hours[i].agent_id == ep.agent_id){
                                ep.hour_id = hours[i].id
                                ep.hour = hours[i]
                                hours.splice(i, 1)
                                i-=1
                            }
                        }
                        for(let i=0; i<reclamations.length; i++){
                            if(reclamations[i].agent_id == ep.agent_id){
                                ep.agent.heure_reclam += 12
                            }
                        }
                    })
                    etatHours.map((ep) => {
                        ep = this.setEtatHour(ep)
                    })
                    etatHours.sort((a, b) => {
                        if(a.numSort < b.numSort) return -1
                        if(a.numSort > b.numSort) return 1
                        return 0
                    })
                    etatHours.sort((a, b) => {
                        if(a.site < b.site) return -1
                        if(a.site > b.site) return 1
                        return 0
                    })
                    this.setState({
                        message: '',
                        group: data.group,
                        month: data.month,
                        year: data.year,
                        selectedGroup: data.group,
                        selectedMonth: data.month,
                        selectedYear: data.year,
                        etatHours: etatHours,
                        fonctions: data.fonctions,
                        agences: data.agences,
                        beginDate: data.begin_date,
                        endDate: data.end_date,
                        enableConfirm: data.enable_confirm,
                    },
                    () => {
                        this.state.etatHours.map((ep) => {
                            if(ep.agent_id == "4921") console.log("always find paskal");
                        })
                        this.props.toggleLoading(false)
                    })
                }
            }
        })
        .catch((e) => {
            setTimeout(() =>{
                this.updateData()
            }, 10000)
        })
    }
    componentDidMount(){
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
        document.title = "Hour - TLS"
        this.updateData()
    }
    resize() {
        this.setState({
            heightWindow: window.innerHeight,
            widthWindow: window.innerWidth
        });
    }
    handleClickGroup(e, value){
        console.log("value click group: " + value)
        e.stopPropagation()
        this.setState({
            group: value
        })
    }
    handleClickMonth(e, m){
        console.log("value click month: " + m)
        e.stopPropagation()
        this.setState({
            month: m
        })
    }
    handleClickYear(e, y){
        console.log("value click year: " + y)
        e.stopPropagation()
        this.setState({
            year: y
        })
    }
    render(){
        const {message, currentEtatHour, etatHours, selectedGroup, group, month, year, beginDate, endDate, currentItem, 
            enableConfirm, fonctions, agences, showSelectItem, heightWindow, widthWindow} = this.state
        const {user} = this.props
        return  (
            <div className="table" onClick={() => {
                    this.closeModal()
                    this.hideGroupPointage()}
                }>
                <div id="tableContainer">
                    <div className="table">
                        <div className="row-header">
                            <h3 className="center">
                                {
                                    group && 
                                    <SelectGroupSite
                                        updateData={this.updateData}
                                        beginDate={beginDate}
                                        endDate={endDate}
                                        selectedGroup={selectedGroup}
                                        group={group}
                                        month={month}
                                        year={year}
                                        handleClickGroup={this.handleClickGroup}
                                        handleClickMonth={this.handleClickMonth}
                                        handleClickYear={this.handleClickYear}
                                        clickItem={this.clickItem}
                                        currentItem={currentItem}
                                        toggleSelect={this.toggleSelectItem}
                                        showItem={showSelectItem}
                                        enableConfirm={enableConfirm}/>
                                }
                            </h3>
                        </div>
                        <div style={{'height': (heightWindow - 200) + "px"}}>
                            {
                                message ?
                                    <h2 className="center secondary">{message}</h2>
                                :
                                    <DataTable 
                                        user={user}
                                        month={month}
                                        year={year}
                                        currentEtatHour={currentEtatHour}
                                        etatHours={etatHours} 
                                        fonctions={fonctions}
                                        agences={agences}
                                        closeModal={this.closeModal}
                                        handleDoubleClickAgent={this.handleDoubleClickAgent}
                                        updateData={this.updateData}
                                        updateHour={this.updateHour}
                                        updateConfirm={this.updateConfirm}
                                        heightWindow={heightWindow} 
                                        widthWindow={widthWindow}
                                        beginDate={beginDate}
                                        endDate={endDate}
                                        enableConfirm={enableConfirm}/>
                            }
                        </div>
                    </div>
                </div>
            </div>
        )
    }
}