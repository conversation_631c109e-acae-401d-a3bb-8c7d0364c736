import React, { Component } from 'react'
import axios from 'axios'
import ConfirmHourModal from './ConfirmHourModal'
import moment from 'moment'
import Site from '../agent/site/Site'
import Pointage from './pointage/Pointage'


export default class HourDetail extends Component {
    constructor(props){
        super(props)
        this.state = {
            activeTab: 'hour',
            showEditSiteModal:false,
            ********************: false,
            showEditAgentModal: false,
            showArchiveAgentModal: false,
            showEditAgentMenu: false
        }
        this.updateHour= this.updateHour.bind(this)
        this.updateData = this.updateData.bind(this)
        this.closeModal = this.closeModal.bind(this)
        this.handleClickArchiveAgent = this.handleClickArchiveAgent.bind(this)
        this.handleClickEditAgent = this.handleClickEditAgent.bind(this)
        this.handleClickConfirmAgent = this.handleClickConfirmAgent.bind(this)
        this.updateConfirm = this.updateConfirm.bind(this)
        this.handleClickEditSite = this.handleClickEditSite.bind(this)
        this.handleChangeTab = this.handleChangeTab.bind(this)
    }
    handleChangeTab(e){
        this.setState({
            activeTab: e.target.id
        })
    }
    handleClickEditSite(){
        this.setState({
            showEditSiteModal: true
        })
    }
    updateConfirm(){
        this.props.updateConfirm()
    }
    updateHour(){
        this.props.updateHour(this.props.currentEtatHour.agent_id)
        this.setState({
            showEditSiteModal: false,
        })
    }
    handleClickConfirmAgent(){
        this.setState({
            ********************: true
        })
    }
    handleClickEditAgent(){
        this.setState({
            showEditAgentModal: true
        })
    }
    handleClickArchiveAgent(){
        this.setState({
            showArchiveAgentModal: true
        })
    }
    closeModal(){
        this.setState({
            showEditAgentModal: false,
            showArchiveAgentModal: false,
            ********************: false,
            showEditSiteModal: false,
        })
    }
    updateData(){
        this.props.updateData()
    }
    toggleEditAgentMenu(e, value){
        e.stopPropagation()
        this.setState({
            showEditAgentMenu: value
        })
    }
    componentDidMount(){
        this.updateHour()
    }
    getPlageDateHour(){
        const {beginDate, endDate} = this.props
        return 'Du ' + moment(beginDate).format("DD MMM YYYY") + ' au ' + moment(endDate).format("DD MMM YYYY")
    }
    render(){
        const {enableConfirm, user, heightWindow, currentEtatHour, month, year} = this.props
        const {showEditSiteModal, showEditAgentMenu, ********************, activeTab} = this.state
        const {date_embauche, date_confirmation, date_conf_soit} = currentEtatHour.agent
        return (
            <div id="hourDetail" style={{height: heightWindow}} onClick={(e) => {
                    e.stopPropagation()
                    this.toggleEditAgentMenu(e, false) 
                }}>
                {
                    showEditSiteModal &&
                    <Site 
                        agent_id={currentEtatHour.agent_id} 
                        defaultSite={{idsite: currentEtatHour.site_id, nom: currentEtatHour.site}} 
                        closeModal={this.closeModal} 
                        updateHour={this.updateHour}/>
                }
                {******************** && <ConfirmHourModal
                    action={'/api/hours/confirm/' + currentEtatHour.agent_id + '/' + year + '/' + month}
                    closeModal={this.closeModal}
                    hour={currentEtatHour}
                    updateConfirm={this.updateConfirm}
                    plageDate={this.getPlageDateHour()}/>
                }
                <div className="overview-container">
                    <div className="head-title-overview" title={currentEtatHour.agent.nom}>
                        <div style={{height:"40px", lineHeight:"40px" }}>
                            <div className="title-overview">
                                <span style={{opacity: .9}}>
                                    {
                                        currentEtatHour.societe_id == 1 ? 'DGM-' + currentEtatHour.agent.numero_employe :
                                        currentEtatHour.societe_id == 2 ? 'SOIT-' + currentEtatHour.agent.num_emp_soit :
                                        currentEtatHour.societe_id == 3 ? 'ST-' + currentEtatHour.agent.numero_stagiaire :
                                        currentEtatHour.societe_id == 4 ? 'SM' :
                                        currentEtatHour.agent.numero_employe ? currentEtatHour.agent.numero_employe :
                                        currentEtatHour.agent.numero_stagiaire ? currentEtatHour.agent.numero_stagiaire :
                                        <span className="purple">Non définie</span>
                                    }
                                </span>
                            </div>
                            <div className="overview-edit-icon">
                                { 
                                    (!currentEtatHour.confirm && ['op', 'root'].includes(user.role)) && 
                                    <img onClick={(e) => {this.toggleEditAgentMenu(e, !showEditAgentMenu)}} className="overview-edit-img" src="/img/parametre.svg"/>
                                }    
                                {
                                    showEditAgentMenu &&
                                    <div className="dropdown-overview-edit">
                                        { (enableConfirm && !currentEtatHour.confirm) && <span onClick={this.handleClickConfirmAgent}>Confirmer la total d'heure</span>}
                                        { (!currentEtatHour.confirm) && <span onClick={this.handleClickEditSite}>Changer le site</span>}
                                        { (!currentEtatHour.confirm) && <span onClick={this.handleClickEditAgent}>Changer le groupe du site</span>}
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                    <span className="overview-break-overflow" title={currentEtatHour.agent.nom}>
                        <b>Nom : </b>{currentEtatHour.agent.nom}
                    </span><br/>
                    <b>Site : </b> {currentEtatHour.site}<br/>
                    <b>Fonction : </b> {currentEtatHour.fonction}<br/>
                    <b>Agence : </b> {currentEtatHour.agent.agence}<br/>
                    <b>Date d'embauhe : </b> 
                    {
                        date_embauche ? moment(date_embauche).format('DD MMM YYYY') : 
                        moment(date_confirmation).isBefore(moment(date_conf_soit)) ? moment(date_confirmation).format('DD MMM YYYY') :
                        moment(date_conf_soit).format('DD MMM YYYY')
                    }
                    <br/>
                </div>
                <div style={{position: 'relative', top: '2px'}}>
                    <div className="table">
                        <div className="cell">
                            <div id="tabHeaderOverview">
                                <ul>
                                    <li id="hour" className={activeTab == 'hour' ? "active-tab" : ""} onClick={this.handleChangeTab}>Heure</li>
                                </ul>
                                {
                                    (!currentEtatHour.isAfterConfirmable || currentEtatHour.reclamable) &&
                                    <ul>
                                        <li id="reclamation" className={activeTab == 'reclamation' ? "active-tab" : ""} onClick={this.handleChangeTab}>Réclamation</li>
                                    </ul>
                                }
                                <ul>
                                    <li id="pointage" className={activeTab == 'pointage' ? "active-tab" : ""} onClick={this.handleChangeTab}>Pointage</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="tabContentOverview">
                    <div id="tabContainer">
                        {
                            activeTab == 'hour' &&
                            <table className="fixed_header default layout-fixed">
                                <tbody style={{maxHeight: (heightWindow - 410) + "px"}}>
                                    <tr>
                                        <td>Nb. heures contrat</td> 
                                        <td className="cellHourDetail">{currentEtatHour.agent.nb_heure_contrat}</td>
                                    </tr>
                                    <tr>
                                        <td>Heure travaillé</td> 
                                        <td className="cellHourDetail">{currentEtatHour.heure_trav}</td>
                                    </tr>
                                    <tr>
                                        <td>Diff. HC/HT</td> 
                                        <td className="cellHourDetail">{currentEtatHour.diffHCHT}</td>
                                    </tr>
                                    <tr>
                                        <td>Heure réclammé</td> 
                                        <td className="cellHourDetail">{currentEtatHour.heure_reclam}</td>
                                    </tr>
                                    {
                                        !currentEtatHour.sal_forfait &&
                                        <tr>
                                            <td>Heure férié</td> 
                                            <td className="cellHourDetail">{currentEtatHour.heure_ferie}</td>
                                        </tr>
                                    }
                                    {
                                        !currentEtatHour.sal_forfait &&
                                        <tr>
                                            <td>Heure dimanche</td> 
                                            <td className="cellHourDetail">{currentEtatHour.heure_dim}</td>
                                        </tr>
                                    }
                                    {
                                        !currentEtatHour.sal_forfait &&
                                        <tr>
                                            <td>Heure nuit</td> 
                                            <td className="cellHourDetail">{currentEtatHour.heure_nuit}</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        }
                        {
                            activeTab == 'pointage' &&
                            <Pointage
                                isAfterConfirmable={currentEtatHour.isAfterConfirmable}
                                agentId={currentEtatHour.agent_id}
                                pointages={currentEtatHour.pointages}
                                heightWindow={heightWindow} 
                                updateHour={this.updateHour}
                                isConfirmed={currentEtatHour.confirm}
                                intervalDate={'Du '
                                     + moment(currentEtatHour.beginDate).format('ddd DD MMM YYYY') + ' au '
                                     + moment(currentEtatHour.endDate).format('ddd DD MMM YYYY')
                                }/>
                        }
                        {
                            activeTab == "reclamation" &&
                            <Pointage 
                                reclamation="1"
                                isAfterConfirmable={currentEtatHour.isAfterConfirmable}
                                reclamable = {currentEtatHour.reclamable}
                                agentId={currentEtatHour.agent_id}
                                pointages={currentEtatHour.reclamations} 
                                heightWindow={heightWindow} 
                                updateHour={this.updateHour}
                                isConfirmed={currentEtatHour.confirm}
                                intervalDate={'Du '
                                     + moment(currentEtatHour.beginDate).subtract(1, 'month').format('ddd DD MMM YYYY') + ' au '
                                     + moment(currentEtatHour.beginDate).subtract(1, 'day').format('ddd DD MMM YYYY')
                                }/>
                        }
                    </div>
                </div>
            </div>
        )
    }
}