import React, { Component } from 'react'
import $ from 'jquery'

import './dataTable.css'
import HourDetail from '../HourDetail';

export default class DataTable extends Component {
    constructor(props){
        super(props)
        this.state = {
            selectedAgentId: 0,
            searchAgent: '',
            searchTimeoutId: 0,
        }
        this.showAgent = this.showAgent.bind(this)
        this.handleChangeSearchAgent = this.handleChangeSearchAgent.bind(this)
        this.handleClickRefresh = this.handleClickRefresh.bind(this)
        this.updateHour = this.updateHour.bind(this)
        this.handleDoubleClickAgent = this.handleDoubleClickAgent.bind(this)
        this.updateConfirm = this.updateConfirm.bind(this)
    }
    updateConfirm(){
        this.props.updateConfirm()
    }
    updateHour(id){
        this.props.updateHour(id)
    }
    handleDoubleClickAgent(a){
        this.props.handleDoubleClickAgent(a)
    }
    handleClickRefresh(){
        this.props.updateData()
        this.setState({
            searchAgent: '',
        })
    }
    handleChangeSearchAgent(e){
        this.setState({
            inputSearch: e.target.value
        }, () => {
            const {searchTimeoutId, inputSearch} = this.state
            if(searchTimeoutId)
                clearTimeout(searchTimeoutId)
            const timeoutId = setTimeout(() => {
                this.setState({
                    searchAgent: inputSearch
                })
            }, 1000)
            this.setState({
                searchTimeoutId: timeoutId
            })
        })
    }
    showAgent(agent){
        const {searchAgent} = this.state
        if(searchAgent){
            const search = searchAgent.toLocaleLowerCase().replace(/[.*+?^{}()|[\]\\]/g, '\\$&')
            let patt = new RegExp(search)
            const matricule = 
                agent.societe_id == 1 ? 'DGM-' + agent.numero_employe :
                agent.societe_id == 2 ? 'SOIT-' + agent.num_emp_soit : 
                agent.societe_id == 3 ? 'ST-' + agent.numero_stagiaire :
                agent.societe_id == 4 ? 'SM' :
                agent.numero_employe ? agent.numero_employe :
                agent.numero_stagiaire ? agent.numero_stagiaire :
                'Ndf'
            if(agent.numero_employe && patt.test(agent.numero_employe.toLocaleLowerCase()))
                return true
            else if(agent.num_emp_soit && patt.test(agent.num_emp_soit.toLocaleLowerCase()))
                return true
            else if(agent.numero_stagiaire && patt.test(agent.numero_stagiaire.toLocaleLowerCase()))
                return true
            else if(agent.societe_id == 4 && patt.test('SM'))
                return true
            else if(!agent.numero_employe && !agent.numero_stagiaire && patt.test('Ndf'))
                return true
            else if(patt.test(matricule))
                return true
            else if(patt.test(agent.site.toLocaleLowerCase()))
                return true
            else if(patt.test(agent.nom.toLocaleLowerCase()))
                return true
            return false
        }
        return true
    }
    numberWithSpace(x) {
        if(x)
            return Number.parseInt(x).toString().replace(/\B(?=(\d{3})+(?!\d))/g, " ")
        return ""
    }
    handleClickAgent(agentId){
        this.setState({
            selectedAgentId: agentId
        })
    }
    componentDidMount(){
        this.$hScroll = $(this.hScroll)
        this.$vScroll = $(this.vScroll)
        this.$hScroll.scroll(() => {
            this.$vScroll.width(this.$hScroll.width() + this.$hScroll.scrollLeft());
        });
    }
    render(){
        const {agences, fonctions, user, currentEtatHour, etatHours, heightWindow, widthWindow, 
            enableConfirm, beginDate, endDate, month, year} = this.props
        const {selectedAgentId, inputSearch} = this.state

        return (
            <div>
                <div>
                    <div className="filterSide">
                        <input className="input-fixed" value="" placeholder="Rechercher" value={inputSearch} onChange={this.handleChangeSearchAgent}/>
                    </div>
                    <div className="filterSide right">
                        <img id="refreshPointageBtn" src="/img/order_desc.svg" onClick={() => this.props.updateData()}/>
                        <img id="refreshPointageBtn" src="/img/refresh.svg" onClick={this.handleClickRefresh}/>
                    </div>
                </div>
                {
                    currentEtatHour &&
                    <HourDetail
                        month={month}
                        year={year}
                        beginDate={beginDate}
                        endDate={endDate}
                        user={user}
                        agences={agences}
                        fonctions={fonctions}
                        updateData={this.handleClickRefresh}
                        updateHour={this.updateHour}
                        updateConfirm={this.updateConfirm}
                        currentEtatHour={currentEtatHour}
                        heightWindow={heightWindow} 
                        widthWindow={widthWindow}
                        enableConfirm={enableConfirm}/>
                }
                <div className="table-container-relative" style={{
                        maxHeight: (heightWindow - 180),
                        width: (widthWindow - 290) + "px"
                    }}>
                    <table className="fixed-column default">
                        <thead>
                            <tr>
                                <th className="cellConfirm"></th>
                                <th className="cellNum">Num.</th>
                                <th className="cellNom">Nom</th>
                                <th className="cellNom">Site</th>
                                <th className="cellHTrav center">H trav.</th>
                                <th className="cellHS center">H Réclam.</th>
                                <th className="cellHS center">H Ferié</th>
                                <th className="cellHS center">H Dim.</th>
                                <th className="cellHS center">H Nuit</th>
                            </tr>
                        </thead>
                        <tbody>
                            {
                                etatHours &&
                                etatHours.map((ep) => {
                                    if(this.showAgent(ep.agent))
                                        return <tr 
                                                className={(selectedAgentId == ep.agent_id ? 'selected-row' : '') + (ep.confirm ? ' confirmed': '')} 
                                                key={ep.agent_id} 
                                                onClick={() => this.handleClickAgent(ep.agent_id)} 
                                                onDoubleClick={() => this.handleDoubleClickAgent(ep)}
                                            >
                                            <td className="cellConfirm">
                                                { ep.confirm && <span className="checkmark-label"></span> }
                                            </td>
                                            <td className="cellNum">
                                                {
                                                    ep.societe_id == 1 ? 'DGM-' + ep.agent.numero_employe :
                                                    ep.societe_id == 2 ? 'SOIT-' + ep.agent.num_emp_soit : 
                                                    ep.societe_id == 3 ? 'ST-' + ep.agent.numero_stagiaire :
                                                    ep.societe_id == 4 ? 'SM' :
                                                    ep.agent.numero_employe ? ep.agent.numero_employe :
                                                    ep.agent.numero_stagiaire ? ep.agent.numero_stagiaire :
                                                    <span className="purple">Ndf</span>
                                                }
                                            </td>
                                            <td className="cellNom">{ep.agent.nom}</td>
                                            <td className="cellNom">{ep.site}</td>
                                            <td className="cellHTrav">{ep.heure_trav}</td>
                                            <td className="cellHS">{ep.heure_reclam}</td>
                                            <td className="cellHS">{ep.heure_ferie}</td>
                                            <td className="cellHS">{ep.heure_dim}</td>
                                            <td className="cellHS">{ep.heure_nuit}</td>
                                        </tr>
                                })
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        )
    }
}