import React, { Component } from 'react'
import axios from 'axios'
import DatePicker  from 'react-datepicker'

import Modal from '../../modal/Modal'
import Site from '../../agent/site/Site'
import moment from 'moment'

export default class EditPointageModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            showSite: false,
            site: null,
            date_pointage: null,
            horaire: '',
            error: null,
            disableSave: false,
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.handleChangeSite = this.handleChangeSite.bind(this)
        this.handleChangeDatePointage = this.handleChangeDatePointage.bind(this)
        this.handleChangeHoraire = this.handleChangeHoraire.bind(this)
    }
    handleChangeHoraire(e){
        this.setState({
            horaire: e.target.value
        })
    }
    handleChangeSite(site){
        this.setState({
            site: site,
            showSite: false
        })
    }
    handleChangeDatePointage(date){
        this.setState({
            date_pointage: date
        })
    }
    changeContact(contact){
        this.setState({
            contact: contact,
            showContact: false
        })
    }
    handleChangeQuality(event){
        this.setState({
            quality: event.target.value
        })
    }
    handleChangePassword(event){
        this.setState({
            password: event.target.value
        })
    }
    handleChangeOrdre(event){
        this.setState({
            ordre: event.target.value
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    componentDidMount(){
        const {currentPointage, reclamation, agentId, isAfterConfirmable} = this.props
        if(currentPointage){
            this.setState({
                site: {
                    idsite: currentPointage.idsite,
                    nom: currentPointage.site
                },
                date_pointage: moment(currentPointage.date_pointage).toDate(),
                horaire: moment(currentPointage.date_pointage).format("HH:mm:ss") == "07:00:00" ? "j" : "n",
            })
        }
        else{
            const url = '/api/agents/get_site/'+ agentId 
                + (reclamation ? '?reclamation=1' : '')
                + ((reclamation && isAfterConfirmable) ? '&' : '')
                + ((!reclamation && isAfterConfirmable) ? '?' : '')
                + (isAfterConfirmable ? 'is_after_confirmable=1': '')
            axios.get(url)
            .then(({data}) => {
                this.setState({
                    site: data.site,
                    date_pointage: moment(data.default_date).toDate()
                })
            })
        }
    }
    toggleSite(value){
        this.setState({
            showSite: value
        })
    }
    handleSave(){
        this.setState({
            error: null,
            disableSave: true
        })
        const {reclamation, isAfterConfirmable} = this.props
        const {site, date_pointage, horaire} = this.state
        let data = new FormData()
        data.append("agent_id", this.props.agentId)
        if(site && date_pointage && horaire){
            if(reclamation)
                data.append("reclamation", reclamation)
            if(isAfterConfirmable)
                data.append("is_after_confirmable", isAfterConfirmable)
            data.append("site_id", site.idsite)
            data.append("date_pointage", moment(date_pointage).format('YYYY-MM-DD'))
            data.append("horaire", horaire)
            data.append("username", localStorage.getItem("username"))
            data.append("secret", localStorage.getItem("secret"))
            axios.post(this.props.action, data)
            .then(({data}) => {
                this.setState({
                    disableSave: false
                })
                if(data.message){
                    if(data.message == "already_exist")
                        this.setState({
                            error:{
                                key: "",
                                value: "Ce pointage existe déjà."
                            }
                        })
                    else if(data.message == "out_of_interval"){
                        this.setState({
                            error:{
                                key: "",
                                value: "Ce date est en dehors du pointage."
                            }
                        })
                    }
                }
                else if(data){
                    this.props.updateHour()
                    this.props.closeModal()
                }
            })
            .catch(() => {
                this.setState({
                    disableSave: false
                })
            })
        }
        else if(!site){
            this.setState({
                error: {
                    key: 'site',
                    value: 'Champ site requis'
                }
            })
        }
        else if(!date_pointage){
            this.setState({
                error: {
                    key: 'date_pointage',
                    value: 'Champ date requis'
                }
            })
        }
        else if(!horaire){
            this.setState({
                error: {
                    key: 'horaire',
                    value: 'Champ horaire requis'
                }
            })
        }
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        const {reclamation, currentPointage} = this.props
        const {showSite, site, date_pointage, horaire, error, disableSave} = this.state
        return (
            <div>
                <Modal disableSave={disableSave} handleSave={this.handleSave} handleCancel={this.handleCancel}>
                    <h3> {reclamation ? 'Réclamation' : 'Pointage'} </h3>
                    <div className="input-container">
                        <label className={(error && error.key == 'site') ? 'pink' : ''}>Site *</label>
                        <div className="table">
                            <div className="cell">
                                <input 
                                    disabled={true} 
                                    value={site ? site.nom : ''}/>
                            </div>
                            <div id="cellClientBtn" onClick={()=>{this.toggleSite(true)}}>
                                <img id="clientImg" src="/img/site.svg"/>
                            </div>
                        </div>
                    </div>
                    <div className="input-container">
                        <label className={(error && error.key == 'date_pointage') ? 'pink' : ''}>Date *</label>
                        <DatePicker
                            className="datepicker" 
                            dateFormat="dd-MM-yyyy" 
                            selected={date_pointage} 
                            onChange={this.handleChangeDatePointage}/>
                    </div>
                    <div className="input-container">
                        <label className={(error && error.key == 'horaire') ? 'pink' : ''}>Horaire *</label>
                        <select value={horaire} onChange={this.handleChangeHoraire}>
                            <option></option>
                            <option value="j">Jour</option>
                            <option value="n">Nuit</option>
                            {!currentPointage && <option value="jn">Jour et nuit</option>}
                        </select>
                    </div>
                    { error && <div className="pink">{error.value}</div> }
                </Modal>
                {
                    showSite &&
                    <Site defaultSite={site} closeModal={()=>{this.toggleSite(false)}} changeSite={this.handleChangeSite}/>
                }
            </div>
        )
    }
}