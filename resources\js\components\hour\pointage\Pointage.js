import React, { Component } from 'react'
import moment from 'moment'

import './pointage.css'
import LoadingData from '../../loading/LoadingData'
import IconButton from '../../button/IconButton'
import EditPointageModal from './EditPointageModal'
import DeletePointageModal from './DeletePointageModal'
import RestorePointageModal from './RestorePointageModal'

export default class Pointage extends Component {
    constructor(props){
        super(props)
        this.state = {
            showAddPointage: false,
            showEditPointage: false,
            showDeletePointage: false,
            currentPointage: null,
            editableList: false,
            loading: false,
            widthPx: 0
        }
        this.updateHour = this.updateHour.bind(this)
        this.closeModal = this.closeModal.bind(this)
        this.addModal = this.addModal.bind(this)
        this.editModal = this.editModal.bind(this)
        this.deleteModal = this.deleteModal.bind(this)
    }
    deleteModal(row){
        this.setState({
            showDeletePointage: true,
            currentPointage: row
        })
    }
    editModal(row){
        this.setState({
            showEditPointage: true,
            currentPointage: row
        })
    }
    updateHour(){
        this.props.updateHour()
    }
    addModal(){
        this.setState({
            showAddPointage: true
        })

    }
    closeModal(){
        this.setState({
            showAddPointage: false,
            showEditPointage: false,
            showDeletePointage: false,
        })
    }
    componentDidMount(){
        const {reclamable, isAfterConfirmable, reclamation, pointages} = this.props
        console.log("reclamation: " + reclamation)
        console.log("isAfterConfirmable: " + isAfterConfirmable)
        console.log("reclamable: " + reclamable)
        window.addEventListener("resize", this.resize.bind(this))
        console.log(pointages)
        this.resize()
    }
    resize() {
        if(this.container)
            this.setState({
                widthPx : (this.container.offsetWidth - 340) + "px"
            })
    }
    render(){
        const {showAddPointage, showEditPointage, showDeletePointage, errorMessage, widthPx, loading, currentPointage} = this.state
        const {intervalDate, pointages, heightWindow, agentId, reclamation, isAfterConfirmable, reclamable, isConfirmed} = this.props
        return (
            <div ref={el => (this.container = el)}>
                {
                    showAddPointage &&
                    <EditPointageModal
                        action={"/api/hours/add_pointage"}
                        reclamation={reclamation}
                        isAfterConfirmable={isAfterConfirmable}
                        reclamable={reclamable}
                        closeModal={this.closeModal}
                        agentId={agentId}
                        updateHour={this.updateHour}/>
                }
                {
                    (showEditPointage && currentPointage) &&
                    <EditPointageModal
                        action={"/api/hours/update_pointage/" + currentPointage.id}
                        reclamation={reclamation}
                        closeModal={this.closeModal}
                        agentId={agentId}
                        currentPointage={currentPointage}
                        updateHour={this.updateHour}/>
                }
                {
                    (showDeletePointage && currentPointage) &&
                    <DeletePointageModal
                        action={"/api/hours/delete_pointage/" + currentPointage.id}
                        reclamation={reclamation}
                        isAfterConfirmable={isAfterConfirmable}
                        closeModal={this.closeModal}
                        agentId={agentId}
                        pointage={currentPointage}
                        updateHour={this.updateHour}/>
                }
                {
                    (false) &&
                    <RestorePointageModal
                        action={"/api/hours/restore_pointage/" + currentPointage.id}
                        reclamation={reclamation}
                        isAfterConfirmable={isAfterConfirmable}
                        closeModal={this.closeModal}
                        agentId={agentId}
                        pointage={currentPointage}
                        updateHour={this.updateHour}/>
                }
                {
                    loading ?
                        <LoadingData/>
                    :
                    errorMessage ?
                        <h3 className="default center">{errorMessage}</h3>
                    :
                    <div>
                        <div className="btn-label-container">
                            <div className="table">
                                <div className="cell">
                                    <b>{pointages && pointages.length * 12 + "H"}</b> 
                                    <span className="secondary"> ({intervalDate}) </span>
                                </div>
                                <div className="cell right">
                                    { !isConfirmed && <IconButton onClick={this.addModal} label={reclamation ? "Ajouter une réclamation": "Ajouter un pointage"} src="/img/add.svg"/> }
                                </div>
                            </div>
                        </div>
                        <table className="fixed_header default layout-fixed">
                            <thead>
                                <tr>
                                    <th className="cellDatePointage">Date</th>
                                    <th className="cellHoraire">Horaire</th>
                                    <th style={{width: widthPx, minWidth: widthPx, maxWidth: widthPx}}>Site</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody style={{maxHeight: (heightWindow - 450) + "px"}}>
                                {
                                    pointages && pointages.map((row) =>{
                                        return (
                                            <tr key={row.id} onDoubleClick={() => {this.handleClickRow(row)}}
                                                className={row.soft_delete ? 'pink' : ''}>
                                                <td className="cellDatePointage">
                                                    {
                                                        moment(row.date_pointage).format('ddd DD MMM YY').substr(0, 3).toUpperCase() + 
                                                        moment(row.date_pointage).format('ddd DD MMM YY').substr(3)
                                                    }
                                                </td>
                                                <td className="cellHoraire">
                                                    {
                                                        moment(row.date_pointage).format('HH:mm') == '18:00' ? 'NUIT': 
                                                        moment(row.date_pointage).format('HH:mm') == '07:00' ? 'JOUR' : ''}
                                                </td>
                                                <td style={{width: widthPx, minWidth: widthPx, maxWidth: widthPx}}>
                                                    {row.site}
                                                </td>
                                                <td>
                                                    {
                                                        row.soft_delete ?
                                                            <img onClick={() => {this.editModal(row)}} className="img-btn" title="Modifier" src="/img/cancel_delete.svg"/>
                                                        :
                                                            <>
                                                                <img onClick={() => {this.editModal(row)}} className="img-btn" title="Modifier" src="/img/edit.svg"/>
                                                                <img onClick={() => {this.deleteModal(row)}} className="img-btn img-btn-margin" title="Supprimer" src="/img/delete.svg"/>
                                                            </>
                                                    }
                                                </td>
                                            </tr>)
                                    })
                                }
                            </tbody>
                        </table>
                    </div>
                }
            </div>
        )
    } 
}