import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'

import Modal from '../../modal/Modal'

export default class RestorePointageModal extends Component {
    constructor(props){
        super(props)
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }
    handleSave(){
        const {reclamation, agentId, isAfterConfirmable} = this.props
        const data = new FormData()
        if(reclamation)
            data.append("reclamation", reclamation)
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        data.append("agent_id", agentId) 
        data.append("is_after_confirmable", isAfterConfirmable ? 1 : 0)
        console.log(this.props.action)
        axios.post(this.props.action, data)
        .then(({data}) => {
            if(data){
                this.props.updateHour()
                this.props.closeModal()
            }
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        const {pointage, reclamation} = this.props
        return ( 
            <Modal  
                width="md" 
                handleSave={this.handleSave} 
                handleCancel={this.handleCancel}
            >
                {
                    pointage &&
                    <div>
                        <h3>Restorer le pointage</h3>
                        <b>Site : </b> {pointage.site && pointage.site.toUpperCase()} <br/>
                        <b>Date : </b> {moment(pointage.date_pointage).format("ddd DD MMM YY")} <br/>
                        <b>Horaire : </b> {moment(pointage.date_pointage).format("HH:mm") == "07:00" ? "JOUR" : "NUIT"} <br/>
                    </div>
                }
            </Modal>
        )
    }
}