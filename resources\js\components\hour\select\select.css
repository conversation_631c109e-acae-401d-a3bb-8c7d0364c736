#selectBox{
    height: 62px;
    background-color: white;
}
#itemSelected > div{
    padding: 3px;
}
#itemSelected > div > input{
    padding: 10px;
    text-align: center;
    border: solid .5px rgba(0, 0, 0, .1);
}
#itemSelected{
    text-align: center;
    padding: 5px;
    border: solid .5px rgba(0, 0, 0, .1);
    cursor: pointer;
}
#itemNotSelected{
    border: solid .5px rgba(0, 0, 0, .1);
    box-shadow: 2px 2px 2px rgba(0, 0, 0, .1);
    background-color: white;
    position: relative;
    z-index: 1000;
}
#itemSelected, #itemNotSelected span{
    padding: 10px;
    background-color: white;
}
#itemNotSelected span{
    cursor: pointer;
}
#itemNotSelected span:hover{
    background-color: whitesmoke;
}
#itemNotSelected{
    position: relative;
}
#itemNotSelected span{
    display: inline-block;
    width: 100%;
}
#cellDropDown{
    display: table-cell;
    width: 30px;
}
.item-selected-hour{
    display: inline-block;
    padding: 10px;
}
.item-selected img{
    cursor: pointer;
}
#itemNotSelected span.active-item{
    background-color: rgba(0, 0, 0, .2);
}
.active-filter{
    background-color: #eeeeee;
}