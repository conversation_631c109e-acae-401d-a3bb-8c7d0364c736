import React, { Component } from 'react'
import axios from 'axios'
import DatePicker  from 'react-datepicker'

import Modal from '../../modal/Modal'
import Site from '../../agent/site/Site'
import moment from 'moment'

export default class EditPointageModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            showSite: false,
            site: null,
            date_pointage: null,
            horaire: '',
            error: null,
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.handleChangeSite = this.handleChangeSite.bind(this)
        this.handleChangeDatePointage = this.handleChangeDatePointage.bind(this)
        this.handleChangeHoraire = this.handleChangeHoraire.bind(this)
    }
    handleChangeHoraire(e){
        this.setState({
            horaire: e.target.value
        })
    }
    handleChangeSite(site){
        this.setState({
            site: site,
            showSite: false
        })
    }
    handleChangeDatePointage(date){
        this.setState({
            date_pointage: date
        })
    }
    changeContact(contact){
        this.setState({
            contact: contact,
            showContact: false
        })
    }
    handleChangeQuality(event){
        this.setState({
            quality: event.target.value
        })
    }
    handleChangePassword(event){
        this.setState({
            password: event.target.value
        })
    }
    handleChangeOrdre(event){
        this.setState({
            ordre: event.target.value
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    componentDidMount(){
        const {currentPointage, reclamation, agentId} = this.props
        if(currentPointage){
            this.setState({
                site: {
                    idsite: currentPointage.idsite,
                    nom: currentPointage.site
                },
                date_pointage: moment(currentPointage.date_pointage).toDate(),
                horaire: moment(currentPointage.date_pointage).format("HH:mm:ss"),
            })
        }
        else{
            axios.get('/api/agents/get_site/'+ agentId + (reclamation ? '?reclamation=1' : ''))
            .then(({data}) => {
                this.setState({
                    site: data.site,
                    date_pointage: moment(data.default_date).toDate()
                })
            })
        }
    }
    toggleSite(value){
        this.setState({
            showSite: value
        })
    }
    handleSave(){
        this.setState({
            error: null
        })
        const {reclamation} = this.props
        const {site, date_pointage, horaire} = this.state
        let data = new FormData()
        data.append("agent_id", this.props.agentId)
        if(site && date_pointage && horaire){
            if(reclamation)
                data.append("reclamation", reclamation)
            data.append("site_id", site.idsite)
            data.append("date_pointage", moment(date_pointage).format('YYYY-MM-DD'))
            data.append("horaire", horaire)
            data.append("username", localStorage.getItem("username"))
            data.append("secret", localStorage.getItem("secret"))
            console.log(data.values)
            axios.post(this.props.action, data)
            .then(({data}) => {
                if(data.message){
                    if(data.message == "already_exist")
                        this.setState({
                            error:{
                                key: "",
                                value: "Ce pointage existe déjà."
                            }
                        })
                    else if(data.message == "out_of_interval"){
                        this.setState({
                            error:{
                                key: "",
                                value: "Ce date est en dehors du pointage."
                            }
                        })
                    }
                }
                else if(data){
                    this.props.updateHour()
                    this.props.closeModal()
                }
            })
        }
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        const {reclamation} = this.props
        const {showSite, site, date_pointage, horaire, error} = this.state
        return (
            <div>
                <Modal handleSave={this.handleSave} handleCancel={this.handleCancel}>
                    <h3> {reclamation ? 'Réclamation' : 'Pointage'} </h3>
                    <div className="input-container">
                        <label className={(error && error.key == 'site') ? 'pink' : ''}>Site *</label>
                        <div className="table">
                            <div className="cell">
                                <input 
                                    disabled={true} 
                                    value={site ? site.nom : ''}/>
                            </div>
                            <div id="cellClientBtn" onClick={()=>{this.toggleSite(true)}}>
                                <img id="clientImg" src="/img/site.svg"/>
                            </div>
                        </div>
                    </div>
                    <div className="input-container">
                        <label className={(error && error.key == 'date_pointage') ? 'pink' : ''}>Date *</label>
                        <DatePicker
                            className="datepicker" 
                            dateFormat="dd-MM-yyyy" 
                            selected={date_pointage} 
                            onChange={this.handleChangeDatePointage}/>
                    </div>
                    <div className="input-container">
                        <label className={(error && error.key == 'horaire') ? 'pink' : ''}>Horaire *</label>
                        <select value={horaire} onChange={this.handleChangeHoraire}>
                            <option></option>
                            <option value="07:00:00">Jour</option>
                            <option value="18:00:00">Nuit</option>
                        </select>
                    </div>
                    { error && <div className="pink">{error.value}</div> }
                </Modal>
                {
                    showSite &&
                    <Site defaultSite={site} closeModal={()=>{this.toggleSite(false)}} changeSite={this.handleChangeSite}/>
                }
            </div>
        )
    }
}