import React, { Component } from 'react'

export default class SeachInput extends Component {
    constructor(props){
        super(props)
        this.state = {
            itemSearch: '',
            currentItem: null
        }
        this.handleItemChange = this.handleItemChange.bind(this)
        this.handleItemCLick = this.handleItemCLick.bind(this)
        this.handleClickSaveItem = this.handleClickSaveItem.bind(this)
        this.closeListInput = this.closeListInput.bind(this)
    }

    showItem(item){
        const {itemSearch} = this.state
        if(itemSearch){
            const search = itemSearch.toLocaleLowerCase().replace(/[.*+?^{}()|[\]\\]/g, '\\$&')
            var patt = new RegExp(search)
            if(item.libelle && patt.test(item.libelle.toLocaleLowerCase()))
                return true
            return false
        }
        return true
    }
    handleItemChange(e){
        this.setState({
            itemSearch: e.target.value,
            showItem: true
        })
    }
    handleItemCLick(e){
        e.stopPropagation()
        const {showItem, itemSearch} = this.state
        this.setState({
            showItem: !showItem,
            itemSearch: (!showItem ? '' : itemSearch)
        })
    }
    handleClickSaveItem(e, item){
        e.stopPropagation()
        this.props.handleChange(item)
        this.setState({
            itemSearch: item.libelle,
            showItem: false
        })
    }

    componentDidMount(){
        const {selectedItem} = this.props
        if(selectedItem)
            this.setState({
                itemSearch: selectedItem.libelle
            })
    }

    closeListInput(e){
        e.stopPropagation()
        const {selectedItem} = this.props
        this.setState({showItem: false, itemSearch: selectedItem ? selectedItem.libelle : ''})
    }

    render(){
        const {showItem, itemSearch} = this.state
        const {label, items, disabled} = this.props
        return (
            <div className="input-select-relative" onClick={this.closeListInput}>
                <div className="input-container">
                    <label>{label}</label>
                    <input disabled={disabled} autoComplete="off" onClick={this.handleItemCLick} onChange={this.handleItemChange} type="text" value={itemSearch}/>
                </div>
                <div>
                    {
                        showItem &&
                        <ul id="itemListContainer" className="comment-vg-list">
                            {
                                items && (
                                    itemSearch ? items.map((ag, index) =>{
                                        if(this.showItem(ag))
                                            return <li key={'key_' + ag.id + index} onClick={(e) => {this.handleClickSaveItem(e, ag)}}>
                                                { ag.libelle }
                                            </li>
                                    })
                                    :  
                                    items.slice(0, 9).map((ag, index) =>{
                                        if(this.showItem(ag))
                                            return <li key={'key_' + ag.id + index} onClick={(e) => {this.handleClickSaveItem(e, ag)}}>
                                                { ag.libelle }
                                            </li>
                                    })
                                )
                            }
                            {
                                (items && !itemSearch) && 
                                <li>...</li>
                            }
                        </ul>
                    }
                </div>
            </div>
        )
    }
}