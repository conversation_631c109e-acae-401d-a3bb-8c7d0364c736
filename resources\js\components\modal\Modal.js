import React, { Component } from 'react'

export default class Modal extends Component {
    constructor(props){
        super(props)
        this.state = {
            heightWindow: 0,
            widthWindow: 0,
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }
    handleSave(){
        this.props.handleSave()
    }
    handleCancel(){
        this.props.handleCancel()
    }
    resize() {
        this.setState({
            heightWindow: window.innerHeight,
            widthWindow: window.innerWidth
        });
    }
    componentDidMount(){
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
    }
    render(){
        const {width, labelSave, heightWindow, error, disableSave, readOnly} = this.props
        return (
            <div style={{zIndex: 200, height: heightWindow + "px"}} className="fixed-front">
                <div className="table">
                    <div className="modal-container">
                        <div className={width? ("modal " + width): "modal sm"}>
                            <div className="modal-content">
                                {this.props.children}
                            </div>
                            <div className="table modal-footer">
                                <div className="cell-padding left">
                                    <span className="pink">{error && error.value}</span>
                                </div>
                                <div className="cell-padding right">
                                    {
                                        !readOnly &&
                                        <button disabled={disableSave} onClick={this.handleSave} className="btn-primary fix-width">
                                            {labelSave ? labelSave: 'Valider'}
                                        </button>
                                    }
                                    <button onClick={this.handleCancel} className="btn-default fix-width">{readOnly ? 'Fermer' : 'Annuler'}</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>)
    }
}