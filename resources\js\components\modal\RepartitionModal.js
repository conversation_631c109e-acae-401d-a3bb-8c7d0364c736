import React, { Component } from 'react'
import axios from 'axios'

export default class RepartitionModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            heightWindow: 0,
            widthWindow: 0,
            showModal: false
        }
        this.handleReduce = this.handleReduce.bind(this)
    }
    handleReduce(){
        this.setState({
            showModal: false
        })
    }
    resize(){
        this.setState({
            heightWindow: window.innerHeight,
            widthWindow: window.innerWidth
        })
    }
    updateData(){
        console.log('/api/reports/confirm_repartition_tache')
        axios('/api/reports/confirm_repartition_tache')
        .then(({data}) => {
            this.setState({
                showModal: data
            })
            this.setTimeoutUpdateData()
        })
        .catch(() => {
            this.setTimeoutUpdateData()
        })
    }
    setTimeoutUpdateData(){
        setTimeout(() => {
            this.updateData()
        }, 90000)
    }
    componentDidMount(){
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
        this.updateData()
    }
    render(){
        const {showModal, heightWindow} = this.state
        return (
            <div>
                {
                    showModal &&
                    <div style={{zIndex: 200, height: heightWindow + "px"}} className="fixed-front">
                        <div className="table">
                            <div className="modal-container">
                                <div className="modal sm">
                                    <div className="modal-content">
                                        <h3>Veulliez effectuer la répartition des taches</h3>
                                        <span>Et vérouillez après, merci.</span>
                                        <br/>
                                    </div>
                                    <div className="right modal-footer">
                                        <button onClick={this.handleReduce} className="btn-default fix-width">Réduire</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        )
    }
}