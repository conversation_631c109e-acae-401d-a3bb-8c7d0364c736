import React, { Component } from 'react'
import axios from 'axios'

import './monitor.css'
import Repartition from './repartition/Repartition'
import Configuration from './configuration/Configuration'

export default class Monitor extends Component {
    constructor(props) {
        super(props)
        this.state = {
            heightWindow: 0,
            widthWindow: 0,
            activeMenu: 'repartition',
        }
        this.toggleLoading = this.toggleLoading.bind(this)
        this.handleChangeMenu = this.handleChangeMenu.bind(this)
    }
    toggleLoading(load) {
        this.props.toggleLoading(load)
    }
    handleChangeMenu(e) {
        this.setState({
            activeMenu: e.target.id
        })
    }
    componentDidMount() {
        document.title = "Parametre - TLS"
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
        axios.get('/api/centrales')
            .then(({ data }) => {
                this.setState({
                    centrales: data
                })
            })
    }
    resize() {
        this.setState({
            heightWindow: window.innerHeight,
            widthWindow: window.innerWidth
        });
    }
    render() {
        const { activeMenu, heightWindow, widthWindow } = this.state
        return (
            <div className="table">
                <div className="row-header">
                    <div id="monitorMenu">
                        <ul>
                            <li id="repartition"
                                className={activeMenu == "repartition" ? "active-menu" : undefined}
                                onClick={this.handleChangeMenu}
                            >
                                Répartition
                            </li>
                            <li id="configuration"
                                className={activeMenu == "configuration" ? "active-menu" : undefined}
                                onClick={this.handleChangeMenu}
                            >
                                Configuration
                            </li>
                        </ul>
                    </div>
                </div>
                <div className="row">
                    {
                        activeMenu == "repartition" &&
                        <Repartition
                            heightWindow={heightWindow}
                            widthWindow={widthWindow}
                            toggleLoading={this.toggleLoading}
                        />
                    }
                    {
                        activeMenu == "configuration" &&
                        <Configuration
                            heightWindow={heightWindow}
                            widthWindow={widthWindow}
                            toggleLoading={this.toggleLoading}
                        />
                    }
                </div>
            </div>
        )
    }
}