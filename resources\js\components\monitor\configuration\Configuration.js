import React, { Component } from 'react'
import GroupSite from './groupSite/GroupSite'
import GroupSMS from './groupSMS/GroupSMS'

export default class Configuration extends Component {
    constructor(props){
        super(props)
        this.state = {
            groupSims: [],
            groupSites: [],
        }
        this.updateData = this.updateData.bind(this)
    }
    updateData(){
        this.props.toggleLoading(true)
        axios.get('/api/monitor/configuration')
        .then(({data}) => {
            if(data)
                this.setState({
                    groupSims: data.group_sims,
                    groupSites: data.group_sites,
                }, () => {
                    this.props.toggleLoading(false)
                })
        })
    }
    componentDidMount(){
        this.updateData()
    }
    render(){
        const {heightWindow, widthWindow} = this.props
        const {groupSims, groupSites} = this.state
        return  (
            <div className="table">
                <div id="tableContainer" style={{
                    width: (widthWindow/2 - 220), 
                    maxWidth: (widthWindow/2 - 220), 
                    minWidth: (widthWindow/2 - 220),
                }}>
                    <GroupSMS 
                        groups={groupSims} 
                        updateData={this.updateData}
                        heightWindow={heightWindow} 
                        widthWindow={widthWindow}/>
                </div>
                <div id="overviewContainer">
                    <GroupSite
                        groups={groupSites}
                        groupSMS={groupSims} 
                        updateData={this.updateData}
                        heightWindow={heightWindow}
                        widthWindow={widthWindow}/>
                </div>
            </div>
        )
    }
}