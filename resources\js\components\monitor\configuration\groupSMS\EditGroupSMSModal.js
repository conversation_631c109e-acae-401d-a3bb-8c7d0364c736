import React, { Component } from 'react'
import axios from 'axios'

import Modal from '../../../modal/Modal'

export default class EditGroupSMSModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            nom: '',
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.handleChangeNom = this.handleChangeNom.bind(this)
    }
    handleChangeNom(e){
        this.setState({
            nom: e.target.value
        })
    }
    handleSave(){
        const {nom} = this.state
        this.setState({
            error: null,
        })
        if(nom){
            this.setState({
                disableSave: true,
            })
            let data = new FormData()
            data.append("nom", nom)
            data.append("username", localStorage.getItem("username"))
            data.append("secret", localStorage.getItem("secret"))
            axios.post(this.props.action, data)
            .then(({data}) => {
                if(data){
                    this.props.updateData()
                }
            })
            .finally(()=>{
                this.setState({
                    disableSave: false,
                })
            })
        }
        else this.setState({
            error: {
                key: "nom",
                value: "Le champ nom doit être rempli."
            }
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    componentDidMount(){
        const {group} = this.props
        if(group){
            this.setState({
                nom: group.nom
            })
        }
    }
    render(){
        const {disableSave, error, nom} = this.state
        return (
            <div>
                <Modal 
                        disableSave={disableSave} 
                        handleSave={this.handleSave} 
                        handleCancel={this.handleCancel}
                    >
                    <h3>Groupe SMS</h3>
                    <div className="input-container">
                        <label className={error && error.key == "nom" ? "pink" : ""}>Nom</label>
                        <input onChange={this.handleChangeNom} value={nom}/>
                    </div>
                    {error && <div className="pink">{error.value}</div>}
                </Modal>
            </div>
        )
    }
}