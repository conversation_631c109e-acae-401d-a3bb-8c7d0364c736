
import React, { Component } from 'react'
import DeleteGroupSMSModal from './DeleteGroupSMSModal'
import EditGroupSMSModal from './EditGroupSMSModal'

export default class GroupSMS extends Component {
    constructor(props){
        super(props)
        this.state = {
            currentGroup: null,
            showAddGroupSMS: false,
            showEditGroupSMS: false,
            showDeleteGroupSMS: false,
        }
        this.updateData = this.updateData.bind(this)
        this.closeModal = this.closeModal.bind(this)
    }
    updateData(){
        this.props.updateData()
        this.closeModal()
    }
    handleShowDeleteGroupSMS(group){
        this.setState({
            currentGroup: group,
            showDeleteGroupSMS: true
        })
    }
    handleShowEditGroupSMS(group){
        this.setState({
            currentGroup: group,
            showEditGroupSMS: true
        })
    }
    handleShowAddGroupSMS(){
        this.setState({
            showAddGroupSMS: true
        })
    }
    closeModal(){
        this.setState({
            showAddGroupSMS: false,
            showEditGroupSMS: false,
            showDeleteGroupSMS: false,
        })
    }
    render(){
        const {currentGroup, showAddGroupSMS, showEditGroupSMS, showDeleteGroupSMS} = this.state
        const {widthWindow, heightWindow, groups} = this.props
        return(
            <div>
                {
                    showAddGroupSMS &&
                    <EditGroupSMSModal 
                        action="/api/group_sim/store" 
                        closeModal={this.closeModal}
                        updateData={this.updateData}/>
                }
                {
                    (currentGroup && showEditGroupSMS) &&
                    <EditGroupSMSModal 
                        action={"/api/group_sim/update/" + currentGroup.id} 
                        group={currentGroup}
                        closeModal={this.closeModal}
                        updateData={this.updateData}/>
                }
                {
                    (currentGroup && showDeleteGroupSMS) &&
                    <DeleteGroupSMSModal
                        action={"/api/group_sim/delete/" + currentGroup.id} 
                        nom={currentGroup.nom}
                        closeModal={this.closeModal}
                        updateData={this.updateData}/>
                }
                <div className="table">
                    <div className="row-header">
                        <h3 className="h3-table">
                            <div className="cell">
                                Groupe SMS <img className="edit-icon-cell" src="/img/add.svg" onClick={() => this.handleShowAddGroupSMS()}/>
                            </div>
                        </h3>
                    </div>
                    <div className="row-table">
                        <table className="fixed_header layout-fixed">
                            <tbody style={{'height': (heightWindow - 190)}}>
                                {
                                    groups.map((row) =>{
                                        return (
                                            <tr onDoubleClick={()=>{ this.clickTransmitter(row.transmitter)}} key={'tr_' + row.id}>
                                                <td style={{
                                                    width: widthWindow/2 - 370, 
                                                    maxWidth: widthWindow/2 - 370, 
                                                    minWidth: widthWindow/2 - 370, 
                                                }}>
                                                    {row.nom}
                                                </td>
                                                <td>
                                                    <img className="edit-icon-cell" 
                                                        src="/img/edit.svg"
                                                        onClick={() => this.handleShowEditGroupSMS(row)}/>
                                                    
                                                    <img className="edit-icon-cell" 
                                                        src="/img/delete.svg"
                                                        onClick={() => this.handleShowDeleteGroupSMS(row)}/>
                                                </td>
                                            </tr>
                                        )
                                    })
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        )
    }
}