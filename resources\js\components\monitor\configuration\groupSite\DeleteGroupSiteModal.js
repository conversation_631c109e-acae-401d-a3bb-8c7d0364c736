import React, { Component } from 'react'
import Modal from '../../../modal/Modal'
import axios from 'axios'

export default class DeleteGroupSiteModal extends Component {
    constructor(props){
        super(props)
        this.state = {
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }
    handleSave(){
        const data = new FormData()
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        axios.post(this.props.action, data)
        .then(({data}) => {
            if(data)
                this.props.updateData()
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        return (
            <Modal confirm={false} handleSave={this.handleSave} handleCancel={this.handleCancel}>
                <div>
                    <h3>{this.props.nom}</h3>
                    <div>Voulez-vous vraiment supprimer ce groupe ?</div>
                </div>
            </Modal>)
    }
}