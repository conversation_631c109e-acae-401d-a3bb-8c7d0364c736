import React, { Component } from 'react'
import axios from 'axios'

import Modal from '../../../modal/Modal'

export default class EditGroupSiteModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            nom: '',
            groupSmsId: '',
            groupSiteId: ''
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.handleChangeNom = this.handleChangeNom.bind(this)
        this.handleChangeGroupSMS = this.handleChangeGroupSMS.bind(this)
        this.handleChangeGroupSite = this.handleChangeGroupSite.bind(this)
    }
    handleChangeGroupSite(e){
        this.setState({
            groupSiteId: e.target.value
        })
    }
    handleChangeGroupSMS(e){
        this.setState({
            groupSmsId: e.target.value
        })
    }
    handleChangeNom(e){
        this.setState({
            nom: e.target.value
        })
    }
    handleSave(){
        const {groupSmsId, groupSiteId, nom} = this.state
        const {group} = this.props
        this.setState({
            error: null,
        })
        if(nom && groupSmsId && (!group || (group && groupSiteId))){
            this.setState({
                disableSave: true,
            })
            let data = new FormData()
            data.append("nom", nom)
            data.append("vigilance_group_id", groupSiteId)
            data.append("sim_group_id", groupSmsId)

            data.append("username", localStorage.getItem("username"))
            data.append("secret", localStorage.getItem("secret"))
            axios.post(this.props.action, data)
            .then(({data}) => {
                if(data){
                    this.props.updateData()
                }
            })
            .finally(()=>{
                this.setState({
                    disableSave: false,
                })
            })
        }
        else if(!nom)
            this.setState({
                error: {
                    key: "nom",
                    value: "Le champ 'Nom' doit être rempli."
                }
            })
        else if(!groupSiteId)
            this.setState({
                error: {
                    key: "groupSiteId",
                    value: "Le champ 'Groupe Site' doit être rempli."
                }
            })
        else if(!groupSmsId)
            this.setState({
                error: {
                    key: "groupSmsId",
                    value: "Le champ 'Groupe SMS' doit être rempli."
                }
            })
    }
    handleCancel(){
        this.props.closeModal()
    }
    componentDidMount(){
        const {group} = this.props
        if(group){
            this.setState({
                nom: group.nom,
                groupSmsId: group.sim_group_id,
                groupSiteId: group.vigilance_group_id,
            })
        }
    }
    render(){
        const {disableSave, error, nom, groupSmsId, groupSiteId} = this.state
        const {groupSites, groupSMS, group} = this.props
        return (
            <div>
                <Modal 
                        disableSave={disableSave} 
                        handleSave={this.handleSave} 
                        handleCancel={this.handleCancel}
                    >
                    <h3>Groupe Site</h3>
                    <div className="input-container">
                        <label className={error && error.key == "nom" ? "pink" : ""}>Nom *</label>
                        <input onChange={this.handleChangeNom} value={nom}/>
                    </div>
                    <div className="input-container">
                        <label className={error && error.key == "groupSiteId" ? "pink" : ""}>Groupe Fichier {group && "*"}</label>
                        <select value={groupSiteId} onChange={this.handleChangeGroupSite}>
                            <option></option>
                            {
                                groupSites.map((g) => (
                                    <option key={g.id} value={g.id}>{g.nom}</option>
                                ))
                            }
                        </select>
                    </div>
                    <div className="input-container">
                        <label className={error && error.key == "groupSmsId" ? "pink" : ""}>Groupe SMS *</label>
                        <select value={groupSmsId} onChange={this.handleChangeGroupSMS}>
                            <option></option>
                            {
                                groupSMS.map((g) => (
                                    <option key={g.id} value={g.id}>{g.nom}</option>
                                ))
                            }
                        </select>
                    </div>
                    {error && <div className="pink">{error.value}</div>}
                </Modal>
            </div>
        )
    }
}