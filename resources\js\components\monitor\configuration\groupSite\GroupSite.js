
import React, { Component } from 'react'
import DeleteGroupSiteModal from './DeleteGroupSiteModal'
import EditGroupSiteModal from './EditGroupSiteModal'

export default class GroupSite extends Component {
    constructor(props){
        super(props)
        this.state = {
            currentGroup: null,
            showAddGroupSite: false,
            showEditGroupSite: false,
            showDeleteGroupSite: false,
        }
        this.updateData = this.updateData.bind(this)
        this.closeModal = this.closeModal.bind(this)
    }
    updateData(){
        this.props.updateData()
        this.closeModal()
    }
    handleShowDeleteGroupSite(group){
        this.setState({
            currentGroup: group,
            showDeleteGroupSite: true
        })
    }
    handleShowEditGroupSite(group){
        this.setState({
            currentGroup: group,
            showEditGroupSite: true
        })
    }
    handleShowAddGroupSite(){
        this.setState({
            showAddGroupSite: true
        })
    }
    closeModal(){
        this.setState({
            showAddGroupSite: false,
            showEditGroupSite: false,
            showDeleteGroupSite: false,
        })
    }
    render(){
        const {currentGroup, showAddGroupSite, showEditGroupSite, showDeleteGroupSite} = this.state
        const {widthWindow, heightWindow, groups, groupSMS} = this.props
        return(
            <div>
                {
                    showAddGroupSite &&
                    <EditGroupSiteModal 
                        action="/api/group_sites/store" 
                        groupSites={groups}
                        groupSMS={groupSMS}
                        closeModal={this.closeModal}
                        updateData={this.updateData}/>
                }
                {
                    (currentGroup && showEditGroupSite) &&
                    <EditGroupSiteModal 
                        action={"/api/group_sites/update/" + currentGroup.id} 
                        groupSites={groups}
                        groupSMS={groupSMS}
                        group={currentGroup}
                        closeModal={this.closeModal}
                        updateData={this.updateData}/>
                }
                {
                    (currentGroup && showDeleteGroupSite) &&
                    <DeleteGroupSiteModal
                        action={"/api/group_sites/delete/" + currentGroup.id} 
                        nom={currentGroup.nom}
                        closeModal={this.closeModal}
                        updateData={this.updateData}/>
                }
                <div className="table">
                    <div className="row-header">
                        <h3 className="h3-table">
                            <span className="cell">
                                Groupe Site 
                                <img className="edit-icon-cell" src="/img/add.svg" onClick={() => this.handleShowAddGroupSite()}/>
                            </span>
                            <span className="cell right">
                                
                            </span>
                        </h3>
                    </div>
                    <div className="row-table">
                        <table className="fixed_header layout-fixed">
                            <tbody style={{'height': (heightWindow - 190)}}>
                                {
                                    groups.map((row) =>{
                                        return (
                                            <tr key={row.id}>
                                                <td style={{
                                                        width: (widthWindow/2 - 150) / 2, 
                                                        maxWidth: (widthWindow/2 - 150) / 2, 
                                                        minWidth: (widthWindow/2 - 150) / 2, 
                                                    }}
                                                    className={(row.group_2 && row.group_sim) ? "" : "pink"}
                                                >
                                                    {row.nom}
                                                    {   
                                                        row.id != row.vigilance_group_id && 
                                                        <span className="secondary">
                                                            <span className="chevron-separator">{' > '}</span>
                                                            {row.group_2}
                                                        </span>
                                                    }
                                                </td>
                                                <td style={{
                                                    width: (widthWindow/2 - 150) / 2, 
                                                    maxWidth: (widthWindow/2 - 150) / 2, 
                                                    minWidth: (widthWindow/2 - 150) / 2, 
                                                }}>
                                                    {row.group_sim}
                                                </td>
                                                <td>
                                                    <img className="edit-icon-cell" 
                                                        src="/img/edit.svg"
                                                        onClick={() => this.handleShowEditGroupSite(row)}/>
                                                    <img className="edit-icon-cell" 
                                                        src="/img/delete.svg"
                                                        onClick={() => this.handleShowDeleteGroupSite(row)}/>
                                                </td>
                                            </tr>)
                                    })
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        )
    }
}