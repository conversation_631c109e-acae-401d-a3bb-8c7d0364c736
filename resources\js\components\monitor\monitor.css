.cellTransmitter{
    width: 150px;
    min-width: 150px;
    max-width: 150px;
}
.cellNbSite{
    width: 80px;
    min-width: 80px;
    max-width: 80px;
}
.cellMonitorNom{
    width: 300px;
    min-width: 300px;
    max-width: 300px;
}
.cellMonitorProm{
    width: 110px;
    min-width: 110px;
    max-width: 110px;
}
.cellMonitorCentrale{
    width: 130px;
    min-width: 130px;
    max-width: 130px;
}
.cellVigilance{
    width: 220px;
    min-width: 220px;
    max-width: 220px;
}
#centraleFilter{
    padding: 10px;
    width: 90px;
}
#monitorMenu{
    padding: 20px 20px 0px 20px;
    height: 80px;
    max-height: 80px;
    min-height: 80px;
}
#monitorMenu > ul{
    padding: 0;
    margin: 0;
    display: block;
    width: 100%;
    height: 100%;
    background-color: #ddd;
}
#monitorMenu > ul > li{
    display: inline-block;
    font-size: 20px;
    height: 60px;
    line-height: 60px;
    padding: 0px 20px;
    border-right: solid 10px white;
    list-style:none;
    cursor: pointer;
}
#monitorMenu > ul > li:hover{
    color: #666;
}
li.active-menu{
    background-color: #336666;
    color: white;
}
#monitorMenu > ul > li.active-menu:hover{
    color: whitesmoke;
}
