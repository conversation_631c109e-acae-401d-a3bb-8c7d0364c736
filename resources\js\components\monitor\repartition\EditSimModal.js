import React, { Component } from 'react'
import axios from 'axios'

import Modal from '../../modal/Modal'

export default class EditSimModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            groupId: '',
            gatewayId: '',
            groups: [],
            gateways: [],
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.handleChangeGroup = this.handleChangeGroup.bind(this)
        this.handleChangeGateway = this.handleChangeGateway.bind(this)
    }
    handleChangeGateway(e){
        this.setState({
            gatewayId: e.target.value
        })

    }
    handleChangeGroup(e){
        this.setState({
            groupId: e.target.value
        })
    }
    handleSave(){
        const {groupId, gatewayId} = this.state
        const {sim} = this.props
        this.setState({
            error: null,
        })
        if(gatewayId){
            this.setState({
                disableSave: true,
            })
            let data = new FormData()
            data.append("numero", sim.transmitter)
            data.append("sim_group_id", groupId)
            data.append("gateway_id", gatewayId)
            data.append("username", localStorage.getItem("username"))
            data.append("secret", localStorage.getItem("secret"))
            axios.post(this.props.action, data)
            .then(({data}) => {
                if(data){
                    this.props.updateData()
                }
            })
            .finally(()=>{
                this.setState({
                    disableSave: false,
                })
            })
        }
        /*else if(!groupId)
            this.setState({
                error: {
                    key: "groupId",
                    value: "Le champ 'Groupe SIM' doit être rempli."
                }
            })*/
        else if(!gatewayId)
            this.setState({
                error: {
                    key: "gatewayId",
                    value: "Le champ 'Passerelle' doit être rempli."
                }
            })
    }
    handleCancel(){
        this.props.closeModal()
    }
    componentDidMount(){
        const {sim} = this.props
        if(sim){
            this.setState({
                nom: sim.transmitter,
                groupId: sim.sim_group_id,
                gatewayId: sim.gateway_id,
            })
        }
        axios.get("/api/group_sim")
        .then(({data}) => {
            this.setState({
                groups: data.group_sims,
                gateways: data.gateways
            })
        })
    }
    render(){
        const {disableSave, error, groupId, groups, gatewayId, gateways} = this.state
        const {sim} = this.props
        return (
            <div>
                <Modal 
                        disableSave={disableSave} 
                        handleSave={this.handleSave} 
                        handleCancel={this.handleCancel}
                    >
                    <h3>SIM</h3>
                    <span>{sim.transmitter}</span>
                    <hr/>
                    <div className="input-container">
                        <label className={error && error.key == "gatewayId" ? "pink" : ""}>Passerelle *</label>
                        <select value={gatewayId} onChange={this.handleChangeGateway}>
                            <option></option>
                            {
                                gateways.map((g) => (
                                    <option key={g.id} value={g.id}>{g.ip}</option>
                                ))
                            }
                        </select>
                    </div>
                    <div className="input-container">
                        <label className={error && error.key == "groupId" ? "pink" : ""}>Groupe SIM </label>
                        <select value={groupId} onChange={this.handleChangeGroup}>
                            <option></option>
                            {
                                groups.map((g) => (
                                    <option key={g.id} value={g.id}>{g.nom}</option>
                                ))
                            }
                        </select>
                    </div>
                    {error && <div className="pink">{error.value}</div>}
                </Modal>
            </div>
        )
    }
}