import React, { Component } from 'react'
import EditSimModal from './EditSimModal'

export default class Repartition extends Component {
    constructor(props){
        super(props)
        this.state = {
            transmitters: [],
            sites: [],
            centrales: [],
            currentSim: null,
            currentCentraleId: null,
            showEditSim: false,
            showEditSiteMenu: false,
        }
        this.updateData = this.updateData.bind(this)
        this.closeModal = this.closeModal.bind(this)
        this.showEditSimModal = this.showEditSimModal.bind(this)
        this.toggleEditSimMenu = this.toggleEditSimMenu.bind(this)
    }
    showEditSimModal(){
        this.setState({
            showEditSim: true,
        })
    }
    toggleEditSimMenu(value){
        this.setState({
            showEditSiteMenu: value
        })
    }
    closeModal(){
        this.setState({
            showEditSim: false
        })
    }
    clickTransmitter(transmitter){
        const currentCentraleId = this.state.currentCentraleId
        let param = transmitter.transmitter
        if(!transmitter.transmitter) param = 'null'
        axios.get('/api/monitor/show/' + param + (currentCentraleId ? ('?idcentrale=' + currentCentraleId) : ''))
        .then(({data})=>{
            this.setState({
                currentSim: transmitter,
                sites: data,
            })
        })
    }
    changeCentrale(event){
        this.updateData(event.target.value)
    }
    updateData(idcentrale){
        this.props.toggleLoading(true)
        axios.get('/api/monitor/repartition' + (idcentrale ? ('?idcentrale=' + idcentrale) : ''))
        .then(({data}) => {
            this.setState({
                currentCentraleId: idcentrale,
                transmitters: data,
                currentSim: null,
                sites: null
            }, () => {
                this.props.toggleLoading(false)
                this.setState({
                    showEditSim: false
                })
            })
        })
    }
    componentDidMount(){
        this.updateData()
    }
    render(){
        const {heightWindow, widthWindow} = this.props
        const {transmitters, sites, centrales, currentSim, showEditSim, showEditSiteMenu} = this.state
        return  (
            <div className="table"  onClick={() => { if(showEditSiteMenu) this.toggleEditSimMenu(false)}}>
                <div id="tableContainer" style={{
                    width: (widthWindow/2 - 220) + 'px', 
                    maxWidth: (widthWindow/2 - 220) + 'px', 
                    minWidth: (widthWindow/2 - 220) + 'px',
                }}>
                    {
                        (currentSim && showEditSim) &&
                        <EditSimModal 
                            action={"/api/sim_gateways/update/" + currentSim.transmitter}
                            closeModal={this.closeModal}
                            updateData={this.updateData}
                            sim={currentSim}/>
                    }
                    <div className="table">
                        <div className="row-header">
                            <h3 className="h3-table">
                                <span className="cell">Récepteurs</span>
                                <span className="cell right">
                                    <select id="centraleFilter" onChange={(event) => {this.changeCentrale(event)}}>
                                        <option value="">Tous</option>
                                        {
                                            centrales.map((row) => (
                                                <option key={row.idcentrale} value={row.idcentrale}> {row.nom}</option>
                                            ))
                                        }
                                    </select>
                                </span>
                            </h3>
                        </div>
                        <div className="row-table">
                            <table className="fixed_header layout-fixed">
                                <thead>
                                    <tr>
                                        <th className="cellTransmitter">Récépteur</th>
                                        <th className="cellNbSite">Nb site</th>
                                        <th className="cellNbSite">Passerelle</th>
                                        <th>Groupe SIM</th>
                                    </tr>
                                </thead>
                                <tbody style={{'height': (heightWindow - 240)}}>
                                    {
                                        transmitters.map((row) =>{
                                            return (
                                                <tr onDoubleClick={()=>{this.clickTransmitter(row)}} 
                                                    className={(row.transmitter && /^032[0-9]{7}$/.test(row.transmitter) && !row.group) ? "pink" : ""}
                                                    key={'tr_' + row.transmitter}>
                                                    <td className="cellTransmitter">{row.transmitter ? row.transmitter : <span style={{color: 'red'}}>Non défini</span>}</td>
                                                    <td className="cellNbSite right">{row.nb_site}</td>
                                                    <td className="cellNbSite center">{row.gateway}</td>
                                                    <td>{row.group}</td>
                                                </tr>)
                                        })
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div id="overviewContainer">
                    {
                        currentSim &&
                        <div className="table">
                            <div className="row-header">
                                <div className="overview-container">
                                    <div className="head-title-overview">
                                        <div style={{height:"40px", lineHeight:"40px" }}>
                                            <div 
                                                className="title-overview">
                                                <span className={!currentSim.transmitter ? "red" : ""}>
                                                    {currentSim.transmitter ? currentSim.transmitter : 'Non défini'}
                                                </span>
                                            </div>
                                            {
                                                (currentSim.transmitter && /^032[0-9]{7}$/.test(currentSim.transmitter)) &&
                                                <div className="overview-edit-icon">
                                                    <img onClick={() => {this.toggleEditSimMenu(!showEditSiteMenu)}} className="overview-edit-img" src="/img/parametre.svg"/>
                                                    {
                                                        showEditSiteMenu && <div className="dropdown-overview-edit">
                                                            <span onClick={()=>{ this.showEditSimModal()}}>Mettre à jour</span>
                                                        </div>
                                                    }
                                                </div>
                                            }
                                        </div>
                                    </div>
                                    <span><b>Passerelle : </b> {currentSim.gateway}</span><br/>
                                    <span><b>Nb. site : </b> {sites && sites.length}</span><br/>
                                </div>
                            </div>
                            
                            <div className="row-table">
                                <table className="fixed_header layout-fixed">
                                    <thead>
                                        <tr>
                                            <th className="cellMonitorNom">Site</th>
                                            <th className="cellMonitorProm">Prom</th>
                                            <th className="cellMonitorCentrale">Centrale</th>
                                            <th className="cellVigilance">Vigilance</th>
                                            <th>Dernier transmission</th>
                                        </tr>
                                    </thead>
                                    <tbody style={{'height': (heightWindow - 340)}}>
                                        {
                                            sites.map((row) =>{
                                                return (
                                                    <tr key={row.idsite}
                                                        className={
                                                            (
                                                                row.sms && row.correct_transmitter && row.correct_transmitter != row.transmitter && (
                                                                    (row.vigilance && row.ct_id && row.g_id && row.g_id == row.ct_id) ||
                                                                    (!row.vigilance && row.ct_id && row.ct_id == 1)
                                                                )
                                                            ) ? 'purple' :
                                                            (
                                                                row.sms && (
                                                                    (row.vigilance && row.t_id && row.g_id && row.t_id != row.g_id) ||
                                                                    (!row.vigilance && row.t_id && row.t_id != 1)
                                                                )
                                                            ) ? 'pink' : ''}>
                                                        <td className="cellMonitorNom">{row.nom}</td>
                                                        <td className="cellMonitorProm">{row.prom}</td>
                                                        <td className="cellMonitorCentrale">{row.centrale}</td>
                                                        <td className="cellVigilance">
                                                            { row.vigilance == 1 && row.horaire }
                                                        </td>
                                                        <td>{row.date_last_signal}</td>
                                                    </tr>)
                                            })
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    }
                </div>
            </div>
        )
    }
}