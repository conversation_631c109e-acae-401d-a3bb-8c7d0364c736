import React, { Component } from 'react'
import axios from 'axios'
import PasswordModal from './password/PasswordModal'

import './nav.css'
import LogoutModal from './logout/LogoutModal'
import ExtensionModal from './extension/ExtensionModal'
import moment from 'moment'
import { formDataOption } from '../../../../auth'

export default class Nav extends Component {
    constructor(props) {
        super(props)
        this.state = {
            connectionLost: 0,
            alarm: 0,
            anomalie: 0,
            site: 0,
            vigilance: 0,
            timeoutId: 0,
            rappel: 0,
            call_reminders_count: 0,
            toggleProfilMenu: false,
            showPasswordModal: false,
            showExtensionModal: false,
            showLogoutModal: false,
            heightWindow: 0,
            currentItem: 'alarm',
            currentHour: '',
            reclamation: 0,
            service24: 0,
            dateService: moment().format('YYYY-MM-DD HH:mm:ss'),
        }
        this.closeLogoutModal = this.closeLogoutModal.bind(this)
        this.closePasswordModal = this.closePasswordModal.bind(this)
        this.closeExtensionModal = this.closeExtensionModal.bind(this)
        this.handleShowPasswordModal = this.handleShowPasswordModal.bind(this)
        this.updateCount = this.updateCount.bind(this)
        this.toggleProfilMenu = this.toggleProfilMenu.bind(this)
        this.handleLogout = this.handleLogout.bind(this)
        this.handleChangeItem = this.handleChangeItem.bind(this)
        this.handleShowExtensionModal = this.handleShowExtensionModal.bind(this)
        this.updateDateService = this.updateDateService.bind(this)
    }

    updateDateService() {
        const now = moment();
        const currentHour = now.hour();
        let targetDate = now.format("YYYY-MM-DD");
        let targetTime = "06:00:00";

        if (currentHour >= 6 && currentHour < 18) {
            targetTime = "06:00:00";
        } else if (currentHour >= 18 || currentHour < 6) {
            if (currentHour < 6) {
                targetDate = now.subtract(1, "day").format("YYYY-MM-DD");
            }
            targetTime = "18:00:00";
        }

        const formattedDateTime = `${targetDate} ${targetTime}`;
        this.setState({ dateService: formattedDateTime });
    };
    closeLogoutModal() {
        this.setState({
            showLogoutModal: false
        })
    }
    handleChangeItem(value) {
        this.setState({
            currentItem: value
        })
        this.props.changeMenu(value)
    }
    handleShowExtensionModal() {
        this.setState({
            showExtensionModal: true
        })
    }
    handleShowPasswordModal() {
        this.setState({
            showPasswordModal: true
        })
    }
    closePasswordModal() {
        this.setState({
            showPasswordModal: false
        })
    }
    closeExtensionModal() {
        this.setState({
            showExtensionModal: false
        })
    }
    handleLogout() {
        console.log("handleLogout")
        this.setState({
            showLogoutModal: true
        })
    }
    toggleProfilMenu() {
        this.setState({
            toggleProfilMenu: !this.state.toggleProfilMenu
        })
    }
    componentDidMount() {
        // this.updateDateService()
        const { user } = this.props
        if (['room', 'root'].includes(user.role))
            this.updateCount()
        this.setState({
            currentItem: ['rh', 'op'].includes(user.role) ? 'agent' : 'alarm'
        })
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
    }
    resize() {
        this.setState({
            heightWindow: window.innerHeight
        });
    }
    updateCount() {
        console.log('updateCount')
        if (localStorage.getItem('id'))
            this.setState({
                alarm: localStorage.getItem('alarm'),
                site: localStorage.getItem('site'),
                pointage_error: localStorage.getItem('pointage_error'),
                currentHour: moment(localStorage.getItem('datetime'), 'YYYY-MM-DD HH:mm:ss').format('HH : mm'),
                reclamation: localStorage.getItem('reclamation'),
                service24: localStorage.getItem('service24'),
                call_reminders_count: localStorage.getItem('call_reminders_count'),
            })
        this.setTimeoutUpdateCount()
    }
    setTimeoutUpdateCount() {
        this.clearTimeoutNav()
        const timeoutId = setTimeout(this.updateCount, 5000)
        this.setState({
            timeoutId: timeoutId
        })
    }
    clearTimeoutNav() {
        if (this.state.timeoutId)
            clearTimeout(this.state.timeoutId)
    }
    componentWillUnmount() {
        this.clearTimeoutNav()
    }
    render() {
        const { showLogoutModal, showPasswordModal, currentItem, rappel, alarm, site, vigilance_recu, vigilance_total, vigilance_panne, pointage_error,
            showExtensionModal, currentHour, connectionLost, toggleProfilMenu, heightWindow, reclamation, anomalie, service24, call_reminders_count } = this.state
        const { user } = this.props
        return (
            <nav>
                <div className="center">
                    <img id="logoTls" src="/img/tls_logo.svg" />
                    {
                        connectionLost > 3 &&
                        <p id="connectionLost">Vérifier votre connexion internet</p>
                    }
                    {
                        currentHour &&
                        <div id='hourLabel'>{currentHour}</div>
                    }
                </div>
                <div id="profilContainer">
                    <div onClick={this.toggleProfilMenu}>
                        <img id="profilImg" src="/img/profil.svg" /><br />
                        <span id="profilName">{user.name + (user.extension ? " [ " + user.extension + " ]" : "")}</span>
                    </div>
                    {
                        toggleProfilMenu &&
                        <div>
                            <ul id="profilMenuList">
                                {/*
                                <li>
                                    <div onClick={this.handleShowPasswordModal}>Changer d'extension</div>
                                </li>
                                */}
                                <li>
                                    <div onClick={this.handleShowPasswordModal}>Changer de mot de passe</div>
                                </li>
                                <li>
                                    <div onClick={this.handleLogout}>Se déconnecter</div>
                                </li>
                            </ul>
                            <div onClick={this.toggleProfilMenu} className="center">
                                <img style={{ padding: "10px", height: "40px", cursor: "pointer" }} src="/img/chevron_up.svg" />
                            </div>
                        </div>
                    }
                </div>
                {
                    !toggleProfilMenu &&
                    <ul id="navItem" style={{ maxHeight: heightWindow - 340 }}>
                        {
                            (['tech', 'room', 'root'].includes(user.role)) &&
                            <li className={currentItem == "alarm" ? "active" : ""}>
                                <div onClick={() => { this.handleChangeItem("alarm") }} className="table">
                                    <span className="cell">ALARME</span>
                                    <span className="cell right">
                                        {alarm != 0 &&
                                            <span className={alarm > 100 ? "badge bg-pink" : alarm > 10 ? "badge bg-purple" : "badge bg-default"}>
                                                {alarm}
                                            </span>}
                                    </span>
                                </div>
                            </li>
                        }

                        {
                            (['room', 'root', 'client'].includes(user.role)) &&
                            <li className="menutitle">
                                <div onClick={() => { this.handleChangeItem("a_rappeler") }} className="table">
                                    <span className="cell">APPELLE</span>
                                    <span className="cell right">
                                        {call_reminders_count > 0 &&
                                            <span className={call_reminders_count > 10 ? "badge bg-pink" : call_reminders_count > 5 ? "badge bg-purple" : "badge bg-default"}>
                                                {call_reminders_count}
                                            </span>}
                                    </span>
                                </div>
                                <ul className={["appelle", "a_rappeler"].includes(currentItem) ? "active sub-menu" : "sub-menu"}>
                                    <li className={currentItem == "a_rappeler" ? "active" : ""}>
                                        <div onClick={() => { this.handleChangeItem("a_rappeler") }} className="table">
                                            <span className="cell">A rappeler</span>
                                        </div>
                                    </li>
                                    <li className={currentItem == "appelle" ? "active" : ""}>
                                        <div onClick={() => { this.handleChangeItem("appelle") }}>Tous</div>
                                    </li>
                                    <li className={currentItem == "contact" ? "active" : ""}>
                                        <div onClick={() => { this.handleChangeItem("contact") }}>Contact</div>
                                    </li>
                                </ul>
                            </li>
                        }
                        {
                            (['room', 'root', 'client'].includes(user.role)) &&
                            <li className="menutitle">
                                <div onClick={() => { this.handleChangeItem("vigilance") }} className="table">
                                    <span className="cell">VIGILANCE</span>
                                    <span className="cell right">
                                    </span>
                                </div>
                                <ul className={["vigilance_pointeuse", "vigilance"].includes(currentItem) ? "active sub-menu" : "sub-menu"}>
                                    <li className={currentItem == "vigilance" ? "active" : ""}>
                                        <div onClick={() => { this.handleChangeItem("vigilance") }} className="table">
                                            <span className="cell">Bouton</span>
                                        </div>
                                    </li>
                                    {
                                        ['room', 'root'].includes(user.role) &&
                                        <li className={currentItem == "vigilance_pointeuse" ? "active" : ""}>
                                            <div onClick={() => { this.handleChangeItem("vigilance_pointeuse") }}>Biométrique</div>
                                        </li>
                                    }
                                </ul>
                            </li>
                        }
                        {
                            (['room', 'root'].includes(user.role)) &&
                            <li className={currentItem == "service24" ? "active" : ""}>
                                <div onClick={() => { this.handleChangeItem("service24") }} className="table">
                                    <span className="cell">SERVICE 24</span>
                                    {/* <span className="cell right">
                                        {service24 != 0 &&
                                            <span className={service24 > 100 ? "badge bg-pink" : service24 > 10 ? "badge bg-purple" : "badge bg-default"}>
                                                {service24}
                                            </span>}
                                    </span> */}
                                </div>
                            </li>
                        }
                        {
                            (['room', 'root'].includes(user.role)) &&
                            <li className={currentItem == "reclamation" ? "active" : ""}>
                                <div onClick={() => { this.handleChangeItem("reclamation") }} className="table">
                                    <span className="cell">RECLAMATION</span>
                                    <span className="cell right">
                                        {reclamation != 0 &&
                                            <span className="badge bg-pink">
                                                {reclamation}
                                            </span>}
                                    </span>
                                </div>
                            </li>
                        }
                        {
                            (['room', 'root'].includes(user.role)) &&
                            <li className={currentItem == "pointage" ? "active" : ""}>
                                <div onClick={() => { this.handleChangeItem("pointage") }} className="table">
                                    <span className="cell">POINTAGE</span>
                                    <span className="cell right">
                                        {
                                            pointage_error != 0 &&
                                            <span className="badge bg-pink">
                                                {pointage_error}
                                            </span>
                                        }
                                    </span>
                                </div>
                            </li>
                        }
                        {
                            ((user.role == "root" || [1, 7, 13, 18].includes(Number.parseInt(user.id)))) &&
                            <li className={currentItem == "pointage_late" ? "active" : ""}>
                                <div onClick={() => { this.handleChangeItem("pointage_late") }} className="table">
                                    <span className="cell">PTG TARD</span>
                                    <span className="cell right">
                                    </span>
                                </div>
                            </li>
                        }
                        {
                            ['root', 'room'].includes(user.role) &&
                            <li className="menutitle">
                                <div onClick={() => { this.handleChangeItem("site") }} className="table">
                                    <span className="cell">SITE</span>
                                    <span className="cell right">
                                        {site != 0 && <span className="badge bg-pink">{site}</span>}
                                    </span>
                                </div>
                                <ul className={["archive_site", "site"].includes(currentItem) ? "active sub-menu" : "sub-menu"}>
                                    <li className={currentItem == "site" ? "active" : ""}>
                                        <div onClick={() => { this.handleChangeItem("site") }} className="table">
                                            <span className="cell">Actif</span>
                                            <span className="cell right">
                                            </span>
                                        </div>
                                    </li>
                                    {
                                        ['room', 'root'].includes(user.role) &&
                                        <li className={currentItem == "archive_site" ? "active" : ""}>
                                            <div onClick={() => { this.handleChangeItem("archive_site") }}>
                                                Archive
                                            </div>
                                        </li>
                                    }
                                </ul>
                            </li>
                        }
                        {
                            ['room', 'root'].includes(user.role) &&
                            <li className={currentItem == "client" ? "active" : ""}>
                                <div onClick={() => { this.handleChangeItem("client") }} className="table">
                                    <span className="cell">CLIENT</span>
                                    <span className="cell right">
                                    </span>
                                </div>
                            </li>
                        }
                        {
                            ['rh', 'op', 'root', 'room'].includes(user.role) &&
                            <li className="menutitle">
                                <div onClick={() => { this.handleChangeItem("agent") }} className="table">
                                    <span className="cell">AGENT</span>
                                    <span className="cell right">
                                    </span>
                                </div>
                                <ul className={["archive", "agent"].includes(currentItem) ? "active sub-menu" : "sub-menu"}>
                                    <li className={currentItem == "agent" ? "active" : ""}>
                                        <div onClick={() => { this.handleChangeItem("agent") }} className="table">
                                            <span className="cell">Actif</span>
                                            <span className="cell right">
                                            </span>
                                        </div>
                                    </li>
                                    {
                                        ['rh', 'root', 'room'].includes(user.role) &&
                                        <li className={currentItem == "archive" ? "active" : ""}>
                                            <div onClick={() => { this.handleChangeItem("archive") }}>
                                                Archive
                                            </div>
                                        </li>
                                    }
                                </ul>
                            </li>
                        }
                        {
                            ['room', 'root'].includes(user.role) &&
                            <li className={currentItem == "pointeuse" ? "active" : ""}>
                                <div onClick={() => { this.handleChangeItem("pointeuse") }} className="table">
                                    <span className="cell">POINTEUSE</span>
                                    <span className="cell right">
                                    </span>
                                </div>
                            </li>
                        }
                        {
                            ['rh', 'room', 'op', 'root', 'client'].includes(user.role) &&
                            <li className={currentItem == "report" ? "active" : ""}>
                                <div onClick={() => { this.handleChangeItem("report") }} className="table">
                                    <span className="cell">RAPPORT</span>
                                    <span className="cell right">
                                    </span>
                                </div>
                            </li>
                        }
                        {
                            ['tech', 'root'].includes(user.role) &&
                            <li className={currentItem == "monitor" ? "active" : ""}>
                                <div onClick={() => { this.handleChangeItem("monitor") }} className="table">
                                    <span className="cell">PARAMETRE</span>
                                    <span className="cell right">
                                    </span>
                                </div>
                            </li>
                        }
                    </ul>
                }
                {
                    showLogoutModal &&
                    <LogoutModal
                        closeModal={this.closeLogoutModal}
                        clearLogin={() => { this.props.clearLogin() }}
                        updateAuthUser={() => { this.props.updateAuthUser() }} />
                }
                {
                    showPasswordModal &&
                    <PasswordModal closeModal={this.closePasswordModal} action="/api/change_password" />
                }
                {
                    showExtensionModal &&
                    <ExtensionModal closeModal={this.closeExtensionModal} action={"/api/update_extension/" + user.id} />
                }
            </nav>
        )
    }
}
