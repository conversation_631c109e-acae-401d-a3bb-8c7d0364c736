import React, { Component } from 'react'
import axios from 'axios'

import Modal from '../../modal/Modal'

export default class ExtensionModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            extension: '',
            error: ''
        }
        this.handleChangeExtension = this.handleChangeExtension.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.handleSave = this.handleSave.bind(this)
    }
    handleChangeExtension(e){
        this.setState({
            extension: e.target.value,
            error: ''
        })
    }
    toggleAgent(value){
        this.setState({
            showAgent: value
        })
    }
    handleSave(){
        const {extension} = this.state
        this.setState({
            disableSave: true,
            error: '',
        })
        if(!extension) {
            this.setState({
                error: 'Extension du mot de passe requis.',
                disableSave: false
            })
        }
        else {
            let data = new FormData()
            data.append("extension", extension)
            axios.post(this.props.action, data)
            .then(({data}) => {
                if(data.error){
                    console.log(data.error)
                    this.setState({
                        error: data.error
                    })
                }
                else if(data){
                    this.props.closeModal()
                }
            })
            .finally(()=>{
                this.setState({
                    disableSave: false
                })
            })
        }
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        const {extension, disableSave, error} = this.state
        return (
            <div style={{color: "#444"}}>
                <Modal disableSave={disableSave} handleSave={this.handleSave} handleCancel={this.handleCancel}>
                    <h3>Modification</h3>
                    <div className="input-container">
                        <label>Extension *</label>
                        <input onChange={this.handleChangeExtension} value={extension}/>
                    </div>
                    <div className="pink" style={{fontSize: '10pt', fontStyle: 'italic'}}>{error}</div>
                </Modal>
            </div>
        )
    }
}