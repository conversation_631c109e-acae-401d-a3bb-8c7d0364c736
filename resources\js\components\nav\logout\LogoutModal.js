import React, { Component } from 'react'
import Modal from '../../modal/Modal'
import axios from 'axios'

export default class LogoutModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            error: ''
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }
    handleSave(){
        this.setState({error: '', disableSave: true})
        const username = localStorage.getItem('username')
        const secret = localStorage.getItem('secret')
        let data = new FormData()
        data.append("username", username)
        data.append("secret", secret)
        axios.post("/api/logout", data)
        .then(({data}) => {
            if(data.error)
                this.setState({error: data.error})
            else {
                this.props.clearLogin()
                this.props.closeModal()
                localStorage.removeItem("username")
                localStorage.removeItem("secret")
                localStorage.removeItem("id")
                localStorage.removeItem("name")
            }
        })
        .catch( (err) => {
            console.error(err)
            this.props.updateAuthUser()
            this.setState({
                disableSave: false
            })
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        const {disableSave, error} = this.state
        return (
            <Modal disableSave={disableSave} handleSave={this.handleSave} handleCancel={this.handleCancel}>
                <div style={{color: "#444"}}>
                    <h3>Se déconnecter</h3>
                    {
                        !error ?
                            <div>Voulez-vous vraiment vous déconnecter?</div>
                        :
                            <p className="red">{error}</p>
                    }
                </div>
            </Modal>)
    }
}