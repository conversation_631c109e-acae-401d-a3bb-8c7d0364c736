nav > ul{
    padding-left: 0px;
}
#profilContainer{
    padding: 20px 10px;
    background-color: rgba(0, 0, 0, .3);
    text-align: center;
}
#profilImg{
    width: 40px;
    height: 40px;
    border: white 3px solid;
    border-radius: 50px;
    cursor: pointer;
}
#profilName{
    font-size: 10px;
}
nav > ul >li{
    padding: 20px 10px;
    list-style:none;
    border-bottom: solid .5px rgba(255, 255, 255, .3);
}
nav > ul >li > div{
    text-decoration: none;
    color: white;
    cursor: pointer;
}
ul#profilMenuList{
    padding: 0;
}
ul#profilMenuList > li{
    padding: 20px 10px;
    list-style:none;
    color: white;
    font-size: 12px;
    border-top: solid .5px rgba(255, 255, 255, .3);
}
ul#profilMenuList > li > div{
    cursor: pointer;
}

ul#navItem{
    overflow: hidden; 
    overflow-y: auto;
    padding: 10px;
}
ul#navItem::-webkit-scrollbar {
    width: 5px;
}
/* Track */
ul#navItem::-webkit-scrollbar-track {
    background: #336666; 
}
/* Handle */
ul#navItem::-webkit-scrollbar-thumb {
    background: #444;
}
/* Handle on hover */
ul#navItem::-webkit-scrollbar-thumb:hover {
    background: #555;
}
ul#navItem > li:not(.menutitle):hover, ul.sub-menu > li:hover {
    background: rgba(0, 0, 0, .1);
}
ul.sub-menu{
    display: block;
    width: 100%;
    list-style: none;
    line-height: 2em; 
    line-height: 2em;
    overflow: hidden;
    max-height:0; 
    transition-property: max-height,padding, margin-top;
    transition-duration: 0.3s;
    transition-timing-function: ease-in; 
    margin-top: 0px;
    padding: 0px 0px;
}
ul.sub-menu.active{
    max-height:300px;
    margin-top: 5px;
    padding: 5px 0px;
}
.menutitle:hover .sub-menu {
    max-height:300px;
    margin-top: 5px;
    padding: 5px 0px;
}
ul.sub-menu > li{
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    list-style: none;
    color: #ddd;
    padding: 10px;
}
ul.sub-menu > li > div{
    cursor: pointer;
}
li.active{
    background-color: rgba(250, 250, 250, .1);
}
@keyframes pulse {
    0% {
        background-color: white;
    }
    100% {
        background-color: #e91e63;
    }
}
#connectionLost{
    padding: 20px;
    color: white;
    background-color: #f44336;
    animation: pulse 1s infinite;
}
#hourLabel{
    font-size: 18pt;
    font-weight: bold;
    margin-bottom: 20px;
}