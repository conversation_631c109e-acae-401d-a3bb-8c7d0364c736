import React, { Component } from 'react'
import axios from 'axios'

import Modal from '../../modal/Modal'

export default class PasswordModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            currentPassword: '',
            newPassword: '',
            confirmPassword: '',
            error: ''
        }
        this.handleChangeCurrentPassword = this.handleChangeCurrentPassword.bind(this)
        this.handleChangeNewPassword = this.handleChangeNewPassword.bind(this)
        this.handleChangeConfirmPassword = this.handleChangeConfirmPassword.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.handleSave = this.handleSave.bind(this)
    }
    handleChangeCurrentPassword(e){
        this.setState({
            currentPassword: e.target.value,
            error: ''
        })
    }
    handleChangeNewPassword(e){
        this.setState({
            newPassword: e.target.value,
            error: ''
        })
    }
    handleChangeConfirmPassword(e){
        this.setState({
            confirmPassword: e.target.value,
            error: ''
        })
    }
    toggleAgent(value){
        this.setState({
            showAgent: value
        })
    }
    handleSave(){
        const {currentPassword, newPassword, confirmPassword} = this.state
        this.setState({
            disableSave: true,
            error: '',
        })
        console.log('set password')
        if(!currentPassword) {
            this.setState({
                error: 'Mot de passe actuel requis.',
                disableSave: false
            })
        }
        else if(!newPassword) {
            this.setState({
                error: 'Nouveau mot de passe requis.',
                disableSave: false
            })
        }
        else if(!confirmPassword) {
            this.setState({
                error: 'Confirmation du mot de passe requis.',
                disableSave: false
            })
        }
        else if(newPassword != confirmPassword) {
            this.setState({
                error: 'Confirmation du mot de passe incorrecte.',
                disableSave: false
            })
        }
        else {
            let data = new FormData()
            data.append("email", localStorage.getItem('username'))
            data.append("password", currentPassword)
            data.append("new_password", newPassword)
            axios.post(this.props.action, data)
            .then(({data}) => {
                if(data.error){
                    console.log(data.error)
                    this.setState({
                        error: data.error
                    })
                }
                else if(data){
                    localStorage.removeItem("username")
                    localStorage.removeItem("secret")
                }
            })
            .finally(()=>{
                this.setState({
                    disableSave: false
                })
            })
        }
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        const {currentPassword, newPassword, confirmPassword, disableSave, error} = this.state
        return (
            <div style={{color: "#444"}}>
                <Modal disableSave={disableSave} handleSave={this.handleSave} handleCancel={this.handleCancel}>
                    <h3>Mot de passe</h3>
                    <div className="input-container">
                        <label>Mot de passe actuel *</label>
                        <input onChange={this.handleChangeCurrentPassword} value={currentPassword} type="password"/>
                    </div>
                    <div className="input-container">
                        <label>Nouveau mot de passe *</label>
                        <input onChange={this.handleChangeNewPassword} value={newPassword} type="password"/>
                    </div>
                    <div className="input-container">
                        <label>Confirmation *</label>
                        <input onChange={this.handleChangeConfirmPassword} value={confirmPassword} type="password"/>
                    </div>
                    <div className="pink" style={{fontSize: '10pt', fontStyle: 'italic'}}>{error}</div>
                </Modal>
            </div>
        )
    }
}