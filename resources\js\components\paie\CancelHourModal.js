import React, { Component } from 'react'
import axios from 'axios'

import Modal from '../modal/Modal'

import 'react-datepicker/dist/react-datepicker.css'

export default class CancelHourModal extends Component {
    constructor(props){
        super(props)
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }
    handleSave(){
        const data = new FormData()
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        axios.post(this.props.action, data)
        .then(({data}) => {
            if(data){
                    this.props.updateCancelHour()
                    this.props.closeModal()
            }
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        const {paie, plageDate} = this.props
        return ( 
            <Modal  
                width="md" 
                handleSave={this.handleSave} 
                handleCancel={this.handleCancel}
            >
                {
                    paie &&
                    <div>
                        <h3>Annuler la confiration du calcul d'heure</h3>
                        <h2>
                            {
                                paie.societe_id == 1 ? 'DGM-' + paie.agent.numero_employe :
                                paie.societe_id == 2 ? 'SOIT-' + paie.agent.num_emp_soit :
                                paie.societe_id == 3 ? 'ST-' + paie.agent.numero_stagiaire :
                                paie.societe_id == 4 ? 'SM' :
                                paie.agent.numero_employe ? paie.agent.numero_employe :
                                paie.agent.numero_stagiaire ? paie.agent.numero_stagiaire :
                                <span className="purple">Ndf</span>
                            }
                        </h2> 
                        {paie.agent.nom} <br/>
                        <b>{plageDate}</b>
                    </div>
                }
            </Modal>
        )
    }
}