import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'

import Modal from '../modal/Modal'

export default class EditPaieModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            nom: '',
            numero_employe: '',
            num_emp_soit: '',
            numero_stagiaire: '',
            date_embauche: '',
            date_confirmation: '',
            date_conf_soit: '',
            nb_heure_contrat: '',

            idm_depl: 0,
            part_variable: 0,
            perdiem: 0,
            prime_anc: 0,
        }
        this.handleChangeSalaire = this.handleChangeSalaire.bind(this)
        this.handleChangeNom = this.handleChangeNom.bind(this)
        this.handleChangeNumEmp = this.handleChangeNumEmp.bind(this)
        this.handleChangeNumEmpSoit = this.handleChangeNumEmpSoit.bind(this)
        this.handleChangeNumStg = this.handleChangeNumStg.bind(this)
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.handleChangePartVariable = this.handleChangePartVariable.bind(this)
        this.handleChangeIdmDepl = this.handleChangeIdmDepl.bind(this)
        this.handleChangePerdiem = this.handleChangePerdiem.bind(this)
        this.handleChangePrimeAnc = this.handleChangePrimeAnc.bind(this)
    }
    handleChangePrimeAnc(e){
        this.setState({
            prime_anc: e.target.value
        })
    }
    handleChangePerdiem(e){
        this.setState({
            perdiem: e.target.value
        })
    }
    handleChangeIdmDepl(e){
        this.setState({
            idm_depl: e.target.value
        })
    }
    handleChangePartVariable(e){
        this.setState({
            part_variable: e.target.value
        })
    }
    handleChangeSalaire(e){
        this.setState({
            sal_base: e.target.value
        })
    }
    handleChangeSalaire(e){
        this.setState({
            sal_base: e.target.value
        })
    }
    handleChangeTab(event){
        this.setState({
            activeTab: event.target.id
        })
    }
    handleChangeNom(event){
        this.setState({
            nom: event.target.value
        })
    }
    handleChangeNumEmp(event){
        this.setState({
            numero_employe: event.target.value
        })
    }
    handleChangeNumEmpSoit(event){
        this.setState({
            num_emp_soit: event.target.value
        })
    }
    handleChangeNumStg(event){
        this.setState({
            numero_stagiaire: event.target.value
        })
    }
    componentDidMount(){
        const {currentEtatPaie} = this.props
        if(currentEtatPaie)
            this.setState({
                idm_depl: currentEtatPaie.idm_depl,
                part_variable: currentEtatPaie.part_variable,
                perdiem: currentEtatPaie.perdiem,
                prime_anc: currentEtatPaie.prime_anc
            })
    }
    handleSave(){
        const {
            idm_depl, part_variable, perdiem, prime_anc} = this.state
        this.setState({
            disableSave: true,
            error: null,
        })
        let data = new FormData()
        if(idm_depl)
            data.append("idm_depl", idm_depl)
        if(part_variable)
            data.append("part_variable", part_variable)
        if(perdiem)
            data.append("perdiem", perdiem)
        if(prime_anc)
            data.append("prime_anc", prime_anc)

        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        axios.post(this.props.action, data)
        .then(({data}) => {
            if(data.error){
                const firstKey = Object.keys(data.error)[0]
                const firstValue = data.error[firstKey][0]
                this.setState({
                    error: {
                        key: firstKey,
                        value: firstValue
                    },
                    disableSave: false,
                })
            }
            else if(data){
                this.props.closeModal()
                this.props.updatePaie(data.agent_id)
            }
        })
        .finally(()=>{
            this.setState({
                disableSave: false
            })
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    
    render(){
        const {currentEtatPaie} = this.props
        const {disableSave, error, part_variable, idm_depl, perdiem, prime_anc} = this.state
        return (
            <div>
                <Modal 
                        disableSave={disableSave} 
                        width="lg" 
                        handleSave={this.handleSave} 
                        handleCancel={this.handleCancel}
                        error={error}
                    >
                    <h3>Etat de paie</h3>
                    <div className="overview-container">
                        <br/>
                        <b>Matricule : </b>
                        <span>
                            {
                                currentEtatPaie.societe_id == 1 ? 'DGM-' + currentEtatPaie.agent.numero_employe :
                                currentEtatPaie.societe_id == 2 ? 'SOIT-' + currentEtatPaie.agent.num_emp_soit :
                                currentEtatPaie.societe_id == 3 ? 'ST-' + currentEtatPaie.agent.numero_stagiaire :
                                currentEtatPaie.societe_id == 4 ? 'SM' :
                                currentEtatPaie.agent.numero_employe ? currentEtatPaie.agent.numero_employe :
                                currentEtatPaie.agent.numero_stagiaire ? currentEtatPaie.agent.numero_stagiaire :
                                <span className="purple">Non définie</span>
                            }
                        </span><br/>
                        <b>Nom : </b>{currentEtatPaie.agent.nom}<br/>
                        <b>Site : </b> {currentEtatPaie.site}<br/>
                        <b>Fonction : </b> {currentEtatPaie.fonction}<br/>
                    </div>
                    <div className="table">
                        <div className="row">
                            <div className="cell-50">
                                <div className="input-container">
                                    <label>Salaire de base</label>
                                    <input type="number" disabled={true} value={currentEtatPaie.sal_base}/>
                                </div>
                            </div>
                            <div className="cell-50">
                                <div className="input-container">
                                    <label>Part variable</label>
                                    <input type="number" onChange={this.handleChangePartVariable} value={part_variable}/>
                                </div>
                            </div>
                        </div>
                        <div className="row">
                            <div className="cell-50">
                                <div className="input-container">
                                    <label>Indemnité de déplacement</label>
                                    <input type="number" onChange={this.handleChangeIdmDepl} value={idm_depl}/>
                                </div>
                            </div>
                            <div className="cell-50">
                                <div className="input-container">
                                    <label>Perdiem</label>
                                    <input type="number" onChange={this.handleChangePerdiem} value={perdiem}/>
                                </div>
                            </div>
                        </div>
                        <div className="row">
                            <div className="cell-50">
                                <div className="input-container">
                                    <label>Prime d'ancienneté</label>
                                    <input type="number" onChange={this.handleChangePrimeAnc} value={prime_anc}/>
                                </div>
                            </div>
                            <div className="cell-50">
                            </div>
                        </div>
                    </div>
                </Modal>
            </div>
        )
    }
}