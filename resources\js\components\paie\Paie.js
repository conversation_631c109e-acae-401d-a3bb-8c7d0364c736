import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'
import SelectGroupSite from './select/SelectGroupSite'

import './paie.css'
import DataTable from './dataTable/DataTable'

export default class Paie extends Component {
    constructor(props){
        super(props)
        this.state = {
            currentDate: null,
            group: null,
            month: null,
            year: null,
            etatPaies: [],
            fonctions: [],
            agences: [],
            beginDate: null,
            endDate: null,
            selectedGroup: null,
            selectedMonth: null,
            selectedYear: null,
            currentEtatPaie: null,
            enableConfirm: false,
        }
        this.toggleLoading = this.toggleLoading.bind(this)
        this.hideGroupPointage = this.hideGroupPointage.bind(this)
        this.clickItem = this.clickItem.bind(this)
        this.toggleSelectItem = this.toggleSelectItem.bind(this)
        this.handleClickGroup = this.handleClickGroup.bind(this)
        this.handleClickMonth = this.handleClickMonth.bind(this)
        this.handleClickYear = this.handleClickYear.bind(this)
        this.updateData = this.updateData.bind(this)
        this.updatePaie = this.updatePaie.bind(this)
        this.updateConfirm = this.updateConfirm.bind(this)
        this.updateCancelHour = this.updateCancelHour.bind(this)
        this.closeModal = this.closeModal.bind(this)
        this.handleDoubleClickAgent = this.handleDoubleClickAgent.bind(this)
    }
    updateCancelHour(){
        let {etatPaies, currentEtatPaie} = this.state
        etatPaies.map((ep) => {
            if(ep.agent_id == currentEtatPaie.agent_id)
                ep.confirm_hour = false
        })
        this.setState({
            etatPaies: etatPaies,
            currentEtatPaie: null,
        })
    }
    updateConfirm(){
        let {etatPaies, currentEtatPaie} = this.state
        etatPaies.map((ep) => {
            if(ep.agent_id == currentEtatPaie.agent_id)
                ep.confirm = true
        })
        this.setState({
            etatPaies: etatPaies,
            currentEtatPaie: null,
        })
    }
    closeModal(){
        this.setState({
            currentEtatPaie: null
        })
    }
    handleDoubleClickAgent(ep){
        this.setState({
            currentEtatPaie: ep
        })
    }
    toggleSelectItem(){
        this.setState({
            currentEtatPaie: null,
            showSelectItem: !this.state.showSelectItem
        })
    }
    hideGroupPointage(){
        const {selectedGroup, selectedMonth, selectedYear} = this.state 
        this.setState({
            showSelectItem: false,
            group: selectedGroup,
            month: selectedMonth,
            year: selectedYear,
        })
    }
    toggleLoading(load){
        this.props.toggleLoading(load)

    }
    clickItem(type, value){
        this.setState({
            currentItemType: type,
            currentItem: value,
            searchSite: '',
            filterStatus: '',
            showSelectItem: false
        }, () => {
        })
    }
    setEtatPaie(ep){
        ep.heure_dim = 0
        ep.heure_nuit = 0
        ep.heure_ferie = 0
        ep.heure_reclam = 0
        if(ep.paie && ep.paie.confirm){
            ep.confirm = true
            ep.nb_heure_convenu = ep.paie.nb_heure_convenu
            ep.nb_heure_contrat = ep.paie.nb_heure_contrat
            ep.nb_heure = ep.paie.nb_heure_convenu ? ep.paie.nb_heure_convenu : ep.paie.nb_heure_contrat
            ep.sal_base = ep.paie.sal_base
            ep.societe_id = ep.paie.societe_id
            ep.fonction = ep.paie.fonction
            ep.prime_ex = ep.paie.prime_ex
            ep.prime_div = ep.paie.prime_div
            ep.idm_depl = ep.paie.idm_depl
            ep.prime_ass = ep.paie.prime_ass
            ep.prime_resp = ep.paie.prime_resp
            ep.prime_entr = ep.paie.prime_entr
            ep.prime_anc = ep.paie.prime_anc
        }
        else{
            ep.confirm = false
            ep.nb_heure_convenu = ep.agent.nb_heure_convenu
            ep.nb_heure_contrat = ep.agent.nb_heure_contrat
            ep.nb_heure = ep.agent.nb_heure_convenu ? ep.agent.nb_heure_convenu : ep.agent.nb_heure_contrat
            ep.sal_base = ep.agent.sal_base
            ep.societe_id = ep.agent.societe_id
            ep.fonction = ep.agent.fonction
            ep.prime_ex = ep.agent.prime_ex
            ep.prime_div = ep.agent.prime_div
            ep.idm_depl = ep.agent.idm_depl
            ep.prime_ass = ep.agent.prime_ass
            ep.prime_resp = ep.agent.prime_resp
            ep.prime_entr = ep.agent.prime_entr
            ep.prime_anc = ep.agent.prime_anc
        }

        if(ep.paie && ep.paie.confirm_hour){
            ep.confirm_hour = true
            ep.site_id = ep.paie.site_id
            ep.site = ep.paie.site
            ep.heure_trav = ep.paie.heure_trav
            ep.heure_reclam = ep.paie.heure_reclam
            ep.sal_forfait = ep.paie.sal_forfait
            if(!ep.paie.sal_forfait){
                ep.heure_dim = ep.paie.heure_dim
                ep.heure_nuit = ep.paie.heure_nuit
                ep.heure_ferie = ep.paie.heure_ferie
            }
        }
        else{
            ep.confirm_hour = false
            ep.site_id = ep.agent.site_id
            ep.site = ep.agent.site
            ep.heure_trav = ep.agent.heure_trav
            ep.heure_reclam = ep.agent.heure_reclam
            ep.sal_forfait = ep.agent.sal_forfait
            if(!ep.agent.sal_forfait){
                ep.heure_dim = ep.agent.heure_dim
                ep.heure_nuit = ep.agent.heure_nuit
                ep.heure_ferie = ep.agent.heure_ferie
            }
        }
        
        if(ep.paie && (ep.paie.confirm || ep.paie.edited)){
            ep.perdiem = ep.paie.perdiem
            ep.part_variable = ep.paie.part_variable
            ep.idm_depl = ep.paie.idm_depl
            ep.prime_anc = ep.paie.prime_anc
        }
        else{
            ep.perdiem = ep.agent.perdiem
            ep.part_variable = ep.agent.part_variable
            ep.idm_depl = ep.agent.idm_depl
            ep.prime_anc = ep.agent.prime_anc
        }

        ep.numSort = 
            ep.societe_id == 1 ? '1-' + ('0000' + ep.agent.numero_employe).slice(-5) :
            ep.societe_id == 2 ? '2-' + ('0000' + ep.agent.num_emp_soit).slice(-5) : 
            ep.societe_id == 3 ? '3-' + ('0000' + ep.agent.numero_stagiaire).slice(-5) :
            ep.societe_id == 4 ? '4-SM' :
            ep.agent.numero_employe ? '5-' + ('0000' + ep.agent.numero_employe).slice(-5) :
            ep.agent.numero_stagiaire ? '5-' + ('0000' + ep.agent.numero_stagiaire).slice(-5) :
            '7-Ndf'
        ep.diffHCHT = ep.heure_trav - ep.nb_heure
        ep.salMens = (ep.diffHCHT == -12 || ep.heure_trav > ep.nb_heure) ? ep.sal_base : ep.sal_base*ep.heure_trav/ep.nb_heure
        ep.heureSup = (ep.heure_trav > ep.nb_heure) ? ep.heure_trav - ep.nb_heure : 0
        ep.heureSup30 = ep.heureSup > 33.6 ? 33.6 : ep.heureSup
        ep.mHeureSup30 = ep.nb_heure ? ep.sal_base / ep.nb_heure * ep.heureSup30 * 0.3 : 0
        ep.heureSup50 = ep.heureSup - ep.heureSup30
        ep.mHeureSup50 = ep.nb_heure ? ep.sal_base / ep.nb_heure * ep.heureSup50 * 0.5 : 0
        ep.mMajferie = ep.nb_heure ? ep.sal_base / ep.nb_heure * ep.heure_ferie : 0
        ep.mMajDim = ep.nb_heure ? ep.sal_base / ep.nb_heure * ep.heure_dim * 0.4 : 0
        ep.mMajNuit = ep.nb_heure ? ep.sal_base / ep.nb_heure * ep.heure_nuit * 0.3 : 0
        ep.totalMaj = ep.mHeureSup30 + ep.mHeureSup50 + ep.mMajferie + ep.mMajDim + ep.mMajNuit
        ep.pExProrata = (ep.heure_trav <= ep.nb_heure) ? ep.prime_ex : ep.prime_ex / ep.nb_heure * ep.heure_trav
        ep.pDivProrata = (ep.heure_trav <= ep.nb_heure) ? ep.prime_div : ep.prime_div / ep.nb_heure * ep.heure_trav
        ep.idmDeplProrata = (ep.heure_trav <= ep.nb_heure) ? ep.idm_depl : ep.idm_depl / ep.nb_heure * ep.heure_trav
        ep.pAssProrata = (ep.heure_trav <= ep.nb_heure) ? ep.prime_ass : ep.prime_ass / ep.nb_heure * ep.heure_trav
        ep.pRespProrata = (ep.heure_trav <= ep.nb_heure) ? ep.prime_resp : ep.prime_resp / ep.nb_heure * ep.heure_trav
        ep.pEntrProrata = (ep.heure_trav <= ep.nb_heure) ? ep.prime_entr : ep.prime_entr / ep.nb_heure * ep.heure_trav
        ep.pAncProrata = (ep.heure_trav <= ep.nb_heure) ? ep.prime_anc : ep.prime_anc / ep.nb_heure * ep.heure_trav
        ep.totalProGrat = ep.pExProrata + ep.pDivProrata + ep.pRespProrata + ep.idmDeplProrata + ep.pAssProrata + ep.pEntrProrata + ep.pAncProrata
        ep.rappelle = (ep.diffHCHT >= -12) ? ep.sal_base : ep.sal_base*ep.heure_reclam/ep.nb_heure
        return ep
    }
    updatePaie(id){
        const {group, year, month} = this.state
        this.toggleLoading(true)
        axios.get('/api/paies/show/' + id + '/' + year + '/' + month
            + '?username=' + localStorage.getItem("username") + '&secret=' + localStorage.getItem("secret"))
        .then(({data}) => {
            if(data){
                let currentEtatPaie = {}
                let etatPaies = this.state.etatPaies
                let {pointages, reclamations, jour_feries, primes} = data
                for (let i = 0; i < etatPaies.length; i++) {
                    if(etatPaies[i].agent_id == data.agent.id){
                        if((data.paie && data.paie.confirm_hour && group != data.paie.group_id)
                         || (!data.paie && group != data.agent.group_id)){
                            etatPaies.splice(i, 1) 
                            this.setState({
                                etatPaies: etatPaies,
                                currentEtatPaie: null,
                            }, () => {
                                this.toggleLoading(false)
                            })
                        }
                        else{
                            currentEtatPaie.reclamable = data.reclamable
                            currentEtatPaie.isAfterConfirmable = data.is_after_confirmable
                            currentEtatPaie.beginDate = data.begin_date
                            currentEtatPaie.endDate = data.end_date
                            currentEtatPaie.agent_id = data.agent.id
                            currentEtatPaie.agent = data.agent
                            currentEtatPaie.pointages = data.pointages
                            currentEtatPaie.reclamations = data.reclamations
                            currentEtatPaie.primes = data.primes
                            if(data.paie){
                                currentEtatPaie.paie_id = data.paie.id
                                currentEtatPaie.paie = data.paie
                            }
                            if(!data.paie || (data.paie && !data.paie.confirm_hour)){
                                currentEtatPaie.agent.heure_trav = pointages.length * 12
                                currentEtatPaie.agent.heure_reclam = reclamations.length * 12
                                currentEtatPaie.agent.heure_nuit = 0
                                currentEtatPaie.agent.heure_dim = 0
                                currentEtatPaie.agent.heure_ferie = 0
                                for(let j=0; j<pointages.length; j++){
                                    if(moment(pointages[j].date_pointage).format('HH:mm:ss') == '18:00:00')
                                        currentEtatPaie.agent.heure_nuit += 12
                                    if(moment(pointages[j].date_pointage).day() == 0)
                                        currentEtatPaie.agent.heure_dim += 12
                                    jour_feries.forEach(jr => {
                                        if(moment(pointages[j].date_pointage).format('YYYY-MM-DD') == jr)
                                            currentEtatPaie.agent.heure_ferie += 12
                                    })
                                }
                            }
                            if(!data.paie || (data.paie && !data.paie.confirm)){
                                currentEtatPaie.agent.prime_ex = 0
                                currentEtatPaie.agent.prime_div = 0
                                currentEtatPaie.agent.prime_ass = 0
                                currentEtatPaie.agent.prime_resp = 0
                                currentEtatPaie.agent.prime_entr = 0
                                for(let j=0; j<primes.length; j++){
                                    const prime = primes[j]
                                    if(prime.type == 'prime_ex')
                                        currentEtatPaie.agent.prime_ex += prime.montant
                                    else if(prime.type == 'prime_div')
                                        currentEtatPaie.agent.prime_div += prime.montant
                                    else if(prime.type == 'prime_ass')
                                        currentEtatPaie.agent.prime_ass += prime.montant
                                    else if(prime.type == 'prime_resp')
                                        currentEtatPaie.agent.prime_resp += prime.montant
                                    else if(prime.type == 'prime_entr')
                                        currentEtatPaie.agent.prime_entr += prime.montant
                                }
                            }
                            console.log(currentEtatPaie)
                            currentEtatPaie = this.setEtatPaie(currentEtatPaie)
                            etatPaies[i] = currentEtatPaie 
                            this.setState({
                                etatPaies: etatPaies,
                                currentEtatPaie: currentEtatPaie,
                            }, () => {
                                this.toggleLoading(false)
                            })
                        }
                        break;
                    }
                }
            }
        })
        .catch(() => {
            this.toggleLoading(false)
        })
    }
    updateData(){
        console.log("updateData")
        this.setState({
            currentEtatPaie: null,
        })
        const {group, month, year} = this.state
        this.props.toggleLoading(true)
        axios.get('/api/paies?group=' + group + '&month=' + month + '&year=' + year).then(({data}) => {
            if(data){
                if(data.message){
                    this.setState({
                            message: data.message,
                            group: data.group,
                            month: data.month,
                            year: data.year,
                            selectedGroup: data.group,
                            selectedMonth: data.month,
                            selectedYear: data.year,
                            etatPaies: [],
                            beginDate: data.begin_date,
                            endDate: data.end_date,
                        },
                        () => {
                            this.props.toggleLoading(false)
                        }
                    )
                }
                else {
                    let etatPaies = []
                    let pointages = data.pointages;
                    let reclamations = data.reclamations;
                    let paies = data.paies
                    let primes = data.primes
                    for(let i=0; i<data.agents.length; i++){
                        let ep = {
                            confirm: false,
                            agent_id: data.agents[i].id,
                            agent: data.agents[i],
                        }
                        ep.agent.heure_trav = 0
                        ep.agent.heure_reclam = 0
                        ep.agent.heure_nuit = 0
                        ep.agent.heure_dim = 0
                        ep.agent.heure_ferie = 0
                        ep.agent.prime_ex = 0
                        ep.agent.prime_div = 0
                        ep.agent.prime_ass = 0
                        ep.agent.prime_resp = 0
                        ep.agent.prime_entr = 0
                        etatPaies.push(ep)
                    }
                    etatPaies.map((ep) => {
                        for(let i=0; i<pointages.length; i++){
                            if(pointages[i].agent_id == ep.agent_id){
                                ep.agent.heure_trav += 12
                                if(moment(pointages[i].date_pointage).format('HH:mm:ss') == '18:00:00')
                                    ep.agent.heure_nuit += 12
                                if(moment(pointages[i].date_pointage).day() == 0)
                                    ep.agent.heure_dim += 12
                                data.jour_feries.forEach(jr => {
                                    if(moment(pointages[i].date_pointage).format('YYYY-MM-DD') == jr)
                                        ep.agent.heure_ferie += 12
                                })
                                pointages.splice(i, 1)
                                i-=1
                            }
                        }
                        for(let i=0; i<reclamations.length; i++){
                            if(reclamations[i].agent_id == ep.agent_id){
                                ep.agent.heure_reclam += 12
                            }
                        }
                        for(let i=0; i<paies.length; i++){
                            if(paies[i].agent_id == ep.agent_id){
                                ep.paie_id = paies[i].id
                                ep.paie = paies[i]
                                paies.splice(i, 1)
                                i-=1
                            }   
                        }
                        for(let i=0; i<primes.length; i++){
                            if(primes[i].agent_id == ep.agent_id){
                                let prime = primes[i]
                                if(prime.type == "prime_ex")
                                    ep.agent.prime_ex += prime.montant
                                else if(prime.type == "prime_div")
                                    ep.agent.prime_div += prime.montant
                                else if(prime.type == "prime_ass")
                                    ep.agent.prime_ass += prime.montant
                                else if(prime.type == "prime_resp")
                                    ep.agent.prime_resp += prime.montant
                                else if(prime.type == "prime_entr")
                                    ep.agent.prime_entr += prime.montant
                            }
                        }
                    })
                    etatPaies.map((ep) => {
                        ep = this.setEtatPaie(ep)
                    })
                    etatPaies.sort((a, b) => {
                        if(a.numSort < b.numSort) return -1
                        if(a.numSort > b.numSort) return 1
                        return 0
                    })
                    etatPaies.sort((a, b) => {
                        if(a.site < b.site) return -1
                        if(a.site > b.site) return 1
                        return 0
                    })
                    this.setState({
                        message: '',
                        group: data.group,
                        month: data.month,
                        year: data.year,
                        selectedGroup: data.group,
                        selectedMonth: data.month,
                        selectedYear: data.year,
                        etatPaies: etatPaies,
                        fonctions: data.fonctions,
                        agences: data.agences,
                        beginDate: data.begin_date,
                        endDate: data.end_date,
                        enableConfirm: data.enable_confirm,
                    },
                    () => {
                        this.props.toggleLoading(false)
                    })
                }
            }
        })
        .catch((e) => {
            setTimeout(() =>{
                this.updateData()
                console.error(e)
            }, 10000)
        })
    }
    componentDidMount(){
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
        document.title = "Paie - TLS"
        this.updateData()
    }
    resize() {
        this.setState({
            heightWindow: window.innerHeight,
            widthWindow: window.innerWidth
        });
    }
    handleClickGroup(e, value){
        e.stopPropagation()
        this.setState({
            group: value
        })
    }
    handleClickMonth(e, m){
        e.stopPropagation()
        this.setState({
            month: m
        })
    }
    handleClickYear(e, y){
        e.stopPropagation()
        this.setState({
            year: y
        })
    }
    render(){
        const {message, currentEtatPaie, etatPaies, selectedGroup, group, month, year, beginDate, endDate, currentItem, 
            fonctions, agences, showSelectItem, heightWindow, widthWindow, enableConfirm} = this.state
        const {user} = this.props
        return  (
            <div className="table" onClick={() => {
                    this.closeModal()
                    this.hideGroupPointage()}
                }>
                <div id="tableContainer">
                    <div className="table">
                        <div className="row-header">
                            <h3 className="center">
                                {
                                    group && 
                                    <SelectGroupSite
                                        updateData={this.updateData}
                                        beginDate={beginDate}
                                        endDate={endDate}
                                        selectedGroup={selectedGroup}
                                        group={group}
                                        month={month}
                                        year={year}
                                        handleClickGroup={this.handleClickGroup}
                                        handleClickMonth={this.handleClickMonth}
                                        handleClickYear={this.handleClickYear}
                                        clickItem={this.clickItem}
                                        currentItem={currentItem}
                                        toggleSelect={this.toggleSelectItem}
                                        showItem={showSelectItem}
                                        enableConfirm={enableConfirm}/>
                                }
                            </h3>
                        </div>
                        <div style={{'height': (heightWindow - 200) + "px"}}>
                            {
                                message ?
                                    <h2 className="center secondary">{message}</h2>
                                :
                                    <DataTable 
                                        user={user}
                                        month={month}
                                        year={year}
                                        currentEtatPaie={currentEtatPaie}
                                        etatPaies={etatPaies} 
                                        fonctions={fonctions}
                                        agences={agences}
                                        closeModal={this.closeModal}
                                        handleDoubleClickAgent={this.handleDoubleClickAgent}
                                        updateData={this.updateData}
                                        updatePaie={this.updatePaie}
                                        updateConfirm={this.updateConfirm}
                                        updateCancelHour={this.updateCancelHour}
                                        heightWindow={heightWindow} 
                                        widthWindow={widthWindow}
                                        beginDate={beginDate}
                                        endDate={endDate}/>
                            }
                        </div>
                    </div>
                </div>
            </div>
        )
    }
}