import React, { Component } from 'react'
import axios from 'axios'
import ConfirmAgentModal from '../agent/ConfirmAgentModal'
import EditAgentModal from '../agent/EditAgentModal'
import ConfirmPaieModal from './ConfirmPaieModal'
import moment from 'moment'
import EditPaieModal from './EditPaieModal'
import CancelHourModal from './CancelHourModal'
import Pointage from '../hour/pointage/Pointage'
import PaieDetailTab from './tab/PaieDetailTab'
import Prime from '../agent/prime/Prime'
import Sanction from '../agent/sanction/Sanction'


export default class PaieDetail extends Component {
    constructor(props){
        super(props)
        this.state = {
            editAgent: null,
            showEditPaieModal:false,
            ********************: false,
            showCancelHourModal: false,
            showEditAgentModal: false,
            showArchiveAgentModal: false,
            showEditAgentMenu: false,
            activeTab: 'paie',
        }
        this.updatePaie= this.updatePaie.bind(this)
        this.updateData = this.updateData.bind(this)
        this.closeModal = this.closeModal.bind(this)
        this.handleClickArchiveAgent = this.handleClickArchiveAgent.bind(this)
        this.handleClickEditAgent = this.handleClickEditAgent.bind(this)
        this.handleClickConfirmAgent = this.handleClickConfirmAgent.bind(this)
        this.updateConfirm = this.updateConfirm.bind(this)
        this.updateCancelHour = this.updateCancelHour.bind(this)
        this.handleClickEditPaie = this.handleClickEditPaie.bind(this)
        this.handleClickCancelHour = this.handleClickCancelHour.bind(this)
        this.handleChangeTab = this.handleChangeTab.bind(this)
    }
    handleChangeTab(e){
        this.setState({
            activeTab: e.target.id
        })
    }
    handleClickCancelHour(){
        this.setState({
            showCancelHourModal: true
        })
    }
    handleClickEditPaie(){
        this.setState({
            showEditPaieModal: true
        })
    }
    updateConfirm(){
        this.props.updateConfirm()
    }
    updateCancelHour(){
        this.props.updateCancelHour()
    }
    updatePaie(id){
        this.props.updatePaie(id)
    }

    handleClickConfirmAgent(){
        this.setState({
            ********************: true
        })
    }
    handleClickEditAgent(){
        this.setState({
            showEditAgentModal: true
        })
    }
    handleClickArchiveAgent(){
        this.setState({
            showArchiveAgentModal: true
        })
    }
    closeModal(){
        this.setState({
            showEditAgentModal: false,
            showArchiveAgentModal: false,
            ********************: false,
            showEditPaieModal: false,
            showCancelHourModal: false,
        })
    }
    updateData(){
        this.props.updateData()
    }
    toggleEditAgentMenu(e, value){
        e.stopPropagation()
        this.setState({
            showEditAgentMenu: value
        })
    }
    loadCurrentEtatPaie(){
        const {currentEtatPaie} = this.props
        const url = '/api/agents/show/' + currentEtatPaie.agent_id
            + '?username=' + localStorage.getItem("username") + '&secret=' + localStorage.getItem("secret")
        axios.get(url)
        .then(({data}) => {
            console.log(data)
            if(data){
                this.setState({
                    editAgent: data.agent,
                })
            }
        })
        .catch(() => {
            this.toggleLoading(false)
        })
    }
    componentDidMount(){
        this.loadCurrentEtatPaie()
    }
    getPlageDatePaie(){
        const {beginDate, endDate} = this.props
        return 'Du ' + moment(beginDate).format("DD MMM YYYY") + ' au ' + moment(endDate).format("DD MMM YYYY")
    }
    render(){
        const {fonctions, agences, user, heightWindow, currentEtatPaie, month, year} = this.props
        const {activeTab, editAgent, showEditPaieModal, showEditAgentMenu, showEditAgentModal, showArchiveAgentModal, ********************, showCancelHourModal} = this.state

        return (
            <div id="paieDetail" style={{height: heightWindow}} onClick={(e) => {
                    e.stopPropagation()
                    this.toggleEditAgentMenu(e, false) 
                }}>
                {showEditPaieModal && <EditPaieModal
                    action={'/api/paies/update/' + currentEtatPaie.agent_id + '/' + year + '/' + month}
                    closeModal={this.closeModal}
                    currentEtatPaie={currentEtatPaie}
                    updatePaie={this.updatePaie}
                    plageDate={this.getPlageDatePaie()}/>
                }
                {******************** && <ConfirmPaieModal
                    action={'/api/paies/confirm/' + currentEtatPaie.agent_id + '/' + year + '/' + month}
                    closeModal={this.closeModal}
                    paie={currentEtatPaie}
                    updateConfirm={this.updateConfirm}
                    plageDate={this.getPlageDatePaie()}/>
                }
                {showCancelHourModal && <CancelHourModal
                    action={'/api/hours/cancel_confirmation/' + currentEtatPaie.paie_id}
                    closeModal={this.closeModal}
                    paie={currentEtatPaie}
                    updateCancelHour={this.updateCancelHour}
                    plageDate={this.getPlageDatePaie()}/>
                }
                {showEditAgentModal && <EditAgentModal
                    action={'/api/agents/update/' + editAgent.id}
                    closeModal={this.closeModal}
                    updateAgent={this.updatePaie}
                    fonctions={fonctions}
                    agent={editAgent}
                    agences={agences}/>
                }
                {showArchiveAgentModal && <ConfirmAgentModal
                    action={'/api/agents/soft_delete/' + editAgent.id}
                    closeModal={this.closeModal}
                    updateData={this.updateData}
                    nom={editAgent.nom}/>
                }
                <div className="overview-container">
                    <div className="head-title-overview" title={currentEtatPaie.agent.nom}>
                        <div style={{height:"40px", lineHeight:"40px" }}>
                            <div className="title-overview">
                                <span style={{opacity: .9}}>
                                    {
                                        currentEtatPaie.societe_id == 1 ? 'DGM-' + currentEtatPaie.agent.numero_employe :
                                        currentEtatPaie.societe_id == 2 ? 'SOIT-' + currentEtatPaie.agent.num_emp_soit :
                                        currentEtatPaie.societe_id == 3 ? 'ST-' + currentEtatPaie.agent.numero_stagiaire :
                                        currentEtatPaie.societe_id == 4 ? 'SM' :
                                        currentEtatPaie.agent.numero_employe ? currentEtatPaie.agent.numero_employe :
                                        currentEtatPaie.agent.numero_stagiaire ? currentEtatPaie.agent.numero_stagiaire :
                                        <span className="purple">Non définie</span>
                                    }
                                </span>
                            </div>
                            <div className="overview-edit-icon">
                                {
                                    ['rh', 'root'].includes(user.role) && 
                                    <img onClick={(e) => {this.toggleEditAgentMenu(e, !showEditAgentMenu)}} className="overview-edit-img" src="/img/parametre.svg"/>
                                }
                                {
                                    showEditAgentMenu &&
                                    <div className="dropdown-overview-edit">
                                        { currentEtatPaie.confirm && <span onClick={this.handleClickNotificationAgent}>Imprimer la fiche de paie</span>}
                                        { (!currentEtatPaie.confirm && currentEtatPaie.confirm_hour) && <span onClick={this.handleClickCancelHour}>Annuler le calcul d'heure</span>}
                                        { (!currentEtatPaie.confirm && currentEtatPaie.confirm_hour) && <span onClick={this.handleClickConfirmAgent}>Confirmer l'état de paie</span>}
                                        { !currentEtatPaie.confirm && <span onClick={this.handleClickEditPaie}>Modifier l'état de paie</span>}
                                        { (!currentEtatPaie.confirm && !currentEtatPaie.confirm_hour && editAgent) && <span onClick={this.handleClickEditAgent}>Modifier l'agent</span>}
                                        { (!currentEtatPaie.confirm && !currentEtatPaie.confirm_hour && editAgent) && <span onClick={this.handleClickArchiveAgent}>Mettre en archive</span>}
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                    <span className="overview-break-overflow" title={currentEtatPaie.agent.nom}>
                        <b>Nom : </b>{currentEtatPaie.agent.nom}
                    </span><br/>
                    <b>Site : </b> {currentEtatPaie.site}<br/>
                    <b>Fonction : </b> {currentEtatPaie.fonction}<br/>
                    <b>Agence : </b> {currentEtatPaie.agent.agence}<br/>
                    <b>Date d'embauhe : </b> 
                    {
                        currentEtatPaie.agent.date_embauche ? moment(currentEtatPaie.agent.date_embauche).format('DD MMM YYYY') : 
                        moment(currentEtatPaie.agent.date_confirmation).isBefore(moment(currentEtatPaie.agent.date_conf_soit)) ? moment(currentEtatPaie.agent.date_confirmation).format('DD MMM YYYY') :
                        moment(currentEtatPaie.agent.date_conf_soit).format('DD MMM YYYY')
                    }
                    <br/>
                </div>
                <div style={{position: 'relative', top: '2px'}}>
                    <div className="table">
                        <div className="cell">
                            <div id="tabHeaderOverview">
                                <ul>
                                    <li id="paie" className={activeTab == 'paie' ? "active-tab" : ""} onClick={this.handleChangeTab}>Paie</li>
                                </ul>
                                <ul>
                                    <li id="sanction" className={activeTab == 'sanction' ? "active-tab" : ""} onClick={this.handleChangeTab}>Sanction</li>
                                </ul>
                                <ul>
                                    <li id="prime" className={activeTab == 'prime' ? "active-tab" : ""} onClick={this.handleChangeTab}>Prime</li>
                                </ul>
                                {
                                    (!currentEtatPaie.isAfterConfirmable || currentEtatPaie.reclamable) &&
                                    <ul>
                                        <li id="reclamation" className={activeTab == 'reclamation' ? "active-tab" : ""} onClick={this.handleChangeTab}>Réclamation</li>
                                    </ul>
                                }
                                <ul>
                                    <li id="pointage" className={activeTab == 'pointage' ? "active-tab" : ""} onClick={this.handleChangeTab}>Pointage</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="tabContentOverview">
                    <div id="tabContainer">
                        {
                            activeTab == 'paie' &&
                            <PaieDetailTab 
                                heightWindow={heightWindow} 
                                currentEtatPaie={currentEtatPaie}/>
                        }
                        {
                            activeTab == 'sanction' &&
                            <Sanction 
                                paieDate={moment(currentEtatPaie.endDate).format('YYYY-MM-DD')}
                                confirmed={currentEtatPaie.confirm}
                                agentId={currentEtatPaie.agent_id} 
                                heightWindow={heightWindow}/>
                        }
                        {
                            activeTab == 'prime' &&
                            <Prime 
                                paieDate={moment(currentEtatPaie.endDate).format('YYYY-MM-DD')}
                                confirmed={currentEtatPaie.confirm}
                                agentId={currentEtatPaie.agent_id} 
                                heightWindow={heightWindow}/>
                        }
                        {
                            activeTab == 'pointage' &&
                            <Pointage
                                isAfterConfirmable={currentEtatPaie.isAfterConfirmable}
                                agentId={currentEtatPaie.agent_id}
                                pointages={currentEtatPaie.pointages}
                                heightWindow={heightWindow} 
                                updateHour={this.updateHour}
                                isConfirmed={true}
                                intervalDate={'Du '
                                     + moment(currentEtatPaie.beginDate).format('ddd DD MMM YYYY') + ' au '
                                     + moment(currentEtatPaie.endDate).format('ddd DD MMM YYYY')
                                }/>
                        }
                        {
                            activeTab == "reclamation" &&
                            <Pointage
                                reclamation="1"
                                isAfterConfirmable={currentEtatPaie.isAfterConfirmable}
                                reclamable = {currentEtatPaie.reclamable}
                                agentId={currentEtatPaie.agent_id}
                                pointages={currentEtatPaie.reclamations} 
                                heightWindow={heightWindow} 
                                updateHour={this.updateHour}
                                isConfirmed={true}
                                intervalDate={'Du '
                                     + moment(currentEtatPaie.beginDate).subtract(1, 'month').format('ddd DD MMM YYYY') + ' au '
                                     + moment(currentEtatPaie.beginDate).subtract(1, 'day').format('ddd DD MMM YYYY')
                                }/>
                        }
                    </div>
                </div>
            </div>
        )
    }
}