import React, { Component } from 'react'
import $ from 'jquery'

import './dataTable.css'
import PaieDetail from '../PaieDetail';

export default class DataTable extends Component {
    constructor(props){
        super(props)
        this.state = {
            selectedAgentId: 0,
            searchAgent: '',
            searchTimeoutId: 0,
        }
        this.showAgent = this.showAgent.bind(this)
        this.handleChangeSearchAgent = this.handleChangeSearchAgent.bind(this)
        this.handleClickRefresh = this.handleClickRefresh.bind(this)
        this.updatePaie = this.updatePaie.bind(this)
        this.handleDoubleClickAgent = this.handleDoubleClickAgent.bind(this)
        this.updateConfirm = this.updateConfirm.bind(this)
        this.updateCancelHour = this.updateCancelHour.bind(this)
    }
    updateCancelHour(){
        this.props.updateCancelHour()
    }
    updateConfirm(){
        this.props.updateConfirm()
    }
    updatePaie(id){
        this.props.updatePaie(id)
    }
    handleDoubleClickAgent(a){
        this.props.updatePaie(a.id)
    }
    handleClickRefresh(){
        this.props.updateData()
        this.setState({
            searchAgent: '',
        })
    }
    handleChangeSearchAgent(e){
        this.setState({
            inputSearch: e.target.value
        }, () => {
            const {searchTimeoutId, inputSearch} = this.state
            if(searchTimeoutId)
                clearTimeout(searchTimeoutId)
            const timeoutId = setTimeout(() => {
                this.setState({
                    searchAgent: inputSearch
                })
            }, 1000)
            this.setState({
                searchTimeoutId: timeoutId
            })
        })
    }
    showAgent(agent){
        const {searchAgent} = this.state
        if(searchAgent){
            const search = searchAgent.toLocaleLowerCase().replace(/[.*+?^{}()|[\]\\]/g, '\\$&')
            let patt = new RegExp(search)
            const matricule = 
                agent.societe_id == 1 ? 'DGM-' + agent.numero_employe :
                agent.societe_id == 2 ? 'SOIT-' + agent.num_emp_soit : 
                agent.societe_id == 3 ? 'ST-' + agent.numero_stagiaire :
                agent.societe_id == 4 ? 'SM' :
                agent.numero_employe ? agent.numero_employe :
                agent.numero_stagiaire ? agent.numero_stagiaire :
                'Ndf'
            if(agent.numero_employe && patt.test(agent.numero_employe.toLocaleLowerCase()))
                return true
            else if(agent.num_emp_soit && patt.test(agent.num_emp_soit.toLocaleLowerCase()))
                return true
            else if(agent.numero_stagiaire && patt.test(agent.numero_stagiaire.toLocaleLowerCase()))
                return true
            else if(agent.societe_id == 4 && patt.test('SM'))
                return true
            else if(!agent.numero_employe && !agent.numero_stagiaire && patt.test('Ndf'))
                return true
            else if(patt.test(matricule))
                return true
            return false
        }
        return true
    }
    numberWithSpace(x) {
        if(x)
            return Number.parseInt(x).toString().replace(/\B(?=(\d{3})+(?!\d))/g, " ")
        return 0
    }
    handleClickAgent(agentId){
        this.setState({
            selectedAgentId: agentId
        })
    }
    componentDidMount(){
        this.$hScroll = $(this.hScroll)
        this.$vScroll = $(this.vScroll)
        this.$hScroll.scroll(() => {
            this.$vScroll.width(this.$hScroll.width() + this.$hScroll.scrollLeft());
        });
    }
    render(){
        const {agences, fonctions, user, currentEtatPaie, etatPaies, heightWindow, widthWindow, 
            beginDate, endDate, month, year} = this.props
        const {selectedAgentId, inputSearch} = this.state

        return (
            <div>
                <div>
                    <div className="filterSide">
                        <input className="input-fixed" placeholder="Rechercher" value={inputSearch} onChange={this.handleChangeSearchAgent}/>
                    </div>
                    <div className="filterSide right">
                        <img id="refreshPointageBtn" src="/img/order_desc.svg" onClick={() => this.props.updateData()}/>
                        <img id="refreshPointageBtn" src="/img/refresh.svg" onClick={this.handleClickRefresh}/>
                    </div>
                </div>
                {
                    currentEtatPaie &&
                    <PaieDetail
                        month={month}
                        year={year}
                        beginDate={beginDate}
                        endDate={endDate}
                        user={user}
                        agences={agences}
                        fonctions={fonctions}
                        updateData={this.handleClickRefresh}
                        updatePaie={this.updatePaie}
                        updateConfirm={this.updateConfirm}
                        updateCancelHour={this.updateCancelHour}
                        currentEtatPaie={currentEtatPaie}
                        heightWindow={heightWindow} 
                        widthWindow={widthWindow}/>
                }
                <div className="table-container-relative" style={{
                        width: (widthWindow - 290),
                        maxHeight: (heightWindow - 180),
                    }}>
                    <table className="fixed-column default">
                        <thead>
                            <tr>
                                <th className="cellConfirm"></th>
                                <th className="cellNum">Num.</th>
                                <th className="cellNom">Nom</th>
                                <th className="cellNom">Site</th>
                                <th className="cellHContrat center">H contrat</th>
                                <th className="cellHConv center">H conv.</th>
                                <th className="cellHTrav center">H trav.</th>
                                <th className="cellDiffHCHT center">Diff. HC/HT</th>
                                <th className="cellSalBase center">Sal. Base</th>
                                <th className="cellSalMens center">Sal. Mensuel</th>
                                <th className="cellHS center">HS</th>
                                <th className="cellHS center">HS30%</th>
                                <th className="cellHSMaj center">MH.S 30%</th>
                                <th className="cellHS center">HS50%</th>
                                <th className="cellHSMaj center">MH.S 50%</th>
                                {/*
                                <th className="cellHS center">H Ferié</th>
                                <th className="cellHSMaj center">Mmajferié</th>
                                <th className="cellHS center">HMdim</th>
                                <th className="cellHSMaj center">MMajdim</th>
                                <th className="cellHS center">HMajnui</th>
                                <th className="cellHSMaj center">MMajnui</th>
                                */}
                                <th className="cellHSMaj center">Total Maj</th>
                                <th className="cellHSMaj center">Prime excep.</th>
                                <th className="cellHSMaj center">P ex. Prorata</th>
                                <th className="cellHSMaj center">Prime div.</th>
                                <th className="cellHSMaj center">P div. Prorata</th>
                                <th className="cellHSMaj center">Idm depl.</th>
                                <th className="cellHSMaj center">I depl Prorata</th>
                                <th className="cellHSMaj center">Prime ass.</th>
                                <th className="cellHSMaj center">P ass. Prorata</th>
                                <th className="cellHSMaj center">Prime resp.</th>
                                <th className="cellHSMaj center">P resp. Prorata</th>
                                <th className="cellHSMaj center">Prime entr.</th>
                                <th className="cellHSMaj center">P entr. Prorata</th>
                                <th className="cellHSMaj center">Prime anc.</th>
                                <th className="cellHSMaj center">P anc. Prorata</th>
                                <th className="cellHSMaj center">Total Pro{'&'}Grat</th>
                                <th className="cellHSMaj center">H reclam.</th>
                                <th className="cellHSMaj center">Rappelle</th>
                            </tr>
                        </thead>
                        <tbody>
                            {
                                etatPaies &&
                                etatPaies.map((ep) => {
                                    if(this.showAgent(ep.agent))
                                        return <tr 
                                                className={(selectedAgentId == ep.agent_id ? 'selected-row' : '') + (ep.confirm ? ' confirmed': '')} 
                                                key={ep.agent_id} 
                                                onClick={() => this.handleClickAgent(ep.agent_id)} 
                                                onDoubleClick={() => this.updatePaie(ep.agent_id)}
                                        >
                                            <td className="cellConfirm">
                                                { 
                                                    ep.confirm ? <span className="checkmark-label"></span>
                                                    : !ep.confirm_hour ? <span className="circle-label"></span>
                                                    : null }
                                            </td>
                                            <td className="cellNum">
                                                {
                                                    ep.societe_id == 1 ? 'DGM-' + ep.agent.numero_employe :
                                                    ep.societe_id == 2 ? 'SOIT-' + ep.agent.num_emp_soit : 
                                                    ep.societe_id == 3 ? 'ST-' + ep.agent.numero_stagiaire :
                                                    ep.societe_id == 4 ? 'SM' :
                                                    ep.agent.numero_employe ? ep.agent.numero_employe :
                                                    ep.agent.numero_stagiaire ? ep.agent.numero_stagiaire :
                                                    <span className="purple">Ndf</span>
                                                }
                                            </td>
                                            <td className="cellNom">{ep.agent.nom}</td>
                                            <td className="cellNom">{ep.site}</td>
                                            <td className="cellHContrat">{ep.nb_heure_contrat}</td>
                                            <td className="cellHContrat">{ep.nb_heure_convenu}</td>
                                            <td className="cellHTrav">{ep.heure_trav}</td>
                                            <td className="cellDiffHCHT">{ep.diffHCHT}</td>
                                            <td className="cellSalBase">{this.numberWithSpace(ep.sal_base)}</td>
                                            <td className="cellSalMens">{this.numberWithSpace(ep.salMens)}</td>
                                            <td className="cellHS">{ep.heureSup}</td>
                                            <td className="cellHS">{ep.heureSup30}</td>
                                            <td className="cellHSMaj">{this.numberWithSpace(ep.mHeureSup30)}</td>
                                            <td className="cellHS">{ep.heureSup50}</td>
                                            <td className="cellHSMaj">{this.numberWithSpace(ep.mHeureSup50)}</td>
                                            {/*
                                            <td className="cellHS">{ep.heure_ferie}</td>
                                            <td className="cellHSMaj">{this.numberWithSpace(ep.mMajferie)}</td>
                                            <td className="cellHS">{ep.heure_dim}</td>
                                            <td className="cellHSMaj">{ep.mMajDim && this.numberWithSpace(ep.mMajDim)}</td>
                                            <td className="cellHS">{ep.heure_nuit}</td>
                                            <td className="cellHSMaj">{this.numberWithSpace(ep.mMajNuit)}</td>
                                            */}
                                            <td className="cellHSMaj">{this.numberWithSpace(ep.totalMaj)}</td>
                                            <td className="cellHSMaj">{this.numberWithSpace(ep.prime_ex)}</td>
                                            <td className="cellHSMaj">{this.numberWithSpace(ep.pExProrata)}</td>
                                            <td className="cellHSMaj">{this.numberWithSpace(ep.prime_div)}</td>
                                            <td className="cellHSMaj">{this.numberWithSpace(ep.pDivProrata)}</td>
                                            <td className="cellHSMaj">{this.numberWithSpace(ep.idm_depl)}</td>
                                            <td className="cellHSMaj">{this.numberWithSpace(ep.idmDeplProrata)}</td>
                                            <td className="cellHSMaj">{this.numberWithSpace(ep.prime_ass)}</td>
                                            <td className="cellHSMaj">{this.numberWithSpace(ep.pAssProrata)}</td>
                                            <td className="cellHSMaj">{this.numberWithSpace(ep.prime_resp)}</td>
                                            <td className="cellHSMaj">{this.numberWithSpace(ep.pRespProrata)}</td>
                                            <td className="cellHSMaj">{this.numberWithSpace(ep.prime_entr)}</td>
                                            <td className="cellHSMaj">{this.numberWithSpace(ep.pEntrProrata)}</td>
                                            <td className="cellHSMaj">{this.numberWithSpace(ep.prime_anc)}</td>
                                            <td className="cellHSMaj">{this.numberWithSpace(ep.pAncProrata)}</td>
                                            <td className="cellHSMaj">{this.numberWithSpace(ep.totalProGrat)}</td>
                                            <td className="cellHS">{ep.heure_reclam}</td>
                                            <td className="cellHSMaj">{this.numberWithSpace(ep.rappelle)}</td>
                                        </tr>
                                })
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        )
    }
}