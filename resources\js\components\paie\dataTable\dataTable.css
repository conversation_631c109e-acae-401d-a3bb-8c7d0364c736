.table-container-relative {
  margin: auto;
  position: relative;
  z-index: 1;
  overflow: scroll;
}
table.fixed-column td, table.fixed-column th {
  box-sizing: border-box;
  font-family: "CallingCode";
}

table.fixed-column thead th{
  font-family: "Bahnschrift";
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 2;
}

table.fixed-column thead th:first-child{
  left: 0;
  z-index: 3;
}
table.fixed-column thead th:nth-child(2){
  left: 50px;
  z-index: 3;
}
table.fixed-column thead th:nth-child(3){
  left: 160px;
  z-index: 3;
}

table.fixed-column tbody {
  overflow: scroll;
  height: 200px;
}

/* MAKE LEFT COLUMN FIXEZ */
table.fixed-column tr > :first-child{
  position: -webkit-sticky;
  position: sticky; 
  left: 0;
  font-family: "Bahnschrift";
}
table.fixed-column tr > :nth-child(2){
  position: -webkit-sticky;
  position: sticky; 
  left: 50px; 
  font-family: "Bahnschrift";
}
table.fixed-column tr > :nth-child(3){
  position: -webkit-sticky;
  position: sticky; 
  left: 160px; 
  font-family: "Bahnschrift";
}

table.fixed-column tr > :first-child, 
table.fixed-column tr > :nth-child(2), 
table.fixed-column tr > :nth-child(3){
  background-color: white;
}

table.fixed-column tr.confirmed > :first-child{
  background-color: #336666;
}
table.fixed-column tr:hover > td:first-child, 
table.fixed-column tr:hover > td:nth-child(2), 
table.fixed-column tr:hover > td:nth-child(3){
  background-color: #d0dddd;
}
table.fixed-column tr.selected-row > :first-child, 
table.fixed-column tr.selected-row > :nth-child(2),
table.fixed-column tr.selected-row > :nth-child(3){
    background-color: #666;
    color: white;
}
table.fixed-column tr.selected-row:hover > :first-child, 
table.fixed-column tr.selected-row:hover > :nth-child(2), 
table.fixed-column tr.selected-row:hover > :nth-child(3){
    background-color: #888;
    color: white;
}
.circle-label{
  display: block;
  margin: auto;
  width: 15px;
  height: 15px;
  border: solid 3px rgba(0, 0, 0, .4);
  border-radius: 50%;
}
.checkmark-label{
  display: block;
  margin: auto;
  width: 8px;
  height: 15px;
  border: solid white;
  border-width: 0 3px 3px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}


.cellNom, .cellSite, .cellFonction, .cellHContrat, .cellHConv, .cellHTrav, .cellDiffHCHT,
.cellClass, .cellAgence, .cellDateEmbauche, .cellSalBase{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.cellConfirm{
  width: 50px;
  max-width: 50px;
  min-width: 50px;
}
.cellNum{
  width: 110px;
  max-width: 110px;
  min-width: 110px;
}
.cellSite, .cellFonction{
  width: 200px;
  max-width: 200px;
  min-width: 200px;
}
.cellHContrat, .cellHConv, .cellHTrav, .cellDiffHCHT, .cellHS{
  width: 70px;
  max-width: 70px;
  min-width: 70px;
}
.cellClass, .cellAgence, .cellDateEmbauche, .cellSalBase, .cellSalMens, .cellHSMaj{
  width: 100px;
  max-width: 100px;
  min-width: 100px;
}
td.cellHContrat, td.cellHConv, td.cellHTrav, td.cellDiffHCHT, .cellHS{
  text-align: center;
}
td.cellDateEmbauche{
  text-align: center;
}
td.cellSalBase, td.cellSalMens, td.cellHSMaj{
  text-align: right;
}