import React, { Component } from 'react'
import axios from 'axios'

import './select.css'
import moment from 'moment'

export default class SelectGroupSite extends Component {
    constructor(props){
        super(props)
        this.state = {
            groups: [],
            showItem: false
        }
        this.toggleSelect = this.toggleSelect.bind(this)
        this.handleSearch = this.handleSearch.bind(this)
    }
    getGroup(){
        axios.get('/api/group_pointage_sites')
        .then(({data}) => {
            this.setState({
                groups: data
            })
        })
        .catch((e) => {
            setTimeout(() => {
                this.getGroup()
            }, 10000)
        })
    }
    componentDidMount(){
        this.getGroup()
    }
    getClassActiveMonth(m){
        const {month} = this.props
        return month == m ? 'active-item' : ''
    }
    getClassActiveYear(y){
        const {year} = this.props
        return year == y ? 'active-item' : ''
    }
    handleSearch(event){
        event.stopPropagation()
        this.props.toggleSelect()
        this.props.updateData()
    }
    toggleSelect(event){
        event.stopPropagation()
        this.props.toggleSelect()
    }
    findHeaderName(){
        const {selectedGroup, beginDate, endDate} = this.props
        const {groups} = this.state
        const months = {
            1: 'Janvier',
            2: 'Février',
            3: 'Mars',
            4: 'Avril',
            5: 'Mai',
            6: 'Juin',
            7: 'Juillet',
            8: 'Août',
            9: 'Septembre',
            10: 'Octobre',
            11: 'Novembre',
            12: 'Décembre',
        }
        let currentGroup = ''
        for(let i=0; i<groups.length; i++){
            if(groups[i].id == selectedGroup){
                currentGroup = groups[i].nom
                break;
            } 
        }
        if(currentGroup && beginDate && endDate)
            return 'Etat de paie ' + currentGroup.toUpperCase() + ' du ' + moment(beginDate).format('DD MMM YYYY') + ' au ' + moment(endDate).format('DD MMM YYYY')
        return ''
    }
    render(){
        const {groups } = this.state
        const {enableConfirm, showItem, group} = this.props

        return (
            <div id="selectBox">
                <div onClick={this.toggleSelect} id="itemSelected">
                    <span className={"item-selected-hour" + (enableConfirm ? " active-filter" : "")}>{this.findHeaderName()}</span>
                </div>
                {
                    showItem
                    &&
                    <div id="itemNotSelected">
                        <div className="table">
                            <div className="cell-30 padding-side">
                            {
                                groups.map((item) => (
                                    <span className={group == item.id ? 'active-item' : ''} onClick={(e) => this.props.handleClickGroup(e, item.id)} key={item.id}>{item.nom}</span>
                                ))
                            }
                            </div>
                            <div className="cell-40">
                                <div className="table">
                                    <div className="cell-33">
                                        <span className={this.getClassActiveMonth(1)} onClick={(e) => {this.props.handleClickMonth(e, 1)}}>Janvier</span>
                                        <span className={this.getClassActiveMonth(2)} onClick={(e) => {this.props.handleClickMonth(e, 2)}}>Février</span>
                                        <span className={this.getClassActiveMonth(3)} onClick={(e) => {this.props.handleClickMonth(e, 3)}}>Mars</span>
                                        <span className={this.getClassActiveMonth(4)} onClick={(e) => {this.props.handleClickMonth(e, 4)}}>Avril</span>
                                    </div>
                                    <div className="cell-33">
                                        <span className={this.getClassActiveMonth(5)} onClick={(e) => {this.props.handleClickMonth(e, 5)}}>Mai</span>
                                        <span className={this.getClassActiveMonth(6)} onClick={(e) => {this.props.handleClickMonth(e, 6)}}>Juin</span>
                                        <span className={this.getClassActiveMonth(7)} onClick={(e) => {this.props.handleClickMonth(e, 7)}}>Juillet</span>
                                        <span className={this.getClassActiveMonth(8)} onClick={(e) => {this.props.handleClickMonth(e, 8)}}>Août</span>
                                    </div>
                                    <div className="cell-33">
                                        <span className={this.getClassActiveMonth(9)} onClick={(e) => {this.props.handleClickMonth(e, 9)}}>Septembre</span>
                                        <span className={this.getClassActiveMonth(10)} onClick={(e) => {this.props.handleClickMonth(e, 10)}}>Octobre</span>
                                        <span className={this.getClassActiveMonth(11)} onClick={(e) => {this.props.handleClickMonth(e, 11)}}>Novembre</span>
                                        <span className={this.getClassActiveMonth(12)} onClick={(e) => {this.props.handleClickMonth(e, 12)}}>Décembre</span>
                                    </div>
                                </div>
                            </div>
                            <div className="cell-30 padding-side">
                                <span className={this.getClassActiveYear(2020)} onClick={(e) => {this.props.handleClickYear(e, 2020)}}>2020</span>
                                <span className={this.getClassActiveYear(2021)} onClick={(e) => {this.props.handleClickYear(e, 2021)}}>2021</span>
                                <span className={this.getClassActiveYear(2022)} onClick={(e) => {this.props.handleClickYear(e, 2022)}}>2022</span>
                            </div>
                        </div>
                        <div className="padding-bottom">
                            <button className="btn-primary" onClick={this.handleSearch}>Rechercher</button>
                        </div>
                    </div>
                }
            </div>
        )
    }
}