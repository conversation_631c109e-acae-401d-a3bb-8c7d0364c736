import React, { Component } from 'react'


export default class PaieDetailTab extends Component {
    constructor(props){
        super(props)
    }
    numberWithSpace(x){
        if(x)
            return Number.parseInt(x).toString().replace(/\B(?=(\d{3})+(?!\d))/g, " ")
        return 0
    }
    render(){
        const {heightWindow, currentEtatPaie} = this.props
        return (
            <table className="fixed_header default layout-fixed">
                <tbody style={{maxHeight: (heightWindow - 360) + "px"}}>
                    <tr>
                        <td>Nb. heures contrat</td> 
                        <td className="cellHourDetail">{currentEtatPaie.agent.nb_heure_contrat}</td>
                    </tr>
                    <tr>
                        <td>Heure travaillé</td> 
                        <td className="cellHourDetail">{currentEtatPaie.heure_trav}</td>
                    </tr>
                    <tr>
                        <td>Diff. HC/HT</td> 
                        <td className="cellHourDetail">{currentEtatPaie.diffHCHT}</td>
                    </tr>
                    <tr>
                        <td>Salaire de base</td>
                        <td className="cellHourDetail">
                            {this.numberWithSpace(currentEtatPaie.sal_base)}
                        </td>
                    </tr>
                    <tr className="price">
                        <td>Salaire mensuel</td>
                        <td className="cellHourDetail">
                            {this.numberWithSpace(currentEtatPaie.salMens)}
                        </td>
                    </tr>
                    <tr>
                        <td>Heure réclammé</td> 
                        <td className="cellHourDetail">{currentEtatPaie.heure_reclam}</td>
                    </tr>
                    {
                        !currentEtatPaie.sal_forfait &&
                        <tr>
                            <td>Heure férié</td> 
                            <td className="cellHourDetail">{currentEtatPaie.heure_ferie}</td>
                        </tr>
                    }
                    {
                        !currentEtatPaie.sal_forfait &&
                        <tr>
                            <td>Majoration heure férié</td> 
                            <td className="cellHourDetail">{this.numberWithSpace(currentEtatPaie.mMajferie)}</td>
                        </tr>
                    }
                    {
                        !currentEtatPaie.sal_forfait &&
                        <tr>
                            <td>Heure dimanche</td> 
                            <td className="cellHourDetail">{currentEtatPaie.heure_dim}</td>
                        </tr>
                    }
                    {
                        !currentEtatPaie.sal_forfait &&
                        <tr>
                            <td>Majoration heure dimanche</td> 
                            <td className="cellHourDetail">{currentEtatPaie.mMajDim && this.numberWithSpace(currentEtatPaie.mMajDim)}</td>
                        </tr>
                    }
                    {
                        !currentEtatPaie.sal_forfait &&
                        <tr>
                            <td>Heure nuit</td> 
                            <td className="cellHourDetail">{currentEtatPaie.heure_nuit}</td>
                        </tr>
                    }
                    {
                        !currentEtatPaie.sal_forfait &&
                        <tr>
                            <td>Majoration heure nuit</td> 
                            <td className="cellHourDetail">{this.numberWithSpace(currentEtatPaie.mMajNuit)}</td>
                        </tr>
                    }
                    <tr>
                        <td>Heure Supplémentaire</td> 
                        <td className="cellHourDetail">{currentEtatPaie.heureSup}</td>
                    </tr>
                    <tr>
                        <td>Heure Sup 30%</td>
                        <td className="cellHourDetail">{currentEtatPaie.heureSup30}</td>
                    </tr>
                    <tr>
                        <td>Majoration Heure Sup 30%</td>
                        <td className="cellHourDetail">
                            {this.numberWithSpace(currentEtatPaie.mHeureSup30)}
                        </td>
                    </tr>
                    <tr>
                        <td>Heure Sup 50%</td>
                        <td className="cellHourDetail">{currentEtatPaie.heureSup50}</td>
                    </tr>
                    <tr>
                        <td>Majoration Heure Sup 50%</td>
                        <td className="cellHourDetail">
                            {this.numberWithSpace(currentEtatPaie.mHeureSup50)}
                        </td>
                    </tr>
                    <tr>
                        <td>Heure Ferié</td>
                        <td className="cellHourDetail">{currentEtatPaie.heure_ferie}</td>
                    </tr>
                    <tr>
                        <td>Majoration heure ferié</td>
                        <td className="cellHourDetail">
                            {this.numberWithSpace(currentEtatPaie.mMajferie)}
                        </td>
                    </tr>
                    <tr>
                        <td>Heure dimanche</td>
                        <td className="cellHourDetail">{currentEtatPaie.heure_dim}</td>
                    </tr>
                    <tr>
                        <td>Majoration heure dimanche</td>
                        <td className="cellHourDetail">
                            {this.numberWithSpace(currentEtatPaie.mMajDim)}
                        </td>
                    </tr>
                    <tr>
                        <td>Heure nuit</td>
                        <td className="cellHourDetail">{currentEtatPaie.heure_nuit}</td>
                    </tr>
                    <tr>
                        <td>Majoration heure nuit</td>
                        <td className="cellHourDetail">
                            {this.numberWithSpace(currentEtatPaie.mMajNuit)}
                        </td>
                    </tr>
                    <tr className="price">
                        <td>Total Majoration</td>
                        <td className="cellHourDetail">
                            {this.numberWithSpace(currentEtatPaie.totalMaj)}
                        </td>
                    </tr>
                    <tr>
                        <td>Prime exceptionnel</td>
                        <td className="cellHourDetail">{this.numberWithSpace(currentEtatPaie.prime_ex)}</td>
                    </tr>
                    <tr>
                        <td>Prime ex. Prorata</td>
                        <td className="cellHourDetail">{this.numberWithSpace(currentEtatPaie.pExProrata)}</td>
                    </tr>
                    <tr>
                        <td>Prime divers</td>
                        <td className="cellHourDetail">{this.numberWithSpace(currentEtatPaie.prime_div)}</td>
                    </tr>
                    <tr>
                        <td>Prime div. Prorata</td>
                        <td className="cellHourDetail">{this.numberWithSpace(currentEtatPaie.pDivProrata)}</td>
                    </tr>
                    <tr>
                        <td>Indemnité de déplacement</td>
                        <td className="cellHourDetail">{this.numberWithSpace(currentEtatPaie.idm_depl)}</td>
                    </tr>
                    <tr>
                        <td>Idm dépl Prorata</td>
                        <td className="cellHourDetail">{this.numberWithSpace(currentEtatPaie.idmDeplProrata)}</td>
                    </tr>
                    <tr>
                        <td>Prime assiduité</td>
                        <td className="cellHourDetail">{this.numberWithSpace(currentEtatPaie.prime_ass)}</td>
                    </tr>
                    <tr>
                        <td>Prime ass. Prorata</td>
                        <td className="cellHourDetail">{this.numberWithSpace(currentEtatPaie.pAssProrata)}</td>
                    </tr>
                    <tr>
                        <td>Prime responsable</td>
                        <td className="cellHourDetail">{this.numberWithSpace(currentEtatPaie.prime_resp)}</td>
                    </tr>
                    <tr>
                        <td>Prime resp. Prorata</td>
                        <td className="cellHourDetail">{this.numberWithSpace(currentEtatPaie.pRespProrata)}</td>
                    </tr>
                    <tr>
                        <td>Prime entretient</td>
                        <td className="cellHourDetail">{this.numberWithSpace(currentEtatPaie.prime_entr)}</td>
                    </tr>
                    <tr>
                        <td>Prime entr. Prorata</td>
                        <td className="cellHourDetail">{this.numberWithSpace(currentEtatPaie.pEntrProrata)}</td>
                    </tr>
                    <tr>
                        <td>Prime ancienneté</td>
                        <td className="cellHourDetail">{this.numberWithSpace(currentEtatPaie.prime_anc)}</td>
                    </tr>
                    <tr>
                        <td>Prime anc. Prorata</td>
                        <td className="cellHourDetail">{this.numberWithSpace(currentEtatPaie.pAncProrata)}</td>
                    </tr>
                    <tr className="price">
                        <td>Total Pro{'&'}Grat</td>
                        <td className="cellHourDetail">{this.numberWithSpace(currentEtatPaie.totalProGrat)}</td>
                    </tr>
                </tbody>
            </table>
        )
    }
}