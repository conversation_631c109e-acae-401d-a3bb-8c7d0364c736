#panicContainer{
    position: fixed;
    width: 100%;
    height: 100%;
    bottom:0px;
    left:0px;
    background-color: rgba(0, 0, 0, .3);
    z-index: 1000 !important;
}
#modalPanic{
    display: table-cell;
    vertical-align: middle;
    text-align: center;
}
#modalContent{
    display: inline-block;
    width: 600px;
    padding: 20px;
    background-color: white;
}
#panicContent{
    display: table-row;
    text-align: left;
}
.padding-border-modal{
    padding: 10px 20px 20px 20px;
    border: solid rgba(0, 0, 0, .1) .5px;
    margin-bottom: 50px;
}
#panicBtn{
    display: inline-block;
    margin-right: 20px;
    padding: 10px;
    text-decoration: none;
    background-color: #366666;
    color: white;
    cursor: pointer;
}
.fix-width-right{
    display: inline-block;
    width: 120px;
    margin-right: 10px;
    text-align: center; 
}
.fix-width{
    display: inline-block;
    width: 120px;
    margin-left: 10px;
    text-align: center; 
}
.cellHabilite{
    width: 200px;
    min-width: 200px;
    max-width: 200px;
}
.cellPhone{
    width: 150px;
    min-width: 150px;
    max-width: 150px;
}
.cellCodeVocale:focus-within, .cellPhone:focus-within{
    border: solid 2px #366666;
}
.cellCodeVocale > input, .cellPhone > input {
    outline: none;
    border: none;
    background: none;
}
#panicList{
    width: 320px;
    overflow: auto;
}
.panic-card-container{
    background-color: white;
    margin: 10px;
    padding: 10px;
    width: 270px;
    box-shadow: 5px 5px 5px rgba(0, 0, 0, .3);
}
.panic-card{
    padding: 20px;
    border: .5px solid rgba(0, 0, 0, .1);
    background-color: white;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: left;
    cursor: pointer;
}
.active.panic-card-container{
    background-color: black;
}
.not-seen.panic-card-container{
    background-color: #f44336;
}
@keyframes pulse {
    0% {
        background-color: white;
    }
    100% {
        background-color: #e91e63;
    }
}
.float-panic-badge{
    padding: 0px 5px;
    float: right;
    border-radius: 5px;
    background-color: #f44336;
    color: white;
    font-size: 12pt;
}
.user-traite-panic{
    opacity: .7;
}
#panicBar{
    position: fixed;
    bottom: 0;
    left: 0;
    width: 220px;
    padding: 10px;
    color: white;
    background-color: #f44336;
    animation: pulse 1s infinite;
}
#nbPanic{
    padding: 0px 5px;
    float: right;
    border-radius: 5px;
    background-color: white;
    color: #444;
}