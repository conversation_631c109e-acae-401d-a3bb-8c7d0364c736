import React, { Component } from 'react'
import axios from 'axios'

import Modal from '../modal/Modal'
import Historique from '../vigilance/historique/Historique'

export default class AlarmLogModal extends Component {
    constructor(props) {
        super(props)
        this.state = {
            site: null
        }
        this.handleCancel = this.handleCancel.bind(this)
    }

    componentDidMount() {
        const { site_id } = this.props
        console.log(site_id)
        axios.get('/api/sites/show/' + site_id + '?username=' + localStorage.getItem("username") + '&secret=' + localStorage.getItem("secret"))
            .then(({ data }) => {
                console.log(site_id, "data", data)
                this.setState({
                    site: data,
                    loading: false
                })
            })
            .catch((e) => {
                console.log(e)
                this.setState({
                    loading: false
                })
            })
    }

    handleCancel() {
        this.props.closeModal()
    }

    render() {
        const { heightWindow } = this.props
        const { site } = this.state
        return (
            <div>
                {
                    site &&
                    <div>
                        <Modal readOnly
                            width="lg"
                            handleCancel={this.handleCancel}
                        >
                            <h3 id="pointageNomHeader">
                                {site.nom}<br />
                                <span id="pointagePhoneHeader">{site.phone_agent.map(agent => agent.numero).join(", ")}</span>
                            </h3>

                            <div>
                                <Historique heightWindow={heightWindow} site={site} />
                            </div>
                        </Modal>
                    </div>
                }
            </div>
        )
    }
}
