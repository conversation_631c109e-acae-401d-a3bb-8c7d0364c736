import React, { Component } from 'react'
import Modal from '../modal/Modal'
import axios from 'axios'

export default class CancelDeletePointageModal extends Component {
    constructor(props){
        super(props)
        this.state = {
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }
    handleSave(){
        let data = new FormData()
        data.append("username", localStorage.getItem('username'))
        data.append("secret", localStorage.getItem('secret'))
        axios.post(this.props.action, data)
        .then(({data}) => {
            this.props.updatePointage(data)
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        return (
            <Modal handleSave={this.handleSave} handleCancel={this.handleCancel}>
                <h3>Restaurer le pointage</h3>
                <div>Voulez-vous annuler la suppression ce pointage ?</div>
            </Modal>)
    }
}