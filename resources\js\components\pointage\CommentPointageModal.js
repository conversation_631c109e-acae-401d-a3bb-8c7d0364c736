import moment from 'moment'
import Modal from '../modal/Modal'
import React, { Component } from 'react'

export default class CommentPointageModal extends Component {
    constructor(props) {
        super(props)
        this.state = {
            comment: this.props.pointage.comment ?? ''
        }
        this.handleChangeComment = this.handleChangeComment.bind(this)
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }
    handleChangeComment(e) {
        this.setState({
            comment: e.target.value
        })
    }

    handleSave() {
        const { comment } = this.state
        let data = new FormData()
        data.append("username", localStorage.getItem('username'))
        data.append("secret", localStorage.getItem('secret'))
        if(comment && comment.trim()){
            data.append("comment", comment)
        }
        data.append("site_id", this.props.pointage.idsite)
        data.append("date_pointage", this.getCurrentService())
        axios.post(this.props.action, data)
            .then(({ data }) => {
                this.props.pointage.comment_id = data.comment_pointage.id
                this.props.pointage.comment = data.comment_pointage.comment
                this.handleCancel()
            })
    }
    handleCancel() {
        this.props.closeModal()
    }

    getCurrentService(){
        const now = moment();
        let serviceDate;
        if (now.hour() >= 18)
            serviceDate = now.set({ hour: 18, minute: 0, second: 0, millisecond: 0 });
        else if (now.hour() >= 7)
            serviceDate = now.set({ hour: 7, minute: 0, second: 0, millisecond: 0 });
        else 
            serviceDate = now.subtract(1, 'days').set({ hour: 18, minute: 0, second: 0, millisecond: 0 });
        return serviceDate.format('YYYY-MM-DD HH:mm:ss');
    }

    render() {
        const {comment} = this.state
        return (
            <Modal 
                handleSave={this.handleSave} 
                handleCancel={this.handleCancel}
            >
                <h3>Pointage</h3>
                <div className="input-container">
                    <label>Commentaire</label>
                    <textarea onChange={this.handleChangeComment} value={comment} />
                </div>

            </Modal>
        )
    }
}
