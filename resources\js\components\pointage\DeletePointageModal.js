import React, { Component } from 'react'
import Modal from '../modal/Modal'
import axios from 'axios'

export default class DeletePointageModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            motif: ''
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.handleChangeMotif = this.handleChangeMotif.bind(this)
    }
    handleChangeMotif(e){
        this.setState({
            motif: e.target.value
        })
    }
    handleSave(){
        const {motif} = this.state
        const {hasDate} = this.props
        let data = new FormData()
        data.append("username", localStorage.getItem('username'))
        data.append("secret", localStorage.getItem('secret'))
        if(hasDate && motif && motif.trim())
            data.append("motif", motif.trim())
        axios.post(this.props.action, data)
        .then(({data}) => {
            this.props.updatePointage(data)
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        const {hasDate, currentPointage} = this.props
        const {motif} = this.state
        return (
            <Modal disableSave={(hasDate ? !(motif && motif.trim()) : false)}  handleSave={this.handleSave} handleCancel={this.handleCancel}>
                <h3>{currentPointage.reclamation_id ? "Réclamation" : "Pointage"}</h3>
                <div>{currentPointage && currentPointage.reclamation_id ? "Voulez-vous vraiment supprimer cette réclamation ?" : "Voulez-vous vraiment supprimer ce pointage ?"}</div>
                {
                    hasDate && !currentPointage?.reclamation_id &&
                    <div className="input-container">
                        <label>Motif *</label>
                        <input onChange={this.handleChangeMotif} value={motif}/>
                    </div>
                }
            </Modal>)
    }
}