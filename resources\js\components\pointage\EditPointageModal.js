import React, { Component } from 'react'
import axios from 'axios'

import Modal from '../modal/Modal'
import Agent from './agent/Agent'
import Site from './site/Site'
import EditReclamationModal from './EditReclamationModal'

export default class EditPointageModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            currentAgent: null,
            selectedAgents: [],
            agentSearch: '',
            vigilance: true,
            motif: '',
            type_pointage_id: '',
            site: null,
            typePointages: [],
            agents: null,
            showAgent: false,
            showAgentTable: false,
            showError: false,
            showReclamation:false,
            typeReclamation: '',
            disableAgent: false,
            disableSave: false,
            unregisteredAgent: false,
            errorInfo: '',
        }
        this.handleAgentChange = this.handleAgentChange.bind(this)
        this.handleVigilanceChange = this.handleVigilanceChange.bind(this)
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.handleAgentCLick = this.handleAgentCLick.bind(this)
        this.hideAgentClick = this.hideAgentClick.bind(this)
        this.handleClickAgentItem = this.handleClickAgentItem.bind(this)
        this.handleChangeAgent = this.handleChangeAgent.bind(this)
        this.showAgent = this.showAgent.bind(this)
        this.toggleAgent = this.toggleAgent.bind(this)
        this.handleUncheckAgent = this.handleUncheckAgent.bind(this)
        this.handleChangeMotif = this.handleChangeMotif.bind(this)
        this.handleChangeTypePointage = this.handleChangeTypePointage.bind(this)
        this.handleChangeSite = this.handleChangeSite.bind(this)
        this.handleCloseModalReclamation = this.handleCloseModalReclamation.bind(this)
        this.handleOpenModalReclamation = this.handleOpenModalReclamation.bind(this)
    }
    toggleSiteList(status){
        this.setState({
            showSite: status
        })
    }

    updatePointeuseList(site){
        if(site.pointage_biometrique){
            axios.get('/api/type_pointages')
            .then(({data}) => {
                this.setState({
                    typePointages: data,
                })
            })
        }
    }

    handleChangeSite(site){
        this.setState({
            site: site,
            showSite: false,
        })
        this.updatePointeuseList(site)
    }

    handleChangeMotif(e){
        this.setState({
            motif: e.target.value
        })
    }
    handleChangeTypePointage(e){
        let commentaire = ''
        if(e.target.value == 1)
            commentaire = "L'agent est arrivé à ____"
        else if(e.target.value == 2)
            commentaire = "L'agent est enrolé à ____"
        else if(e.target.value == 3)
            commentaire = "L'agent est enregistré à ____"
        else if(e.target.value == 4)
            commentaire = "L'agent est arrivé à ____ , transféré par ____"
        else if(e.target.value == 5)
            commentaire = "L'agent en mouvement de ____ à ____"
        else if(e.target.value == 6)
            commentaire = "L'agent a fait son empreinte à ____"
        else if(e.target.value == 7)
            commentaire = "Envoi de l'intervention ____  à ____ pour confirmer l'empreinte"
        else if(e.target.value == 8)
            commentaire = "La pointeuse est en panne depuis ____"
        else if(e.target.value == 9)
            commentaire = "La pointeuse est installé le ____ à ____"
        else if(e.target.value == 10)
            commentaire = "Heure d'ouverture du site à ____"
        else if(e.target.value == 11)
            commentaire = "Agent 24H"
        this.setState({
            type_pointage_id: e.target.value,
            motif: commentaire
        })
    }
    handleUncheckAgent(agent){
        const {selectedAgents, agents} = this.state
        let checkedAgents = []
        let uncheckAgents = agents
        uncheckAgents.push(agent)
        for(let i = 0; i<selectedAgents.length; i++){
            if(selectedAgents[i].id != agent.id)
                checkedAgents.push(selectedAgents[i])
        }
        this.setState({
            agents: uncheckAgents,
            selectedAgents: checkedAgents
        })
    }
    handleChangeAgent(agents){
        let checkedAgents = this.state.selectedAgents
        agents.forEach(a => {
            checkedAgents.unshift(a)
        });
        this.setState({
            currentAgent: agents[0],
            agentSearch: '',
            showAgentTable: false,
            selectedAgents: checkedAgents
        })
        this.updateAgent()
    }
    showAgent(agent){
        const {agentSearch} = this.state
        if(agentSearch){
            const search = agentSearch.toLocaleLowerCase().replace(/[.*+?^{}()|[\]\\]/g, '\\$&')
            var patt = new RegExp(search)
            if(agent.numero_employe && patt.test(agent.numero_employe.toLocaleLowerCase()))
                return true
            else if(agent.numero_stagiaire && patt.test(agent.numero_stagiaire.toLocaleLowerCase()))
                return true
            else if(agent.num_emp_soit && patt.test(agent.num_emp_soit.toLocaleLowerCase()))
                return true
            else if(agent.nom && patt.test(agent.nom.toLocaleLowerCase()))
                return true
            else if(agent.site && patt.test(agent.site.toLocaleLowerCase()))
                return true
            return false
        }
        return true
    }
    handleClickAgentItem(event, agent){
        event.stopPropagation()
        const {selectedAgents, agents} = this.state
        let sAgents = selectedAgents
        let uncheckAgent = []
        sAgents.unshift(agent)
        for(let i=0; i<agents.length; i++){
            if(agent.id != agents[i].id)
                uncheckAgent.push(agents[i])
        }
        this.setState({
            showAgent: false,
            currentAgent: agent,
            agentSearch: '',
            selectedAgents: sAgents,
            agents: uncheckAgent
        }, () => {
            console.log(this.state.selectedAgents)
        })
    }
    hideAgentClick(e){
        this.setState({
            showAgent: false
        })
    }
    handleAgentCLick(e){
        e.stopPropagation()
        this.setState({
            showAgent: !this.state.showAgent,
            agentSearch: (!this.state.showAgent ? '' : this.state.agentSearch)
        })
    }
    handleAgentChange(e){
        this.setState({
            agentSearch: e.target.value,
            showAgent: true
        })
    }

    handleCloseModalReclamation(e){
        this.setState({ 
            showReclamation: false 
        });
    };

    handleOpenModalReclamation(){
        this.setState({ 
            showReclamation: true,
            unregisteredAgent: true
        });
    };

    handleVigilanceChange(e){
        this.setState({
            vigilance: e.target.checked
        })
    }
    updateAgent(loading){
        this.setState({
            showAgent: false
        })
        axios.get('/api/pointages/select_agents/' + this.props.site.idsite)
        .then(({data}) => {
            if(data){
                const {selectedAgents} = this.state
                let uncheckedAgents = []
                let agents = data.agents
                for(let i=0; i<agents.length; i++){
                    let found = false;
                    for(let j=0; j<selectedAgents.length; j++){
                        if(agents[i].id == selectedAgents[j].id){
                            found = true
                            break
                        }
                    }
                    if(!found) uncheckedAgents.push(agents[i])
                }
                this.setState({
                    agents: uncheckedAgents,
                    showAgent: loading
                })
            }
        })
    }
    componentDidMount(){
        const {pointage, site} = this.props
        console.log("pointage: " , pointage)
        if(pointage)
            this.setState({
                site: site,
                currentAgent: pointage,
                agentSearch: pointage.nom,
                type_pointage_id: pointage.type_pointage_id,
                motif: pointage.motif,
                vigilance: pointage.vigilance ? true : false,
                disableAgent: true
            })
        else
            this.setState({
                site: site,
            })
        this.updateAgent()
        this.updatePointeuseList(site)
    }
    toggleAgent(value){
        this.setState({
            showAgentTable: value
        })
    }
    handleSave(){
        const {selectedAgents, currentAgent, vigilance, type_pointage_id, site, motif} = this.state
        const {datePointage} = this.props
        let data = {}
        this.setState({
            errorInfo: '',
            showError: false,
            disableSave: true,
        })
        data.site_id = site.idsite
        if(currentAgent)
            data.agent_id = currentAgent.id
        if(datePointage)
            data.date_pointage = datePointage
        
        if(site && site.pointage_biometrique){
            data.type_pointage_id = type_pointage_id
            if(motif)
                data.motif = motif.trim()
        }
        else
            data.vigilance = (vigilance ? 1 : 0)
        
        data.agents = selectedAgents.map((a) => (a.id))
        data.username = localStorage.getItem('username')
        data.secret = localStorage.getItem('secret')
        
        console.log(this.props.action)
        if(currentAgent){
            console.log(data)
            axios.post(this.props.action, data)
            .then(({data}) => {
                console.log(data)
                if(data.error){
                    if(data.service24){
                        this.setState({
                            errorInfo: data.error,
                            typeReclamation: 'service24',
                            showReclamation: true,
                            unregisteredAgent: false
                        })}
                    else if(data.mis_a_pied){
                        this.setState({
                            errorInfo: data.error,
                            typeReclamation: 'mis_a_pied',
                            showReclamation: true,
                            unregisteredAgent: false
                    })}
                    else{
                        this.setState({
                            errorInfo: data.error,
                            showReclamation: false,
                            unregisteredAgent: true
                        })
                    }
                }
                else if(data.constructor === Array)
                    data.forEach(d => {
                        this.props.updatePointage(d)
                    })
                else
                    this.props.updatePointage(data)
                this.setState({
                    disableSave: false
                })
            })
            .catch(() => {
                this.setState({
                    disableSave: false
                })
            })
        }
        else setTimeout(() => {
            this.setState({
                showError: true
            })
        }, 200) 
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        const {selectedAgents, disableAgent, agentSearch, vigilance, agents, showAgent, showSite, errorInfo,
            showAgentTable, showError, disableSave, type_pointage_id, motif, typePointages, site, showReclamation, unregisteredAgent, typeReclamation} = this.state
        const {pointage, hasDate, restore, updateReclamationPointage} = this.props
        return (
            <div>
                {
                    site &&
                    <div onClick={this.hideAgentClick}>
                        <Modal disableSave={disableSave || (site.pointage_biometrique ? (
                                (!hasDate && (!motif || !motif.trim() || /____/.test(motif)))
                                || (hasDate && !this.state.site)
                            ) : false)} 
                            width="md" 
                            handleSave={this.handleSave} 
                            handleCancel={this.handleCancel}
                        >
                            <h3 id="pointageNomHeader">
                                {site.nom}<br/>
                                <span id="pointagePhoneHeader">{site.phone_agent}</span>
                            </h3>
                            
                            <div>
                                <div className="input-select-relative">
                                    <div>
                                        <label>Agent</label>
                                        <div>
                                            <div className="table">
                                                <div className="cell" style={{height:40, maxHeight:40, minHeight:40}}>
                                                    <div className="input-container">
                                                        <input disabled={disableAgent} autoComplete="off" onClick={this.handleAgentCLick} onChange={this.handleAgentChange} type="text" value={agentSearch}/>
                                                    </div>
                                                </div>
                                                {
                                                    !disableAgent &&
                                                    <div id="cellRefresh">
                                                        <img id="refreshPointageBtn" src="/img/all_agent.svg" onClick={()=> {this.toggleAgent(true)}}/>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                        <div>
                                            {
                                                showAgent &&
                                                <ul id="agentListContainer" className="comment-vg-list">
                                                    {
                                                        agents && agents.map((ag, index) =>{
                                                            if(this.showAgent(ag))
                                                                return <li key={'key_' + ag.id + index} onClick={(e) => {this.handleClickAgentItem(e, ag)}}>
                                                                    {
                                                                        ag.nom
                                                                    } {
                                                                        ag.societe_id == 1 ? 'DGM-' + ag.numero_employe :
                                                                        ag.societe_id == 2 ? 'SOIT-' + ag.num_emp_soit :
                                                                        ag.societe_id == 3 ? 'ST-' + ag.numero_stagiaire :
                                                                        ag.societe_id == 4 ? 'SM' :
                                                                        ag.numero_employe ? ag.numero_employe :
                                                                        ag.numero_stagiaire ? ag.numero_stagiaire :
                                                                        <span className="purple">Ndf</span>
                                                                    }
                                                                </li>
                                                        })
                                                    }
                                                </ul>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    { 
                                        (!disableAgent && selectedAgents) &&
                                        <table className="default" id="selectedAgentTable">
                                            <tbody style={{display:'block', width: '100%', maxHeight: 250, overflowY: 'auto'}}>
                                                {
                                                    selectedAgents.map((ag) => {
                                                        return <tr>
                                                            <td className="numAgentCell">
                                                                {
                                                                    ag.societe_id == 1 ? 'DGM-' + ag.numero_employe :
                                                                    ag.societe_id == 2 ? 'SOIT-' + ag.num_emp_soit :
                                                                    ag.societe_id == 3 ? 'ST-' + ag.numero_stagiaire :
                                                                    ag.societe_id == 4 ? 'SM' :
                                                                    ag.numero_employe ? ag.numero_employe :
                                                                    ag.numero_stagiaire ? ag.numero_stagiaire :
                                                                    <span className="purple">Ndf</span>
                                                                }
                                                            </td>
                                                            <td>{ag.nom}</td>
                                                            <td className="deleteIconCell">
                                                                <img onClick={() => {this.handleUncheckAgent(ag)}}
                                                                    className="img-btn" 
                                                                    title="Supprimer" 
                                                                    src="/img/delete.svg"/>
                                                            </td>
                                                        </tr>
                                                    })
                                                }
                                            </tbody>
                                        </table>
                                    }
                                    <br/>
                                </div>
                                {
                                    (pointage && !restore && !pointage.reclamation_id) &&
                                    <div className="input-container">
                                        <label>Site *</label>
                                        <div id="promInputTable" className="table">
                                            <span className="cell">{site.nom}</span>
                                            {
                                                <span id="cellProm" onClick={() => {this.toggleSiteList(true)}}>
                                                    <img id="clientImg" src="/img/site.svg"/>
                                                </span>
                                            }
                                        </div>
                                    </div>
                                }
                                {
                                    !site.pointage_biometrique && 
                                    <div className="input-container">
                                        {
                                            showAgent ? 
                                            <label><br/><br/></label>
                                            :
                                            <label className="checkbox-container">
                                                Vigilance
                                                <input onChange={this.handleVigilanceChange} name="vigilance" checked={vigilance} type="checkbox"/>
                                                <span className="checkmark"></span>
                                            </label>
                                        }
                                    </div>
                                }
                                {
                                    (site.pointage_biometrique == 1 && (!pointage || !pointage.dtarrived)) && 
                                    <>
                                        <div className="input-container">
                                            <label>Motif *</label>
                                            <select onChange={this.handleChangeTypePointage} value={type_pointage_id}>
                                                <option></option>
                                                {
                                                    typePointages.map(type => (
                                                        <option key={type.id} value={type.id}>
                                                            {type.nom}
                                                        </option>
                                                    ))
                                                }
                                            </select>
                                        </div>
                                        <div className="input-container">
                                            <label>Commentaire *</label>
                                            <input onChange={this.handleChangeMotif} value={motif}/>
                                        </div>
                                    </>
                                }
                                <div className="input-container" style={{textDecoration: 'underline'}}>
                                    <span style={{marginBottom:20}} onClick={this.handleOpenModalReclamation}>
                                        <a>Réclamer le pointage</a>
                                    </span>
                                </div>
                            </div>
                            {
                                showError &&
                                <span className="pink">Veuillez selectionner un agent</span>
                            }
                            {
                                errorInfo &&
                                <span className="pink">{errorInfo}</span>
                            }
                            {
                                showAgentTable && <Agent changeAgent={this.handleChangeAgent} agentList={selectedAgents} currentSite={site} closeModal={() => {this.toggleAgent(false)}}/>
                            }
                        </Modal>
                        {
                            showReclamation && 
                            <EditReclamationModal closeModal={this.handleCloseModalReclamation} 
                                updateReclamationPointage={updateReclamationPointage}
                                site={site} 
                                agent={selectedAgents.length == 1 ? selectedAgents[0] : null} 
                                isUnregisteredAgent={unregisteredAgent} 
                                typeReclamation={typeReclamation}
                                closePointageModal={this.handleCancel}/>
                        }
                        {
                            showSite && 
                            <Site
                                currentSite={site}
                                closeModal={() => {this.toggleSiteList(false)}}
                                changeSite={this.handleChangeSite}/>
                        }
                    </div>
                }
            </div>
        )
    }
}