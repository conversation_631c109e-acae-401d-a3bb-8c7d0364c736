import React, { Component } from 'react';
import Modal from '../modal/Modal';
import './inputScroll.css';

export default class EditReclamationModal extends Component {
    constructor(props) {
        super(props);
        this.state = {
            supervisor: {},
            supervisors: [],
            error: '',
            agent: this.props.agent ?? {},
            agents: [],
            showInputAgent: false,
            showInputSupervisor: true,
            showAgent: false,
            searchValue: '',
            searchSupValue: '',
            showSupervisor: false,
            unregisteredAgentName: '',
            isUnregisteredAgent: false,
            type: this.props.typeReclamation,
            desableSave: true,
            showError: false,
            errorInfo: '',
            isLoading: false,
        };
        this.handleCancel = this.handleCancel.bind(this);
        this.handleCancelAll = this.handleCancelAll.bind(this);
        this.handleChangeSupervisor = this.handleChangeSupervisor.bind(this);
        this.handleSave = this.handleSave.bind(this);
        this.handleSwapAgent = this.handleSwapAgent.bind(this);
        this.handleSearchValueChange = this.handleSearchValueChange.bind(this)
        this.handleSearchSupChange = this.handleSearchSupChange.bind(this)
        this.debounceTimeout = null
        this.handleChangeAgentName = this.handleChangeAgentName.bind(this)
        this.handleChangeStatusAgent = this.handleChangeStatusAgent.bind(this)
        this.handleChangeType = this.handleChangeType.bind(this)
        this.handleDesableSave = this.handleDesableSave.bind(this)
    }

    handleChangeType(event){
        this.setState({
            type: event.target.value
        })
    }

    updateAgent(loading){
        this.setState({
            showAgent: false
        })
        axios.get('/api/agents/modal_sd?offset=0&search=' + this.state.searchValue)
        .then(({data}) => {
            if(data){
                const {agent} = this.state
                let agents = data.agents
                const filteredData = agents.filter(item => item.id !== agent.id);
                this.setState({
                    agents: filteredData.slice(0, 6),
                    showAgent: loading
                })
            }
        })
    }

    updateSupervisor(loading){
        this.setState({
            showSupervisor: false
        })
        axios.get('/api/pointages/get_admin_user?value=' + this.state.searchSupValue, {username: localStorage.getItem("username"), secret:localStorage.getItem("secret")})
        .then(({data}) => {
            if(data){
                const {supervisor} = this.state
                let sups = data.users
                const filteredData = sups.filter(item => item.id !== supervisor.id);
                this.setState({
                    supervisors: filteredData.slice(0, 6),
                    showSupervisor: loading
                })
            }
        })
    }

    componentDidUpdate(){
        const { agent, supervisor, isUnregisteredAgent, unregisteredAgentName, desableSave, type, isLoading } = this.state
        const allTypes = ['service24', 'mis_a_pied', 'archive']
        if(isUnregisteredAgent && unregisteredAgentName.trim() && supervisor?.id){
            if(desableSave){
                this.setState({
                    desableSave: false
                })
            }
        }
        else if(!isUnregisteredAgent && agent?.id && supervisor?.id && allTypes.includes(type)){
            if(desableSave){
                this.setState({
                    desableSave: false
                })
            }
        }
        else if(!desableSave){
            this.setState({
                desableSave: true
            })
        }
        else if(!desableSave && isLoading){
            this.setState({
                desableSave: true
            })
        }
    }

    handleAgentCLick(e, ag){
        this.setState({
            agent: ag
        })
    }

    handleCancelAll(){
        this.props.closeModal()
        this.props.closePointageModal()
        this.setState({isLoading: false})
    }
    
    handleCancel(){
        this.props.closeModal()
    }

    handleChangeSupervisor(event){
        this.setState({
            supervisor: event.target.value
        })
    }

    handleSwapAgent (e, ag){
        if(ag.soft_delete){
            this.setState({
                agent: ag,
                type: 'archive',
                searchValue: '',
            })
        }
        else{
            this.setState({
                agent: ag,
                searchValue: '',
            })
        }
    }
    handleSwapSupervisor (e, sup){
        this.setState({
            supervisor: sup,
            searchSupValue: '',
        })
    }

    handleChangeStatusAgent(){
        const { isUnregisteredAgent } = this.state
        this.setState({
            isUnregisteredAgent: !isUnregisteredAgent
        })
    }

    handleDesableSave(value){
        this.setState({
            desableSave: value ?? true
        })
    }
    handleSave(){
        const { supervisor, agent, unregisteredAgentName, isUnregisteredAgent, type } = this.state
        const { site, updateReclamationPointage } = this.props
        this.setState({
            desableSave:true,
            isLoading: true
        })
        let data = new FormData()
        console.log('supervisor: ', supervisor)
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        data.append("site_id", site.idsite)
        if(isUnregisteredAgent)
            data.append("agent_name", unregisteredAgentName)
        else{
            data.append("agent_id", agent ? agent.id : '')
            data.append("type", type)
        }
        if(supervisor?.id)
            data.append("superviseur_id", supervisor.id)
        this.setState({
            errorInfo: ''
        })
        axios.post('/api/pointages/store_reclamation', data)
        .then(({data}) => {
            if(data.error){
                this.setState({
                    errorInfo: data.error,
                    desableSave: false,
                    isLoading: false
                })
            }    
            else {
                updateReclamationPointage(data.reclamation)
                this.handleCancelAll()
            }
        })
    }

    handleSearchValueChange(e){
        const value = e.target.value
        this.setState({searchValue:value})
        if (this.debounceTimeout) {
            clearTimeout(this.debounceTimeout);
        }
        this.debounceTimeout = setTimeout(() => {
            this.updateAgent(true);  
        }, 600);
    }
    
    handleChangeAgentName(e){
        const value = e.target.value
        this.setState({unregisteredAgentName:value})
    }
    handleSearchSupChange(e){
        const value = e.target.value
        this.setState({searchSupValue:value})
        if (this.debounceTimeout) {
            clearTimeout(this.debounceTimeout);
        }
        this.debounceTimeout = setTimeout(() => {
            this.updateSupervisor(true);  
        }, 600);
    }
    render() {
        const { site } = this.props
        const { supervisor, agent, showInputAgent, searchValue, agents, 
            showAgent, supervisors, showSupervisor, showInputSupervisor, 
            searchSupValue, unregisteredAgentName, isUnregisteredAgent, 
            type, errorInfo, desableSave, isLoading } = this.state
        return (
            <div>
                <div>
                    <Modal width="md" 
                        handleSave={this.handleSave} 
                        handleCancel={this.handleCancel}
                        disableSave={desableSave || isLoading}
                    >
                        <h3>Réclamation</h3>
                        <div className='input-container'>
                            <label className="checkbox-container">
                                Agent non enregistré
                                <input onChange={this.handleChangeStatusAgent} name="vigilance" checked={isUnregisteredAgent} type="checkbox"/>
                                <span className="checkmark"></span>
                            </label>
                        </div>
                        {!isUnregisteredAgent ?
                            <div>
                                <label>Agent *</label>
                                <div>
                                    <div className="table">
                                        <div className="cell" style={{height: 'auto', maxHeight: 'auto', minHeight: 40}} >
                                            <div 
                                                className="input-container" 
                                                style={{
                                                    display: 'flex',
                                                    flexWrap: 'wrap', 
                                                    alignItems: 'center',  
                                                    border: '1px solid #ccc',
                                                    padding: '8px',
                                                }}
                                                onClick={() => {this.setState({showInputAgent: true})}}
                                            >
                                                <span style={{ marginRight: '8px' }}>
                                                    { agent?.id &&
                                                        <>
                                                            {
                                                                agent.societe_id == 1 ? 'DGM-' + agent.numero_employe :
                                                                agent.societe_id == 2 ? 'SOIT-' + agent.num_emp_soit :
                                                                agent.societe_id == 3 ? 'ST-' + agent.numero_stagiaire :
                                                                agent.societe_id == 4 ? 'SM' :
                                                                agent.numero_employe ? agent.numero_employe :
                                                                agent.numero_stagentiaire ? agent.numero_stagiaire :
                                                                <span className="purple">Ndf</span>
                                                            }
                                                            {agent.nom}
                                                        </>
                                                    }
                                                </span>
                                                {
                                                    (showInputAgent || !agent?.id) && 
                                                    <input 
                                                        value={searchValue} 
                                                        style={{
                                                            border: 'none', 
                                                            outline: 'none', 
                                                            padding: '8px', 
                                                            backgroundColor: 'transparent',
                                                            flex: '1 1 auto',
                                                            minWidth: '150px',
                                                        }} 
                                                        onChange={(event) =>{this.handleSearchValueChange(event)}}
                                                        type="text"
                                                    />
                                                }
                                            </div>
                                        </div>
                                    </div>
                                        
                                    <div>
                                        { agents.length > 0 && showAgent &&
                                            <ul id="agentListContainer" style={{width: '100%'}} className="comment-vg-list">
                                                {
                                                    agents && agents.map((ag, index) =>{
                                                        return <li key={'key_' + ag.id + index} onClick={(e) => {this.handleSwapAgent(e, ag), this.setState({showAgent:false})}}>
                                                            {ag.nom } 
                                                            {
                                                                ag.societe_id == 1 ? 'DGM-' + ag.numero_employe :
                                                                ag.societe_id == 2 ? 'SOIT-' + ag.num_emp_soit :
                                                                ag.societe_id == 3 ? 'ST-' + ag.numero_stagiaire :
                                                                ag.societe_id == 4 ? 'SM' :
                                                                ag.numero_employe ? ag.numero_employe :
                                                                ag.numero_stagiaire ? ag.numero_stagiaire :
                                                                <span className="purple">Ndf</span>
                                                            }
                                                        </li>
                                                    })   
                                                }
                                            </ul>
                                        }
                                    </div>
                                </div>
                            </div>
                        :
                            <div>
                                <label>Nom d'agent non enregistré *</label>
                                <input 
                                    value={unregisteredAgentName} 
                                    onChange={(event) =>{this.handleChangeAgentName(event)}}
                                    type="text"
                                />
                            </div>
                        }
                        <div className="input-container">
                            <label>Superviseur *</label>
                            <div className="table">
                                <div className="cell" style={{height: 'auto', maxHeight: 'auto', minHeight: 40}} >
                                    <div 
                                        className="input-container" 
                                        style={{
                                            display: 'flex',
                                            flexWrap: 'wrap', 
                                            alignItems: 'center',  
                                            border: '1px solid #ccc',
                                            padding: '8px',
                                        }}
                                        onClick={() => {this.setState({showInputSupervisor: true})}}
                                    >
                                        {supervisor?.id && <span style={{ marginRight: '8px' }}>{supervisor ? (supervisor.name + ' <' + supervisor.email + '>') : ''}</span>}
                                        {showInputSupervisor &&
                                            <input value={searchSupValue}  style={{
                                                border: 'none', 
                                                outline: 'none', 
                                                padding: '8px', 
                                                backgroundColor: 'transparent',
                                                flex: '1 1 auto',
                                                minWidth: '150px',
                                            }} onChange={(event) =>{this.handleSearchSupChange(event)}} type="text"/>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div>
                                { supervisors.length > 0 && showSupervisor &&
                                    <ul id="agentListContainer" style={{width: '100%'}} className="comment-vg-list">
                                        {
                                            supervisors && supervisors.map((sup, index) =>{
                                                return <li key={'key_' + sup.id + index} onClick={(e) => {this.handleSwapSupervisor(e, sup), this.setState({showSupervisor:false})}}>
                                                    {sup.name } 
                                                    {' <'+ sup.email +'>'}
                                                </li>
                                            })   
                                        }
                                    </ul>
                                }
                            </div>
                        </div>
                        {
                            !isUnregisteredAgent &&
                            <div className="input-container">
                                <label>Type de Réclamation *</label>
                                <select id="choix" name="choix" value={type} onChange={(event)=>{this.handleChangeType(event)}}>
                                    <option></option>
                                    <option value="service24">Service 24</option>
                                    <option value="mis_a_pied">Mis à pied</option>
                                    <option value="archive">Agent archivé</option>
                                    {/* <option value="sal_forfait">Salaire forfaitaire</option> */}
                                </select>
                            </div>
                        }
                        {
                            errorInfo && 
                            <span className="pink">{errorInfo}</span>
                        }
                    </Modal>
                </div>
            </div>
        );
    }
}
