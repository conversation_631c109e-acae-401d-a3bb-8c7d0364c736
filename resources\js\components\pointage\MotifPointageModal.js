import React, { Component } from 'react'
import Modal from '../modal/Modal'
import axios from 'axios'

export default class MotifPointageModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            motif: ''
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.handleChangeMotif = this.handleChangeMotif.bind(this)
    }
    handleChangeMotif(e){
        this.setState({
            motif: e.target.value
        })
    }
    handleSave(){
        const {motif} = this.state
        let data = new FormData()
        data.append("username", localStorage.getItem('username'))
        data.append("secret", localStorage.getItem('secret'))
        if(motif && motif.trim())
            data.append("motif", motif.trim())
        axios.post(this.props.action, data)
        .then(({data}) => {
            this.props.updatePointage(data)
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    componentDidMount(){
        const {pointage} = this.props
        this.setState({
            motif: pointage.motif
        })
    }
    render(){
        const {pointage} = this.props
        const {motif} = this.state
        return (
            <Modal 
                disableSave={(!motif && (!pointage.dtarrived || pointage.soft_delete)) ? true : false}  
                handleSave={this.handleSave} 
                handleCancel={this.handleCancel}
            >
                <h3>Pointage</h3>
                <div>Changer le motif de pointage</div>
                {
                    pointage &&
                    <div className="input-container">
                        <label>Motif {(!pointage.dtarrived || pointage.soft_delete) ? '*' : ''}</label>
                        <input onChange={this.handleChangeMotif} value={motif}/>
                    </div>
                }
            </Modal>)
    }
}