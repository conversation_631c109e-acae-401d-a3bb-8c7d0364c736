import React, { Component } from 'react'
import axios from 'axios'

import './agent.css'
import LoadingData from '../../loading/LoadingData'
import InfiniteScroll from 'react-infinite-scroll-component'

export default class Agent extends Component {
    constructor(props) {
        super(props)
        this.state = {
            selectedAgents: [],
            searchValue: '',
            showAddModal: false,
            showEditModal: false,
            showDeleteModal: false,
            loading: true,
            desableSubmit: false,
            agents: []
        }
        this.handleSaveSelect = this.handleSaveSelect.bind(this)
        this.handleChangeSelected = this.handleChangeSelected.bind(this)
        this.setSelectedAgent = this.setSelectedAgent.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.closeModal = this.closeModal.bind(this)
        this.handleShowAddModal = this.handleShowAddModal.bind(this)
        this.handleShowEditModal = this.handleShowEditModal.bind(this)
        this.handleShowDeleteModal = this.handleShowDeleteModal.bind(this)
        this.handleSeachAgent = this.handleSeachAgent.bind(this)
        this.updateData = this.updateData.bind(this)
        this.handleEnterPress = this.handleEnterPress.bind(this)
        this.fetchMoreData = this.fetchMoreData.bind(this)
        this.agentIsSelected = this.agentIsSelected.bind(this)
    }
    handleEnterPress(event) {
        if (event.key === 'Enter') {
            this.updateData(true)
        }
    }
    agentIsSelected(agent) {
        let isSelect = false
        this.state.selectedAgents.forEach(a => {
            if (agent.id == a.id)
                isSelect = true
        });
        return isSelect
    }
    handleChangeSelected(e, agent) {
        e.stopPropagation()
        if (this.props.isForBadge || !agent.pointage_id) {
            const { selectedAgents } = this.state
            console.log(selectedAgents)
            const agents = selectedAgents.slice()
            let foundAgent = false
            selectedAgents.forEach((a) => {
                if (a.id == agent.id) foundAgent = true
            })
            if (!foundAgent) {
                agents.push(agent)
                this.setState({
                    selectedAgents: agents
                })
            }
        }
    }
    handleSaveSelect() {
        const agents = this.state.selectedAgents
        const { currentSite } = this.props
        this.setState({
            desableSubmit: true
        })
        if (currentSite) {
            const data = {
                site_id: currentSite.idsite,
                agent_ids: agents.map((a) => (a.id))
            }
            axios.post('/api/agents/change_site', data)
                .then(({ data }) => {
                    this.props.changeAgent(agents)
                })
        }
        else {
            this.props.changeAgent(agents)
        }
    }
    handleCancel() {
        this.props.closeModal()
    }
    handleShowAddModal() {
        this.setState({
            showAddModal: true
        })
    }
    handleShowEditModal() {
        this.setState({
            showEditModal: true
        })
    }
    handleShowDeleteModal() {
        this.setState({
            showDeleteModal: true
        })
    }
    handleSeachAgent(event) {
        this.setState({
            searchValue: event.target.value
        })
    }
    updateData(loading, clearSearch) {
        const { agents, searchValue } = this.state
        const params = new URLSearchParams()
        params.append('offset', (loading ? 0 : agents.length))
        if (loading)
            this.setState({
                agents: [],
                allDataLoaded: false,
            })
        if (clearSearch)
            this.setState({
                searchValue: ''
            })
        else
            params.append('search', searchValue)

        axios.get('/api/agents/modal?' + params)
            .then(({ data }) => {
                if (data) {
                    if (loading) {
                        this.container.scroll(0, 0)
                        this.setState({
                            agents: data.agents
                        })
                    }
                    else {
                        const list = agents.slice().concat(data.agents)
                        this.setState({
                            agents: list
                        })
                    }
                    this.setState({
                        allDataLoaded: (data.agents.length < 50)
                    })
                }
            })
            .catch((e) => {
                setTimeout(() => {
                    this.updateData()
                }, 10000)
            })
    }

    fetchMoreData() {
        setTimeout(() => {
            this.updateData()
        }, 300);
    }

    setSelectedAgent(value) {
        this.setState({
            selectedAgents: [value]
        })
    }
    componentDidMount() {
        const { agentList } = this.props
        this.setState({
            selectedAgents: agentList ? agentList : []
        })
        this.updateData(true)
    }
    closeModal() {
        this.setState({
            showAddModal: false,
            showEditModal: false,
            showDeleteModal: false
        })
    }
    render() {
        const { isForBadge } = this.props
        const { allDataLoaded, agents, selectedAgents, searchValue, desableSubmit } = this.state
        return (
            <div style={{ zIndex: 200 }} className="fixed-front">
                <div className="table">
                    <div className="modal-container">
                        <div className="modal lg">
                            <div className="modal-content">
                                <div className="table">
                                    <div className="cell">
                                        <h3>Agents</h3>
                                    </div>
                                    <div className="cell right">
                                        <div id="searchSite">
                                            <div>
                                                <input onKeyDown={this.handleEnterPress} onChange={this.handleSeachAgent} value={searchValue} type="text" />
                                                <img onClick={() => { this.updateData(true) }} src="/img/search.svg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <table className="fixed_header default layout-fixed">
                                    <thead>
                                        <tr>
                                            <th className="cellAgentRadio">
                                                {
                                                    selectedAgents.length > 0 &&
                                                    <label className="checkbox-container checkbox-container-lg">
                                                        <input disabled checked name="agentRadio" type="checkbox" />
                                                        <span className="checkmark-lg"></span>
                                                    </label>
                                                }
                                            </th>
                                            <th className="cellAgentNum">Num.</th>
                                            <th className="cellAgentNom">Nom</th>
                                            <th>Site</th>
                                        </tr>
                                    </thead>
                                    <tbody id="scrollableAgent" ref={el => (this.container = el)} style={{ height: "400px" }}>
                                        <InfiniteScroll
                                            scrollableTarget="scrollableAgent"
                                            dataLength={agents.length}
                                            next={this.fetchMoreData}
                                            hasMore={!allDataLoaded}
                                            loader={<LoadingData />}
                                        >
                                            {
                                                agents.map((agent) => {
                                                    return (
                                                        <tr key={agent.id}
                                                            style={{ color: ((!isForBadge && agent.pointage_id) ? '#e91e63' : '') }}
                                                            onClick={(e) => { this.handleChangeSelected(e, agent) }}>
                                                            <td className="cellAgentRadio">
                                                                {
                                                                    (isForBadge || !agent.pointage_id) &&
                                                                    <label className="checkbox-container checkbox-container-lg">
                                                                        <input
                                                                            disabled
                                                                            checked={this.agentIsSelected(agent)}
                                                                            name="agentRadio" type="checkbox" />
                                                                        <span className="checkmark-lg"></span>
                                                                    </label>
                                                                }
                                                            </td>
                                                            <td className="cellAgentNum">
                                                                {
                                                                    agent.societe_id == 1 ? 'DGM-' + agent.numero_employe :
                                                                        agent.societe_id == 2 ? 'SOIT-' + agent.num_emp_soit :
                                                                            agent.societe_id == 3 ? 'ST-' + agent.numero_stagiaire :
                                                                                agent.societe_id == 4 ? 'SM' :
                                                                                    agent.numero_employe ? agent.numero_employe :
                                                                                        agent.numero_stagiaire ? agent.numero_stagiaire :
                                                                                            <span className="purple">Ndf</span>
                                                                }
                                                            </td>
                                                            <td className="cellAgentNom">{agent.nom}</td>
                                                            <td>{agent.site}</td>
                                                        </tr>)
                                                })
                                            }
                                            {
                                                (allDataLoaded && agents.length == 0) &&
                                                <tr>
                                                    <td className='center secondary'>Aucun données trouvé</td>
                                                </tr>
                                            }
                                        </InfiniteScroll>
                                    </tbody>
                                </table>
                                {/* {!allDataLoaded && <LoadingData />} */}

                            </div>
                            <div className="modal-footer">
                                <div className="table">
                                    <div className="cell right">
                                        <button disabled={selectedAgents.length == 0 || desableSubmit} onClick={this.handleSaveSelect} className="btn-primary fix-width">Selectionner</button>
                                        <button onClick={this.handleCancel} className="btn-default fix-width">Annuler</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }
}
