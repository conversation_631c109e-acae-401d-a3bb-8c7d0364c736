import React, { Component } from 'react'
import Modal from '../../modal/Modal'
import axios from 'axios'

export default class DeleteAgentModal extends Component {
    constructor(props){
        super(props)
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }
    handleSave(){
        const data = new FormData()
        axios.post(this.props.action, data)
        .then(({data}) => {
            this.props.updateAgents(true)
            this.props.closeModal()
            this.props.setSelectedAgent(null)
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        return (
            <Modal handleSave={this.handleSave} handleCancel={this.handleCancel}>
                <h3>Agent</h3>
                <div>
                    Voulez-vous vraiment supprimer l'agent : <br/>
                    <center><b>{this.props.nom}</b></center>
                </div>
            </Modal>)
    }
}