import React, { Component } from 'react'
import axios from 'axios'

import Modal from '../../modal/Modal'

export default class EditAgentModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            nom: '',
            numero_stagiaire: '',
            numero_employe: '',
        }
        this.handleChangeNom = this.handleChangeNom.bind(this)
        this.handleChangeNumStg = this.handleChangeNumStg.bind(this)
        this.handleChangeNumEmp = this.handleChangeNumEmp.bind(this)
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }
    handleChangeNom(event){
        this.setState({
            nom: event.target.value
        })
    }
    handleChangeNumStg(event){
        this.setState({
            numero_stagiaire: event.target.value
        })
    }
    handleChangeNumEmp(event){
        this.setState({
            numero_employe: event.target.value
        })
    }
    handleChangeAdresse(event){
        this.setState({
            adresse: event.target.value
        })
    }
    componentDidMount(){
        const {agent} = this.props
        this.setState({
            nom:  agent ? agent.nom : '',
            numero_stagiaire: agent ? agent.numero_stagiaire : '',
            numero_employe: agent ? agent.numero_employe : '',
        })
    }
    toggleAgent(value){
        this.setState({
            showAgent: value
        })
    }
    handleSave(){
        const {nom, numero_stagiaire, numero_employe} = this.state
        let data = new FormData()
        if(nom)
            data.append("nom", nom)
        if(numero_stagiaire)
            data.append("numero_stagiaire", numero_stagiaire)
        if(numero_employe)
            data.append("numero_employe", numero_employe)
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        axios.post(this.props.action, data)
        .then(({data}) => {
            this.props.setSelectedAgent(data)
            this.props.updateAgents()
            this.props.closeModal()
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        const {nom, numero_stagiaire, numero_employe} = this.state
        return (
            <div>
                <Modal handleSave={this.handleSave} handleCancel={this.handleCancel}>
                    <h3>Agent</h3>
                    <div className="input-container">
                        <label>Nom</label>
                        <input onChange={this.handleChangeNom} value={nom}/>
                    </div>
                    <div className="input-container">
                        <label>Num. Stagiaire</label>
                        <input onChange={this.handleChangeNumStg} value={numero_stagiaire}/>
                    </div>
                    <div className="input-container">
                        <label>Num. Employe</label>
                        <input onChange={this.handleChangeNumEmp} value={numero_employe}/>
                    </div>
                </Modal>
            </div>
        )
    }
}