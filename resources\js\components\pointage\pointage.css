#searchSiteGardiennage {
    box-sizing: border-box;
    padding: 10px;
    vertical-align: middle;
    text-align: left;
    border: solid .5px rgba(0, 0, 0, .3);
}

img.refresh-icon {
    height: 30px;
    padding: 5px;
    border-radius: 5px;
    cursor: pointer;
}

img.refresh-icon:hover {
    background-color: whitesmoke;
}

.site-gardiennage-container {
    margin-bottom: 20px;
    padding: 20px;
}

.site-gardiennage-container>h4 {
    margin: 5px 0px
}

.border-cell {
    border: solid .5px rgba(0, 0, 0, .1);
    text-align: center;
}

.border-cell>input {
    border: none;
    outline: none;
}

#phoneAgent {
    padding: 10px;
    color: #444;
    background-color: whitesmoke;
}

.phone-call {
    cursor: pointer;
    padding: 5px;
    border: #aaaaaa solid 1px;
    margin-right: 8px;
}

.phone-call:hover {
    background-color: #aaa;
}

.add-pointage-btn {
    padding-left: 10px;
    width: 30px;
    cursor: pointer;
}

#filterBar {
    display: table-row;
}

div.filterSide {
    display: inline-block;
    width: 50%;
    padding: 10px 20px;
}

#refreshPointageBtn {
    padding-left: 10px;
    width: 25px;
    cursor: pointer;
    vertical-align: middle;
}

.datePointageCell {
    width: 200px;
    min-width: 200px;
    max-width: 200px;
}

.numAgentCell {
    width: 100px;
    min-width: 100px;
    max-width: 100px;
}

.deleteIconCell {
    width: 40px;
    min-width: 40px;
    max-width: 40px;
}

.vigilanceAgentCell {
    text-align: center;
    vertical-align: middle;
    width: 60px;
    min-width: 60px;
    max-width: 60px;
}

.vigilanceTypePointageCell {
    text-align: right;
    vertical-align: middle;
    width: 300px;
    min-width: 300px;
    max-width: 300px;
}

.vigilancePointeuseCell {
    text-align: center;
    vertical-align: middle;
    width: 180px;
    min-width: 180px;
    max-width: 180px;
}

.vigilanceAgentCell>img {
    width: 15px;
    cursor: pointer;
}

.h24AgentCell {
    text-align: center;
    width: 70px;
    min-width: 70px;
    max-width: 70px;
}

.editPointageCell {
    text-align: right;
    width: 100px;
    min-width: 100px;
    max-width: 100px;
}

.editPointageCell>img {
    padding-right: 10px;
    width: 25px;
    cursor: pointer;
}

.headerPointage>div {
    display: inline-block;
}

.nameSitePointage {
    width: 40%;
}

.anomaliePointage {
    width: 20%;
    text-align: center;
}

.phoneAgentPointage {
    width: 40%;
    text-align: right;
}

#cellRefresh {
    display: table-cell;
    width: 40px;
    min-width: 40px;
    max-width: 40px;
    vertical-align: middle;
}

#cellRefresh>img {
    vertical-align: middle;
}

ul#agentListContainer {
    width: calc(100% - 40px);
}

h3#pointageNomHeader {
    max-width: 400px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

#pointagePhoneHeader {
    font-weight: normal;
    font-size: 11pt;
    font-style: italic;
}

#selectedAgentTable>tbody tr {
    display: table;
    width: 100%;
    table-layout: fixed;
    /* even columns width , fix width of table too*/
}

table#selectedAgentTable {
    width: 100%;
}

#siteField,
#horaireSelect {
    display: inline-block;
    padding: 10px;
    border: solid .5px rgba(0, 0, 0, .1);
    margin-right: 10px;
}

#horaireSelect {
    width: 170px;
}

#searchSiteBtn {
    display: inline-block;
    padding: 10px;
    border: solid .5px rgba(0, 0, 0, .1);
    background-color: #336666;
    color: white;
}

#addPointageButton {
    color: white;
    background-color: #336666;
    text-align: center;
    cursor: pointer;
}

.card-pointeuse {
    padding: 5px 0px 5px 10px;
    border-left: solid 5px #888;
}
