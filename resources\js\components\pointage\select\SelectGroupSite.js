import React, { Component } from 'react'
import axios from 'axios'

import './select.css'

export default class SelectGroupSite extends Component {
    constructor(props){
        super(props)
        this.state = {
            groups: null,
            showItem: false
        }
        this.toggleSelect = this.toggleSelect.bind(this)
        this.handleClickItem = this.handleClickItem.bind(this)
    }
    handleClickItem(e, key, value){
        e.stopPropagation()
        this.props.clickItem(key, value)
    }
    componentDidMount(){
        axios.get('/api/group_pointage_sites')
        .then(({data}) => {
            this.setState({
                groups: data
            })
        })
    }
    toggleSelect(event){
        event.stopPropagation()
        this.props.toggleSelect()
    }
    render(){
        const {groups} = this.state
        const {showItem, currentItem, defaultLabel} = this.props
        return (
            <div id="selectBox">
                <div onClick={this.toggleSelect} id="itemSelected">
                    <span className="item-selected">{currentItem? currentItem.nom: (defaultLabel ? defaultLabel : "POINTAGE")}</span>
                </div>
                {
                    showItem
                    &&
                    <div id="itemNotSelected">
                        {
                            groups &&
                            groups.map((item) => (
                                <span onClick={(e) => this.handleClickItem(e, 'item', item)} key={item.id}>{item.nom}</span>
                            ))
                        }
                    </div>
                }
            </div>
        )
    }
}