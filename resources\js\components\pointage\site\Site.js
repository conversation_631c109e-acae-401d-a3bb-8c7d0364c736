import React, { Component } from 'react'
import axios from 'axios'

import LoadingData from '../../loading/LoadingData'
import InfiniteScroll from 'react-infinite-scroll-component'

export default class Site extends Component {
    constructor(props) {
        super(props)
        this.state = {
            selectedSite: null,
            searchValue: '',
            loading: true,
            sites: [],
            allDataLoaded: false,
        }
        this.handleSaveSelect = this.handleSaveSelect.bind(this)
        this.handleChangeSelected = this.handleChangeSelected.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.handleSeachSite = this.handleSeachSite.bind(this)
        this.updateData = this.updateData.bind(this)
        this.handleEnterPress = this.handleEnterPress.bind(this)
        this.fetchMoreData = this.fetchMoreData.bind(this)
    }
    handleEnterPress(event) {
        if (event.key === 'Enter') {
            this.updateData(true)
        }
    }
    handleChangeSelected(site) {
        this.setState({
            selectedSite: site
        })
    }
    handleSaveSelect() {
        this.props.changeSite(this.state.selectedSite)
    }
    handleCancel() {
        this.props.closeModal()
    }
    handleCancel() {
        this.props.closeModal()
    }
    handleSeachSite(event) {
        this.setState({
            searchValue: event.target.value
        })
    }
    updateData(loading, clearSearch) {
        const { sites, searchValue } = this.state
        const { currentSite } = this.props
        const params = new URLSearchParams()
        params.append('offset', (loading ? 0 : sites.length))
        if (loading)
            this.setState({
                sites: [],
                allDataLoaded: false,
            })
        if (clearSearch)
            this.setState({
                searchValue: ''
            })
        else
            params.append('search', searchValue)

        axios.get('/api/selectable_sites/' + currentSite.idsite + '?' + params)
            .then(({ data }) => {
                if (data) {
                    if (loading) {
                        this.container.scroll(0, 0)
                        this.setState({
                            sites: data
                        })
                    }
                    else {
                        const list = sites.slice().concat(data)
                        this.setState({
                            sites: list
                        })
                    }
                    this.setState({
                        allDataLoaded: (data.length < 50)
                    })
                }
            })
    }

    fetchMoreData() {
        setTimeout(() => {
            this.updateData()
        }, 300);
    }

    componentDidMount() {
        this.updateData(true)
    }
    render() {
        const { allDataLoaded, sites, selectedSite, searchValue } = this.state
        return (
            <div style={{ zIndex: 200 }} className="fixed-front">
                <div className="table">
                    <div className="modal-container">
                        <div className="modal md">
                            <div className="modal-content">
                                <div className="table">
                                    <div className="cell">
                                        <h3>Sites</h3>
                                    </div>
                                    <div className="cell right">
                                        <div id="searchSite">
                                            <div>
                                                <input onKeyDown={this.handleEnterPress} onChange={this.handleSeachSite} value={searchValue} type="text" />
                                                <img onClick={() => { this.updateData(true) }} src="/img/search.svg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <table className="fixed_header default layout-fixed">
                                    <tbody id="scrollableSite" ref={el => (this.container = el)} style={{ height: "400px" }}>
                                        <InfiniteScroll
                                            scrollableTarget="scrollableSite"
                                            dataLength={sites.length}
                                            next={this.fetchMoreData}
                                            hasMore={!allDataLoaded}
                                            loader={<LoadingData />}
                                        >{
                                                sites.map((site) => {
                                                    return (
                                                        <tr key={site.idsite} onClick={() => { this.handleChangeSelected(site) }}>
                                                            <td className="cellSiteRadio">
                                                                <label className="checkbox-container">
                                                                    <input checked={(selectedSite && selectedSite.idsite == site.idsite)} name="siteRadio" type="checkbox" />
                                                                    <span className="radiomark-lg"></span>
                                                                </label>
                                                            </td>
                                                            <td className="cellSiteNom">{site.nom}</td>
                                                        </tr>)
                                                })
                                            }
                                            {/* {
                                                !allDataLoaded &&
                                                <tr><LoadingData/></tr>
                                            } */}
                                            {
                                                (allDataLoaded && sites.length == 0) &&
                                                <tr>
                                                    <td className='center secondary'>Aucun données trouvé</td>
                                                </tr>
                                            }
                                        </InfiniteScroll>
                                    </tbody>
                                </table>

                            </div>
                            <div className="modal-footer">
                                <div className="table">
                                    <div className="cell right">
                                        <button disabled={selectedSite == null} onClick={this.handleSaveSelect} className="btn-primary fix-width">Selectionner</button>
                                        <button onClick={this.handleCancel} className="btn-default fix-width">Annuler</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }
}
