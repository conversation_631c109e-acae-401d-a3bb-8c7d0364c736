#siteSearch{
    width: 250px;
    padding: 10px;
}
#cellAddSiteBtn{
    display: table-cell;
    text-align: right;
    vertical-align: middle;
    width: 30px;
}
#addSiteBtn{
    height: 30px;
    padding: 3px;
}
#addSiteBtn:hover{
    background-color: #eee;
    cursor: pointer;
}
.cellSiteRadio{
    width: 70px;
    max-width: 70px;
    min-width: 70px;
}
.cellSiteNom{
    width: 300px;
    max-width: 300px;
    min-width: 300px;
}
.cellSitePhone{
    width: 110px;
    min-width: 110px;
    max-width: 110px;
}
.cellNumAgent{
    width: 80px;
    min-width: 80px;
    max-width: 80px;
    text-align: center;
}
.cellEmpreinte{
    width: 80px;
    min-width: 80px;
    max-width: 80px;
    text-align: center;
}