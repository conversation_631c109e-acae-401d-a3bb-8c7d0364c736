import React, { Component } from 'react'
import Modal from '../modal/Modal'
import axios from 'axios'

export default class DeletePointageModal extends Component {
    constructor(props) {
        super(props)
        this.state = {
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }
    async handleSave() {
        const data = new FormData()
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        await axios.post(this.props.action, data)
            .then(({ data }) => {
                if (data) {
                    this.props.updateData(true)
                    this.props.closeModal()
                }
            })
    }
    handleCancel() {
        this.props.closeModal()
    }
    render() {
        return (
            <Modal confirm={true} handleSave={this.handleSave} handleCancel={this.handleCancel}>
                <div>
                    <h3>Archiver le pointage ?</h3>
                    <div>{this.props.nom}</div>
                    <hr />
                </div>
            </Modal>)
    }
}
