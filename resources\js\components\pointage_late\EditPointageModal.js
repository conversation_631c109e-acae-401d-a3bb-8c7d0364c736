import React, { Component } from 'react'
import axios from 'axios'

import Modal from '../modal/Modal'
import Site from './site/Site'
import Agent from './agent/Agent'
import DatePicker from 'react-datepicker'
import 'react-datepicker/dist/react-datepicker.css'
import moment from 'moment'

export default class EditPointageModal extends Component {
    constructor(props) {
        super(props)
        this.state = {
            showAgentTable: false,
            showPromList: false,
            disabled: false,
            site: null,
            agent: null,
            horairePointage: '',
            datePointage: '',
            error: '',
            pointageList: []
        }
        this.handleSiteChange = this.handleSiteChange.bind(this)
        this.handleAgentChange = this.handleAgentChange.bind(this)
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.toggleSiteList = this.toggleSiteList.bind(this)
        this.toggleAgent = this.toggleAgent.bind(this)
        this.handleChangeDatePointage = this.handleChangeDatePointage.bind(this)
        this.handleChangeHorairePointage = this.handleChangeHorairePointage.bind(this)
        this.addDateService = this.addDateService.bind(this)
        this.removeDatePointage = this.removeDatePointage.bind(this)
    }
    removeDatePointage(datePointage){
        const {pointageList} = this.state
        const newPointageList = []
        pointageList.forEach(p => {
            if(p != datePointage)
                newPointageList.push(p)
        })
        this.setState({
            pointageList: newPointageList
        })

    }
    addDateService(){
        const {horairePointage, datePointage, pointageList} = this.state
        const selectedPointage = moment(datePointage).format("YYYY-MM-DD") + " " + horairePointage
        if(horairePointage && datePointage) {
            if(!pointageList.includes(selectedPointage)){
                pointageList.push(selectedPointage)
                this.setState({
                    pointageList: pointageList,
                    datePointage: '',
                    horairePointage: ''
                })
            }
            else {
                this.setState({
                    datePointage: '',
                    horairePointage: ''
                })
            }
        }
    }

    handleChangeHorairePointage(e){
        this.setState({
            horairePointage: e.target.value,
        })
    }
    handleChangeDatePointage(date){
        this.setState({
            datePointage: date,
        })
    }
    toggleAgent(value){
        this.setState({
            showAgentTable: value
        })
    }
    handleSiteChange(value) {
        this.setState({
            site: value,
            showPromList: false
        })
    }
    handleAgentChange(value) {
        this.setState({
            agent: value,
            showAgentTable: false
        })
    }

    componentDidMount() {
        const { pointage } = this.props
        if (pointage) {
            this.setState({
                isUpdate: true,
                id: pointage.id,
                nom: pointage.nom,
                sim: pointage.sim,
                optic: pointage.optic,
                withoutSensor: pointage.without_sensor,
                groupId: pointage.group_diag_id,
                site: {
                    idsite: pointage.site_id,
                    nom: pointage.site
                }
            })
        }
        axios.get('/api/group_diag_sites')
            .then(({ data }) => {
                this.setState({
                    groupDiagSites: data
                })
            })
    }
    toggleSiteList(value) {
        this.setState({
            showPromList: value
        })
    }
    handlePointageChange(event) {
        this.setState({
            pointage: event.target.value
        })
    }
    handleSave() {
        const { site, agent, pointageList } = this.state
        const { action } = this.props
        this.setState({
            error: "",
            disabled: true
        })
        let data = {}
        if (site)
            data.site_id = site.idsite
        if (agent)
            data.agent_id = agent.id
        if(pointageList.length > 0)
            data.date_pointages = pointageList
        data.username = localStorage.getItem("username")
        data.secret = localStorage.getItem("secret")
        axios.post(action, data)
            .then(({ data }) => {
                if (data.error)
                    this.setState({
                        error: data.error,
                        disabled: false
                    })
                else
                    this.props.updatePointage(true)
            })
    }
    
    toggleAgent(value){
        this.setState({
            showAgentTable: value
        })
    }

    handleCancel() {
        this.props.closeModal()
    }
    render() {
        const { site, agent, pointageList, horairePointage, datePointage, error, showPromList, showAgentTable, disabled } = this.state
        return (
            <div>
                <Modal handleSave={this.handleSave} handleCancel={this.handleCancel} disableSave={disabled || horairePointage || datePointage} width="md" >
                    <h3>Pointage</h3>
                    <div className="input-container">
                        <label>Agent *</label>
                        <div id="agentInputTable" className="table">
                            <span className="cell">
                                {
                                    agent && (
                                    (
                                        agent.societe_id == 1 ? 'DGM-' + agent.numero_employe :
                                        agent.societe_id == 2 ? 'SOIT-' + agent.num_emp_soit :
                                        agent.societe_id == 3 ? 'ST-' + agent.numero_stagiaire :
                                        agent.societe_id == 4 ? 'SM' :
                                        agent.numero_employe ? agent.numero_employe :
                                        agent.numero_stagiaire ? agent.numero_stagiaire :
                                        'Ndf'
                                    ) + ' ' + agent.nom
                                )}
                            </span>
                            <span id="cellAgent"  onClick={()=> {this.toggleAgent(true)}}>
                                <img src="/img/user_agent.svg"/>
                            </span>
                        </div>
                    </div>
                    <div className="input-container">
                        <label>Site *</label>
                        <div id="promInputTable" className="table">
                            <span className="cell">{site && site.nom}</span>
                            <span id="cellProm" onClick={() => { this.toggleSiteList(true) }}>
                                <img src="/img/site.svg" />
                            </span>
                        </div>
                    </div>
                    
                    <div className="row">
                        <div className="cell-45">
                            <div className="input-container">
                                <label>Date *</label>
                                <DatePicker
                                    className="datepicker" 
                                    dateFormat="dd-MM-yyyy"
                                    selected={datePointage} 
                                    onChange={this.handleChangeDatePointage}/> 
                            </div>
                        </div>
                        <div className="cell-45">
                            <div className="input-container">
                                <label>Horaire *</label>
                                <select value={horairePointage} onChange={this.handleChangeHorairePointage}>
                                    <option></option>
                                    <option value="07:00:00">JOUR</option>
                                    <option value="18:00:00">NUIT</option>
                                </select>
                            </div>
                        </div>
                        <div className="cell-10">
                            <img onClick={() => { this.addDateService() }} src="/img/add.svg"/>
                        </div>
                    </div>
                    
                    <table className="fixed_header default layout-fixed">
                        <tbody style={{ height: "200px" }}>
                            {
                                pointageList.map((p, index) =>{
                                    return <tr key={'key_' + index}>
                                        <td className="cellSiteRadio">
                                            <img src="/img/delete.svg" width={20} onClick={() => { this.removeDatePointage(p) }} />
                                        </td>
                                        <td>
                                            {moment(p).format("ddd DD MMM YYYY") + " " + (moment(p).format("HH:mm:ss") == "18:00:00" ? "NUIT" : "JOUR")}
                                        </td>
                                    </tr>
                                })
                            }
                        </tbody>
                    </table>
                    {
                        error &&
                        <div className='red'>{error}</div>
                    }
                </Modal>
                {
                    showAgentTable && 
                    <Agent 
                        changeAgent={this.handleAgentChange} 
                        currentSite={site} 
                        closeModal={() => {this.toggleAgent(false)}}/>
                }
                {
                    showPromList &&
                    <Site
                        defaultSite={site}
                        closeModal={() => { this.toggleSiteList(false) }}
                        changeSite={this.handleSiteChange}/>
                }

            </div>
        )
    }
}