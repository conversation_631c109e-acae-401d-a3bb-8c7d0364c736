import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'
import DeletePointageModal from './DeletePointageModal'
import Pointage from '../agent/pointage/Pointage'
//import RetourPointageModal from './RetourPointageModal'

import 'react-datepicker/dist/react-datepicker.css'
import Operation from '../agent/operation/Operation'

export default class PointageDetail extends Component {
    constructor(props) {
        super(props)
        this.state = {
            showDeletePointageModal: false,
            showRetourPointageModal: false,
            returnData: null,
            heightWindow: 0,
        }
        this.handleChangeTab = this.handleChangeTab.bind(this)
        this.handleClickDeletePointage = this.handleClickDeletePointage.bind(this)
        this.handleClickRestorePointage = this.handleClickRestorePointage.bind(this)
        this.closePointageModal = this.closePointageModal.bind(this)
        this.updatePointage = this.updatePointage.bind(this)
        this.updateData = this.updateData.bind(this)
        this.handleClickCancelRegister = this.handleClickCancelRegister.bind(this)
        this.handleRetourOk = this.handleRetourOk.bind(this)
        this.handleShowRetourModal = this.handleShowRetourModal.bind(this)
        this.updateAgent = this.updateAgent.bind(this)
    }
    handleShowRetourModal(data) {
        this.setState({
            showRetourPointageModal: true,
            returnData: data,
        })
    }
    handleClickRestorePointage() {
        const { currentPointage } = this.props
        this.props.toggleLoading(true)
        let data = new FormData()
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        axios.post('/api/hours/restore_pointage/' + currentPointage.id, data)
            .then(({ data }) => {
                this.props.toggleLoading(false)
                this.updatePointage(currentPointage.id)
            })
    }
    updateAgent() {
        const { currentPointage } = this.props
        this.props.updateAgent(currentPointage.agent_id)
    }
    handleClickCancelRegister() {
        const { currentPointage } = this.props
        this.props.toggleLoading(true)
        let data = new FormData()
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        axios.post('/api/pointages/cancel_register/' + currentPointage.id, data)
            .then(({ data }) => {
                this.handleShowRetourModal(data)
            })
    }
    closePointageModal() {
        this.setState({
            showEditPointageModal: false,
            showDeletePointageModal: false,
            showRetourPointageModal: false,
        })
    }
    updateData() {
        this.props.updateData(true)
    }
    updatePointage(id) {
        this.closePointageModal()
        this.props.updatePointage(id, true)
    }
    handleClickDeletePointage() {
        this.setState({
            showDeletePointageModal: true,
            showEditPointageMenu: false
        })
    }
    handleChangeTab(event) {
        if (event.target.id != this.props.activeTab) {
            this.props.handleChangeTab(event.target.id)
        }
    }
    handleRetourOk() {
        const { returnData } = this.state
        const { currentPointage } = this.props

        if (returnData[returnData.length - 2] == 'success')
            this.updatePointage(currentPointage.id, false)
        else {
            this.props.toggleLoading(false)
        }
        this.setState({
            showRetourPointageModal: false
        })
    }
    componentDidMount() {
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
    }
    resize() {
        this.setState({
            heightWindow: window.innerHeight,
        });
    }
    render() {
        const { showDeletePointageModal, heightWindow } = this.state
        const { activeTab, user, showEditPointageMenu, currentPointage, fonctions, agences, currentDate } = this.props
        return (
            <div>
                {/*
                    showRetourPointageModal &&
                    <RetourPointageModal
                        handleOk={this.handleRetourOk}
                        updatePointage={() => this.props.updatePointage(currentPointage.id, false)}
                        details={returnData} />
                */}
                {/*
                    showEditPointageModal &&
                    <EditPointageModal
                        action={'/api/pointages/update/' + currentPointage.id}
                        closeModal={this.closePointageModal}
                        updatePointage={this.updatePointage}
                        fonctions={fonctions}
                        pointage={currentPointage}
                        agences={agences} />
                */}
                {
                    showDeletePointageModal &&
                    <DeletePointageModal
                        action={'/api/hours/delete_pointage/' + currentPointage.id}
                        closeModal={this.closePointageModal}
                        updateData={this.updateData}
                        nom={'#' + ("000" + currentPointage.id).slice(-3) + ' ' + currentPointage.site} />
                }
                <div className="overview-container">
                    <div className="head-title-overview" title={currentPointage.nom}>
                        <div style={{ height: "40px", lineHeight: "40px" }}>
                            <div className="title-overview">
                                <span className={currentPointage.soft_delete ? "red" : ""} style={{ opacity: .9 }}>
                                    {
                                        currentPointage.societe_id == 1 ? 'DGM-' + currentPointage.numero_employe :
                                            currentPointage.societe_id == 2 ? 'SOIT-' + currentPointage.num_emp_soit :
                                                currentPointage.societe_id == 3 ? 'ST-' + currentPointage.numero_stagiaire :
                                                    currentPointage.societe_id == 4 ? 'SM' :
                                                        currentPointage.numero_employe ? currentPointage.numero_employe :
                                                            currentPointage.numero_stagiaire ? currentPointage.numero_stagiaire :
                                                                'Ndf'
                                    }
                                </span>
                            </div>
                            <div className="overview-edit-icon">
                                <img onClick={() => { this.props.toggleEditPointageMenu(!showEditPointageMenu) }} className="overview-edit-img" src="/img/parametre.svg" />
                                {
                                    showEditPointageMenu &&
                                    <div className="dropdown-overview-edit">
                                        {/* <span onClick={this.handleClickRegisterPointage}>Modifier</span> */}
                                        {
                                            currentPointage.soft_delete ?
                                                <span onClick={this.handleClickRestorePointage}>Restaurer</span>
                                                :
                                                <span onClick={this.handleClickDeletePointage}>Mettre en archive</span>
                                        }
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                    <span className="overview-break-overflow" title={currentPointage.nom}>
                        <b>Nom : </b>{currentPointage.nom}
                    </span><br />
                    <span className="overview-break-overflow" title={currentPointage.site}>
                        <b>Site : </b> {currentPointage.site}
                    </span><br />
                    <span className="overview-break-overflow">
                        <b>Date : </b> {moment(currentPointage.date_pointage).format("DD-MM-YY")} {moment(currentPointage.date_pointage).format("HH:mm:ss") == "18:00:00" ? "NUIT" : "JOUR"}
                    </span>
                </div>
                <div style={{ position: 'relative', top: '2px' }}>
                    <div className="table">
                        <div className="cell">
                            <div id="tabHeaderOverview">
                                <ul>
                                    <li id="pointage" className={activeTab == 'pointage' ? "active-tab" : ""} onClick={this.handleChangeTab}>Pointage</li>
                                    <li id="operation" className={activeTab == 'operation' ? "active-tab" : ""} onClick={this.handleChangeTab}>Traçabilité</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="tabContentOverview">
                    <div id="tabContainer">
                        {
                            activeTab == 'pointage' &&
                            <Pointage currentDate={currentDate} agentId={currentPointage.agent_id} nomAgent={currentPointage.nom} heightWindow={heightWindow} updateAgent={this.updateAgent} />
                        }
                        {
                            activeTab == 'operation' &&
                            <Operation
                                currentDate={currentDate}
                                action={'api/pointages/operation/' + currentPointage.id}
                                heightTable={heightWindow - 500} />
                        }
                    </div>
                </div>
            </div>
        )
    }
}
