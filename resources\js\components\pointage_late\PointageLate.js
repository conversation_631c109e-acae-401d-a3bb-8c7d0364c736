import React, { useState, useEffect, useRef, useCallback } from 'react';
import axios from 'axios';
import moment from 'moment';
import 'moment/locale/fr';
import PointageDetail from './PointageDetail';
import EditPointageModal from './EditPointageModal';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingData from '../loading/LoadingData';

const PointageLate = ({ user, toggleLoading }) => {
    const [allDataLoaded, setAllDataLoaded] = useState(false);
    const [currentDate, setCurrentDate] = useState('');
    const [inputSearch, setInputSearch] = useState('');
    const [currentPointage, setCurrentPointage] = useState(null);
    const [pointages, setPointages] = useState([]);
    const [heightWindow, setHeightWindow] = useState(window.innerHeight);
    const [widthWindow, setWidthWindow] = useState(window.innerWidth);
    const [activeTab, setActiveTab] = useState('pointage');
    const [showEditPointageMenu, setShowEditPointageMenu] = useState(false);
    const [showAddPointageModal, setShowAddPointageModal] = useState(false);
    const [showEditPointageModal, setShowEditPointageModal] = useState(false);
    const containerRef = useRef(null);

    const updateData = useCallback(
        (loading = false, clearSearch = false) => {
            if (loading) {
                toggleLoading(true)
                setShowAddPointageModal(false)
            }
            if (clearSearch) setInputSearch('');
            setCurrentPointage(null);

            axios.get("/api/pointages/late"
                + '?username=' + localStorage.getItem("username") + '&secret=' + localStorage.getItem("secret")
                + "&offset= " + (loading ? 0 : pointages.length) + "&search=" + (clearSearch ? '' : inputSearch))
                .then(({ data }) => {
                    if (data) {
                        if (loading) {
                            containerRef.current.scroll(0, 0);
                            setPointages(data);
                            toggleLoading(false);
                        } else {
                            setPointages(prev => [...prev, ...data]);
                        }
                        setAllDataLoaded(data.length < 50);
                    }
                })
                .catch(() => setTimeout(() => updateData(loading, clearSearch), 10000));
        },
        [pointages.length, inputSearch, toggleLoading]
    );

    const fetchMoreData = () => setTimeout(updateData, 300);

    const updatePointage = (id, isUpdate) => {
        toggleLoading(true);
        axios.get(`/api/pointages/show/${id}?username=${localStorage.getItem("username")}&secret=${localStorage.getItem("secret")}`)
            .then(({ data }) => {
                if (data) {
                    setCurrentPointage(data);
                    setShowAddPointageModal(false);
                    setShowEditPointageModal(false);
                    toggleLoading(false);
                }
            })
            .catch(() => toggleLoading(false));
    };

    const handleEnterPress = (event) => {
        if (event.key === 'Enter') updateData(true);
    };

    useEffect(() => {
        updateData(true);
        setCurrentDate(Date.now())
        const handleResize = () => {
            setHeightWindow(window.innerHeight);
            setWidthWindow(window.innerWidth);
        };
        window.addEventListener("resize", handleResize);
        document.title = "Pointage - TLS";
        return () => window.removeEventListener("resize", handleResize);
    }, []);

    return (
        <div className="table" onClick={() => showEditPointageMenu && setShowEditPointageMenu(false)}>
            <div id="tableContainer">
                <div className="table">
                    <div className="row-header">
                        <h3 className="h3-table">
                            <div className="cell fix-cell-pointage">Pointage</div>
                            <span className="cell center">
                                <div id="searchSite">
                                    <div>
                                        <input onKeyDown={handleEnterPress} onChange={(e) => setInputSearch(e.target.value)} value={inputSearch} type="text" />
                                        <img onClick={() => updateData(true)} src="/img/search.svg" alt="search" />
                                    </div>
                                </div>
                            </span>
                            <span id="cellAddContactBtn">
                                <img height={30} onClick={() => setShowAddPointageModal(true)} title="Nouveau pointage" src="/img/add.svg" alt="add" />
                            </span>
                        </h3>
                    </div>
                    <div className="row-table">
                        <table className="fixed_header visible-scroll layout-fixed">
                            <thead>
                                <tr>
                                    <th className="cellAgentPointage">Agent</th>
                                    <th className="cellDatePointage">Date</th>
                                    <th className="cellSitePointeuse">
                                        Site
                                        <img src="/img/refresh_table.svg" onClick={() => updateData(true)} alt="refresh" />
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="scrollableDiv" ref={containerRef} style={{ height: `${heightWindow - 160}px` }}>
                                <InfiniteScroll
                                    scrollableTarget="scrollableDiv"
                                    dataLength={pointages.length}
                                    next={fetchMoreData}
                                    hasMore={!allDataLoaded}
                                    loader={<LoadingData />}
                                >
                                    {pointages.map(row => (
                                        <tr
                                            key={row.id}
                                            onDoubleClick={() => updatePointage(row.id)}
                                            className={`${row.soft_delete ? "red" : ""} ${(currentPointage && currentPointage.id === row.id) ? "selected-row" : ""}`}
                                        >
                                            <td className="cellAgentPointage">
                                                {
                                                    row.societe_id == 1 ? 'DGM-' + row.numero_employe :
                                                        row.societe_id == 2 ? 'SOIT-' + row.num_emp_soit :
                                                            row.societe_id == 3 ? 'ST-' + row.numero_stagiaire :
                                                                row.societe_id == 4 ? 'SM' :
                                                                    row.numero_employe ? row.numero_employe :
                                                                        row.numero_stagiaire ? row.numero_stagiaire :
                                                                            'Ndf'
                                                } {row.nom}
                                            </td>
                                            <td className="cellDatePointage">
                                                {moment(row.date_pointage).format("DD-MM-YY")} {moment(row.date_pointage).format("HH:mm:ss") == "18:00:00" ? "NUIT" : "JOUR"}
                                            </td>
                                            <td className="cellSitePointeuse" title={row.site}>{row.site}</td>
                                        </tr>
                                    ))}
                                    {
                                        allDataLoaded && pointages.length === 0 && (
                                            <tr><td className='center secondary'>Aucun données trouvé</td></tr>
                                        )}
                                </InfiniteScroll>
                            </tbody>
                        </table>
                        {/* {!allDataLoaded ? <LoadingData /> : <></>} */}

                    </div>
                </div>
            </div>

            {
                showAddPointageModal && (
                    <EditPointageModal
                        action={'/api/pointages/late/store'}
                        closeModal={() => setShowAddPointageModal(false)}
                        updatePointage={() => updateData(true)}
                    />
                )}
            <div className={currentPointage ? "box-shadow-left" : ""} style={{ width: `${widthWindow / 2.5}px`, maxWidth: `${widthWindow / 2.5}px`, minWidth: `${widthWindow / 2.5}px` }} id="overviewContainer">
                {
                    currentPointage ? (
                        <PointageDetail
                            currentPointage={currentPointage}
                            currentDate={currentDate}
                            user={user}
                            updateData={updateData}
                            updatePointage={updatePointage}
                            showEditPointageMenu={showEditPointageMenu}
                            toggleEditPointageMenu={setShowEditPointageMenu}
                            heightWindow={heightWindow}
                            closePointageModal={() => setShowAddPointageModal(false)}
                            activeTab={activeTab}
                            handleChangeTab={setActiveTab}
                            toggleLoading={toggleLoading}
                        />
                    ) : (
                        <div className="img-bg-container">
                            <img className="img-bg-overview" src="/img/tls_background.svg" alt="background" />
                        </div>
                    )}
            </div>
        </div>
    );
};

export default PointageLate;
