import React, { Component } from 'react'
import axios from 'axios'

import Modal from '../modal/Modal'
import Agent from './empreinte/agent/Agent'

export default class RegisterPointeuseModal extends Component {
    constructor(props) {
        super(props)
        this.state = {
            agent: null,
            digit: '',
            error: null,
            disableSave: false,
            showAgentList: false,
        }
        this.handleAgentChange = this.handleAgentChange.bind(this)
        this.handleDigitChange = this.handleDigitChange.bind(this)
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }
    toggleAgent(value) {
        this.setState({
            showAgentList: value
        })
    }
    handleAgentChange(agent) {
        this.setState({
            agent: agent,
            showAgentList: false
        })
    }
    handleDigitChange(e) {
        this.setState({
            digit: e.target.value
        })
    }
    handlePointeuseChange(event) {
        this.setState({
            pointeuse: event.target.value
        })
    }
    handleSave() {
        console.log("handleDoEnrollement")
        const { agent, digit } = this.state
        const { currentPointeuse } = this.props
        console.log(currentPointeuse)
        console.log("is optique: " + currentPointeuse.optic)
        this.setState({
            disableSave: true,
            error: null
        })
        let data = new FormData()
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        if (agent)
            data.append("agent_id", agent.id)
        data.append("digit", digit)
        axios.post("/api/pointeuses/register/" + currentPointeuse.id, data)
            .then(({ data }) => {
                console.log("data result:")
                console.log(data)
                if (data.error) {
                    const firstKey = Object.keys(data.error)[0]
                    const firstValue = data.error[firstKey][0]
                    this.setState({
                        error: {
                            key: firstKey,
                            value: firstValue
                        },
                    }, () => {
                        console.log(this.state.error)
                    })
                }
                else if (data) {
                    this.props.handleShowRetourModal(data)
                }
                this.setState({
                    disableSave: false
                })
            })
            .catch((e) => {
                console.log(e)
                if (e.code == 'ECONNABORTED')
                    this.props.handleShowRetourModal(['Timeout AXIOS...', 'timeout', 'End'])
                this.setState({
                    disableSave: false
                })
            })
    }
    handleCancel() {
        this.props.closeModal()
    }
    render() {
        const { agent, digit, error, showAgentList, disableSave } = this.state
        const { currentPointeuse } = this.props
        return (
            <div>
                <Modal disableSave={disableSave} handleSave={this.handleSave} handleCancel={this.handleCancel}>
                    <h3>Enregistrement</h3>
                    <div className="input-container">
                        <label>Agent *</label>
                        <div>
                            <div className="table">
                                <div className="cell" style={{ height: 40, maxHeight: 40, minHeight: 40 }}>
                                    <div className="input-container">
                                        <input value={agent ? agent.nom : ''} disabled={true} autoComplete="off" type="text" />
                                    </div>
                                </div>
                                <div id="cellRefresh">
                                    <img id="refreshPointageBtn" src="/img/all_agent.svg" onClick={() => { this.toggleAgent(true) }} />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="input-container">
                        <label>Empreinte *</label>
                        <select value={digit} onChange={this.handleDigitChange}>
                            <option></option>
                            {
                                agent && (
                                    !currentPointeuse.optic ?
                                        <>
                                            <option value="1">Gauche</option>
                                            <option value="2">Droite</option>
                                        </>
                                        :
                                        <>
                                            {agent.optic_gauche && <option value="3">Gauche</option>}
                                            {agent.optic_droite && <option value="4">Droite</option>}
                                        </>
                                )
                            }
                        </select>
                    </div>
                    {error && <div className="pink">{error.value}</div>}
                </Modal>
                {
                    showAgentList &&
                    <Agent
                        withoutEmpreinte
                        defaultAgent={agent}
                        action={"/api/agents/pointeuse"}
                        opticPointeuse={currentPointeuse.optic}
                        closeModal={() => { this.toggleAgent(false) }}
                        changeAgent={this.handleAgentChange} />
                }
            </div>
        )
    }
}
