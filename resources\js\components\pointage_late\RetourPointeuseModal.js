import React, { Component } from 'react'

import '../modal/modal.css'

export default class RetourPointeuseModal extends Component {
    constructor(props) {
        super(props)
        this.state = {
            heightWindow: 0,
            widthWindow: 0,
        }
        this.handleOk = this.handleOk.bind(this)
    }
    handleOk() {
        this.props.handleOk()
    }
    resize() {
        this.setState({
            heightWindow: window.innerHeight,
            widthWindow: window.innerWidth
        });
    }
    componentDidMount() {
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
    }
    render() {
        const { heightWindow, details } = this.props
        const status = details.length > 1 ? details[details.length - 2] : ''
        return (
            <div style={{ zIndex: 200, height: heightWindow + "px" }} className="fixed-front">
                <div className="table">
                    <div className="modal-container">
                        <div className="modal sm">
                            <div className="modal-content">
                                <h3 className={status == 'success' ? 'primary' : 'pink'}>
                                    {
                                        (status == 'success') ? 'Requète effectué avec succès' :
                                            (status == 'mysql_error') ? 'Erreur de connexion' :
                                                (status == 'device_not_found') ? 'Appareil non connecté' : ''
                                    }
                                </h3>
                                <textarea disabled={true} rows={10} value={details.join('\n')} />
                            </div>
                            <div className="table modal-footer">
                                <div className="cell-padding right">
                                    <button onClick={this.handleOk} className="btn-default fix-width">OK</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>)
    }
}
