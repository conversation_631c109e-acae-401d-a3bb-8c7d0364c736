import React, { Component } from 'react'
import axios from 'axios'

import './agent.css'
import LoadingData from '../../loading/LoadingData'
import InfiniteScroll from 'react-infinite-scroll-component'

export default class Agent extends Component {
    constructor(props) {
        super(props)
        this.state = {
            selectedAgent: null,
            searchValue: '',
            showAddModal: false,
            showEditModal: false,
            showDeleteModal: false,
            loading: true,
            agents: []
        }
        this.handleSaveSelect = this.handleSaveSelect.bind(this)
        this.handleChangeSelected = this.handleChangeSelected.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.closeModal = this.closeModal.bind(this)
        this.handleShowAddModal = this.handleShowAddModal.bind(this)
        this.handleShowEditModal = this.handleShowEditModal.bind(this)
        this.handleShowDeleteModal = this.handleShowDeleteModal.bind(this)
        this.handleSeachAgent = this.handleSeachAgent.bind(this)
        this.updateData = this.updateData.bind(this)
        this.handleEnterPress = this.handleEnterPress.bind(this)
        this.fetchMoreData = this.fetchMoreData.bind(this)
    }
    handleEnterPress(event) {
        if (event.key === 'Enter') {
            this.updateData(true)
        }
    }
    handleChangeSelected(e, agent) {
        e.stopPropagation()
        this.setState({
            selectedAgent: agent
        })
    }
    handleSaveSelect() {
        const { selectedAgent } = this.state
        this.props.changeAgent(selectedAgent)
    }
    handleCancel() {
        this.props.closeModal()
    }
    handleShowAddModal() {
        this.setState({
            showAddModal: true
        })
    }
    handleShowEditModal() {
        this.setState({
            showEditModal: true
        })
    }
    handleShowDeleteModal() {
        this.setState({
            showDeleteModal: true
        })
    }
    handleSeachAgent(event) {
        this.setState({
            searchValue: event.target.value
        })
    }
    updateData(loading, clearSearch) {
        const { agents, searchValue } = this.state
        const params = new URLSearchParams()
        params.append('offset', (loading ? 0 : agents.length))
        if (loading)
            this.setState({
                agents: [],
                allDataLoaded: false,
            })
        if (clearSearch)
            this.setState({
                searchValue: ''
            })
        else
            params.append('search', searchValue)

        axios.get('/api/agents/modal?' + params)
            .then(({ data }) => {
                if (data) {
                    if (loading) {
                        this.container.scroll(0, 0)
                        this.setState({
                            agents: data.agents
                        })
                    }
                    else {
                        const list = agents.slice().concat(data.agents)
                        this.setState({
                            agents: list
                        })
                    }
                    this.setState({
                        allDataLoaded: (data.agents.length < 50)
                    })
                }
            })
            .catch((e) => {
                setTimeout(() => {
                    this.updateData()
                }, 10000)
            })
    }

    fetchMoreData() {
        setTimeout(() => {
            this.updateData()
        }, 300);
    }
    componentDidMount() {
        this.updateData(true)
    }
    closeModal() {
        this.setState({
            showAddModal: false,
            showEditModal: false,
            showDeleteModal: false
        })
    }
    render() {
        const { isForBadge } = this.props
        const { allDataLoaded, agents, selectedAgent, searchValue } = this.state
        return (
            <div style={{ zIndex: 200 }} className="fixed-front">
                <div className="table">
                    <div className="modal-container">
                        <div className="modal lg">
                            <div className="modal-content">
                                <div className="table">
                                    <div className="cell">
                                        <h3>Agents</h3>
                                    </div>
                                    <div className="cell right">
                                        <div id="searchSite">
                                            <div>
                                                <input onKeyDown={this.handleEnterPress} onChange={this.handleSeachAgent} value={searchValue} type="text" />
                                                <img onClick={() => { this.updateData(true) }} src="/img/search.svg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <table className="fixed_header default layout-fixed">
                                    <thead>
                                        <tr>
                                            <th className="cellAgentRadio">
                                            </th>
                                            <th className="cellAgentNum">Num.</th>
                                            <th className="cellAgentNom">Nom</th>
                                            <th>Site</th>
                                        </tr>
                                    </thead>
                                    <tbody id="scrollableAgent" ref={el => (this.container = el)} style={{ height: "400px" }}>
                                        <InfiniteScroll
                                            scrollableTarget="scrollableAgent"
                                            dataLength={agents.length}
                                            next={this.fetchMoreData}
                                            hasMore={!allDataLoaded}
                                            loader={<LoadingData />}
                                        >
                                            {
                                                agents.map((agent) => {
                                                    return (
                                                        <tr key={agent.id}
                                                            style={{ color: ((!isForBadge && agent.pointage_id) ? '#e91e63' : '') }}
                                                            onClick={(e) => { this.handleChangeSelected(e, agent) }}>
                                                            <td className="cellAgentRadio">
                                                                <label className="checkbox-container">
                                                                    <input checked={(selectedAgent && selectedAgent.id == agent.id)} name="agentRadio" type="radio" />
                                                                    <span className="radiomark-lg"></span>
                                                                </label>
                                                            </td>
                                                            <td className="cellAgentNum">
                                                                {
                                                                    agent.societe_id == 1 ? 'DGM-' + agent.numero_employe :
                                                                        agent.societe_id == 2 ? 'SOIT-' + agent.num_emp_soit :
                                                                            agent.societe_id == 3 ? 'ST-' + agent.numero_stagiaire :
                                                                                agent.societe_id == 4 ? 'SM' :
                                                                                    agent.numero_employe ? agent.numero_employe :
                                                                                        agent.numero_stagiaire ? agent.numero_stagiaire :
                                                                                            <span className="purple">Ndf</span>
                                                                }
                                                            </td>
                                                            <td className="cellAgentNom">{agent.nom}</td>
                                                            <td>{agent.site}</td>
                                                        </tr>)
                                                })
                                            }
                                            {
                                                (allDataLoaded && agents.length == 0) &&
                                                <tr>
                                                    <td className='center secondary'>Aucun données trouvé</td>
                                                </tr>
                                            }
                                        </InfiniteScroll>
                                    </tbody>
                                </table>
                                {/* {!allDataLoaded && <LoadingData />} */}

                            </div>
                            <div className="modal-footer">
                                <div className="table">
                                    <div className="cell right">
                                        <button disabled={!selectedAgent} onClick={this.handleSaveSelect} className="btn-primary fix-width">Selectionner</button>
                                        <button onClick={this.handleCancel} className="btn-default fix-width">Annuler</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }
}
