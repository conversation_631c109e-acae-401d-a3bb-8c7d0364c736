import React, { Component } from 'react'
import axios from 'axios'

import Modal from '../modal/Modal'
import Site from './site/Site'

export default class EditPointeuseModal extends Component {
    constructor(props) {
        super(props)
        this.state = {
            isUpdate: false,
            withoutSensor: false,
            optic: false,
            groupId: '',
            nom: '',
            id: '',
            sim: '',
            error: '',
            site: null
        }
        this.handleGroupChange = this.handleGroupChange.bind(this)
        this.handleSimChange = this.handleSimChange.bind(this)
        this.handleSiteChange = this.handleSiteChange.bind(this)
        this.handleIDChange = this.handleIDChange.bind(this)
        this.handleNomChange = this.handleNomChange.bind(this)
        this.handleCapteurChange = this.handleCapteurChange.bind(this)
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.toggleSiteList = this.toggleSiteList.bind(this)
        this.handleEmpreinteOptic = this.handleEmpreinteOptic.bind(this)
        this.handleWithoutSensor = this.handleWithoutSensor.bind(this)
    }
    handleWithoutSensor(e) {
        this.setState({
            withoutSensor: e.target.checked
        })
    }
    handleGroupChange(e) {
        this.setState({
            groupId: e.target.value
        })
    }
    handleSimChange(e) {
        this.setState({
            sim: e.target.value
        })
    }
    handleSiteChange(value) {
        this.setState({
            site: value,
            showPromList: false
        })
    }

    handleEmpreinteOptic(e) {
        this.setState({
            optic: e.target.checked
        })
    }
    componentDidMount() {
        const { pointeuse } = this.props
        if (pointeuse) {
            this.setState({
                isUpdate: true,
                id: pointeuse.id,
                nom: pointeuse.nom,
                sim: pointeuse.sim,
                optic: pointeuse.optic,
                withoutSensor: pointeuse.without_sensor,
                groupId: pointeuse.group_diag_id,
                site: {
                    idsite: pointeuse.site_id,
                    nom: pointeuse.site
                }
            })
        }
        axios.get('/api/group_diag_sites')
            .then(({ data }) => {
                this.setState({
                    groupDiagSites: data
                })
            })
    }
    toggleSiteList(value) {
        this.setState({
            showPromList: value
        })
    }
    handleIDChange(e) {
        this.setState({
            id: e.target.value
        })
    }
    handleNomChange(event) {
        this.setState({
            nom: event.target.value
        })
    }
    handleCapteurChange(event) {
        this.setState({
            capteurId: event.target.value
        })
    }
    handlePointeuseChange(event) {
        this.setState({
            pointeuse: event.target.value
        })
    }
    handleSave() {
        const { id, nom, sim, site, optic, groupId, withoutSensor } = this.state
        const { action } = this.props
        this.setState({
            error: ""
        })
        let data = new FormData()
        if (site)
            data.append("site_id", site.idsite)
        data.append("optic", (optic ? 1 : 0))
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        data.append("without_sensor", (withoutSensor ? 1 : 0))
        data.append("group_diag_id", groupId)
        data.append("id", id)
        data.append("nom", nom)
        data.append("sim", sim)
        axios.post(action, data)
            .then(({ data }) => {
                if (data.error)
                    this.setState({
                        error: data.error
                    })
                else {
                    this.props.updatePointeuse(data, true)
                    this.props.closeModal()
                }
            })
    }
    handleCancel() {
        this.props.closeModal()
    }
    render() {
        const { id, site, nom, sim, optic, error, isUpdate, showPromList, groupDiagSites, groupId, withoutSensor } = this.state
        const { pointeuse } = this.props
        return (
            <div>
                <Modal handleSave={this.handleSave} handleCancel={this.handleCancel}>
                    <h3>Pointeuse</h3>
                    <div className="input-container">
                        <label className="checkbox-container">
                            Sans capteur
                            <input onChange={this.handleWithoutSensor} checked={withoutSensor} type="checkbox" />
                            <span className="checkmark"></span>
                        </label>
                    </div>
                    {
                        !withoutSensor &&
                        <div className="input-container">
                            <label className="checkbox-container">
                                Capteur d'empreinte optique
                                <input onChange={this.handleEmpreinteOptic} checked={optic} type="checkbox" />
                                <span className="checkmark"></span>
                            </label>
                        </div>
                    }
                    {
                        !isUpdate &&
                        <div className="input-container">
                            <label>ID *</label>
                            <input value={id} onChange={this.handleIDChange} type="number" />
                        </div>
                    }
                    <div className="input-container">
                        <label>Site *</label>
                        <div id="promInputTable" className="table">
                            <span className="cell">{site && site.nom}</span>
                            {
                                <span id="cellProm" onClick={() => { this.toggleSiteList(true) }}>
                                    <img id="clientImg" src="/img/site.svg" />
                                </span>
                            }
                        </div>
                    </div>
                    <div className="input-container">
                        <label>Description *</label>
                        <input value={nom} onChange={this.handleNomChange} type="text" />
                    </div>
                    <div className="input-container">
                        <label>SIM</label>
                        <input value={sim} onChange={this.handleSimChange} type="text" />
                    </div>
                    <div className="input-container">
                        <label>Groupe diagnostique *</label>
                        <select onChange={this.handleGroupChange} value={groupId}>
                            <option value="" />
                            {
                                groupDiagSites &&
                                groupDiagSites.map((group) => (
                                    <option key={group.id} value={group.id}>{group.nom}</option>
                                ))
                            }
                        </select>
                    </div>
                    {
                        error &&
                        <div className='red'>{error}</div>
                    }
                </Modal>
                {
                    showPromList &&
                    <Site
                        defaultSite={site}
                        closeModal={() => { this.toggleSiteList(false) }}
                        changeSite={this.handleSiteChange} />
                }

            </div>
        )
    }
}
