import React from 'react';
import Modal from '../modal/Modal';
import '../../../css/operator-selection.css';

const OperatorSelectionModal = ({ onSelect, onCancel }) => {
    const operators = [
        { id: 'yas', name: 'Ya<PERSON>', code: '#120#', logo: '/img/yas.svg' },
        { id: 'orange', name: 'Orange', code: '#888#', logo: '/img/orange.png' },
        { id: 'airtel', name: 'Airtel', code: '*123#', logo: '/img/airtel.png' }
    ];

    return (
        <Modal handleCancel={onCancel}>
            <div>
                <h3>Sélectionner l'opérateur</h3>
                <div className="operator-grid">
                    {operators.map(operator => (
                        <div
                            key={operator.id}
                            className="operator-card"
                            onClick={() => onSelect(operator.code)}
                        >
                            <img
                                src={operator.logo}
                                alt={operator.name}
                                className="operator-logo"
                            />
                            <div className="operator-name">
                                {operator.name}
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </Modal>
    );
};

export default OperatorSelectionModal;



