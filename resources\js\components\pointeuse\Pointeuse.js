import React, { useState, useEffect, useRef, useCallback } from 'react';
import axios from 'axios';
import moment from 'moment';
import 'moment/locale/fr';
import PointeuseDetail from './PointeuseDetail';
import EditPointeuseModal from './EditPointeuseModal';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingData from '../loading/LoadingData';

const Biometrique = ({ user, toggleLoading }) => {
    const [allDataLoaded, setAllDataLoaded] = useState(false);
    const [currentDate, setCurrentDate] = useState('');
    const [inputSearch, setInputSearch] = useState('');
    const [currentPointeuse, setCurrentPointeuse] = useState(null);
    const [pointeuses, setPointeuses] = useState([]);
    const [heightWindow, setHeightWindow] = useState(window.innerHeight);
    const [widthWindow, setWidthWindow] = useState(window.innerWidth);
    const [activeTab, setActiveTab] = useState('empreinte');
    const [showEditPointeuseMenu, setShowEditPointeuseMenu] = useState(false);
    const [showAddPointeuseModal, setShowAddPointeuseModal] = useState(false);
    const containerRef = useRef(null);

    const updateData = useCallback(
        (loading = false, clearSearch = false) => {
            if (loading) toggleLoading(true);
            if (clearSearch) setInputSearch('');
            setCurrentPointeuse(null);

            axios.get(`/api/pointeuses?offset=${loading ? 0 : pointeuses.length}&search=${clearSearch ? '' : inputSearch}`)
                .then(({ data }) => {
                    if (data) {
                        if (loading) {
                            containerRef.current.scroll(0, 0);
                            setPointeuses(data);
                            toggleLoading(false);
                        } else {
                            setPointeuses(prev => [...prev, ...data]);
                        }
                        setAllDataLoaded(data.length < 50);
                    }
                })
                .catch(() => setTimeout(() => updateData(loading, clearSearch), 10000));
        },
        [pointeuses.length, inputSearch, toggleLoading]
    );

    const fetchMoreData = () => setTimeout(updateData, 300);

    const updatePointeuse = (id, isUpdate) => {
        toggleLoading(true);
        axios.get(`/api/pointeuses/show/${id}?username=${localStorage.getItem("username")}&secret=${localStorage.getItem("secret")}`)
            .then(({ data }) => {
                if (data) {
                    const updatedPointeuses = pointeuses.map(p =>
                        p.id === data.pointeuse.id ? (isUpdate ? null : data.pointeuse) : p
                    ).filter(Boolean);
                    if (isUpdate) updatedPointeuses.unshift(data.pointeuse);
                    setPointeuses(updatedPointeuses);
                    setCurrentPointeuse(data.pointeuse);
                    setShowAddPointeuseModal(false);
                    setShowEditPointeuseModal(false);
                    toggleLoading(false);
                }
            })
            .catch(() => toggleLoading(false));
    };

    const handleEnterPress = (event) => {
        if (event.key === 'Enter') updateData(true);
    };

    const getColor = (pointeuse) => {
        if (currentDate) {
            const thirtyDaysAgo = moment(currentDate).subtract(30, 'days');
            if (!pointeuse.last_date_pointage || moment(pointeuse.last_date_pointage).isBefore(thirtyDaysAgo)) {
                return 'red';
            }
            if (!pointeuse.numero_employe && pointeuse.date_embauche && moment(pointeuse.date_embauche).isBefore(moment(currentDate).subtract(6, 'months'))) {
                return 'green';
            }
        }
        return '';
    };

    useEffect(() => {
        updateData(true);
        setCurrentDate(Date.now())
        const handleResize = () => {
            setHeightWindow(window.innerHeight);
            setWidthWindow(window.innerWidth);
        };
        window.addEventListener("resize", handleResize);
        document.title = "Pointeuse - TLS";
        return () => window.removeEventListener("resize", handleResize);
    }, []);

    return (
        <div className="table" onClick={() => showEditPointeuseMenu && setShowEditPointeuseMenu(false)}>
            <div id="tableContainer">
                <div className="table">
                    <div className="row-header">
                        <h3 className="h3-table">
                            <div className="cell fix-cell-pointeuse">Pointeuse</div>
                            <span className="cell center">
                                <div id="searchSite">
                                    <div>
                                        <input onKeyDown={handleEnterPress} onChange={(e) => setInputSearch(e.target.value)} value={inputSearch} type="text" />
                                        <img onClick={() => updateData(true)} src="/img/search.svg" alt="search" />
                                    </div>
                                </div>
                            </span>
                            {user.role === "root" && (
                                <span id="cellAddContactBtn">
                                    <img height={30} onClick={() => setShowAddPointeuseModal(true)} title="Nouveau pointeuse" src="/img/add.svg" alt="add" />
                                </span>
                            )}
                        </h3>
                    </div>
                    <div className="row-table">
                        <table className="fixed_header visible-scroll layout-fixed">
                            <thead>
                                <tr>
                                    <th className="cellNum">ID</th>
                                    <th className="cellSite">Site</th>
                                    <th className="cellSitePointeuse">
                                        Description
                                        <img src="/img/refresh_table.svg" onClick={() => updateData(true)} alt="refresh" />
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="scrollableDiv" ref={containerRef} style={{ height: `${heightWindow - 160}px` }}>
                                <InfiniteScroll
                                    scrollableTarget="scrollableDiv"
                                    dataLength={pointeuses.length}
                                    next={fetchMoreData}
                                    hasMore={!allDataLoaded}
                                    loader={<LoadingData />}
                                >
                                    {pointeuses.map(row => (
                                        <tr
                                            key={row.id}
                                            onDoubleClick={() => updatePointeuse(row.id)}
                                            className={`${row.soft_delete ? "red" : row.check_panic === 1 ? "purple" : ""} ${(currentPointeuse && currentPointeuse.id === row.id) ? "selected-row" : ""}`}
                                        >
                                            <td className="cellNum">{("000" + row.id).slice(-4)}</td>
                                            <td className="cellSite" title={row.site}>{row.site}</td>
                                            <td className="cellSitePointeuse" title={row.nom}>{row.nom}</td>
                                        </tr>
                                    ))}
                                    {allDataLoaded && pointeuses.length === 0 && (
                                        <tr><td className='center secondary'>Aucun données trouvé</td></tr>
                                    )}
                                </InfiniteScroll>
                            </tbody>
                        </table>
                        {/* {!allDataLoaded ? <LoadingData /> : <></>} */}

                    </div>
                </div>
            </div>
            {showAddPointeuseModal && (
                <EditPointeuseModal
                    action={'/api/pointeuses/store'}
                    closeModal={() => setShowAddPointeuseModal(false)}
                    updatePointeuse={updatePointeuse}
                />
            )}
            <div className={currentPointeuse ? "box-shadow-left" : ""} style={{ width: `${widthWindow / 2.5}px`, maxWidth: `${widthWindow / 2.5}px`, minWidth: `${widthWindow / 2.5}px` }} id="overviewContainer">
                {currentPointeuse ? (
                    <PointeuseDetail
                        currentPointeuse={currentPointeuse}
                        currentDate={currentDate}
                        user={user}
                        updateData={updateData}
                        updatePointeuse={updatePointeuse}
                        getColor={getColor}
                        showEditPointeuseMenu={showEditPointeuseMenu}
                        toggleEditPointeuseMenu={setShowEditPointeuseMenu}
                        heightWindow={heightWindow}
                        closePointeuseModal={() => setShowAddPointeuseModal(false)}
                        activeTab={activeTab}
                        handleChangeTab={setActiveTab}
                        toggleLoading={toggleLoading}
                    />
                ) : (
                    <div className="img-bg-container">
                        <img className="img-bg-overview" src="/img/tls_background.svg" alt="background" />
                    </div>
                )}
            </div>
        </div>
    );
};

export default Biometrique;
