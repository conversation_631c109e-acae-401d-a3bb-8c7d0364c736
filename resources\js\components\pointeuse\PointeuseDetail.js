import React, { useState, useEffect } from 'react';
import axios from 'axios';
import EditPointeuseModal from './EditPointeuseModal';
import DeletePointeuseModal from './DeletePointeuseModal';
import RetourPointeuseModal from './RetourPointeuseModal';
import 'react-datepicker/dist/react-datepicker.css';
import Empreinte from './empreinte/Empreinte';
import Operation from '../agent/operation/Operation';
import Historique from '../site/historique/Historique';
import RegisterPointeuseModal from './RegisterPointeuseModal';
import OperatorSelectionModal from './OperatorSelectionModal';

const PointeuseDetail = ({
    activeTab,
    user,
    showEditPointeuseMenu,
    currentPointeuse,
    fonctions,
    agences,
    currentDate,
    toggleEditPointeuseMenu,
    handleChangeTab,
    updatePointeuse,
    toggleLoading,
    updateData,
}) => {
    const [showEditPointeuseModal, setShowEditPointeuseModal] = useState(false);
    const [showDeletePointeuseModal, setShowDeletePointeuseModal] = useState(false);
    const [showRetourPointeuseModal, setShowRetourPointeuseModal] = useState(false);
    const [showRegisterPointeuseModal, setShowRegisterPointeuseModal] = useState(false);
    const [returnData, setReturnData] = useState(null);
    const [heightWindow, setHeightWindow] = useState(window.innerHeight);
    const [showOperatorModal, setShowOperatorModal] = useState(false);

    const handleClickRegisterPointeuse = () => setShowRegisterPointeuseModal(true);

    const handleToggleActivation = () => {
        toggleLoading(true);
        axios
            .post(`/api/pointeuses/toggle_activation/${currentPointeuse.id}`, new FormData())
            .then(({ data }) => updatePointeuse(data));
    };

    const handleShowRetourModal = (data) => {
        setShowRetourPointeuseModal(true);
        setReturnData(data);
    };

    const handleClickUpdateSim = () => {
        setShowOperatorModal(true);
    };

    // Add this new handler
    const handleOperatorSelect = (ussdCode) => {
        setShowOperatorModal(false);
        toggleLoading(true);

        const data = new FormData();
        data.append("username", localStorage.getItem("username"));
        data.append("secret", localStorage.getItem("secret"));
        data.append("ussdCode", ussdCode);

        axios
            .post(`/api/pointeuses/update_sim/${currentPointeuse.id}`, data)
            .then(({ data }) => handleShowRetourModal(data))
            .finally(() => toggleLoading(false));
    };

    const handleClickCancelRegister = () => {
        toggleLoading(true);
        const data = new FormData();
        data.append("username", localStorage.getItem("username"));
        data.append("secret", localStorage.getItem("secret"));
        axios
            .post(`/api/pointeuses/cancel_register/${currentPointeuse.id}`, data)
            .then(({ data }) => handleShowRetourModal(data));
    };

    const handleClickListId = () => {
        toggleLoading(true);
        const data = new FormData();
        data.append("username", localStorage.getItem("username"));
        data.append("secret", localStorage.getItem("secret"));
        axios
            .post(`/api/pointeuses/list_id/${currentPointeuse.id}`, data)
            .then(({ data }) => handleShowRetourModal(data));
    };

    const closePointeuseModal = () => {
        setShowEditPointeuseModal(false);
        setShowDeletePointeuseModal(false);
        setShowRetourPointeuseModal(false);
        setShowRegisterPointeuseModal(false);
    };

    const handleRetourOk = () => {
        if (returnData[returnData.length - 2] === 'success') {
            updatePointeuse(currentPointeuse.id, false);
        } else {
            toggleLoading(false);
        }
        setShowRetourPointeuseModal(false);
    };

    useEffect(() => {
        const resize = () => setHeightWindow(window.innerHeight);
        window.addEventListener("resize", resize);
        return () => window.removeEventListener("resize", resize);
    }, []);

    return (
        <div>
            {showRegisterPointeuseModal && (
                <RegisterPointeuseModal
                    currentPointeuse={currentPointeuse}
                    closeModal={closePointeuseModal}
                    handleShowRetourModal={handleShowRetourModal}
                />
            )}
            {showOperatorModal && (
                <OperatorSelectionModal
                    onSelect={handleOperatorSelect}
                    onCancel={() => setShowOperatorModal(false)}
                />
            )}
            {showRetourPointeuseModal && (
                <RetourPointeuseModal
                    handleOk={handleRetourOk}
                    updatePointeuse={() => updatePointeuse(currentPointeuse.id, false)}
                    details={returnData}
                />
            )}
            {showEditPointeuseModal && (
                <EditPointeuseModal
                    action={`/api/pointeuses/update/${currentPointeuse.id}`}
                    closeModal={closePointeuseModal}
                    updatePointeuse={updatePointeuse}
                    fonctions={fonctions}
                    pointeuse={currentPointeuse}
                    agences={agences}
                />
            )}
            {showDeletePointeuseModal && (
                <DeletePointeuseModal
                    action={`/api/pointeuses/delete/${currentPointeuse.id}`}
                    closeModal={closePointeuseModal}
                    updateData={updateData}
                    nom={`#${("000" + currentPointeuse.id).slice(-3)} ${currentPointeuse.site}`}
                />
            )}

            <div className="overview-container">
                <div className="head-title-overview" title={currentPointeuse.nom}>
                    <div style={{ height: "40px", lineHeight: "40px" }}>
                        <div className="title-overview">
                            <span className={currentPointeuse.soft_delete ? "red" : ""} style={{ opacity: 0.9 }}>
                                {`#${("000" + currentPointeuse.id).slice(-4)}`}
                            </span>
                        </div>
                        <div className="overview-edit-icon">
                            <img
                                onClick={() => toggleEditPointeuseMenu(!showEditPointeuseMenu)}
                                className="overview-edit-img"
                                src="/img/parametre.svg"
                                alt="edit"
                            />
                            {showEditPointeuseMenu && (
                                <div className="dropdown-overview-edit">
                                    <span onClick={handleClickRegisterPointeuse}>Enregistrer un empreinte</span>
                                    {currentPointeuse.register_mode && (
                                        <span onClick={handleClickCancelRegister}>Annuler l'enregrement</span>
                                    )}
                                    <span onClick={handleClickListId}>Mettre à jour les empreintes</span>
                                    {user.role === "root" && (
                                        <>
                                            <span onClick={handleToggleActivation}>
                                                {`${currentPointeuse.soft_delete ? 'Activer' : 'Désactiver'} la pointeuse`}
                                            </span>
                                            <span onClick={() => setShowEditPointeuseModal(true)}>Modifier la pointeuse</span>
                                            <span onClick={handleClickUpdateSim}>Requête USSD</span>
                                        </>
                                    )}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
                <span className="overview-break-overflow" title={currentPointeuse.site}>
                    <b>Site : </b> {currentPointeuse.site}
                </span>
                <br />
                <span className="overview-break-overflow" title={currentPointeuse.nom}>
                    <b>Description : </b>{currentPointeuse.nom}
                </span>
                <div className="table">
                    <span className="cell">
                        <b>SIM : </b> {currentPointeuse.sim}
                    </span>
                    <span className="cell right" style={{ height: "30px" }}>
                        {currentPointeuse.optic ? (
                            <span className="badge bg-purple">Optique</span>
                        ) : (
                            <span className="badge bg-primary">Capacitif</span>
                        )}
                    </span>
                </div>
            </div>

            <div style={{ position: 'relative', top: '2px' }}>
                <div className="table">
                    <div className="cell">
                        <div id="tabHeaderOverview">
                            <ul>
                                <li
                                    id="empreinte"
                                    className={activeTab == 'empreinte' ? "active-tab" : ""}
                                    onClick={() => handleChangeTab('empreinte')}
                                >
                                    Empreinte
                                </li>
                                <li
                                    id="historique"
                                    className={activeTab == 'historique' ? "active-tab" : ""}
                                    onClick={() => handleChangeTab('historique')}
                                >
                                    Historique
                                </li>
                                <li
                                    id="operation"
                                    className={activeTab == 'operation' ? "active-tab" : ""}
                                    onClick={() => handleChangeTab('operation')}
                                >
                                    Traçabilité
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div id="tabContentOverview">
                <div id="tabContainer">
                    {activeTab == 'empreinte' && (
                        <Empreinte
                            currentPointeuse={currentPointeuse}
                            updatePointeuse={() => updatePointeuse(currentPointeuse.id)}
                            handleShowRetourModal={handleShowRetourModal}
                            heightWindow={heightWindow - 450}
                            toggleLoading={toggleLoading}
                        />
                    )}
                    {activeTab == 'historique' && (
                        <Historique
                            data="pointeuse"
                            nomSite={currentPointeuse.nom}
                            id={currentPointeuse.id}
                            heightWindow={heightWindow - 430}
                        />
                    )}
                    {activeTab == 'operation' && (
                        <Operation
                            currentDate={currentDate}
                            action={`api/pointeuses/operation/${currentPointeuse.id}`}
                            heightTable={heightWindow - 500}
                        />
                    )}
                </div>
            </div>
        </div>
    );
};

export default PointeuseDetail;
