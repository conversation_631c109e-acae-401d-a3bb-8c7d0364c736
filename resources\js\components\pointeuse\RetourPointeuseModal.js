import React, { useState, useEffect } from 'react';

import '../modal/modal.css';

const RetourPointeuseModal = ({ handleOk, details }) => {
    const [heightWindow, setHeightWindow] = useState(window.innerHeight);

    const resize = () => {
        setHeightWindow(window.innerHeight);
    };

    useEffect(() => {
        window.addEventListener('resize', resize);
        return () => window.removeEventListener('resize', resize);
    }, []);

    const status = (details && details.length > 1) ? details[details.length - 2] : '';

    return (
        <div style={{ zIndex: 200, height: heightWindow + 'px' }} className="fixed-front">
            <div className="table">
                <div className="modal-container">
                    <div className="modal sm">
                        <div className="modal-content">
                            <h3 className={status === 'success' ? 'primary' : 'pink'}>
                                {status === 'success'
                                    ? 'Requète effectué avec succès'
                                    : status === 'mysql_error'
                                        ? 'Erreur de connexion'
                                        : status === 'device_not_found'
                                            ? 'Appareil non connecté'
                                            : ''}
                            </h3>
                            <textarea disabled rows={10} value={details ? details.join('\n') : ''} />
                        </div>
                        <div className="table modal-footer">
                            <div className="cell-padding right">
                                <button onClick={handleOk} className="btn-default fix-width">OK</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default RetourPointeuseModal;
