import React, { Component } from 'react'

import './empreinte.css'
import IconButton from '../../button/IconButton'
import EnrollEmpreinteModal from './EnrollEmpreinteModal'
import RemoveEmpreinteModal from './RemoveEmpreinteModal'

export default class Empreinte extends Component {
    constructor(props) {
        super(props)
        this.state = {
            errorMessage: '',
            begin: null,
            end: null,
            month: 0,
            year: 0,
            loading: false,
            widthPx: 0,
            showEnrollModal: false,
            showRemoveModal: false,
            empreinteSearch: '',
        }
        this.handleChangeMonth = this.handleChangeMonth.bind(this)
        this.handleChangeYear = this.handleChangeYear.bind(this)
        this.handleShowRetourModal = this.handleShowRetourModal.bind(this)
        this.handleChangeEmpreinteSearch = this.handleChangeEmpreinteSearch.bind(this)
        this.handleClickGetTemplate = this.handleClickGetTemplate.bind(this)
    }

    handleClickGetTemplate(row) {
        const { currentPointeuse } = this.props
        this.props.toggleLoading(true)
        let data = new FormData()
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        data.append("empreinte_id", row.empreinte_id)
        data.append("digit", row.digit)
        data.append("agent_id", row.agent_id)
        axios.post('/api/pointeuses/get_template/' + currentPointeuse.id, data)
            .then(({ data }) => {
                this.handleShowRetourModal(data)
            })
    }

    handleChangeEmpreinteSearch(e) {
        this.setState({
            empreinteSearch: e.target.value
        })
    }

    showEmpreinte(empreinte) {
        const { empreinteSearch } = this.state
        if (empreinteSearch) {
            const search = empreinteSearch.toLocaleLowerCase().replace(/[.*+?^{}()|[\]\\]/g, '\\$&')
            var patt = new RegExp(search)
            if (empreinte.numero_employe && patt.test(empreinte.numero_employe.toLocaleLowerCase()))
                return true
            else if (empreinte.num_emp_soit && patt.test(empreinte.num_emp_soit.toLocaleLowerCase()))
                return true
            else if (empreinte.numero_stagiaire && patt.test(empreinte.numero_stagiaire.toLocaleLowerCase()))
                return true
            else if (empreinte.nom && patt.test(empreinte.nom.toLocaleLowerCase()))
                return true
            else if (empreinte.empreinte_id && patt.test(("000" + empreinte.empreinte_id).slice(-3).toLocaleLowerCase()))
                return true
            return false
        }
        return true
    }
    handleShowRetourModal(data) {
        this.props.handleShowRetourModal(data)
        this.setState({
            showEnrollModal: false,
            showRemoveModal: false,
        })
    }
    showDefineModal(v) {
        this.setState({
            currentEmpreinte: v,
            showDefineModal: true,
        })
    }
    toggleDefineModal(v) {
        this.setState({
            showDefineModal: v
        })
    }
    toggleEnrollModal(v) {
        this.setState({
            showEnrollModal: v,
        })
    }
    showRemoveModal(v) {
        this.setState({
            currentEmpreinte: v,
            showRemoveModal: true,
        })
    }
    toggleRemoveModal(v) {
        this.setState({
            showRemoveModal: v,
        })
    }
    handleChangeMonth(e) {
        this.setState({
            month: e.target.value
        })
    }
    handleChangeYear(e) {
        this.setState({
            year: e.target.value
        })
    }
    componentDidMount() {
        this.resize()
    }
    resize() {
        if (this.container)
            this.setState({
                widthPx: (this.container.offsetWidth - 320) + "px"
            })
    }
    render() {
        const { empreinteSearch, currentEmpreinte, errorMessage, showEnrollModal, showRemoveModal, loading, widthPx, showDefineModal } = this.state
        const { currentPointeuse, heightWindow } = this.props
        return (
            <div ref={el => (this.container = el)}>
                {
                    loading ?
                        <LoadingData />
                        :
                        errorMessage ?
                            <h3 className="default center">{errorMessage}</h3>
                            :
                            <div>
                                <div className="table">
                                    <div className="row-header">
                                        <h3 className="h3-table">
                                            <div className="cell">
                                                <input onChange={this.handleChangeEmpreinteSearch} value={empreinteSearch} id="searchEmpreinte" type="text" />
                                            </div>
                                            <div className="cell right fix-cell-empreinte">
                                                <span id="newAgentBtn">
                                                    <IconButton onClick={() => this.toggleEnrollModal(true)} label="Enroler une empriente" src="/img/add.svg" />
                                                </span>
                                            </div>
                                        </h3>
                                    </div>
                                </div>
                                <table className="fixed_header default layout-fixed">
                                    <thead>
                                        <tr>
                                            <th className="cellNumAgent">ID</th>
                                            <th style={{ width: widthPx, maxWidth: widthPx, minWidth: widthPx }}>Nom</th>
                                            <th className="cellEmpreinte">Emp.</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody style={{ height: heightWindow + "px" }}>
                                        {
                                            currentPointeuse && currentPointeuse.agents.map((row) => {
                                                if (this.showEmpreinte(row))
                                                    return (
                                                        <tr className={row.soft_delete ? 'red' : ''} key={row.id} onDoubleClick={() => { this.handleClickRow(row) }}>
                                                            <td className="cellNumAgent">
                                                                {("000" + row.empreinte_id).slice(-3)}
                                                            </td>
                                                            <td style={{ width: widthPx, maxWidth: widthPx, minWidth: widthPx }}>
                                                                [{
                                                                    row.societe_id == 1 ? 'DGM-' + row.numero_employe :
                                                                        row.societe_id == 2 ? 'SOIT-' + row.num_emp_soit :
                                                                            row.societe_id == 3 ? 'ST-' + row.numero_stagiaire :
                                                                                row.societe_id == 4 ? 'SM' :
                                                                                    row.numero_employe ? row.numero_employe :
                                                                                        row.numero_stagiaire ? row.numero_stagiaire :
                                                                                            <span className="purple">Ndf</span>
                                                                }] {row.nom}
                                                            </td>
                                                            <td className="cellEmpreinte">
                                                                {
                                                                    row.digit &&
                                                                    <span className={"badge " + (row.digit == 2 ? "bg-secondary" : "bg-dark")}>
                                                                        {["2", "4"].includes(row.digit.toString()) ? "D" : "G"}
                                                                    </span>
                                                                }
                                                            </td>
                                                            <td>
                                                                <img onClick={() => this.showRemoveModal(row)} className="img-btn" title="Supprimer" src="/img/delete.svg" />
                                                                {
                                                                    row.get_digit &&
                                                                    <img onClick={() => this.handleClickGetTemplate(row)} className="img-btn" title="Enregistrer l'empreinte" src="/img/register.svg" />
                                                                }
                                                                {
                                                                    !row.agent_id &&
                                                                    <img onClick={() => this.showDefineModal(row)} className="img-btn" title="Identifier l'empreinte" src="/img/define_agent.svg" />
                                                                }
                                                            </td>
                                                        </tr>
                                                    )
                                            })
                                        }
                                    </tbody>
                                </table>
                            </div>
                }
                {
                    showEnrollModal ?
                        <EnrollEmpreinteModal
                            action={"/api/pointeuses/enroll/" + currentPointeuse.id}
                            currentPointeuse={currentPointeuse}
                            closeModal={() => this.toggleEnrollModal(false)}
                            handleShowRetourModal={this.handleShowRetourModal} /> : <></>
                }
                {
                    showDefineModal ?
                        <EnrollEmpreinteModal
                            alreadyInDevice
                            action={"/api/empreintes/define/" + currentEmpreinte.id}
                            currentPointeuse={currentPointeuse}
                            closeModal={() => this.toggleDefineModal(false)}
                            updatePointeuse={() => this.props.updatePointeuse()} /> : <></>
                }
                {
                    showRemoveModal ?
                        <RemoveEmpreinteModal
                            currentEmpreinte={currentEmpreinte}
                            closeModal={() => this.toggleRemoveModal(false)}
                            handleShowRetourModal={this.handleShowRetourModal} /> : <></>
                }
            </div>
        )
    }
}
