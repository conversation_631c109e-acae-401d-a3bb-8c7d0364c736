import React, { Component } from 'react'
import axios from 'axios'

import './empreinte.css'
import Modal from '../../modal/Modal'
import Agent from './agent/Agent'

export default class EnrollEmpreinteModal extends Component {
    constructor(props) {
        super(props)
        this.state = {
            agent: null,
            digit: '',
            error: null,
            disableSave: false,
            showAgentList: false,
        }
        this.handleAgentChange = this.handleAgentChange.bind(this)
        this.handleDigitChange = this.handleDigitChange.bind(this)
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }
    toggleAgent(value) {
        this.setState({
            showAgentList: value
        })
    }
    handleAgentChange(agent) {
        this.setState({
            agent: agent,
            showAgentList: false
        })
    }
    handleDigitChange(e) {
        this.setState({
            digit: e.target.value
        })
    }
    handlePointeuseChange(event) {
        this.setState({
            pointeuse: event.target.value
        })
    }
    handleSave() {
        console.log("handleDoEnrollement")
        const { agent, digit } = this.state
        const { alreadyInDevice, currentPointeuse, action } = this.props
        console.log(action)
        console.log("is optique: " + currentPointeuse.optic)
        this.setState({
            disableSave: true,
            error: null
        })
        let data = new FormData()
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        if (agent)
            data.append("agent_id", agent.id)
        data.append("digit", digit)

        axios.post(action, data)
            .then(({ response }) => {
                if (response.error) {
                    const firstKey = Object.keys(response.error)[0]
                    const firstValue = response.error[firstKey][0]
                    this.setState({
                        error: {
                            key: firstKey,
                            value: firstValue
                        },
                    }, () => {
                        console.log(this.state.error)
                    })
                }
                else if (response) {
                    if (!alreadyInDevice)
                        this.props.handleShowRetourModal(response)
                    else
                        this.props.updatePointeuse()
                    this.props.closeModal()
                }
                this.setState({
                    disableSave: false
                })
            })
            .catch((e) => {
                console.log(e)
                if (e.code == 'ECONNABORTED')
                    this.props.handleShowRetourModal(['Timeout AXIOS...', 'timeout', 'End'])
                this.setState({
                    disableSave: false
                })
            })
    }
    handleCancel() {
        this.props.closeModal()
    }
    render() {
        const { agent, digit, error, showAgentList, disableSave } = this.state
        const { alreadyInDevice, currentPointeuse } = this.props
        return (
            <div>
                <Modal disableSave={disableSave} handleSave={this.handleSave} handleCancel={this.handleCancel}>
                    <h3>{alreadyInDevice ? 'Définir l\'agent' : 'Enrolement'}</h3>
                    <div className="input-container">
                        <label>Agent *</label>
                        <div>
                            <div className="table">
                                <div className="cell" style={{ height: 40, maxHeight: 40, minHeight: 40 }}>
                                    <div className="input-container">
                                        <input value={agent ? agent.nom : ''} disabled={true} autoComplete="off" type="text" />
                                    </div>
                                </div>
                                <div id="cellRefresh">
                                    <img id="refreshPointageBtn" src="/img/all_agent.svg" onClick={() => { this.toggleAgent(true) }} />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="input-container">
                        <label>Doigt *</label>
                        <select value={digit} onChange={this.handleDigitChange}>
                            <option></option>
                            {
                                (!alreadyInDevice && agent) && (
                                    !currentPointeuse.optic ?
                                        <>
                                            {agent.capacitif_gauche && <option value="1">Gauche</option>}
                                            {agent.capacitif_droite && <option value="2">Droite</option>}
                                        </>
                                        :
                                        <>
                                            {agent.optic_gauche && <option value="3">Gauche</option>}
                                            {agent.optic_droite && <option value="4">Droite</option>}
                                        </>
                                )
                            }
                            {
                                alreadyInDevice && (
                                    !currentPointeuse.optic ?
                                        <>
                                            <option value="1">Gauche</option>
                                            <option value="2">Droite</option>
                                        </>
                                        :
                                        <>
                                            <option value="3">Gauche</option>
                                            <option value="4">Droite</option>
                                        </>
                                )
                            }
                        </select>
                    </div>
                    {error && <div className="pink">{error.value}</div>}
                </Modal>
                {
                    showAgentList &&
                    <Agent
                        action={alreadyInDevice ? "/api/agents/modal" : "/api/agents/pointeuse"}
                        defaultAgent={agent}
                        opticPointeuse={currentPointeuse.optic}
                        closeModal={() => { this.toggleAgent(false) }}
                        changeAgent={this.handleAgentChange} />
                }
            </div>
        )
    }
}
