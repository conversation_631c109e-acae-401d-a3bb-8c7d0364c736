import React, { Component } from 'react'
import Modal from '../../modal/Modal'
import axios from 'axios'

export default class RemoveEmpreinteModal extends Component {
    constructor(props) {
        super(props)
        this.state = {
            disableSave: false
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }
    handleSave() {
        const { currentEmpreinte } = this.props
        const data = new FormData()
        console.log(currentEmpreinte)
        this.setState({
            disableSave: true
        })
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        axios.post("/api/pointeuses/remove_digit/"
            + currentEmpreinte.pointeuse_id + "/"
            + currentEmpreinte.empreinte_id, data, { timeout: 59000 })
            .then(({ data }) => {
                console.log("data result:")
                console.log(data)
                if (data.error) {
                    const firstKey = Object.keys(data.error)[0]
                    const firstValue = data.error[firstKey][0]
                    this.setState({
                        error: {
                            key: firstKey,
                            value: firstValue
                        },
                    }, () => {
                        console.log(this.state.error)
                    })
                }
                else if (data) {
                    this.props.handleShowRetourModal(data)
                }
                this.setState({
                    disableSave: false
                })
            })
            .catch((e) => {
                console.log(e)
                if (e.code == 'ECONNABORTED')
                    this.props.handleShowRetourModal(['Timeout AXIOS...', 'timeout', 'End'])
                this.setState({
                    disableSave: false
                })
            })
    }
    handleCancel() {
        this.props.closeModal()
    }
    render() {
        const { disableSave } = this.state
        const { currentEmpreinte } = this.props
        return (
            <Modal disableSave={disableSave} confirm={true} handleSave={this.handleSave} handleCancel={this.handleCancel}>
                <div>
                    <h3>Suppression de l'empreinte</h3>
                    <div>
                        {
                            currentEmpreinte.societe_id == 1 ? 'DGM-' + currentEmpreinte.numero_employe :
                                currentEmpreinte.societe_id == 2 ? 'SOIT-' + currentEmpreinte.num_emp_soit :
                                    currentEmpreinte.societe_id == 3 ? 'ST-' + currentEmpreinte.numero_stagiaire :
                                        currentEmpreinte.societe_id == 4 ? 'SM' :
                                            currentEmpreinte.numero_employe ? currentEmpreinte.numero_employe :
                                                currentEmpreinte.numero_stagiaire ? currentEmpreinte.numero_stagiaire :
                                                    <span className="purple">Ndf</span>
                        }<br />
                        Agent: {currentEmpreinte.nom}<br />
                        Empreinte: {currentEmpreinte.digit}
                    </div>
                    <hr />
                </div>
            </Modal>)
    }
}
