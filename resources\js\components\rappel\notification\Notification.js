import React from 'react';
import 'moment/locale/fr';

import './notification.css';

const Notification = ({ data, clickItem, type = 'default' }) => {
    const capitalizeFirstLetter = (string) => {
        if (!string) return '';
        return string
            .split(' ')
            .map((s) => s.trim().charAt(0).toUpperCase() + s.trim().slice(1).toLowerCase())
            .join(' ');
    };

    const normalizedNumber = (entry) => {
        if (!entry) return '';
        const normalizedNumber = entry.slice(-9);
        return `0${normalizedNumber}`;
    };

    // Determine title and styling based on notification type
    let title = 'Notification';
    let containerClass = '';

    if (type === 'call_reminder') {
        title = 'Numéro à rappeler';
        containerClass = 'call-reminder-container';
    }

    return (
        <div id="notificationContainer" className={containerClass}>
            <div className="table">
                <div className="cell red"><h4>{title}</h4></div>
                <div className="cell right">
                    <span className="badge bg-light">
                        {data.length > 9 ? '+' : ''} {data.length}
                    </span>
                </div>
            </div>
            <ul id="notificationList">
                {type === 'call_reminder' ? (
                    // Call reminder format
                    data.map((call) => (
                        <li key={call.uniqueid} className="lb-red" onClick={() => clickItem(normalizedNumber(call.numero))}>
                            {call.site ? capitalizeFirstLetter(call.site) : 'Site inconnu'}<br />
                            {call.contacttype === "agent"
                                ? 'Agent'
                                : (call.client
                                    ? call.client
                                    : 'Numéro inconnu')} : {normalizedNumber(call.numero)}
                        </li>
                    ))
                ) : (
                    // Default notification format
                    data.map((site) => {
                        if (site.manque)
                            return <li key={site.idsite} className="lb-green" onClick={() => clickItem(site.idsite)}>
                                Manque de transmission : {capitalizeFirstLetter(site.nom)}
                            </li>
                        else
                            return <li key={site.idsite} className="lb-red" onClick={() => clickItem(site.idsite)}>
                                Bouton en panne : {capitalizeFirstLetter(site.nom)}
                            </li>
                    })
                )}
            </ul>
        </div>
    );
};

export default Notification;



