/*@keyframes pulse {
    0% {
        background-color: white;
    }
    50% {
        background-color: white;
    }
    100% {
        background-color: #ffc107;
    }
}*/

#notificationContainer {
    position: fixed;
    bottom: 10px;
    left: 15%;
    padding: 10px;
    width: 400px;
    background-color: white;
    opacity: .9;
    border: solid .5px rgba(0, 0, 0, .1);
    z-index: 99;
}

/* Add specific styling for call reminder notifications */
#notificationContainer.call-reminder-container {
    border-left: 3px solid #dc3545;
    /* Red border for call reminders */
}

#notificationContainer h4 {
    margin: 10px 0px;
}

#notificationContainer:hover {
    animation: none
}

#notificationContainer:hover #notificationList {
    max-height: 400px;
    color: rgba(0, 0, 0, .8);
}

#notificationList {
    margin: 0;
    padding: 0;
    display: block;
    list-style: none;
    line-height: 1em;
    overflow: hidden;
    max-height: 0;
    transition: max-height 0.5s ease-in;
    overflow-y: auto;
}

#notificationList>li {
    border-top: solid .5px rgba(0, 0, 0, .1);
    padding: 10px;
    cursor: pointer;
}

#notificationList>li:hover {
    color: rgba(0, 0, 0, .5);
}
