import React, { useState, useEffect, useRef, useCallback } from 'react';
import axios from 'axios';
import InfiniteScroll from 'react-infinite-scroll-component';
import LoadingData from '../loading/LoadingData';
import { formDataOption } from '../../../../auth';
import moment from 'moment';
import 'moment/locale/fr';
import './site.css'

const Rappeler = ({ user, toggleLoading }) => {
    const [rappelers, setRappelers] = useState([]);
    const [inputSearch, setInputSearch] = useState('');
    const [heightWindow, setHeightWindow] = useState(window.innerHeight);
    const [widthWindow, setWidthWindow] = useState(window.innerWidth);
    const containerRef = useRef(null);

    const normalizedNumber = (entry) => {
        const normalizedNumber = entry.slice(-9);
        return `0${normalizedNumber}`;
    };

    const handleEnterPress = (event) => {
        if (event.key === 'Enter') {
            updateData(true);
        }
    };

    const handleChangeSearchSite = (event) => {
        setInputSearch(event.target.value);
    };

    const updateData = useCallback(async (loading = false, clearSearch = false) => {
        console.log("Fetching data...");
        if (loading) {
            toggleLoading(true);
        }

        if (clearSearch) {
            setInputSearch('');
        }

        try {
            const url = `/api/call/get_recall_data?&without_time_restriction=true${(!clearSearch && inputSearch) ? `&search=${inputSearch.replace('+', '%2B')}` : ''}`;

            const response = await axios.get(url, formDataOption);

            if (!response.data || !response.data.data) {
                throw new Error("Invalid API response structure");
            }

            const data = response.data.data;
            if (Array.isArray(data)) { // Ensure data is an array
                setRappelers(data);
                containerRef.current.scroll(0, 0);
                toggleLoading(false);
            } else {
                console.log(data)
            }
        } catch (error) {
            console.error("Error fetching data:", error);
            toggleLoading(false);
            setTimeout(() => {
                updateData(loading, clearSearch);
            }, 1000);
        }
    }, [rappelers, inputSearch, toggleLoading]);

    const resize = useCallback(() => {
        setHeightWindow(window.innerHeight);
        setWidthWindow(window.innerWidth);
    }, []);

    useEffect(() => {
        updateData(true);
        window.addEventListener('resize', resize);
        document.title = 'Site - TLS';

        return () => {
            window.removeEventListener('resize', resize);
        };
    }, []);

    return (
        <div className="table">
            <div id="tableContainer">
                <div className="table">
                    <div className="row-header">
                        <h3 className="h3-table">
                            <div className="cell fix-cell-pointeuse">Numero a rappeler</div>
                            <span className="cell center">
                                <div id="searchSite">
                                    <div>
                                        <input
                                            onKeyDown={handleEnterPress}
                                            onChange={handleChangeSearchSite}
                                            value={inputSearch}
                                            type="text"
                                            placeholder="Search..."
                                        />
                                        <img
                                            onClick={() => updateData(true)}
                                            src="/img/search.svg"
                                            alt="search"
                                            style={{ cursor: 'pointer' }}
                                        />
                                    </div>
                                </div>
                            </span>
                        </h3>
                    </div>
                    <div className="row-table">
                        <table className="fixed_header visible-scroll layout-fixed">
                            <thead>
                                <tr>
                                    <th className="cellAdresse">Contact</th>
                                    <th className="cellAdresse">Site</th>
                                    <th className="cellProm">Type</th>
                                    <th className="cellProm">
                                        Heure
                                        <img
                                            src="/img/refresh_table.svg"
                                            onClick={() => updateData(true, true)}
                                            alt="refresh"
                                            style={{
                                                width: '15px',
                                                float: 'right',
                                                cursor: 'pointer'
                                            }}
                                        />
                                    </th>
                                </tr>
                            </thead>
                            <tbody
                                id="scrollableDiv"
                                ref={containerRef}
                                style={{ height: `${heightWindow - 160}px` }}
                            >
                                <InfiniteScroll
                                    dataLength={rappelers.length}
                                    hasMore={false}
                                    loader={<LoadingData />}
                                    scrollableTarget="scrollableDiv"
                                >
                                    {rappelers.map((call, index) => (
                                        <tr key={index}>
                                            <td className="cellAdresse" title="">
                                                {normalizedNumber(call.src) === call.phone_agent
                                                    ? 'Agent'
                                                    : call.nom
                                                        ? `${call.nom} ${call.prenom || ''}`
                                                        : 'Numero inconnue'}{' '}
                                                : {normalizedNumber(call.src)}
                                            </td>
                                            <td className="cellAdresse">{call.site || 'Site inconnue'}</td>
                                            <td className="cellProm" title={call.disposition}>
                                                {call.disposition}
                                            </td>
                                            <td className="cellProm" title={call.datetime}>
                                                {moment(call.datetime, 'YYYY-MM-DD HH:mm:ss').fromNow()}
                                            </td>
                                        </tr>
                                    ))}
                                    {rappelers.length === 0 && (
                                        <tr>
                                            <td className="center secondary">Aucun données trouvé</td>
                                        </tr>
                                    )}
                                </InfiniteScroll>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div style={{ width: `${widthWindow / 2.5}px`, maxWidth: `${widthWindow / 2.5}px`, minWidth: `${widthWindow / 2.5}px` }} id="overviewContainer">
                <div className="img-bg-container">
                    <img className="img-bg-overview" src="/img/tls_background.svg" alt="background" />
                </div>
            </div>
        </div>
    );
};

export default Rappeler;
