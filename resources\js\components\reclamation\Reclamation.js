import moment from 'moment'
import React, { Component } from 'react'
import <PERSON><PERSON><PERSON><PERSON> from 'react-infinite-scroll-component'
import './reclamation.css'
import ReclamationDetail from './ReclamationDetail'
export default class Reclamation extends Component {
    constructor(props) {
        super(props)
        this.state = {
            reclamations: [],
            allDataLoaded: false,
            heightWindow: 0,
            widthWindow: 0,
            currentReclamation: null,
            pointage: null,
            activeTab: '',
            inputSearch: ''
        }
        this.fetchMoreData = this.fetchMoreData.bind(this)
        this.handleEnterPress = this.handleEnterPress.bind(this)
        this.handleChangeSearchReclamation = this.handleChangeSearchReclamation.bind(this)
        this.toggleLoading = this.toggleLoading.bind(this)
        this.updateData = this.updateData.bind(this)
    }

    toggleLoading(load) {
        this.props.toggleLoading(load)
    }

    updateData(loading){
        const {reclamations} = this.state
        const params = new URLSearchParams()
        if (loading){
            this.toggleLoading(true)
            this.setState({ currentReclamation: null });
        }
        params.append("offset", (loading ? 0 : sites.length))
        if(this.state.inputSearch.trim()){
            params.append("value", this.state.inputSearch)
        }
        axios.get('/api/reclamations', {params: params})
            .then(response => {
                console.log("updateReclamation")
                const newReclamations = response.data.reclamations
                if(loading){
                    this.setState({
                        reclamations: newReclamations,
                        currentReclamation: null
                    }, () => {
                        this.toggleLoading(false)
                    })
                    return
                }
                else{
                    this.setState({
                        reclamations: reclamations.concat(newReclamations)
                    }, () => {
                        this.toggleLoading(false)
                    })
                }
                if (loading) {
                    this.container.scroll(0, 0)
                    this.toggleLoading(false)
                }
                if(newReclamations.length < 50){
                    this.setState({
                        allDataLoaded: true
                    })
                }
            })
            .catch(error => {
                console.log(error)
            })
    }

    handleClickReclamation(id) {
        this.updateReclamation(id)
    }

    updateReclamation(id) {
        this.toggleLoading(true)
        axios.get('/api/reclamations/show/' + id + '?username=' + localStorage.getItem("username") + '&secret=' + localStorage.getItem("secret"))
            .then(({ data }) => {
                if (data) {
                    const reclamation = data.reclamation[0]
                    let tempData = {
                        id: reclamation.agent_id,
                        nom: reclamation.nom,
                        num_emp_soit: reclamation.num_emp_soit,
                        numero_employe: reclamation.numero_employe,
                        numero_stagiaire: reclamation.numero_stagiaire,
                        reclamation_id: reclamation.id,
                        site_id: reclamation.site_id,
                        societe_id: reclamation.societe_id,
                    }
                    this.setState({
                        currentReclamation: data.reclamation[0],
                        pointage: tempData,
                        // activeTab: 'pj'
                    }, () => {
                        this.toggleLoading(false)
                    })
                }
            })
            .catch(() => {
                this.toggleLoading(false)
            })
    }

    deleteReclamation(id) {
        this.toggleLoading(true)
        let data = new FormData()
        data.append("username", localStorage.getItem('username'))
        data.append("secret", localStorage.getItem('secret'))
        data.append("simple_delete", 1)
        axios.post("/api/reclamations/delete/" + id, data)
        .then(response => {
            console.log('response', response)
            if(response.data.succes) {
                const {reclamations} = this.state
                const reclamation_deleted_id = response.data.id 
                const newReclamations = reclamations.filter(reclamation => reclamation.id != reclamation_deleted_id)
                this.setState({
                    reclamations: newReclamations
                }, () => {
                    this.toggleLoading(false)
                })
                console.log('newReclamations', newReclamations)
                return
            }
            else
                this.toggleLoading(false)
        })
        .catch(() => {
            this.toggleLoading(false)
        })
    }

    componentDidMount() {
        this.updateData(true);
        window.addEventListener("resize", this.resize.bind(this))
        document.title = "Réclamation - TLS"
        this.resize()
    }

    resize() {
        this.setState({
            heightWindow: window.innerHeight,
            widthWindow: window.innerWidth
        });
    }

    fetchMoreData() {
        setTimeout(() => {
            this.updateData()
        }, 300);
    }

    handleEnterPress(event) {
        if (event.key === 'Enter') {
            this.updateData(true)
        }
    }

    handleChangeSearchReclamation(event) {
        this.setState({
            inputSearch: event.target.value
        })
    }

    render() {
        const { reclamations, allDataLoaded, currentReclamation, heightWindow, widthWindow, inputSearch, pointage } = this.state
        return (
            <div className='table'>
                <div id='tableContainer'>
                    <div className='table'>
                        <div className="row-header">
                            <h3 className="h3-table">
                                <span className="cell">Réclamation</span>
                                <span className="cell center">
                                    <div id="searchSite">
                                        <div>
                                            <input onKeyDown={this.handleEnterPress} onChange={this.handleChangeSearchReclamation} value={inputSearch} type="text" />
                                            <img onClick={() => { this.updateData(true) }} src="/img/search.svg" />
                                        </div>
                                    </div>
                                </span>
                            </h3>
                        </div>
                        <div className="row-table">
                            <InfiniteScroll
                                scrollableTarget="scrollableDiv"
                                dataLength={reclamations ? reclamations.length : 0}
                                next={this.fetchMoreData}
                                hasMore={!allDataLoaded}
                            >
                                <table className="fixed_header visible-scroll layout-fixed">
                                <thead>
                                    <tr>
                                        <th className="cellNumAg">Num.</th>
                                        <th className="cellAgent">Nom</th>
                                        <th className="cellSiteAg">Site</th>
                                        <th className="cellDate">Type</th>
                                        <th className="editPointageCell"></th>
                                    </tr>
                                </thead>
                                <tbody id="scrollableDiv" ref={el => (this.container = el)} style={{ 'height': (heightWindow - 160) + "px" }}>
                                    {
                                        reclamations.map((row) => {
                                            return (
                                                <tr 
                                                    key={row.id}
                                                    onDoubleClick={() => { this.handleClickReclamation(row.id) }}
                                                    className={ row.pointage_id ? "secondary":(row.service24_id && !row.pointage_id) ? 'red' : ((currentReclamation != null && currentReclamation.id == row.id) || row.pointage_id ? "selected-row" : "")}
                                                    title={"Sup: " + (row.superviseur + " <" +row.superviseur_email + ">")}
                                                >
                                                    {row.agent_id ?
                                                        <td className="cellNumAg">
                                                            {
                                                                row.societe_id == 1 ? 'DGM-' + row.numero_employe :
                                                                row.societe_id == 2 ? 'SOIT-' + row.num_emp_soit :
                                                                row.societe_id == 3 ? 'ST-' + row.numero_stagiaire :
                                                                row.societe_id == 4 ? 'SM' :
                                                                row.numero_employe ? row.numero_employe :
                                                                row.numero_stagiaire ? row.numero_stagiaire :
                                                                <span className="purple">Ndf</span>
                                                            }
                                                        </td>
                                                        :
                                                        <td className="cellNumAg">
                                                            <span className="purple">SM</span>
                                                        </td>
                                                    }
                                                    <td className="cellAgent" >{row.agent_not_registered ?? row.nom}</td>
                                                    <td className="cellSiteAg" >{row.site}</td>
                                                    <td className="cellDate" >{row.type == "service24" ? "Service 24" : row.type == "sm" ? "Non enregistré" : row.type == "archive" ? "Archivé" : row.type == "mis_a_pied" ? "Mis à pied" : row.type == "sal_forfait" ? "Salaire forfaitaire" : ""}</td>
                                                    <td className="editPointageCell" style={{textAlign: "center"}} ><img src="/img/delete.svg" onDoubleClick={() => {this.deleteReclamation(row.id)}} /></td>
                                                </tr>
                                            )
                                        })
                                    }
                                </tbody>
                                </table>
                            </InfiniteScroll>
                        </div>
                    </div>
                </div>
                <div className={currentReclamation ? "box-shadow-left" : ""} style={{ width: (widthWindow / 2.5) + 'px', maxWidth: (widthWindow / 2.5) + 'px', minWidth: (widthWindow / 2.5) + 'px' }} id="overviewContainer">
                    {
                        currentReclamation ?
                            <ReclamationDetail reclamation={currentReclamation} pointage={pointage} updateData={this.updateData}/>
                            :
                            <div className="img-bg-container">
                                <img className="img-bg-overview" src="/img/tls_background.svg" />
                            </div>
                    }
                </div>
            </div>
        )
    }
}
