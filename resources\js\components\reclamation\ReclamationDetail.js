import moment from 'moment'
import React, { Component } from 'react'
import EditPointageModal from '../pointage/EditPointageModal'
import Pointage from '../agent/pointage/Pointage'

export default class ReclamationDetail extends Component {
    constructor(props) {
        super(props)
        this.state = {
            showMakePointageMenu: false,
            showEditPointage: false,
            site: this.props.reclamation.site_detail,
            currentDate: moment().format('YYYY-MM-DD'),
            heightWindow: 0,
            widthWindow: 0,
        }
        this.closePointageModal = this.closePointageModal.bind(this)
    }
    toggleMakePointageMenu(value) {
        this.setState({
            showMakePointageMenu: value
        })
    }
    
    toggleEditPointage(value) {
        this.setState({
            showEditPointage: value
        })
    }

    onChangeToPointage() {
        const {reclamation} = this.props
        const data = new FormData()
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))
        data.append("simple_result", 1)
        axios.post('/api/pointages/reclamation_to_pointage/' + reclamation.id, data)
        .then(res => {
            if(res.data.success){
                this.props.updateData(true)
            }
        })
    }

    closePointageModal() {
        this.setState({
            showEditPointage: false,
            showMakePointageMenu: false,
        })
        this.props.updateData(true)
        console.log("closePointageModal")
    }

    componentDidMount() {
        // const { archive } = this.props
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
        // document.title = archive ? "Archive - TLS" : "Agent - TLS"
    }
    resize() {
        this.setState({
            heightWindow: window.innerHeight,
            widthWindow: window.innerWidth
        });
    }

    render() {
        const { reclamation, pointage, currentDate, updateData } = this.props
        const { showMakePointageMenu, showEditPointage, site, heightWindow, widthWindow } = this.state
        return (
            <div>
                <div className="overview-container">
                    <div className="head-title-overview" title={reclamation.nom}>
                        <div style={{height:"40px", lineHeight:"40px" }}>
                            <div className="title-overview">
                                <span style={{opacity: .9}}>
                                    {reclamation.agent_id ?
                                        <>
                                            {
                                                reclamation.societe_id == 1 ? 'DGM-' + reclamation.numero_employe :
                                                reclamation.societe_id == 2 ? 'SOIT-' + reclamation.num_emp_soit :
                                                reclamation.societe_id == 3 ? 'ST-' + reclamation.numero_stagiaire :
                                                reclamation.societe_id == 4 ? 'SM' :
                                                reclamation.numero_employe ? reclamation.numero_employe :
                                                reclamation.numero_stagiaire ? reclamation.numero_stagiaire :
                                                <span className="purple">Non définie</span>
                                            }
                                        </>
                                        :
                                        <span className="purple">Non définie</span>

                                    }
                                </span>
                            </div>
                            <div className="overview-edit-icon">
                                {
                                    (reclamation.agent_id /*&& reclamation.last_service_ptg_id && reclamation.service24_id*/) &&
                                    <img onClick={() => { this.toggleMakePointageMenu(!showMakePointageMenu) }} className="overview-edit-img" src="/img/parametre.svg" />
                                }
                                {
                                    showMakePointageMenu &&
                                    <div className="dropdown-overview-edit">
                                        <span /*onClick={this.handleClickResetDigitAgent}*/onClick={()=>this.toggleEditPointage(true)}>Convertir en pointage</span>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                    <span className="overview-break-overflow" title={reclamation.nom}>
                        <b>Nom : </b>{reclamation.agent_id ? reclamation.nom : reclamation.agent_not_registered }
                    </span>
                    <span title={reclamation.phone_agent}><b>Site : </b> {reclamation.site}</span><br/>
                    <div className="table">
                        <span className="cell">
                            <span>
                                <b>Date de service : </b> 
                                { moment(reclamation.date_pointage).format('DD MMM YYYY')}
                            </span>
                        </span>
                        {
                            (!reclamation.agent_id) &&
                            <span className="cell right" style={{ height: "30px"}}>
                                <span className="badge bg-primary">Agent non enregistré</span>
                            </span>
                        }
                    </div>
                </div>
                {
                    showEditPointage &&
                    <EditPointageModal
                        hasDate={pointage.dtarrived ? true : false}
                        action={'/api/pointages/reclamation_to_pointage/' + reclamation.id+'?simple_result=1'}
                        site={site}
                        pointage={pointage}
                        // updateReclamationPointage = {this.updateReclamationPointage}
                        updatePointage={updateData}
                        closeModal={this.closePointageModal} />
                }
                <div id="tabContentOverview">
                    <div id="tabContainer">
                        {/* {
                            activeTab == 'pointage' && */}
                            <Pointage currentDate={currentDate} agentId={reclamation.agent_id} nomAgent={reclamation.nom} heightWindow={heightWindow} />
                        {/* } */}
                    </div>
                </div>
            </div>
        )
  }
}
