import React, { Component, lazy } from 'react'
import axios from 'axios'
import moment from 'moment'
import DatePicker  from 'react-datepicker'

const Badge = lazy(() => import('./badge/Badge'))

import './report.css'
import 'react-datepicker/dist/react-datepicker.css'
import SelectGroupSite from './select/SelectGroupSite'
import PointageExportButton from './button/PointageExportButton'
import VigilanceExportButton from './button/VigilanceExportButton'
import AbusifExportButton from './button/AbusifExportButton'
import Repartition from './table/Repartition'
import Indice from './table/Indice'

import RapportAlarme from './rapport_alarme/RapportAlarme'
import Coupure from './coupure/Coupure'

export default class Report extends Component {
    constructor(props){
        super(props)
        this.state = {
            timeoutId: null,
            selectedSiteIds: [],
            groupVigilances: [],
            selectedGroupVigilanceId: null,
            selectedSites: [],
            selectedDate: null,
            selectedHoraire: null,
            selectedMenu: null,
            sites: null,
            users: null,
            vigilanceSiteDay: null,
            vigilanceSiteNight: null,
            pointageSite : null,
            groupSites: null,
            currentDate: null,
            beginDate: null,
            endDate: null,
            isPointageReport: false,
            reports: [
                //{ label: 'Panique', value: 'panique'},
                //{ label: 'Manque de vigilance', value: 'manque_vigilance' },
                { label: 'Bouton abusif', value: 'bouton_abusif', roles: ['root', 'rh', 'room']},
                { label: 'Vigilance', value: 'vigilance_journalier', roles: ['root', 'rh', 'room', 'client', 'op']},
                //{ label: 'Vigilance par site', value: 'vigilance_site'},
                //{ label: 'Pointage journalier', value: 'pointage_agent'},
                { label: 'Pointage', value: 'pointage_mensuel', roles: ['root', 'rh', 'room', 'op']},
                { label: 'Répartition des tâches', value: 'repartition_tache', roles: ['root', 'room']},
                { label: 'Indice de vigilance', value: 'indice_vigilance', roles: ['root', 'room']},
                { label: 'Liste des agents actifs', value: 'liste_agent', roles: ['root', 'rh', 'room']},
                { label: 'Imprimer des badges', value: 'generer_badge', roles: ['root', 'rh']},
                { label: 'Rapport alarme', value: 'rapport_alarme', roles:['root', 'room']},
                { label: 'Problème de transmission', value: 'probleme_transmission', roles:['root']}
                //{ label: 'Total d\'heures', value: 'total_heure'},
                //{ label: 'Site en panne', value: 'site_panne'},
            ],
            heightWindow: 0,
            widthWindow: 0,
            widthCol: 0,
            titleExport: '',
            showSelectItem: false,
            currentItem: null
        }
        this.handleClickMenuItem = this.handleClickMenuItem.bind(this)
        this.handleGetSiteList = this.handleGetSiteList.bind(this)
        this.handleHoraireChange = this.handleHoraireChange.bind(this)
        this.handleGroupVigilanceChange = this.handleGroupVigilanceChange.bind(this)
        this.handleSelectedDate = this.handleSelectedDate.bind(this)
        this.handleEndDate = this.handleEndDate.bind(this)
        this.handleBeginDate = this.handleBeginDate.bind(this)
        this.clickItem = this.clickItem.bind(this)
        this.toggleSelectItem = this.toggleSelectItem.bind(this)
        this.toggleLoading = this.toggleLoading.bind(this)
        this.updateData = this.updateData.bind(this)
    }
    getCurrentGroup(){
        const {groupVigilances, selectedGroupVigilanceId} = this.state
        if(groupVigilances){
            groupVigilances.forEach((g) => {
                if(g.id == selectedGroupVigilanceId)
                    return g
            })
        }
        return {}
    }
    toggleLoading(v){
        this.props.toggleLoading(v)
    }
    toggleSelectItem(){
        this.setState({
            showSelectItem: !this.state.showSelectItem
        })
    }
    clickItem(value){
        const {sites} = this.state
        const currentItem = value
        let ids = []
        let selectedSites = []
        if(currentItem)
            sites.forEach(site => {
                if(site.group_id == currentItem.id){
                    ids.push(site.idsite)
                }
            }) 
        else selectedSites = sites 
        this.setState({
            selectedSiteIds: ids,
            selectedSites: selectedSites,
            currentItem: currentItem,
            showSelectItem: false
        }, () => {
            console.log(this.state.selectedSiteIds)
        })
    }
    handleSelectedDate(date){
        this.setState({
            selectedDate: date,
            sites: null
        })
    }
    handleBeginDate(date){
        this.setState({
            beginDate: date,
            sites: null
        })
    }
    handleEndDate(date){
        this.setState({
            endDate: date,
            sites: null
        })
    }
    handleGroupVigilanceChange(e){
        this.setState({
            selectedGroupVigilanceId: e.target.value
        })
    }
    handleHoraireChange(e){
        this.setState({
            selectedHoraire: e.target.value,
            sites: null
        })
    }
    loadListSiteHoraire(excelFileName){
        const {selectedDate, selectedHoraire} = this.state
        const horaire = (selectedHoraire == '07:00:00') ? 'day' : 'night'
        const selected_date = moment(selectedDate).format("YYYY-MM-DD")
        axios.get("/api/vigilances/report_horaire?horaire=" + horaire + "&date=" + selected_date)
        .then(({data}) => {
            console.log(data)
            let sites = data
            let siteIds = []
            sites.map((st) => {
                siteIds.push(st.prom)
            })
            this.setState({
                sites: sites,
                selectedSiteIds: siteIds,
                titleExport: excelFileName
            })
        })
        .catch(() => {
            setTimeout(() => {
                this.loadListSiteHoraire(excelFileName)
            }, 10000)
        })
    }
    handleGetSiteList(e){
        const {selectedMenu ,selectedHoraire, pointageSite, vigilanceSiteNight, vigilanceSiteDay, selectedDate, beginDate, endDate} = this.state
        let sites = null
        let excelFileName = ''
        if(selectedMenu && ['vigilance_journalier', 'pointage_agent', 'bouton_abusif'].includes(selectedMenu.value)){
            excelFileName = ''
             + (selectedHoraire == '07:00:00' ? 'Jour ' : selectedHoraire == '18:00:00' ? 'Nuit ' : '')
             + moment(selectedDate).format('DD-MM-YYYY')
        }
        else if(selectedMenu && ['pointage_mensuel', 'indice_vigilance'].includes(selectedMenu.value)){
            excelFileName = 'Du '
             + moment(beginDate).format('DD-MM-YYYY')
             + ' à '
             + moment(endDate).format('DD-MM-YYYY')
        }

        
        if(selectedHoraire == '07:00:00' || selectedHoraire == '18:00:00'){
            this.loadListSiteHoraire(excelFileName)
        }
        else if(selectedMenu.value == 'pointage_mensuel')
            sites = pointageSite
        else if(selectedMenu.value == 'indice_vigilance'){

        }
        if(sites){
            let siteIds = []
            sites.map((st) => {
                siteIds.push(st.prom)
            })
            this.setState({
                sites: sites,
                selectedSiteIds: siteIds,
                titleExport: excelFileName
            })
        }
    }
    handleClickMenuItem(item){
        console.log(item)
        this.setState({
            selectedMenu: item,
            sites: null
        })
    }
    resize() {
        this.setState({
            heightWindow: window.innerHeight,
            widthWindow: window.innerWidth,
            widthCol: (window.innerWidth - 220)/3
        });
    }
    loadGroupVigilance(){
        axios.get('/api/group_vigilances_sites?username=' + localStorage.getItem('username') + '&secret=' + localStorage.getItem('secret'))
        .then(({data}) => {
            this.setState({
                groupVigilances: data
            })
        })
        .catch(() => {
            setTimeout(this.loadGroupVigilance, 10000)
        })
    }
    componentDidMount(){
        document.title = "Rapport - TLS"
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
        this.updateData(true)
        this.loadGroupVigilance()
    }
    componentWillUnmount(){
        const {timeoutId} = this.state
        if(timeoutId) clearTimeout(timeoutId)
    }
    updateData(first){
        console.log("updateData")
        if(first)
            this.props.toggleLoading(true)
        axios.get('/api/reports?username=' + localStorage.getItem('username') + '&secret=' + localStorage.getItem('secret'))
        .then(({data}) =>{
            let currentDate = moment(data.pointage_date)
            let beginDate = currentDate.clone().date(1)
            if(beginDate.isAfter(moment()))
                beginDate.subtract(1, 'month')
            let endDate = currentDate.clone()
            if(first){
                this.setState({
                    selectedDate: currentDate.toDate(),
                    beginDate: beginDate.toDate(),
                    endDate: endDate.toDate(),
                    selectedHoraire: data.pointage_date.split(' ')[1],
                    vigilanceSiteNight: data.night,
                    vigilanceSiteDay: data.day,
                    pointageSite: data.pointage_sites,
                    currentDate: moment(data.current_date),
                    groupSites: data.group_sites,
                    users: data.users,
                    locked: data.locked
                })
            }
            else{
                this.setState({
                    vigilanceSiteNight: data.night,
                    vigilanceSiteDay: data.day,
                    pointageSite: data.pointage_sites,
                    currentDate: moment(data.current_date),
                    groupSites: data.group_sites,
                    users: data.users,
                    locked: data.locked
                })
            }
            this.props.toggleLoading(false)
            
            this.setTimeoutUpdateData(false)
        })
        .catch(() =>{
            if(first)
                this.setTimeoutUpdateData(true)
            else
                this.setTimeoutUpdateData(false)
        })
    }
    setTimeoutUpdateData(first){
        let timeoutId = setTimeout(() => {
            this.updateData(first)
        }, 3000)
        this.setState({
            timeoutId: timeoutId
        })
    }
    showSite(site){
        const {currentItem} = this.state
        if(currentItem){
            if(currentItem.id == site.group_id)
                return true
            return false
        }
        return true
    }
    render(){
        const {users, groupSites, currentItem, showSelectItem, titleExport, sites, beginDate, endDate, selectedDate, selectedHoraire, reports,
            groupVigilances, selectedGroupVigilanceId, selectedMenu, selectedSiteIds, widthCol, heightWindow, locked} = this.state
        const {user} = this.props
        return  (
            <div className="table">
                <div id="tableContainer">
                    <div className="full-width">
                        <div className="row-header">
                            <h3 className="h3-table">
                                <span className="cell fix-cell-site">Rapport</span>
                            </h3>
                        </div>
                        <div className="row-table">
                            <table>
                                <tbody>
                                    {
                                        reports.map((row) => {
                                            if(row.roles.includes(user.role)) 
                                                return <tr key={row.value}
                                                    onDoubleClick={() => {this.handleClickMenuItem(row)}}
                                                    className={(selectedMenu && row.value == selectedMenu.value) ? "bg-primary" : ""}
                                                >
                                                    <td>{row.label}</td>
                                                </tr>
                                        })
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div 
                    id="filterContainer"
                    className={(selectedMenu && ['vigilance_journalier', 'pointage_agent', 'pointage_mensuel', 'bouton_abusif', 'repartition_tache'].includes(selectedMenu.value)) ? 'box-shadow-left' : ''}
                    style={{
                        height: heightWindow + 'px', 
                        width: ((selectedMenu && ['repartition_tache', 'generer_badge', 'rapport_alarme', 'probleme_transmission'].includes(selectedMenu.value)) ?  widthCol*2 : widthCol) + 'px', 
                        maxWidth: ((selectedMenu && ['repartition_tache', 'generer_badge', 'rapport_alarme', 'probleme_transmission'].includes(selectedMenu.value)) ?  widthCol*2 : widthCol) + 'px', 
                        minWidth: ((selectedMenu && ['repartition_tache', 'generer_badge', 'rapport_alarme', 'probleme_transmission'].includes(selectedMenu.value)) ?  widthCol*2 : widthCol) + 'px'
                    }}
                >
                    {
                        (selectedMenu && ['vigilance_journalier', 'pointage_agent', 'bouton_abusif'].includes(selectedMenu.value)) &&
                        <div className="table">
                            <div className="row-header">
                                <h3 className="h3-table">
                                    <span className="cell fix-cell-site"> {selectedMenu.label} </span>
                                </h3>
                            </div>
                            <div className="row-table">
                                <div className="reportinput-container input-container">
                                    <label>Date</label>
                                    <DatePicker className="datepicker-full-width" dateFormat="dd-MM-yyyy" selected={selectedDate} onChange={this.handleSelectedDate}/>
                                </div>
                                <div className="report-input-container input-container">
                                    <label>Horaire</label>
                                    <select value={selectedHoraire} onChange={this.handleHoraireChange}>
                                        <option></option>
                                        <option value="07:00:00">JOUR</option>
                                        <option value="18:00:00">NUIT</option>
                                    </select>
                                </div>
                                {
                                    (selectedMenu && selectedMenu.value == 'vigilance_journalier') &&
                                    <div className="report-input-container input-container">
                                        <label>Groupe *</label>
                                        <select value={selectedGroupVigilanceId} onChange={this.handleGroupVigilanceChange}>
                                            <option value=""></option>
                                            {
                                                groupVigilances &&
                                                groupVigilances.map((g) => (
                                                    <option key={g.id} value={g.id}>{g.nom}</option>
                                                ))
                                            }
                                        </select>
                                    </div>
                                }
                                <div className="input-container">
                                    {
                                        ['pointage_agent', 'pointage_mensuel'].includes(selectedMenu.value) &&
                                        <button disabled={!selectedHoraire} 
                                            className="export-report-btn" 
                                            onClick={this.handleGetSiteList}>
                                                Rechercher
                                        </button>
                                    }
                                    {
                                        (selectedMenu && selectedMenu.value == 'bouton_abusif') &&
                                        <AbusifExportButton 
                                            selectedDate = {selectedDate} 
                                            selectedHoraire = {selectedHoraire} 
                                            toggleLoading= {this.toggleLoading}/>
                                    }
                                    {
                                        (selectedMenu && selectedMenu.value == 'vigilance_journalier') &&
                                        <VigilanceExportButton 
                                            selectedDate = {selectedDate} 
                                            selectedHoraire = {selectedHoraire} 
                                            selectedGroupId = {selectedGroupVigilanceId}
                                            groupVigilances = {groupVigilances}
                                            toggleLoading= {this.toggleLoading}/>
                                    }
                                </div>
                            </div>
                        </div>
                    }
                    {
                        (selectedMenu && ['pointage_mensuel', 'indice_vigilance'].includes(selectedMenu.value)) &&
                        <div className="table">
                            <div className="row-header">
                                <h3 className="h3-table">
                                    <span className="cell fix-cell-site"> {selectedMenu.label} </span>
                                </h3>
                            </div>
                            <div className="row-table">
                                <div className="reportinput-container input-container">
                                    <label>Début</label>
                                    <DatePicker className="datepicker-full-width" dateFormat="dd-MM-yyyy" selected={beginDate} onChange={this.handleBeginDate}/>
                                </div>
                                <div className="reportinput-container input-container">
                                    <label>Fin</label>
                                    <DatePicker className="datepicker-full-width" dateFormat="dd-MM-yyyy" selected={endDate} onChange={this.handleEndDate}/>
                                </div>
                                <div className="input-container">
                                    <button className="export-report-btn" onClick={this.handleGetSiteList}>Rechercher</button>
                                </div>
                            </div>
                        </div>
                    }
                    {
                        (selectedMenu && selectedMenu.value == 'repartition_tache') &&
                        <Repartition locked={locked} selectedMenu={selectedMenu} groupSites={groupSites} updateData={this.updateData}/>
                    }
                    {
                        (selectedMenu && selectedMenu.value == 'generer_badge') &&
                        <Badge/>
                    }
                    {
                        (selectedMenu && selectedMenu.value == 'rapport_alarme') &&
                        <RapportAlarme/>
                    }
                    {
                        (selectedMenu && selectedMenu.value == 'probleme_transmission') &&
                        <Coupure/>
                    }
                </div>
                {
                    !(selectedMenu && ['repartition_tache', 'generer_badge', 'rapport_alarme', 'probleme_transmission'].includes(selectedMenu.value)) &&
                    <div 
                        className={(sites && selectedHoraire) ? "box-shadow-left" : ""}
                        style={{height: heightWindow + 'px', width: widthCol + 'px', maxWidth: widthCol + 'px', minWidth: widthCol + 'px'}} id="overviewContainer">
                        {
                            (selectedMenu && selectedMenu.value != 'indice_vigilance' && sites && selectedHoraire) &&
                            <div className="table">
                                <div>
                                    <h3 className="h3-table">
                                        <span className="cell fix-cell-site">
                                            {titleExport}
                                        </span>
                                    </h3>
                                </div>
                                <h3 className="center">
                                    <SelectGroupSite 
                                        user={user}
                                        title={(selectedMenu && selectedMenu.value == 'pointage_mensuel') ? 'POINTAGE' : 'VIGILANCE'}
                                        clickItem={this.clickItem} 
                                        currentItem={currentItem} 
                                        toggleSelect={this.toggleSelectItem} 
                                        showItem={showSelectItem}/>
                                </h3>
                                {
                                    (selectedMenu && ['pointage_mensuel', 'vigilance_journalier'].includes(selectedMenu.value)) &&
                                    <div>
                                        <div>
                                            <table className="fixed_header visible-scroll layout-fixed">
                                                {/*<thead>
                                                    <th className="cellCheckbox"></th>
                                                    <th>Site</th>
                                                </thead>*/}
                                                <tbody style={{'height': (heightWindow - 230) + "px"}}>
                                                    {
                                                        sites.map((row) => {
                                                            if(this.showSite(row))
                                                                return <tr key={row.idsite}>
                                                                        <td>{row.nom}</td>
                                                                    </tr>
                                                        })
                                                    }
                                                </tbody>
                                            </table>
                                        </div>
                                        <div>
                                            <div className="input-container">
                                                {
                                                    (selectedMenu && selectedMenu.value == 'pointage_agent') &&
                                                    <button onClick={this.handleExportPointageDaily} disabled={selectedSiteIds.length == 0} className="export-report-btn">Exporter</button>
                                                }
                                                {
                                                    (selectedMenu && selectedMenu.value == 'pointage_mensuel') &&
                                                    <PointageExportButton 
                                                        siteIds={sites.map((s) => (s.idsite))} 
                                                        currentItem={currentItem} 
                                                        beginDate={beginDate} 
                                                        endDate={endDate}/>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                }
                                
                                {
                                    (selectedMenu && ['indice_vigilance'].includes(selectedMenu.value)) &&
                                    <Indice selectedMenu={selectedMenu} 
                                        beginDate={beginDate} 
                                        endDate={endDate} 
                                        users={users}/>
                                }
                            </div>
                        }
                        {
                            (selectedMenu && selectedMenu.value == 'indice_vigilance' && sites && selectedHoraire) &&
                            <div className="table">
                                <div>
                                    <h3 className="h3-table">
                                        <span className="cell fix-cell-site">
                                            {titleExport}
                                        </span>
                                    </h3>
                                </div>
                                <div>
                                    <Indice selectedMenu={selectedMenu} 
                                        beginDate={beginDate} 
                                        endDate={endDate} 
                                        users={users}/>
                                </div>
                            </div>
                        }
                    </div>
                }
            </div>
        )
    }
}