import React, { Component } from 'react'
import { PDFViewer, Page, Text, Image, View, Document, StyleSheet } from '@react-pdf/renderer'
import QRCode from 'qrcode'
import moment from 'moment'

import './badge.css'
import Agent from '../../pointage/agent/Agent'

const styles = StyleSheet.create({
    page: {
        flexDirection: 'column',
        backgroundColor: 'white'
    },
    section: {
        flexDirection: 'row',
        margin: 0,
        padding: 0,
        flexGrow: 1,
        height:'25%',
        minHeight:'25%',
        maxHeight:'25%',
    },
    badge: {
        margin: 0,
        padding: 5,
        width: '105mm',
        minWidth:'105mm',
        maxWidth:'105mm',
        flexGrow: 1,
        borderStyle: 'solid',
        borderWidth: 1,
        borderColor: 'black'
    },
    badgeContent: {
        flexDirection: 'row',
        flexGrow: 1,
    },
    imgLogo: {
        width: 170
    },
    infoContainer: {
        width: '65mm',
        maxWidth: '65mm',
        minWidth: '65mm'
    },
    imageContainer: {
        textAlign: 'center',
        /*borderStyle: 'solid',
        borderWidth: 1,
        borderColor: 'black',
        width: '40mm',
        maxWidth: '40mm',
        minHeight: '40mm'*/
    },
    matricule: {
        paddingTop: 20,
        textAlign: 'center'
    },
    agent: {
        paddingTop: 20,
        fontSize: '14pt'
    },
    fonction: {
        paddingTop: 20,
        fontSize: '12pt'
    },
    qrcode: {
        //position: 'relative',
        marginTop: '40mm',
        marginLeft: '5mm',
        width: '30mm',
        height: '30mm',
        //top: '40mm',
        //left: '5mm'
    },
});

export default class Badge extends Component {
    constructor(props){
        super(props)
        this.state = {
            showAgentTable: false,
            selectedAgents: [],
            printInterface: false,
            heightViewer: 0,
            disableApercu: true
        }
        this.removeAgent = this.removeAgent.bind(this)
        this.updateAgentList = this.updateAgentList.bind(this)
        this.togglePrintInterface = this.togglePrintInterface.bind(this)
        this.handleLoadImage = this.handleLoadImage.bind(this)
    }
    handleLoadImage(id){
        const {selectedAgents} = this.state
        let newList = []
        for(let i=0; i<selectedAgents.length; i++){
            if(selectedAgents[i].id == id)
                selectedAgents[i].loaded = true
            newList.push(selectedAgents[i])
        }
        this.setState({
            selectedAgents: newList
        })
    }
    togglePrintInterface(status){
        this.setState({
            printInterface: status
        })
    }
    toggleAgent(status){
        this.setState({
            showAgentTable: status
        })
    }
    removeAgent(id){
        const {selectedAgents} = this.state
        let newList = []
        for(let i=0; i<selectedAgents.length; i++){
            if(selectedAgents[i].id != id)
                newList.push(selectedAgents[i])
        }
        this.setState({
            selectedAgents: newList
        })
    }
    updateAgentList(agents){
        const opts = {
            errorCorrectionLevel: 'L',
            type: 'image/jpeg',
            quality: 0.3,
            margin: 1,
            color: {
              dark:"#44444444",
              light:"#FFFFFFFF"
            }
        }
        for(let i=0; i<agents.length; i++){
            QRCode.toDataURL(((agents[i] && agents[i].nom) ? agents[i].nom : "Nom empty"), opts, function (err, url) {
                if (err) throw err
                agents[i].qrcode_uri = url
            })
        }
        this.setState({
            selectedAgents: agents,
            showAgentTable: false,
            disableApercu: true
        })
    }
    resize() {
        this.setState({
            heightViewer: window.innerHeight - 95
        });
    }
    componentDidMount(){
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
    }
    render(){
        const {selectedAgents, showAgentTable, printInterface, heightViewer} =  this.state
        let pages = []
        
        for(let i=0; i < selectedAgents.length/8; i++){
            pages.push(
                <Page key={"page_pdf_" + i} size="A4" style={styles.page}>
                    {
                        [0,1,2,3].map((v) => {
                            let matriculeFirst = ''
                            let matriculeSecond = ''
                            if((8*i + 2*v < selectedAgents.length))
                                matriculeFirst = (
                                    selectedAgents[8*i + 2*v].societe_id == 1 ? 'DGM-' + selectedAgents[8*i + 2*v].numero_employe :
                                    selectedAgents[8*i + 2*v].societe_id == 2 ? 'SOIT-' + selectedAgents[8*i + 2*v].num_emp_soit :
                                    selectedAgents[8*i + 2*v].societe_id == 3 ? 'ST-' + selectedAgents[8*i + 2*v].numero_stagiaire :
                                    selectedAgents[8*i + 2*v].societe_id == 4 ? 'SM' :
                                    selectedAgents[8*i + 2*v].numero_employe ? selectedAgents[8*i + 2*v].numero_employe :
                                    selectedAgents[8*i + 2*v].numero_stagiaire ? selectedAgents[8*i + 2*v].numero_stagiaire :
                                    'Ndf'
                                )
                            if((8*i + 2*v + 1 < selectedAgents.length))
                                matriculeSecond = (
                                    selectedAgents[8*i + 2*v + 1].societe_id == 1 ? 'DGM-' + selectedAgents[8*i + 2*v + 1].numero_employe :
                                    selectedAgents[8*i + 2*v + 1].societe_id == 2 ? 'SOIT-' + selectedAgents[8*i + 2*v + 1].num_emp_soit :
                                    selectedAgents[8*i + 2*v + 1].societe_id == 3 ? 'ST-' + selectedAgents[8*i + 2*v + 1].numero_stagiaire :
                                    selectedAgents[8*i + 2*v + 1].societe_id == 4 ? 'SM' :
                                    selectedAgents[8*i + 2*v + 1].numero_employe ? selectedAgents[8*i + 2*v + 1].numero_employe :
                                    selectedAgents[8*i + 2*v + 1].numero_stagiaire ? selectedAgents[8*i + 2*v + 1].numero_stagiaire :
                                    'Ndf'
                                )
                            
                            return (
                                <View style={styles.section}>
                                    <View style={styles.badge}>
                                        {
                                            (8*i + 2*v < selectedAgents.length) && 
                                            <View style={styles.badgeContent}>
                                                <View style={styles.infoContainer}>
                                                    <Image src="/img/dirickx_guard.jpg"/>
                                                    <Text style={styles.matricule}>
                                                        {matriculeFirst}
                                                    </Text>
                                                    <Text style={styles.agent}>
                                                        {selectedAgents[8*i + 2*v].nom}
                                                    </Text>
                                                    {
                                                        selectedAgents[8*i + 2*v].fonction && 
                                                        <Text  style={styles.fonction}>{selectedAgents[8*i + 2*v].fonction}</Text>
                                                    }
                                                </View>
                                                <View style={styles.imageContainer}>
                                                    <Image 
                                                        src={selectedAgents[8*i + 2*v].qrcode_uri}
                                                        style={styles.qrcode}/>
                                                </View>
                                            </View>
                                        }
                                    </View>
                                    <View style={styles.badge}>
                                        {
                                            (8*i + 2*v + 1 < selectedAgents.length) && 
                                            <View style={styles.badgeContent}>
                                                <View style={styles.infoContainer}>
                                                    <Image src="/img/dirickx_guard.jpg"/>
                                                    <Text style={styles.matricule}>
                                                        {matriculeSecond}
                                                    </Text>
                                                    <Text style={styles.agent}>
                                                        {selectedAgents[8*i + 2*v + 1].nom}
                                                    </Text>
                                                    {   
                                                        selectedAgents[8*i + 2*v + 1].fonction && 
                                                        <Text  style={styles.fonction}>{selectedAgents[8*i + 2*v + 1].fonction}</Text>
                                                    }
                                                </View>
                                                <View style={styles.imageContainer}>
                                                    <Image 
                                                        src={selectedAgents[8*i + 2*v + 1].qrcode_uri}
                                                        style={styles.qrcode}/>
                                                </View>
                                            </View>
                                        }
                                    </View>
                                </View>
                            )
                        })
                    }
                </Page>
            )
        }
        return <div>
            {
                !printInterface ?
                <div>
                    {
                        showAgentTable && 
                        <Agent isForBadge changeAgent={this.updateAgentList} agentList={selectedAgents} closeModal={() => {this.toggleAgent(false)}}/>
                    }
                    <div id="badgeHeaderTable">
                        <div className="row">
                            <div className="cell">
                                <h3>Agents</h3>
                            </div>
                            <div className="cell right">
                                <button className="btn-white" onClick={() => {this.toggleAgent(true)}}>
                                    <img id="addAgentBtn" src="/img/all_agent.svg"/>
                                    Ajouter
                                </button>
                            </div>
                        </div>
                    </div>
                    <table className="default" id="selectedAgentTable">
                        <tbody style={{display:'block', width: '100%', overflowY: 'auto'}}>
                            {
                                selectedAgents.map((ag) => {
                                    return <tr key={ag.id}>
                                        <td className="numAgentCell">
                                            {
                                                ag.societe_id == 1 ? 'DGM-' + ag.numero_employe :
                                                ag.societe_id == 2 ? 'SOIT-' + ag.num_emp_soit :
                                                ag.societe_id == 3 ? 'ST-' + ag.numero_stagiaire :
                                                ag.societe_id == 4 ? 'SM' :
                                                ag.numero_employe ? ag.numero_employe :
                                                ag.numero_stagiaire ? ag.numero_stagiaire :
                                                <span className="purple">Ndf</span>
                                            }
                                        </td>
                                        <td>
                                            {ag.nom}
                                        </td>
                                        <td className="deleteIconCell">
                                            <img onClick={() => {this.removeAgent(ag.id)}}
                                                className="img-btn" 
                                                title="Supprimer" 
                                                src="/img/delete.svg"/>
                                        </td>
                                    </tr>
                                })
                            }
                        </tbody>
                    </table>
                    { 
                        (selectedAgents && selectedAgents.length > 0) &&
                        <div id="apercuBtnContainer">
                            <button onClick={() => {this.togglePrintInterface(true)}} className={selectedAgents.length % 8 == 0 ? "btn-primary" : "btn-default"}>Aperçu</button>
                        </div>
                    }
                </div>
                :
                <div>
                    <div id="btnPrintContainer">
                        <button className="btn-default fix-width" onClick={() => {this.togglePrintInterface(false)}}>Retour</button>
                    </div>
                    <div>
                        <PDFViewer style={{width: '100%', height: heightViewer}}>  
                            <Document title={"Badge " + moment().format('DD-MM-YY')} onRender={() => {this.setState({
                                disableApercu: false
                            })}}>
                                {pages}
                            </Document>
                        </PDFViewer>
                    </div>
                </div>
            }
        </div>
    }
}