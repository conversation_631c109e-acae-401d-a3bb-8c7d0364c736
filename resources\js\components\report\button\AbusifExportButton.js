import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'
const Excel = require("exceljs")
import {saveAs} from "file-saver";

export default class AbusifExportButton extends Component {
    constructor(props){
        super(props)
        this.handleExport = this.handleExport.bind(this)
        this.getNameSiteByProm = this.getNameSiteByProm.bind(this)
    }
    
    getNameSiteByProm(prom){
        const {sites} = this.props
        for(var i=0; i<sites.length; i++){
            if(sites[i].prom == prom)
                return sites[i].nom.toUpperCase()
        };
    }
    
    getVigilanceIntervalSet(time, interval){
        let currentVigilance = moment(time)
        let intervals = []
        if(currentVigilance.isAfter(moment(currentVigilance.format("YYYY-MM-DD") + " 06:50:00"))
         && currentVigilance.isBefore(moment(currentVigilance.format("YYYY-MM-DD") + " 17:50:00"))){
            let vigilanceJour = moment(currentVigilance.format("YYYY-MM-DD") + " 06:50:00")
            while(vigilanceJour.isBefore(moment(currentVigilance.format("YYYY-MM-DD") + " 17:50:00"))){
                let begin = vigilanceJour.clone()
                let nom = vigilanceJour.clone().add('10', 'minutes').format('HH:mm')
                let end = vigilanceJour.clone().add('1', 'hour').clone()
                intervals.push({
                    begin: begin,
                    nom: nom,
                    end: end
                })
                vigilanceJour.add(interval, 'minutes')
            }
        }
        else {
            let vigilanceNuit = moment(currentVigilance.format("YYYY-MM-DD") + " 17:50:00")
            while(vigilanceNuit.isBefore(moment(currentVigilance.format("YYYY-MM-DD") + " 06:50:00").add("1", "day"))){
                let begin = vigilanceNuit.clone()
                let nom = vigilanceNuit.clone().add('10', 'minutes').format('HH:mm')
                let end = vigilanceNuit.clone().add('30', 'minutes').clone()
                intervals.push({
                    begin: begin,
                    nom: nom,
                    end: end,
                })
                vigilanceNuit.add(interval, 'minutes')
            }
        }
        return intervals
    }

    exportAbusif(sites, workbook, total){
        const worksheet = workbook.addWorksheet("Bouton abusif")
        const rows = ['B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 
        'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 
        'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ',]
        const borderStyle = {
            top: {style:'thin'},
            left: {style:'thin'},
            bottom: {style:'thin'},
            right: {style:'thin'}
        }
        const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
        let line = 1

        if(sites.length > 0) {
            worksheet.mergeCells('A' + line +':Z' + line)
            worksheet.getCell('A' + line).value = sites.length + ' Sites sur ' + total
            worksheet.getCell('A' + line).font = {
                size: 16,
                bold: true
            }

            line = line+3
            worksheet.getColumn('A').width = 20
            for(let i=0; i<sites.length; i++){
                let site = sites[i]
                worksheet.getCell('A' + line).value = site.nom
                worksheet.getCell('A' + line).font = {
                    size: 16,
                    bold: true
                }
                worksheet.mergeCells('A' + line +':Z' + line)
                line++
                
                site.agents.forEach(ag => {
                    worksheet.getCell('A' + line).value = (ag.numero_employe ? ag.numero_employe : ag.numero_stagiaire) + ' ' + ag.nom
                    worksheet.mergeCells('A' + line +':Z' + line)
                    line++;
                });

                worksheet.getCell('A' + line).value = 'Vigilance'
                worksheet.getCell('A' + line).font = {bold: true}
                worksheet.getCell('A' + line).border = borderStyle
                worksheet.getCell('A' + (line + 1)).value = 'Nb. bouton'
                worksheet.getCell('A' + (line + 1)).font = {bold: true}
                worksheet.getCell('A' + (line + 1)).border = borderStyle
                for(let j=0; j<site.abusifs.length; j++){
                    worksheet.getCell(rows[j] + line).value = site.abusifs[j].nom
                    worksheet.getCell(rows[j] + line).border = borderStyle
                    worksheet.getCell(rows[j] + line).alignment = alignmentStyle
                    worksheet.getCell(rows[j] + (line + 1)).value = site.abusifs[j].count
                    worksheet.getCell(rows[j] + (line + 1)).border = borderStyle
                    worksheet.getCell(rows[j] + (line + 1)).alignment = alignmentStyle
                }
                line = line + 3
            }
        }
        else{
            worksheet.getCell('A1').value = 'Aucun bouton abusif'
            worksheet.mergeCells('A1:Z1')
        }
    }

    handleExport(){
        console.log("export...")
        const workbook = new Excel.Workbook();
        const {selectedDate, selectedHoraire} = this.props
        const dateTime = moment(selectedDate).format('YYYY-MM-DD') + ' ' + selectedHoraire
        const currentVigilance = moment(dateTime)
        
        this.props.toggleLoading(true)
        axios.get('/api/vigilances/report_abusif?datetime=' + dateTime)
        .then(async ({data}) => {
            console.log('report_abusif')
            console.log(data)
            let sites = data.sites
            const vigilances = data.vigilances
            const agents = data.agents
            for(let i=0; i<sites.length; i++) {
                sites[i].vigilances = []
                vigilances.map((vg) => {
                    if(sites[i].idsite != 58 && sites[i].idsite == vg.site_id)
                        sites[i].vigilances.push(vg)
                })
                /*sites[i].commentaires = []
                commentaires.map((cmt) => {
                    if(sites[i].prom == cmt.prom)
                        sites[i].commentaires.push(cmt)
                })*/
                sites[i].agents = []
                agents.map((ag) => {
                    if(sites[i].idsite == ag.site_id)
                        sites[i].agents.push(ag)
                })
            }
            for(let i=0; i<sites.length; i++){
                let site = sites[i]
                let vigilances = null
                if(currentVigilance.isAfter(moment(currentVigilance.format("YYYY-MM-DD") + " 06:50:00"))
                && currentVigilance.isBefore(moment(currentVigilance.format("YYYY-MM-DD") + " 17:50:00"))){
                    if(site.interval_vigilance_jour)
                        vigilances = this.getVigilanceIntervalSet(dateTime, site.interval_vigilance_jour)
                    else vigilances = this.getVigilanceIntervalSet(dateTime, 60)
                }
                else
                    if(site.interval_vigilance_nuit)
                        vigilances = this.getVigilanceIntervalSet(dateTime, site.interval_vigilance_nuit)
                    else
                        vigilances = this.getVigilanceIntervalSet(dateTime, 30)
                
                site.vigilances.map(({dtarrived}) => {
                    let date = moment(dtarrived)
                    for(let i = 0; i<vigilances.length; i++){
                        if(!vigilances[i].date && date.isAfter(vigilances[i].begin) && date.isBefore(vigilances[i].end)){
                            vigilances[i].date = date.format('HH:mm')
                            vigilances[i].dateMoment = date
                        }
                        if(date.isAfter(vigilances[i].begin) && date.isBefore(vigilances[i].end)){
                            if(!vigilances[i].count) vigilances[i].count = 1
                            else vigilances[i].count = vigilances[i].count + 1
                        }
                    }
                })
    
                const counts = []
                vigilances.forEach((v)=> {
                    if(v.count) counts.push(v.count)
                })
                site.indice = counts.length > 0 ? (counts.reduce((a, b) => a + b, 0) / counts.length) : null
                
                let abusifs = []
                if(counts && (counts.reduce((a, b) => a + b, 0) / counts.length) > 3){
                    for(let i=0; i<vigilances.length; i++){
                        if(vigilances[i].count > 3)
                            abusifs.push(vigilances[i])
                    }
                }
                site.abusifs = abusifs
                delete site.vigilances
                delete site.interval_vigilance_jour
                delete site.interval_vigilance_nuit
            }
            let abusif_sites = []
            sites.map((s) => {
                if(s.indice > 3) abusif_sites.push(s)
            })
            abusif_sites.sort((a, b) => {
                if ( a.indice > b.indice ){
                    return -1;
                }
                if ( a.indice < b.indice ){
                    return 1;
                }
                return 0;
            })

            this.exportAbusif(abusif_sites, workbook, sites.length)

            const buffer = await workbook.xlsx.writeBuffer();
            const fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
            const fileExtension = '.xlsx'
            const blob = new Blob([buffer], {type: fileType})

            saveAs(blob, 'Bouton Abusif du '
                    + moment(selectedDate).format('DD-MM-YYYY')
                    + (selectedHoraire == '07:00:00' ? ' JOUR' : selectedHoraire == '18:00:00' ? ' NUIT' : '') + fileExtension)
            this.props.toggleLoading(false)
        })
        .catch(() => {
            this.props.toggleLoading(false)
        }, 10000)
    }

    render(){
        return (
            <button onClick={this.handleExport}  
                className="export-report-btn">
                    Exporter
            </button>
        )
    }
}