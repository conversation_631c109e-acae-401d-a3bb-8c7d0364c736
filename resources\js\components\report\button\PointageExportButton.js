import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'
const Excel = require("exceljs")
import {saveAs} from "file-saver";

export default class PointageExportButton extends Component {
    constructor(props){
        super(props)
        this.handleExport = this.handleExport.bind(this)
    }

    generateColumnExcel(){
        const {beginDate, endDate} = this.props
        let currentCol = "E"
        let colsByDate = {}
        let currentDate = moment(beginDate).set({hour: 7, minute: 0, second: 0})
        while(moment(currentDate).isBefore(moment(endDate).set({hour: 23}))){
            colsByDate[currentDate.format('YYYY-MM-DD HH:mm:ss')] = {
                title : currentDate.format('DD') + ' ' + 
                    (currentDate.format('HH:mm:ss') == '07:00:00' ? 'J' : 
                    currentDate.format('HH:mm:ss') == '18:00:00' ? 'N' : ''),
                col: currentCol
            }
            let currentArray = currentCol.split('')
            if(currentArray[currentArray.length - 1] != "Z")
                currentArray[currentArray.length - 1] = String.fromCharCode(currentArray[currentArray.length - 1].charCodeAt(0) + 1)
            else if(currentArray.length == 1)
                currentArray = ['A', 'A']
            else{ 
                currentArray[0] = String.fromCharCode(currentArray[0].charCodeAt(0) + 1)
                currentArray[1] = 'A'
            }
            currentCol = currentArray.join('')
            if(currentDate.format('HH:mm:ss') == '07:00:00')
                currentDate.set({hour: 18, minute: 0, second: 0})
            else currentDate.add('day', 1).set({hour: 7, minute: 0, second: 0})
        }
        colsByDate.total = {
            title: "Total",
            col: currentCol
        }
        return colsByDate
    }
    
    handleExport(){
        const {siteIds, currentItem, beginDate, endDate} = this.props
        const colsByDate = this.generateColumnExcel()
        const workbook = new Excel.Workbook();
        const worksheet = workbook.addWorksheet('POINTAGE', {
            views: [
                {state: 'frozen', ySplit: 1},
                //{state: 'frozen', xSplit: 3}
            ]
        })
        worksheet.properties.defaultRowHeight = 24;
        const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
        const alignLeftStyle = { vertical: 'middle'}

        const colors = [
            {
                site: '9c27b0',
                line1: 'f3e5f5',
                line2: 'e1bee7'
            },
            {
                site: '2196f3',
                line1: 'e3f2fd',
                line2: 'bbdefb'
            },
            {
                site: 'cddc39',
                line1: 'f9fbe7',
                line2: 'f0f4c3'
            },
            {
                site: '009688',
                line1: 'e0f2f1',
                line2: 'b2dfdb'
            }
        ]
        const borderStyle = {
            top: {style:'thin'},
            left: {style:'thin'},
            bottom: {style:'thin'},
            right: {style:'thin'}
        }
        const borderHoraire = {
            left: {style:'thin'},
            right: {style:'thin'}
        }
        const fillTitle = {
            type: 'pattern',
            pattern:'solid',
            fgColor:{argb:'FF607d8b'}
        }
        const fillHoraire = {
            type: 'pattern',
            pattern:'solid',
            fgColor:{argb:'FFb0bec5'}
        }

        worksheet.getColumn('A').width = 15
        worksheet.getColumn('B').width = 65
        worksheet.getColumn('C').width = 55
        worksheet.getColumn('D').width = 30

        worksheet.getRow(1).height = 20
        worksheet.getCell('A1').value = 'MATRICULE'
        worksheet.getCell('A1').fill = fillTitle
        worksheet.getCell('A1').border = borderStyle
        worksheet.getCell('A1').alignment = alignmentStyle
        worksheet.getCell('B1').value = 'NOM'
        worksheet.getCell('B1').fill = fillTitle
        worksheet.getCell('B1').border = borderStyle
        worksheet.getCell('B1').alignment = alignLeftStyle
        worksheet.getCell('C1').value = 'SITE'
        worksheet.getCell('C1').fill = fillTitle
        worksheet.getCell('C1').border = borderStyle
        worksheet.getCell('C1').alignment = alignmentStyle 
        worksheet.getCell('D1').value = 'CONTACT'
        worksheet.getCell('D1').fill = fillTitle
        worksheet.getCell('D1').border = borderStyle
        worksheet.getCell('D1').alignment = alignmentStyle

        Object.keys(colsByDate).map((key) => {
            worksheet.getColumn(colsByDate[key].col).width = 5
            worksheet.getCell(colsByDate[key].col + '1').value = colsByDate[key].title
            worksheet.getCell(colsByDate[key].col + '1').fill = fillHoraire
            worksheet.getCell(colsByDate[key].col + '1').border = borderStyle
            worksheet.getCell(colsByDate[key].col + '1').alignment = alignmentStyle
        })

        //Total
        worksheet.getColumn(colsByDate.total.col).width = 8
        worksheet.getCell(colsByDate.total.col + '1').value = colsByDate.total.title
        worksheet.getCell(colsByDate.total.col + '1').fill = fillHoraire
        worksheet.getCell(colsByDate.total.col + '1').border = borderStyle
        worksheet.getCell(colsByDate.total.col + '1').alignment = alignmentStyle

        let data = {
            site_ids : siteIds,
            group_id: currentItem.id,
            begin: moment(beginDate).format('YYYY-MM-DD 00:00:00'),
            end: moment(endDate).format('YYYY-MM-DD 23:00:00')
        }
        axios.post('/api/reports/pointage_mensuel', data)
        .then(async ({data}) => {
            let agents = data.agents
            let pointages = data.pointages
            agents.map((a) => {
                let pointageByAgents = [] 
                let newPointages = []
                pointages.map((p) => {
                    if(p.agent_id == a.id)
                        pointageByAgents.push(p)
                    else newPointages.push(p)
                })
                pointages = newPointages
                a['pointages'] = pointageByAgents
                return a
            })

            let line = 2
            let colorIndex = 0
            let lastSiteId = 0
            agents.map((a) => {
                if(a.pointages.length > 0){
                    if(lastSiteId && lastSiteId != a.site_id){
                        if(colorIndex < colors.length -1)
                            colorIndex++
                        else colorIndex = 0
                    }
                    const fillLine = {
                        type: 'pattern',
                        pattern:'solid',
                        fgColor:{argb:'FF' + colors[colorIndex]['line2']}
                    }
                    const fillSite = {
                        type: 'pattern',
                        pattern:'solid',
                        fgColor:{argb:'FF' + colors[colorIndex].site}
                    }

                    Object.keys(colsByDate).map((key) => {
                        worksheet.getCell(colsByDate[key].col + line).fill = fillLine
                        worksheet.getCell(colsByDate[key].col + line).border = borderStyle
                    })
                    worksheet.getRow(line).height = 20
                    worksheet.getCell('A' + line).value = (
                        a.societe_id == 1 ? a.numero_employe :
                        a.societe_id == 2 ? a.num_emp_soit :
                        a.societe_id == 3 ? a.numero_stagiaire :
                        a.societe_id == 4 ? 'SM' :
                        a.numero_employe ? a.numero_employe :
                        a.numero_stagiaire ? a.numero_stagiaire :
                        'Non définie'
                    )
                    worksheet.getCell('A' + line).border = borderStyle
                    worksheet.getCell('A' + line).fill = fillLine
                    worksheet.getCell('A' + line).alignment = alignmentStyle
                    worksheet.getCell('B' + line).value = a.nom
                    worksheet.getCell('B' + line).border = borderStyle
                    worksheet.getCell('B' + line).fill = fillLine
                    worksheet.getCell('B' + line).alignment = alignLeftStyle
                    worksheet.getCell('C' + line).value = a.site
                    worksheet.getCell('C' + line).border = borderStyle
                    worksheet.getCell('C' + line).fill = fillSite
                    worksheet.getCell('C' + line).alignment = alignmentStyle
                    worksheet.getCell('D' + line).value = a.phone_agent
                    worksheet.getCell('D' + line).border = borderStyle
                    worksheet.getCell('D' + line).fill = fillLine
                    worksheet.getCell('D' + line).alignment = alignmentStyle
                    let total = 0;
                    a.pointages.map((p) => {
                        total += 12
                        worksheet.getCell(colsByDate[p.date_pointage].col + line).value = 12
                        worksheet.getCell(colsByDate[p.date_pointage].col + line).fill = fillSite
                        worksheet.getCell(colsByDate[p.date_pointage].col + line).alignment = alignmentStyle
                        if(a.site_id != p.site_id)
                            worksheet.getCell(colsByDate[p.date_pointage].col + line).note = p.site
                    })
                    
                    worksheet.getCell(colsByDate.total.col + line).value = total
                    worksheet.getCell(colsByDate.total.col + line).fill = fillSite
                    worksheet.getCell(colsByDate.total.col + line).alignment = alignmentStyle

                    lastSiteId = a.site_id
                    line ++;
                }
            })

            const buffer = await workbook.xlsx.writeBuffer();
            const fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
            const fileExtension = '.xlsx'
            const blob = new Blob([buffer], {type: fileType});
            let titleExport = 'POINTAGE '+ currentItem.nom.toUpperCase() + ' du ' + moment(beginDate).format('DD-MM-YYYY') + 
                (moment(beginDate).format('DD-MM-YYYY') != moment(endDate).format('DD-MM-YYYY') ? ' au ' + moment(endDate).format('DD-MM-YYYY'): '') 
            saveAs(blob, titleExport + fileExtension);
        })
    }

    render(){
        const {currentItem, siteIds} = this.props
        return (
            <button onClick={this.handleExport} 
                disabled={!currentItem || siteIds.length == 0} 
                className="export-report-btn">
                    Exporter
            </button>
        )
    }
}