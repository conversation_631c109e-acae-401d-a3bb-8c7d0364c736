import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'
const Excel = require("exceljs")
import {saveAs} from "file-saver";

export default class VigilanceExportButton extends Component {
    constructor(props){
        super(props)
        this.handleExport = this.handleExport.bind(this)
        this.getNameSiteById = this.getNameSiteById.bind(this)
    }

    componentDidMount(){
        
    }
    
    getNameSiteById(id){
        const {sites} = this.props
        for(var i=0; i<sites.length; i++){
            if(sites[i].idsite == id)
                return sites[i].nom.toUpperCase()
        };
    }
    
    getVigilanceIntervalSet(time, interval){
        let currentVigilance = moment(time)
        let intervals = []
        if(currentVigilance.isAfter(moment(currentVigilance.format("YYYY-MM-DD") + " 06:50:00"))
         && currentVigilance.isBefore(moment(currentVigilance.format("YYYY-MM-DD") + " 17:50:00"))){
            let vigilanceJour = moment(currentVigilance.format("YYYY-MM-DD") + " 06:50:00")
            while(vigilanceJour.isBefore(moment(currentVigilance.format("YYYY-MM-DD") + " 17:50:00"))){
                let begin = vigilanceJour.clone()
                let nom = vigilanceJour.clone().add('10', 'minutes').format('HH:mm')
                let end = vigilanceJour.clone().add('1', 'hour').clone()
                intervals.push({
                    begin: begin,
                    nom: nom,
                    end: end
                })
                vigilanceJour.add(interval, 'minutes')
            }
        }
        else {
            let vigilanceNuit = moment(currentVigilance.format("YYYY-MM-DD") + " 17:50:00")
            while(vigilanceNuit.isBefore(moment(currentVigilance.format("YYYY-MM-DD") + " 06:50:00").add("1", "day"))){
                let begin = vigilanceNuit.clone()
                let nom = vigilanceNuit.clone().add('10', 'minutes').format('HH:mm')
                let end = vigilanceNuit.clone().add('30', 'minutes').clone()
                intervals.push({
                    begin: begin,
                    nom: nom,
                    end: end,
                })
                vigilanceNuit.add(interval, 'minutes')
            }
        }
        return intervals
    }

    exportVigilance(site, workbook){
        const {selectedDate, selectedHoraire} = this.props
        const dateTime = moment(selectedDate).format('YYYY-MM-DD') + ' ' + selectedHoraire
        const currentVigilance = moment(dateTime)

        let vigilances = null
        
        if(currentVigilance.isAfter(moment(currentVigilance.format("YYYY-MM-DD") + " 06:50:00"))
        && currentVigilance.isBefore(moment(currentVigilance.format("YYYY-MM-DD") + " 17:50:00"))){
            if(site.interval_vigilance_jour)
                vigilances = this.getVigilanceIntervalSet(dateTime, site.interval_vigilance_jour)
            else vigilances = this.getVigilanceIntervalSet(dateTime, 60)
        }
        else
            if(site.interval_vigilance_nuit)
                vigilances = this.getVigilanceIntervalSet(dateTime, site.interval_vigilance_nuit)
            else
                vigilances = this.getVigilanceIntervalSet(dateTime, 30)
        site.vigilances.map(({dtarrived}) => {
            let date = moment(dtarrived)
            for(let i = 0; i<vigilances.length; i++){
                if(!vigilances[i].date && date.isAfter(vigilances[i].begin) && date.isBefore(vigilances[i].end)){
                    vigilances[i].date = date.format('HH:mm')
                    vigilances[i].dateMoment = date
                }
                if(date.isAfter(vigilances[i].begin) && date.isBefore(vigilances[i].end)){
                    if(!vigilances[i].count) vigilances[i].count = 1
                    else vigilances[i].count = vigilances[i].count + 1
                }
            }
        })
        for(let i = 0; i<vigilances.length; i++){
            //vigilances[i].status = this.getColorCardSite(vigilances[i])
            site.commentaires.forEach(cmt => {
                if(cmt.date_vigilance == vigilances[i].begin.clone().add('10', 'minutes').format('YYYY-MM-DD HH:mm:ss')){
                    vigilances[i].commentaire = cmt.commentaire
                    vigilances[i].objet = cmt.objet
                    var patt = new RegExp(/check-phone +(\d{2}h\d{2})/)
                    if(!vigilances[i].date && cmt.objet && patt.test(cmt.objet.toLowerCase())){
                        vigilances[i].date = patt.exec(cmt.objet.toLowerCase())[1].replace('h', ':')
                    }
                }
            })
        }
    
        const worksheet = workbook.addWorksheet(site.nom)
        worksheet.getColumn('A').width = 15
        worksheet.getColumn('B').width = 15
        worksheet.getColumn('C').width = 15
        worksheet.getColumn('D').width = 50
        worksheet.getColumn('E').width = 80
        worksheet.getCell('A1').value = site.nom
        worksheet.getCell('A1').font = {
            size: 20,
            bold: true
        }
        worksheet.mergeCells('A1:E1')
        worksheet.getCell('A2').value = 'Agent :'
        worksheet.getCell('A2').font = {
            underline: true
        }
        worksheet.mergeCells('A2:E2')
        let vIndex = 3
        site.agents.forEach(ag => {
            worksheet.getCell('A' + vIndex).value = '[' + (ag.numero_employe ? ag.numero_employe : ag.numero_stagiaire) + '] ' + ag.nom
            worksheet.mergeCells('A' + vIndex +':E' + vIndex)
            vIndex++;
        });
        if(!site.agents)
            vIndex++;
        
        worksheet.mergeCells('A' + vIndex +':E' + vIndex)
        vIndex++;
        worksheet.mergeCells('A' + vIndex +':E' + vIndex)
            
        const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
        const headerFont = { bold: true }
        const borderStyle = {
            top: {style:'thin'},
            left: {style:'thin'},
            bottom: {style:'thin'},
            right: {style:'thin'}
        }
        vIndex++;
        const dangerFont = { color: 'red'}
        worksheet.getCell('A' + vIndex).value = 'Vigilance'
        worksheet.getCell('A' + vIndex).alignment = alignmentStyle
        worksheet.getCell('A' + vIndex).font = headerFont
        worksheet.getCell('A' + vIndex).border = borderStyle

        worksheet.getCell('B' + vIndex).value = 'Heure'
        worksheet.getCell('B' + vIndex).alignment = alignmentStyle
        worksheet.getCell('B' + vIndex).font = headerFont
        worksheet.getCell('B' + vIndex).border = borderStyle

        worksheet.getCell('C' + vIndex).value = 'Nb bouton'
        worksheet.getCell('C' + vIndex).alignment = alignmentStyle
        worksheet.getCell('C' + vIndex).font = headerFont
        worksheet.getCell('C' + vIndex).border = borderStyle

        worksheet.getCell('D' + vIndex).value = 'Objet (Cause du manque)'
        worksheet.getCell('D' + vIndex).font = headerFont
        worksheet.getCell('D' + vIndex).border = borderStyle

        worksheet.getCell('E' + vIndex).value = 'Commentaire (Mesure prise)'
        worksheet.getCell('E' + vIndex).font = headerFont
        worksheet.getCell('E' + vIndex).border = borderStyle

        vIndex++;
        vigilances.forEach(vg => {
            worksheet.getCell('A' + vIndex).value = vg.nom
            worksheet.getCell('A' + vIndex).alignment = alignmentStyle
            worksheet.getCell('A' + vIndex).border = borderStyle
            if(!vg.date)
                worksheet.getCell('A' + vIndex).font = dangerFont

            worksheet.getCell('B' + vIndex).value = vg.date
            worksheet.getCell('B' + vIndex).alignment = alignmentStyle
            worksheet.getCell('B' + vIndex).border = borderStyle

            worksheet.getCell('C' + vIndex).value = vg.count
            worksheet.getCell('C' + vIndex).alignment = alignmentStyle
            worksheet.getCell('C' + vIndex).border = borderStyle
        
            worksheet.getCell('D' + vIndex).value = vg.objet
            worksheet.getCell('D' + vIndex).border = borderStyle
            
            worksheet.getCell('E' + vIndex).value = vg.commentaire ? vg.commentaire.replace('\n', ' ') : ''
            worksheet.getCell('E' + vIndex).border = borderStyle

            vIndex ++
        })
    }
    
    handleExport(){
        console.log("export...")
        const workbook = new Excel.Workbook();
        const {selectedDate, selectedHoraire, selectedGroupId, groupVigilances} = this.props
        const dateTime = moment(selectedDate).format('YYYY-MM-DD') + ' ' + selectedHoraire
        let data = {
            datetime: dateTime,
            group_id: selectedGroupId,
        }
        this.props.toggleLoading(true)
        axios.post('/api/vigilances/report_vigilance', data)
        .then(async ({data}) => {
            console.log('report_vigilance')
            console.log(data)
            let sites = data.sites
            const vigilances = data.vigilances
            const commentaires = data.commentaires
            const agents = data.agents
            for(let i=0; i<sites.length; i++) {
                sites[i].vigilances = []
                vigilances.map((vg) => {
                    if(sites[i].idsite == vg.site_id)
                        sites[i].vigilances.push(vg)
                })
                sites[i].commentaires = []
                commentaires.map((cmt) => {
                    if(sites[i].idsite == cmt.site_id)
                        sites[i].commentaires.push(cmt)
                })
                sites[i].agents = []
                agents.map((ag) => {
                    if(sites[i].idsite == ag.site_id)
                        sites[i].agents.push(ag)
                })
            }
            let siteIndex = 0
            while(siteIndex < sites.length){
                console.log("begin export: " + siteIndex)
                console.log(sites[siteIndex])
                this.exportVigilance(sites[siteIndex], workbook)
                console.log("end export\n-------------------------")
                siteIndex ++;
            }
            const buffer = await workbook.xlsx.writeBuffer();
            const fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
            const fileExtension = '.xlsx'
            const blob = new Blob([buffer], {type: fileType})
            let groupName = ''
            groupVigilances.forEach((g) => {
                if(g.id == selectedGroupId)
                    groupName = g.nom
            }) 
            saveAs(blob, 'Vigilance ' + groupName + ' '
                 + moment(selectedDate).format('DD-MM-YYYY')
                 + (selectedHoraire == '07:00:00' ? ' JOUR' : selectedHoraire == '18:00:00' ? ' NUIT' : '') + fileExtension)
            this.props.toggleLoading(false)
        })
        .catch(() => {
            this.props.toggleLoading(false)
        })
    }

    render(){
        const {selectedGroupId} = this.props
        return (
            <button onClick={this.handleExport} 
                disabled={!selectedGroupId} 
                className="export-report-btn">
                    Exporter
            </button>
        )
    }
}