import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'
const Excel = require("exceljs")
import {saveAs} from "file-saver";

export default class PointageExportButton extends Component {
    constructor(props){
        super(props)
        this.handleExport = this.handleExport.bind(this)
    }

    handleExportPointageDaily(){
        const {titleExport, selectedSiteIds, selectedDate, selectedHoraire, sites} = this.state
        const dateTime = moment(selectedDate).format('YYYY-MM-DD') + ' ' + selectedHoraire
        this.props.toggleLoading(true)
        axios.get('/api/reports/pointage_agent?datetime=' + dateTime)
        .then(async ({data}) => {
            let pointages = []
            sites.forEach(st => {
                if(selectedSiteIds.includes(st.prom)){
                    let agents = []
                    data.forEach(ptg => {
                        if(ptg.site_id == st.idsite){
                            agents.push({
                                nom: ptg.nom ? ptg.nom : ptg.prenom,
                                num: ptg.numero_employe ? ptg.numero_employe : ptg.numero_stagiaire
                            })
                        }
                    });
                    pointages.push({
                        site: st.nom.toUpperCase(),
                        agents: agents
                    })
                }
            })
            if(pointages){
                const borderStyle = {
                    top: {style:'thin'},
                    left: {style:'thin'},
                    bottom: {style:'thin'},
                    right: {style:'thin'}
                }
                const workbook = new Excel.Workbook();
                const worksheet = workbook.addWorksheet(titleExport)
                worksheet.getColumn('A').width = 15
                worksheet.getColumn('B').width = 50
                worksheet.getCell('A1').value = titleExport
                worksheet.getCell('A1').font = {
                    size: 20
                }
                worksheet.mergeCells('A1:B1')
                worksheet.mergeCells('A2:B2')
                let line = 3;
                pointages.forEach(ptg => {
                    worksheet.getCell('A' + line).value = ptg.site
                    worksheet.getCell('A' + line).font = {
                        size: 14,
                        bold: true
                    }
                    worksheet.mergeCells('A'+ line + ':B' + line)
                    worksheet.getCell('A' + line).border = borderStyle
                    line++
                    ptg.agents.checked.forEach(ag =>{
                        worksheet.getCell('A' + line).value = ag.num
                        worksheet.getCell('A' + line).border = borderStyle
                        worksheet.getCell('B' + line).value = ag.nom
                        worksheet.getCell('B' + line).border = borderStyle
                        line++;
                        worksheet.mergeCells('A'+ line + ':B' + line)
                    })
                    if(!ptg.agents){
                        worksheet.getCell('A' + line).value = 'Aucun agent'
                        worksheet.getCell('A' + line).font = {color: { argb: 'FFFF0000' }}
                        worksheet.getCell('A' + line).border = borderStyle
                        worksheet.mergeCells('A'+ line + ':B' + line)
                    }
                    line++
                    worksheet.mergeCells('A'+ line + ':B' + line)
                    line++
                });
                
                this.props.toggleLoading(false)
                const buffer = await workbook.xlsx.writeBuffer();
                const fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                const fileExtension = '.xlsx'
                const blob = new Blob([buffer], {type: fileType});
                saveAs(blob, titleExport + fileExtension);
            }
        })
        .catch((e) =>{
            console.error(e)
        })
    }
}