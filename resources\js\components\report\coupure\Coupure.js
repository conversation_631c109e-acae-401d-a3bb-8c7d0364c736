import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'
import EditCoupureModal from './EditCoupureModal'
import IconButton from '../../button/IconButton'
import LoadingData from '../../loading/LoadingData'

export default class Coupure extends Component {
    constructor(props){
        super(props)
        this.state = {
            showLoading: false,
            showAddCoupure: false,
            coupures: []
        }
        this.updateData = this.updateData.bind(this)
    }
    updateData(){
        this.setState({
            showLoading: true
        })
        axios.get('/api/coupures')
        .then(({data}) => {
            this.setState({
                showLoading: false,
                coupures: data
            })
        })
    }
    componentDidMount(){
        this.updateData()
    }
    componentWillUnmount(){
        
    }
    render(){
        const {showLoading, coupures, showAddCoupure} = this.state
        return <>
            {
                showLoading ?
                    <LoadingData/>
                :
                <div className="table">
                    {
                        showAddCoupure &&
                        <EditCoupureModal updateData={this.updateData} closeModal={() => this.setState({showAddCoupure: false})}/>
                    }
                    <div>
                        <h3 className="h3-table">
                            <span className="cell fix-cell-site">
                                Problème de transmission
                            </span>
                            <div className="cell right fix-cell-client">
                                <span id="newClientBtn">
                                    <IconButton onClick={() => this.setState({showAddCoupure: true})} label="Ajouter" src="/img/edit.svg"/>
                                </span>
                            </div>
                        </h3>
                    </div>
                    <div className="table">
                        <div className="row-table">
                            <table className="fixed_header default layout-fixed">
                                <thead>
                                    <tr>
                                        <th>Transmitter</th>
                                        <th className="cellOperateur">Date de vigilance</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {
                                        coupures.map(c => {
                                            return <tr key={c.id}>
                                                <td>{c.transmitter}</td>
                                                <td className="cellOperateur">
                                                    {moment(c.vigilance).format("DD-MM-YYYY HH:mm")}
                                                </td>
                                            </tr>
                                        })
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }
        </>
    }
}