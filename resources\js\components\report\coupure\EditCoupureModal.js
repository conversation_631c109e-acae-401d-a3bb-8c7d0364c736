import React, { Component } from 'react'
import axios from 'axios'
import DatePicker  from 'react-datepicker'
import moment from 'moment'


import 'react-datepicker/dist/react-datepicker.css'
import Modal from '../../modal/Modal'

export default class EditCoupureModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            transmitter: '',
            vigilance: '',
            dateVg: '', 
            timeVg: '',
            disableSave: true,
            error: null,
        }
        this.handleChangeTransmitter = this.handleChangeTransmitter.bind(this)
        this.handleChangeTimeVigilance = this.handleChangeTimeVigilance.bind(this)
        this.handleChangeDateVigilance = this.handleChangeDateVigilance.bind(this)
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)

    }
    handleChangeTimeVigilance(e){
        const {dateVg} = this.state
        const time = e.target.value
        this.setState({
            timeVg: time,
            vigilance: (dateVg && time) ? moment(dateVg).format("YYYY-MM-DD") + ' ' + time : ''
        })
    }
    handleChangeDateVigilance(date){
        const {timeVg} = this.state
        this.setState({
            dateVg: date,
            vigilance: (date && timeVg) ? moment(date).format("YYYY-MM-DD") + ' ' + timeVg : ''
        })
    }
    handleChangeTransmitter(event){
        this.setState({
            transmitter: event.target.value,
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    handleSave(){
        const {transmitter, vigilance} = this.state
        this.setState({
            disableSave: true,
            error: null,
        })
        let data = new FormData()
        if(transmitter && transmitter.trim())
            data.append("transmitter", transmitter.trim())
        if(vigilance)
            data.append("vigilance", vigilance)
        axios.post("/api/coupures/add", data)
        .then(({data}) => {
            if(data.error){
                console.error(data.error)
            }
            else if(data){
                this.props.updateData()
                this.props.closeModal()
            }
        })
        .finally(()=>{
            this.setState({
                disableSave: false
            })
        })
    }
    render(){
        const {vigilance, dateVg, timeVg, transmitter, error} = this.state
        return ( 
            <Modal
                disableSave={!(vigilance && transmitter)} 
                width="md" 
                handleSave={this.handleSave} 
                handleCancel={this.handleCancel}
                error={error}
            >
                <h3>Transmission</h3>
                <div className="input-container">
                    <label>Récepteur *</label>
                    <select value={transmitter} onChange={this.handleChangeTransmitter}>
                        <option></option>
                        <option value="0321134413">SMPP1 - 344 13</option>
                        <option value="0321154685">SMPP2 - 546 85</option>
                        <option value="57.128.20.26">OVH - 57.128.20.26</option>
                        <option value="41.188.49.14">TELMA - 41.188.49.14</option>
                    </select>
                </div>
                <div className="input-container">
                    <label>Date vigilance </label>
                    <DatePicker className="datepicker" dateFormat="dd-MM-yyyy" selected={dateVg} onChange={this.handleChangeDateVigilance}/>
                </div>
                <div className="input-container">
                    <label>Heure vigilance *</label>
                    <select value={timeVg} onChange={this.handleChangeTimeVigilance}>
                        <option></option>
                        <option value="06:00:00">06:00</option>
                        <option value="07:00:00">07:00</option>
                        <option value="08:00:00">08:00</option>
                        <option value="09:00:00">09:00</option>
                        <option value="10:00:00">10:00</option>
                        <option value="11:00:00">11:00</option>
                        <option value="12:00:00">12:00</option>
                        <option value="13:00:00">13:00</option>
                        <option value="14:00:00">14:00</option>
                        <option value="15:00:00">15:00</option>
                        <option value="16:00:00">16:00</option>
                        <option value="17:00:00">17:00</option>
                        <option value="18:00:00">18:00</option>
                        <option value="18:30:00">18:30</option>
                        <option value="19:00:00">19:00</option>
                        <option value="19:30:00">19:30</option>
                        <option value="20:00:00">20:00</option>
                        <option value="20:30:00">20:30</option>
                        <option value="21:00:00">21:00</option>
                        <option value="21:30:00">21:30</option>
                        <option value="22:00:00">22:00</option>
                        <option value="22:30:00">22:30</option>
                        <option value="23:00:00">23:00</option>
                        <option value="23:30:00">23:30</option>
                        <option value="00:00:00">00:00</option>
                        <option value="00:30:00">00:30</option>
                        <option value="01:00:00">01:00</option>
                        <option value="01:30:00">01:30</option>
                        <option value="02:00:00">02:00</option>
                        <option value="02:30:00">02:30</option>
                        <option value="03:00:00">03:00</option>
                        <option value="03:30:00">03:30</option>
                        <option value="04:00:00">04:00</option>
                        <option value="04:30:00">04:30</option>
                        <option value="05:00:00">05:00</option>
                        <option value="05:30:00">05:30</option>
                    </select>
                </div>
                <div className="input-container">
                    <label>Vigilance *</label>
                    <input value={vigilance} disabled/>
                </div>
            </Modal>
        )
    }
}