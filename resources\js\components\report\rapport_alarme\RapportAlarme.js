import React, { Component } from 'react'

import './rapport.css'
import LoadingData from '../../loading/LoadingData'
import axios from 'axios'
import DeleteRapportModal from '../../alarm/rapport/DeleteRapportModal'
import EditRapportModal from '../../alarm/rapport/EditRapportModal'
import moment from 'moment'

export default class RapportAlarme extends Component {
    constructor(props){
        super(props)
        this.state = {
            showLoading: false,
            rapports: [],
            currentRapport: null,
            showAddRapport: false, 
            showEditRapport: false, 
            showDeleteRapport: false,
        }
        this.updateData = this.updateData.bind(this)
        this.handleCloseEditRapportModal = this.handleCloseEditRapportModal.bind(this)
        this.showEditRapportModal = this.showEditRapportModal.bind(this)
        this.showDeleteRapportModal = this.showDeleteRapportModal.bind(this)
    }
    toggleAddRapport(v){
        this.setState({
            showAddRapport: v
        })
    }
    showEditRapportModal(rapport){
        axios.get("/api/rapports/show/" + rapport.id)
        .then(({data}) => {
            console.log(data)
            this.setState({
                showEditRapport: true,
                currentRapport: data.rapport
            })
        })
    }
    showDeleteRapportModal(rapport){
        this.setState({
            showDeleteRapport: true,
            currentRapport: rapport,
        })
    }
    handleCloseEditRapportModal(){
        this.setState({
            showAddRapport: false,
            showEditRapport: false, 
            showDeleteRapport: false,
        })
    }
    updateData(){
        this.setState({
            showLoading: true
        })
        axios.get("/api/rapports?username=" + localStorage.getItem("username") + "&secret=" + localStorage.getItem("secret"))
        .then(({data}) => {
            this.setState({
                showLoading: false,
                rapports: data,
            })
        })
        .catch(() => {
            setTimeout(() => {
                this.updateData()
            }, 10000)
        })
    }
    resize() {
        if(this.container)
            this.setState({
                widthPx : (this.container.offsetWidth - 300) / 2,
                heightPx: window.innerHeight - 150,
            })
    }
    getColor(rapport){
        let code = (
            (!rapport.type_rapport_id) ? 'aaa' :
            //(rapport.dtarrived && !rapport.nb_alarm) ? '9c27b0' : 
            (rapport.soft_delete) ? 'f44336' : '444'
        )
        return '#' + code
    }
    componentDidMount(){
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
        this.updateData()
    }
    render(){
        const {showLoading, rapports, widthPx, heightPx, showAddRapport, showEditRapport, showDeleteRapport, currentRapport} =  this.state
        return <div ref={el => (this.container = el)}>
            {
                showDeleteRapport &&
                <DeleteRapportModal
                    rapport = {currentRapport}
                    site = {currentRapport.site}
                    setSelectedRapport={() => {this.setState({currentRapport: null})}} 
                    updateData={this.updateData} 
                    closeModal={this.handleCloseEditRapportModal} />
            }
            {
                showEditRapport &&
                <EditRapportModal 
                    rapport={currentRapport} 
                    updateData={this.updateData} 
                    closeModal={this.handleCloseEditRapportModal}/>
            }
            {
                showAddRapport &&
                <EditRapportModal 
                    updateData={this.updateData} 
                    closeModal={this.handleCloseEditRapportModal}/>
            }
            <div id="rapportHeaderTable">
                <div className="row">
                    <div className="cell">
                        <h3>Rapports</h3>
                    </div>
                    <div className="cell right">
                        {
                        <button className="btn-white" onClick={() => {this.toggleAddRapport(true)}}>
                            <img id="addAgentBtn" src="/img/all_agent.svg"/>
                            Nouveau rapport sans déclenchement
                        </button>
                        }
                    </div>
                </div>
            </div>
            {
                showLoading ?
                    <LoadingData/>
                :
                    <table  className="fixed_header default layout-fixed">
                        <thead>
                            <tr>
                            <th className="cellRapportCreer">Créé à</th>
                                <th style={{minWidth: widthPx, maxWidth: widthPx, width: widthPx}}>Site</th>
                                <th style={{minWidth: (widthPx-120) + 'px', maxWidth: (widthPx-120) + 'px', width: (widthPx-120) + 'px'}}>Type</th>
                                <th className="cellRapportOperateur">Opérateur</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody style={{height:heightPx}}>
                            {
                                rapports.map((r) => {
                                    return <tr key={r.id} className={!r.type_rapport_id ? "red" : ((r.dtarrived && r.nb_alarm == 0) ? "purple" : "")}>
                                        <td className="cellRapportCreer">{moment(r.created_at).format("HH:mm:ss")}</td>
                                        <td style={{minWidth: widthPx, maxWidth: widthPx, width: widthPx}}>{r.site}</td>
                                        <td style={{minWidth: (widthPx-120) + 'px', maxWidth: (widthPx-120) + 'px', width: (widthPx-120) + 'px'}}>{r.type}</td>
                                        <td className="cellRapportOperateur">{r.operateur.toUpperCase()}</td>
                                        <td>
                                            {
                                                r.operateur != localStorage.getItem("username") ?
                                                    <img onClick={() => {this.showEditRapportModal(r)}}
                                                        className="img-btn" 
                                                        title="Détail" 
                                                        src="/img/detail.svg"/>
                                                : (
                                                    <div>
                                                        {
                                                            r.soft_delete == 1 &&
                                                            <img  onClick={() => {this.showDeleteRapportModal(r)}}
                                                                className="img-btn" 
                                                                title="Restaurer" 
                                                                src="/img/cancel_delete.svg"/>
                                                        }
                                                        {
                                                            !r.soft_delete &&
                                                            <img onClick={() => {this.showEditRapportModal(r)}}
                                                                className="img-btn" 
                                                                title="Modifier" 
                                                                src="/img/edit.svg"/>
                                                        }
                                                        {
                                                            !r.soft_delete &&
                                                            <img onClick={() => {this.showDeleteRapportModal(r)}}
                                                                className="img-btn img-btn-margin" 
                                                                title="Supprimer" 
                                                                src="/img/delete.svg"/>
                                                        }
                                                    </div>
                                                )
                                            }
                                        </td>
                                    </tr>
                                })
                            }
                        </tbody>
                    </table>
            }
        </div>
    }
}