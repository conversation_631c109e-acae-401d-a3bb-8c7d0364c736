.react-datepicker-wrapper, .react-datepicker__input-container { 
    display: block; 
}
.datepicker-full-width .react-datepicker-wrapper .react-datepicker__input-container input { 
    width: 100% 
}
.datepicker-full-width{  
    display: block;
    padding: 10px;
    border: solid .5px rgba(0, 0, 0, .1);
    width: 100%;
}
.report-input-container input, .report-input-container select{
    display: inline-block;
    width: 100%;
    padding: 10px;
    border: solid .5px rgba(0, 0, 0, .2);
}
.export-report-btn{
    padding: 10px;
    background-color: #366666;
    color: white;
    border: none;
}
.export-report-btn:disabled{
    opacity: .7;
}
.cellCheckbox{
    width: 50px;
    min-width: 50px;
    max-width: 50px;
}
.cellOperateur{
    text-align: center;
    width: 170px;
    min-width: 170px;
    max-width: 170px;
}