import React, { Component } from 'react'
import axios from 'axios'

import './select.css'

export default class SelectGroupSite extends Component {
    constructor(props){
        super(props)
        this.state = {
            groups: null,
            showItem: false
        }
        this.toggleSelect = this.toggleSelect.bind(this)
        this.handleClickItem = this.handleClickItem.bind(this)
    }
    handleClickItem(value){
        this.props.clickItem(value)
    }
    componentDidMount(){
        this.props.clickItem(null)
        if(this.props.title == 'POINTAGE')
            axios.get('/api/group_pointage_sites')
            .then(({data}) => {
                this.setState({
                    groups: data,
                })
            })
        else
            axios.get('/api/group_vigilances_sites?username=' + localStorage.getItem('username') + '&secret=' + localStorage.getItem('secret'))
            .then(({data}) => {
                this.setState({
                    groups: data
                })
                if(this.props.user && this.props.user.role == 'client'){
                    console.log(data[0])
                    this.props.clickItem(data[0])
                }
            })
    }
    toggleSelect(event){
        event.stopPropagation()
        this.props.toggleSelect()
    }
    render(){
        const {groups} = this.state
        const {showItem, currentItem, user} = this.props
        return (
            <div id="selectBox">
                <div onClick={this.toggleSelect} id="itemSelected">
                    <span className="item-selected">{currentItem? currentItem.nom: this.props.title}</span>
                </div>
                {
                    (user.role != 'client' && showItem)
                    &&
                    <div id="itemNotSelected">
                        <span onClick={() => this.handleClickItem(null)}>Tous</span>
                        {
                            groups &&
                            groups.map((item) => (
                                <span onClick={() => this.handleClickItem(item)} key={item.id}>{item.nom}</span>
                            ))
                        }
                    </div>
                }
            </div>
        )
    }
}