#selectBox{
    height: 62px;
    background-color: white;
}
#itemSelected{
    text-align: center;
    padding: 5px;
    border: solid .5px rgba(0, 0, 0, .1);
}
#itemNotSelected{
    border: solid .5px rgba(0, 0, 0, .1);
    box-shadow: 2px 2px 2px rgba(0, 0, 0, .1);
}
#itemSelected, #itemNotSelected span{
    padding: 10px;
    background-color: white;
}
#itemNotSelected span{
    cursor: pointer;
}
#itemNotSelected span:hover{
    background-color: whitesmoke;
}
#itemNotSelected{
    position: relative;
}
#itemNotSelected span{
    display: inline-block;
    width: 100%;
}
#cellDropDown{
    display: table-cell;
    width: 30px;
}
.item-selected{
    display: inline-block;
    padding: 10px;
    background-color: whitesmoke;
}
.item-selected-primary{
    display: inline-block;
    padding: 10px;
    background-color: #366666;
    color: whitesmoke;
}
.item-selected img{
    cursor: pointer;
}