import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'

export default class Indice extends Component {
    constructor(props){
        super(props)
        this.state = {
            userIndices: []
        }
    }
    componentDidMount(){
        const {beginDate, endDate, users} = this.props
        let indiceUsers = users
        for(let i=0; i<indiceUsers.length; i++){
            indiceUsers[i].indices = []
        }
        let data = {
            begin: moment(beginDate).format('YYYY-MM-DD 00:00:00'),
            end: moment(endDate).format('YYYY-MM-DD 23:00:00')
        }
        axios.post('/api/reports/indice_tache', data)
        .then(({data}) => {
            if(data){
                data.forEach(d => {
                    for(let i=0; i<indiceUsers.length; i++){
                        if(indiceUsers[i].id == d.user_id ){
                            if(d.indice <= 3)
                                indiceUsers[i].indices.push(d) 
                            else{
                                for(let j=0; j<10; j++)
                                    indiceUsers[i].indices.push(d) 
                            }
                        }
                    }
                });
                for(let i=0; i<indiceUsers.length; i++){
                    if(indiceUsers[i].indices.length > 0){
                        let sum = 0
                        indiceUsers[i].indices.map(idc => {
                            console.log(idc.indice)
                            sum = sum + idc.indice
                        })
                        indiceUsers[i].indice = Number(sum / indiceUsers[i].indices.length).toFixed(2)
                        //indiceUsers[i].indice = Number(indiceUsers[i].indices.reduce((a, b) => a.indice + b.indice, 0) / indiceUsers[i].indices.length).toFixed(2)
                    }
                }
                this.setState({
                    userIndices: indiceUsers
                })
            }
        })
    }
    render(){
        const {userIndices} = this.state
        return (
            <div className="table">
                <div className="row-table">
                    <table className="fixed_header default layout-fixed">
                        <thead>
                            <tr>
                                <th>Opérateur</th>
                                <th className="cellOperateur">Indice</th>
                            </tr>
                        </thead>
                        <tbody>
                            {
                                userIndices.map((u) => {
                                    return <tr key={u.id}>
                                        <td>{u.email}</td>
                                        <td className="cellOperateur">
                                            {u.indice}
                                        </td>
                                    </tr>
                                })
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        )
    }
}