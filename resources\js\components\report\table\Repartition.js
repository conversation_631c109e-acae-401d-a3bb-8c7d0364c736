import React, { Component } from 'react'
import axios from 'axios'

export default class Repartition extends Component {
    constructor(props){
        super(props)
        this.handleLockClick = this.handleLockClick.bind(this)
    }
    handleAssignmentClick(groupId){
        let data = new FormData()
        data.append('username', localStorage.getItem('username'))
        data.append('secret', localStorage.getItem('secret'))
        data.append('group_id', groupId)
        axios.post('/api/repartitions/assign_task', data)
        .then(({data}) => {
            if(data)
                this.props.updateData(true)
        })
    }
    handleCancelClick(groupId){
        let data = new FormData()
        data.append('username', localStorage.getItem('username'))
        data.append('secret', localStorage.getItem('secret'))
        data.append('group_id', groupId)
        axios.post('/api/repartitions/cancel_task', data)
        .then(({data}) => {
            if(data)
                this.props.updateData(true)
        })
    }
    handleLockClick(groupId){
        let data = new FormData()
        data.append('username', localStorage.getItem('username'))
        data.append('secret', localStorage.getItem('secret'))
        axios.post('/api/repartitions/lock_task', data)
        .then(({data}) => {
            if(data)
                this.props.updateData(true)
        })
    }
    render(){
        const {locked, selectedMenu, groupSites} = this.props
        return (
            <div className="table">
                <div className="row-header">
                    <h3 className="h3-table">
                        <span className="cell fix-cell-site"> {selectedMenu.label} </span>
                        <div className="cell right">
                            {
                                !locked && 
                                <button 
                                    className="btn-primary" 
                                    onClick={this.handleLockClick} 
                                    disabled={(groupSites.map((g) => (g.username ? true : false)).includes(false))}
                                >
                                    Vérouiller
                                </button>
                            }
                        </div>
                    </h3>
                </div>
                <div className="row-table">
                    <table className="fixed_header default layout-fixed">
                        <thead>
                            <tr>
                                <th>Vigilance</th>
                                <th className="cellOperateur">Operateur</th>
                            </tr>
                        </thead>
                        <tbody>
                            {
                                groupSites.map((g) => {
                                    return <tr key={g.id}>
                                        <td>{g.nom}</td>
                                        <td className="cellOperateur">
                                            {
                                                (!locked && g.username == localStorage.getItem('username')) ? 
                                                    <button className="btn-default" onClick={() => {this.handleCancelClick(g.id)}}>Annuler</button> 
                                                : g.username ? 
                                                    <button className="btn-white">{g.username.toUpperCase()}</button> 
                                                :
                                                <button className="btn-primary" onClick={() => {this.handleAssignmentClick(g.id)}}>S'assigner</button>
                                            }
                                        </td>
                                    </tr>
                                })
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        )
    }
}