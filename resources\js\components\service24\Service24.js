import moment from 'moment'
import React, { Component } from 'react'
import <PERSON><PERSON><PERSON><PERSON> from 'react-infinite-scroll-component'
import '../reclamation/reclamation.css'
import ServiceDetail from './ServiceDetail'
export default class Service24 extends Component {
    constructor(props) {
        super(props)
        this.state = {
            services: [],
            allDataLoaded: false,
            heightWindow: 0,
            widthWindow: 0,
            currentService: null,
            activeTab: '',
            inputSearch: ''
        }
        this.fetchMoreData = this.fetchMoreData.bind(this)
        this.handleEnterPress = this.handleEnterPress.bind(this)
        this.handleChangeSearchService24 = this.handleChangeSearchService24.bind(this)
        this.toggleLoading = this.toggleLoading.bind(this)
    }

    toggleLoading(load) {
        this.props.toggleLoading(load)
    }

    updateData(loading){
        const {services} = this.state
        const params = new URLSearchParams()
        if (loading)
            this.toggleLoading(true)
        params.append("offset", (loading ? 0 : services.length))
        if(this.state.inputSearch.trim()){
            params.append("value", this.state.inputSearch)
        }
        axios.get('/api/service24', {params: params})
            .then(response => {
                const newServices = response.data.services
                if(loading){
                    this.setState({
                        services: newServices
                    }, () => {
                        this.toggleLoading(false)
                    })
                    return
                }
                else{
                    this.setState({
                        services: services.concat(newServices)
                    }, () => {
                        this.toggleLoading(false)
                    })
                }
                if (loading) {
                    this.container.scroll(0, 0)
                    this.toggleLoading(false)
                }
                if(newServices.length < 50){
                    this.setState({
                        allDataLoaded: true
                    })
                }
            })
            .catch(error => {
                console.log(error)
            })
    }

    handleClickReclamation(id) {
        this.updateService(id)
    }

    updateService(id) {
        this.toggleLoading(true)
        axios.get('/api/service24/show/' + id + '?username=' + localStorage.getItem("username") + '&secret=' + localStorage.getItem("secret"))
            .then(({ data }) => {
                if (data) {
                    this.setState({
                        currentService: data.service[0],
                    }, () => {
                        this.toggleLoading(false)
                    })
                }
            })
            .catch(() => {
                this.toggleLoading(false)
            })
    }

    componentDidMount() {
        this.updateData(true);
        window.addEventListener("resize", this.resize.bind(this))
        document.title = "Service24 - TLS"
        this.resize()
    }

    resize() {
        this.setState({
            heightWindow: window.innerHeight,
            widthWindow: window.innerWidth
        });
    }

    fetchMoreData() {
        setTimeout(() => {
            this.updateData()
        }, 300);
    }

    handleEnterPress(event) {
        if (event.key === 'Enter') {
            this.updateData(true)
        }
    }

    handleChangeSearchService24(event) {
        this.setState({
            inputSearch: event.target.value
        })
    }

    render() {
        const { services, allDataLoaded, currentService, heightWindow, widthWindow, inputSearch } = this.state
        return (
            <div className='table'>
                <div id='tableContainer'>
                    <div className='table'>
                        <div className="row-header">
                            <h3 className="h3-table">
                                <span className="cell">Service 24</span>
                                <span className="cell center">
                                    <div id="searchSite">
                                        <div>
                                            <input onKeyDown={this.handleEnterPress} onChange={this.handleChangeSearchService24} value={inputSearch} type="text" />
                                            <img onClick={() => { this.updateData(true) }} src="/img/search.svg" />
                                        </div>
                                    </div>
                                </span>
                            </h3>
                        </div>
                        <div className="row-table">
                            <InfiniteScroll
                                scrollableTarget="scrollableDiv"
                                dataLength={services ? services.length : 0}
                                next={this.fetchMoreData}
                                hasMore={!allDataLoaded}
                            >
                                <table className="fixed_header visible-scroll layout-fixed">
                                <thead>
                                    <tr>
                                        <th className="cellNumAg">Num.</th>
                                        <th className="cellAgent">Nom</th>
                                        {/* <th className="cellSiteAg">Début</th>
                                        <th className="">Durée</th> */}
                                    </tr>
                                </thead>
                                <tbody id="scrollableDiv" ref={el => (this.container = el)} style={{ 'height': (heightWindow - 160) + "px" }}>
                                    {
                                        services.map((row) => {
                                            return (
                                                <tr 
                                                    key={row.id}
                                                    onDoubleClick={() => { this.handleClickReclamation(row.id) }}
                                                    className={ (!row.pointage_id ? "red " : "") + ((currentService != null && currentService.id == row.id) ? "selected-row" : "")}
                                                    title={"Motif: " + row.motif}
                                                >
                                                    <td className="cellNumAg">
                                                        {
                                                            row.societe_id == 1 ? 'DGM-' + row.numero_employe :
                                                            row.societe_id == 2 ? 'SOIT-' + row.num_emp_soit :
                                                            row.societe_id == 3 ? 'ST-' + row.numero_stagiaire :
                                                            row.societe_id == 4 ? 'SM' :
                                                            row.numero_employe ? row.numero_employe :
                                                            row.numero_stagiaire ? row.numero_stagiaire :
                                                            <span className="purple">Ndf</span>
                                                        }
                                                    </td>
                                                    <td className="cellAgent" >{row.agent_not_registered ?? row.nom}</td>
                                                    {/* <td className="cellSiteAg" >{moment(row.begin_pointage).format("DD/MM/YY") + (moment(row.begin_pointage).format("HH:mm:ss") == "18:00:00" ? " NUIT" : " JOUR")}</td>
                                                    <td className="" >{moment(row.end_pointage).diff(moment(row.begin_pointage), 'hours') / 24}</td> */}
                                                </tr>
                                            )
                                        })
                                    }
                                </tbody>
                                </table>
                            </InfiniteScroll>
                        </div>
                    </div>
                </div>
                <div className={currentService ? "box-shadow-left" : ""} style={{ width: (widthWindow / 2.5) + 'px', maxWidth: (widthWindow / 2.5) + 'px', minWidth: (widthWindow / 2.5) + 'px' }} id="overviewContainer">
                    {
                        currentService ?
                            <ServiceDetail service={currentService}/>
                            :
                            <div className="img-bg-container">
                                <img className="img-bg-overview" src="/img/tls_background.svg" />
                            </div>
                    }
                </div>
            </div>
        )
    }
}
