import moment from 'moment'
import React, { Component } from 'react'

export default class ServiceDetail extends Component {
    constructor(props) {
        super(props)
        this.state = {
        }
    }
    render() {
        const { service } = this.props
        return (
            <div>
                <div className="overview-container">
                    <div className="head-title-overview" title={service.nom}>
                        <div style={{height:"40px", lineHeight:"40px" }}>
                            <div className="title-overview">
                                <span style={{opacity: .9}}>
                                    {
                                        service.societe_id == 1 ? 'DGM-' + service.numero_employe :
                                        service.societe_id == 2 ? 'SOIT-' + service.num_emp_soit :
                                        service.societe_id == 3 ? 'ST-' + service.numero_stagiaire :
                                        service.societe_id == 4 ? 'SM' :
                                        service.numero_employe ? service.numero_employe :
                                        service.numero_stagiaire ? service.numero_stagiaire :
                                        <span className="purple">Non définie</span>
                                    }
                                </span>
                            </div>
                        </div>
                    </div>
                    <span className="overview-break-overflow" title={service.nom}>
                        <b>Nom : </b>{service.nom }
                    </span>
                    <span>
                        <b>Motif : </b> {service.motif}
                    </span><br/>
                    {/* <div>
                        <span>
                            <b>Date de début : </b> 
                            { moment(service.begin_pointage).format('DD MMM YYYY') + (moment(service.begin_pointage).format('HH:mm:ss') == "18:00:00" ? " NUIT" : " JOUR")}
                        </span>
                    </div> */}

                    <div className="table">
                        {/* <span className="cell">
                            <b>Date de fin : </b> 
                            { moment(service.end_pointage).format('DD MMM YYYY') + (moment(service.end_pointage).format('HH:mm:ss') == "18:00:00" ? " NUIT" : " JOUR")}
                        </span> */}
                        {
                            (service.pointage_id) ?
                            <span className="cell right" style={{ height: "30px"}}>
                                <span className="badge bg-primary">Déjà pointé</span>
                            </span>
                            :<span className="cell right" style={{ height: "30px"}}>
                                <span className="badge bg-pink">En attente de pointage</span>
                            </span>
                        }
                    </div>
                </div>
            </div>
        )
  }
}
