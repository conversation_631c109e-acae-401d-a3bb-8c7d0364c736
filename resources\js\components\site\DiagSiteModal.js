import React, { Component } from 'react'
import axios from 'axios'

export default class DiagSiteModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            commentaire: '',
            diagnostique_id: '',
            joignable: '',
            disableSave: false,
            error: ''
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.handleDiagnostiqueChange = this.handleDiagnostiqueChange.bind(this)
        this.handleCommentaireChange = this.handleCommentaireChange.bind(this)
        this.handleJoignableChange = this.handleJoignableChange.bind(this)
    }
    handleDiagnostiqueChange(e){
        this.setState({
            diagnostique_id: e.target.value
        })
    }
    handleCommentaireChange(e){
        this.setState({
            commentaire: e.target.value
        })
    }
    handleJoignableChange(e){
        this.setState({
            joignable: e.target.value
        })
    }
    handleSave(){
        const {diagnostique_id, joignable, commentaire} = this.state
        this.setState({
            disableSave: true,
            error: ''
        })
        const data = new FormData()
        data.append("diagnostique_id", diagnostique_id)
        data.append("joignable", joignable)
        data.append("commentaire", commentaire)
        data.append("username", localStorage.getItem("username"))
        data.append("secret", localStorage.getItem("secret"))

        axios.post(this.props.action, data)
        .then(({data}) => {
            console.log(data)
            if(data.error){
                console.log(data.error)
                const firstKey = Object.keys(data.error)[0]
                const firstValue = data.error[firstKey][0]
                this.setState({
                    error: {
                        key: firstKey,
                        value: firstValue
                    }
                }, () => {
                    console.log(this.state.error)
                })
            }
            else if(data){
                this.props.closeModal()
                this.props.updateSite(data, true)
            }
        })
        .finally(()=>{
            this.setState({
                disableSave: false
            })
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    componentDidMount(){
        const {commentaire} = this.props
        this.setState({
            commentaire: commentaire ? commentaire: ''
        })
    }
    render(){
        const {commentaire, diagnostique_id, joignable, 
            error, disableSave} = this.state
        return (
            <div>
                <div style={{zIndex: 200}} className="fixed-front">
                    <div className="table">
                        <div className="modal-container">
                            <div className="modal-no-padding lg">
                                <div className="modal-content">
                                    <h3 className="header-modal">Diagnositique site</h3>
                                    <div style={{maxHeight: (this.props.height - 220) + "px", overflowY: "auto", padding:"10px 20px"}}>
                                        <div className="table">
                                            <div className="cell-50">
                                                <div className="input-container">
                                                    <label>
                                                        Diagnositique *
                                                        <select onChange={this.handleDiagnostiqueChange} value={diagnostique_id}>
                                                            <option></option>
                                                            <option value="1">Système fonctionnel</option>
                                                            <option value="2">Bouton de ronde en panne</option>
                                                            <option value="3">Manque de transmission</option>
                                                        </select>
                                                    </label>
                                                </div>
                                            </div>
                                            <div className="cell-50">
                                                <div className="input-container">
                                                    <label>Tentative d'appel *</label>
                                                    <select onChange={this.handleJoignableChange} value={joignable}>
                                                        <option></option>
                                                        <option value="1">Joignable</option>
                                                        <option value="0">Injoignable</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="table">
                                            <div className="cell-50">
                                                <div className="input-container">
                                                    <label>Commentaire</label>
                                                    <input onChange={this.handleCommentaireChange} value={commentaire}/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="table modal-footer footer-modal-padding">
                                    <div className="cell left">
                                        <span className="pink">{error && error.value}</span>
                                    </div>
                                    <div className="cell right">
                                        <button disabled={disableSave} onClick={this.handleSave} className="btn-primary fix-width">Valider</button>
                                        <button onClick={this.handleCancel} className="btn-default fix-width">Annuler</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }
}