import React, { Component } from 'react'

export default class <PERSON><PERSON><PERSON> extends Component {
    constructor(props){
        super(props)
    }
    render(){
        const {currentHoraire} = this.props
        return <div style={{paddingTop: 20}}>
            <table className="fixed_header default layout-fixed">
                <thead>
                    <tr>
                        <th className="cellBeginWeek"></th>
                        <th className="cellDayWeek">Lun</th>
                        <th className="cellDayWeek">Mar</th>
                        <th className="cellDayWeek">Mer</th>
                        <th className="cellDayWeek">Jeu</th>
                        <th className="cellDayWeek">Ven</th>
                        <th className="cellDayWeek">Sam</th>
                        <th className="cellDayWeek">Dim</th>
                        <th>Férié</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <th className="cellBeginWeek">J</th>
                        <td className={"cellDayWeek " + (currentHoraire.day_1 == 1 ? "cellChecked" : "")}>
                            {
                                currentHoraire.day_1 == 1 &&
                                <span className="checkmark-label"></span>
                            }
                        </td>
                        <td className={"cellDayWeek " + (currentHoraire.day_2 == 1 ? "cellChecked" : "")}>
                            {
                                currentHoraire.day_2 == 1 &&
                                <span className="checkmark-label"></span>
                            }
                        </td>
                        <td className={"cellDayWeek " + (currentHoraire.day_3 == 1 ? "cellChecked" : "")}>
                            {
                                currentHoraire.day_3 == 1 &&
                                <span className="checkmark-label"></span>
                            }
                        </td>
                        <td className={"cellDayWeek " + (currentHoraire.day_4 == 1 ? "cellChecked" : "")}>
                            {
                                currentHoraire.day_4 == 1 &&
                                <span className="checkmark-label"></span>
                            }
                        </td>
                        <td className={"cellDayWeek " + (currentHoraire.day_5 == 1 ? "cellChecked" : "")}>
                            {
                                currentHoraire.day_5 == 1 &&
                                <span className="checkmark-label"></span>
                            }
                        </td>
                        <td className={"cellDayWeek " + (currentHoraire.day_6 == 1 ? "cellChecked" : "")}>
                            {
                                currentHoraire.day_6 == 1 &&
                                <span className="checkmark-label"></span>
                            }
                        </td>
                        <td className={"cellDayWeek " + (currentHoraire.day_0 == 1 ? "cellChecked" : "")}>
                            {
                                currentHoraire.day_0 == 1 &&
                                <span className="checkmark-label"></span>
                            }
                        </td>
                        <td className={"cellDayWeek " + (currentHoraire.day_ferie == 1 ? "cellChecked" : "")}>
                            {
                                currentHoraire.day_ferie == 1 &&
                                <span className="checkmark-label"></span>
                            }
                        </td>
                    </tr>
                    <tr>
                        <th className="cellBeginWeek">N</th>
                        <td className={"cellDayWeek " + (currentHoraire.night_1 == 1 ? "cellChecked" : "")}>
                            {
                                currentHoraire.night_1 == 1 &&
                                <span className="checkmark-label"></span>
                            }
                        </td>
                        <td className={"cellDayWeek " + (currentHoraire.night_2 == 1 ? "cellChecked" : "")}>
                            {
                                currentHoraire.night_2 == 1 &&
                                <span className="checkmark-label"></span>
                            }
                        </td>
                        <td className={"cellDayWeek " + (currentHoraire.night_3 == 1 ? "cellChecked" : "")}>
                            {
                                currentHoraire.night_3 == 1 &&
                                <span className="checkmark-label"></span>
                            }
                        </td>
                        <td className={"cellDayWeek " + (currentHoraire.night_4 == 1 ? "cellChecked" : "")}>
                            {
                                currentHoraire.night_4 == 1 &&
                                <span className="checkmark-label"></span>
                            }
                        </td>
                        <td className={"cellDayWeek " + (currentHoraire.night_5 == 1 ? "cellChecked" : "")}>
                            {
                                currentHoraire.night_5 == 1 &&
                                <span className="checkmark-label"></span>
                            }
                        </td>
                        <td className={"cellDayWeek " + (currentHoraire.night_6 == 1 ? "cellChecked" : "")}>
                            {
                                currentHoraire.night_6 == 1 &&
                                <span className="checkmark-label"></span>
                            }
                        </td>
                        <td className={"cellDayWeek " + (currentHoraire.night_0 == 1 ? "cellChecked" : "")}>
                            {
                                currentHoraire.night_0 == 1 &&
                                <span className="checkmark-label"></span>
                            }
                        </td>
                        <td className={"cellDayWeek " + (currentHoraire.night_ferie == 1 ? "cellChecked" : "")}>
                            {
                                currentHoraire.night_ferie == 1 &&
                                <span className="checkmark-label"></span>
                            }
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    }
}