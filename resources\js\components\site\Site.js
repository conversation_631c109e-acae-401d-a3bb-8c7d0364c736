import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'
import Historique from './historique/Historique'
import Signale from './signale/Signale'
import Zone from './zone/Zone'
import EditSiteModal from './EditSiteModal'

import './site.css'
import Habilite from './habilite/Habilite'
import DeleteSiteModal from './DeleteSiteModal'
import Pointeuse from './pointeuse/Pointeuse'
// import IconButton from '../button/IconButton'
import Notification from './notification/Notification'
import DiagSiteModal from './DiagSiteModal'
import Operation from '../agent/operation/Operation'
import InfiniteScroll from 'react-infinite-scroll-component'
import LoadingData from '../loading/LoadingData'

export default class Site extends Component {
    constructor(props) {
        super(props)
        this.state = {
            timeoutId: 0,
            searchSite: '',
            inputSearch: '',
            searchTimeoutId: 0,
            currentSite: null,
            sites: [],
            heightWindow: 0,
            widthWindow: 0,
            activeTab: 'habilite',
            showEditSiteModal: false,
            showDiagSiteModal: false,
            showDeleteSiteModal: false,
            showAddSiteModal: false,
            showEditSiteMenu: false,
            notifications: [],
            currentDate: '',
            allDataLoaded: false,
        }
        this.updateData = this.updateData.bind(this)
        this.fetchMoreData = this.fetchMoreData.bind(this)
        this.updateSite = this.updateSite.bind(this)
        this.handleClickSite = this.handleClickSite.bind(this)
        this.handleChangeTab = this.handleChangeTab.bind(this)
        this.handleClickDiagSite = this.handleClickDiagSite.bind(this)
        this.handleClickEditSite = this.handleClickEditSite.bind(this)
        this.handleClickDeleteSite = this.handleClickDeleteSite.bind(this)
        this.handleClickAddSite = this.handleClickAddSite.bind(this)
        this.closeSiteModal = this.closeSiteModal.bind(this)
        this.handleChangeSearchSite = this.handleChangeSearchSite.bind(this)
        this.toggleLoading = this.toggleLoading.bind(this)
        this.toggleEditSiteMenu = this.toggleEditSiteMenu.bind(this)
        this.updateNotification = this.updateNotification.bind(this)
        this.setTimeoutUpdateNotification = this.setTimeoutUpdateNotification.bind(this)
        this.handleEnterPress = this.handleEnterPress.bind(this)
        this.searchTimeout = null
    }
    handleEnterPress(event) {
        if (event.key === 'Enter') {
            this.updateData(true)
        }
    }
    setTimeoutUpdateNotification() {
        if (this.state.timeoutId)
            clearTimeout(this.state.timeoutId)
        let timeoutId = setTimeout(this.updateNotification, 9000)
        this.setState({
            timeoutId: timeoutId
        })
    }
    updateNotification() {
        axios.get('/api/sites/notification')
            .then(({ data }) => {
                console.log('updateNotification')
                this.setState({
                    notifications: data.sites,
                    currentDate: data.now,
                }, () => {
                    this.setTimeoutUpdateNotification()
                })
            })
            .catch(() => {
                this.setTimeoutUpdateNotification()
            })
    }
    toggleEditSiteMenu(value) {
        this.setState({
            showEditSiteMenu: value
        })
    }
    toggleLoading(load) {
        this.props.toggleLoading(load)
    }
    handleChangeSearchSite(event) {
        this.setState({
            inputSearch: event.target.value
        })
    }
    closeSiteModal() {
        this.setState({
            showAddSiteModal: false,
            showDiagSiteModal: false,
            showEditSiteModal: false,
            showDeleteSiteModal: false
        })
    }
    handleClickDiagSite() {
        this.setState({
            showDiagSiteModal: true,
            showEditSiteMenu: false
        })
    }
    handleClickEditSite() {
        this.setState({
            showEditSiteModal: true,
            showEditSiteMenu: false
        })
    }
    handleClickDeleteSite() {
        this.setState({
            showDeleteSiteModal: true,
            showEditSiteMenu: false
        })
    }
    handleClickAddSite() {
        this.setState({
            showAddSiteModal: true
        })
    }
    handleChangeTab(event) {
        if (event.target.id != this.state.activeTab) {
            this.setState({
                activeTab: event.target.id
            })
        }
    }
    updateData(loading, clearSearch) {
        console.log("updateData")
        const { archive } = this.props
        const { sites, inputSearch } = this.state
        this.setState({
            currentSite: null
        })
        if (loading) {
            this.toggleLoading(true)
        }
        if (clearSearch)
            this.setState({
                inputSearch: ''
            })
        axios.get('/api/sites' + (archive ? '/archive' : '') + '?offset=' + (loading ? 0 : sites.length)
            + ((!clearSearch && inputSearch) ? '&search=' + (inputSearch.replace('+', '%2B')) : ''))
            .then(({ data }) => {
                if (data) {
                    if (loading) {
                        this.container.scroll(0, 0)
                        this.setState({
                            sites: data
                        }, () => {
                            this.toggleLoading(false)
                        })
                    }
                    else {
                        const list = sites ? sites.slice().concat(data) : data
                        this.setState({
                            sites: list
                        })
                    }
                    this.setState({
                        allDataLoaded: (data.length < 50)
                    })
                }
            })
            .catch(() => {
                setTimeout(() => {
                    this.updateData(loading, clearSearch)
                }, 10000)
            })
    }

    fetchMoreData() {
        setTimeout(() => {
            this.updateData()
        }, 300);
    }

    setStateSite(data, isUpdate) {
        console.log(data)
        let sites = this.state.sites
        const site = {
            idsite: data.idsite,
            nom: data.nom,
            adresse: data.adresse,
            prom: data.prom,
            arm: data.arm,
            manque: data.manque,
            transmissionType: data.without_system ? '' : (data.gprs ? 'gprs' : data.sms ? 'sms' : 'vocal'),
            numeropuces: data.numeropuces,
            soft_delete: data.soft_delete
        }

        for (let i = 0; i < sites.length; i++) {
            if (sites[i].idsite == data.idsite) {
                if (isUpdate)
                    sites.splice(i, 1)
                else
                    sites[i] = site
                break;
            }
        }
        if (isUpdate) {
            sites.unshift(site)
        }

        this.setState({
            currentSite: {
                id: data.idsite,
                group_id: data.group_id,
                group_pointage_id: data.group_pointage_id,
                centrale_id: data.idcentrale,
                nom: data.nom,
                prom: data.prom,
                puce: data.numeropuces,
                adresse: data.adresse,
                pointeuse: (data.pointeuse == 1),
                defaultPointeuse: data.pointeuse_o,
                vigilance: (data.vigilance == 1),
                type_vigilance: data.day ? 'day' : data.night ? 'night' : '',
                type_pointage: data.pointage_day ? 'day' : data.pointage_night ? 'night' : '',
                notify_arm: (data.notify_arm == 1),
                lat: data.GPSLatitude,
                lng: data.GPSLongitude,
                centrale: data.centrale && data.centrale.nom,
                client: data.client,
                transmission: data.gprs ? "GPRS" : data.sms ? "SMS" : "Vocal",
                last_transmission: data.date_last_signal,
                habilites: data.habilites,
                transmitter: data.transmitter,
                correct_transmitter: data.correct_transmitter,
                port: data.port,
                without_system: data.without_system == 1,
                pointage: data.pointage == 1,
                centrale_drx: data.centrale_drx == 1,
                pointage_biometrique: data.pointage_biometrique == 1,
                nb_agent_day: data.nb_agent_day,
                nb_agent_night: data.nb_agent_night,
                phone_agent: data.phone_agent,
                checkphone: data.checkphone,
                do_checkphone: data.do_checkphone == 1,
                arm: data.arm,
                manque: data.manque,
                soft_delete: data.soft_delete,
                professionnel: data ? data.professionnel : '',
                group_diag_id: data ? data.group_diag_id : '',
                horaire_vigilance_id: data ? data.horaire_vigilance_id : '',
                horaire_pointage_id: data ? data.horaire_pointage_id : '',
                interval_test: data ? data.interval_test : '',
                commentaire: data ? data.commentaire : '',
                total_hour: data ? data.total_hour : '',
                secteur_id: data ? data.secteur_id : '',
                secteur: data.secteur,
                intervention_id: data ? data.intervention_id : '',
                group_intervention_id: data ? data.group_intervention_id : '',
                intervention: data.intervention,
                transmitter_sms: data.transmitter_sms,
                transmitter_gprs: data.transmitter_gprs,
            },
            showAddSiteModal: false,
            showEditSiteModal: false,
            showDiagSiteModal: false,
            showDeleteSiteModal: false,
            activeTab: 'habilite'
        }, () => {
            this.toggleLoading(false)
        })
    }
    updateSite(id, isAfterSave) {
        this.setState({
            showDeleteSiteModal: false
        })
        this.toggleLoading(true)
        axios.get('/api/sites/show/' + id)
            .then(({ data }) => {
                if (data.idsite) {
                    if (data.habilites)
                        data.habilites.sort((a, b) => (a.idordre - b.idordre))
                    this.setStateSite(data, isAfterSave)
                    this.toggleLoading(false)
                }
            })
            .catch(() => {
                this.toggleLoading(false)
            })
    }
    handleClickSite(id) {
        this.updateSite(id)
    }
    componentDidMount() {
        this.updateData(true)
        this.updateNotification()
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
        document.title = "Site - TLS"
    }
    componentWillUnmount() {
        const { timeoutId } = this.state
        if (timeoutId)
            clearTimeout(timeoutId)
    }
    resize() {
        this.setState({
            heightWindow: window.innerHeight,
            widthWindow: window.innerWidth
        });
    }
    render() {
        const { currentDate, notifications, sites, currentSite, inputSearch, heightWindow, widthWindow, activeTab,
            showEditSiteMenu, showEditSiteModal, showDiagSiteModal, showDeleteSiteModal, showAddSiteModal, allDataLoaded } = this.state
        const { user, archive } = this.props
        return (
            <div className="table" onClick={() => { if (showEditSiteMenu) this.toggleEditSiteMenu(false) }}>
                {
                    (['root', 'room'].includes(user.role) && !archive && notifications.length > 0) &&
                    <Notification data={notifications} clickItem={this.handleClickSite} />
                }
                <div id="tableContainer">
                    <div className="table">
                        <div className="row-header">
                            <h3 className="h3-table">
                                <span className="cell fix-cell-site">
                                    {archive ? 'Archives' : 'Sites'}
                                </span>
                                <span className="cell center">
                                    <div id="searchSite">
                                        <div>
                                            <input onKeyDown={this.handleEnterPress} onChange={this.handleChangeSearchSite} value={inputSearch} type="text" />
                                            <img onClick={() => { this.updateData(true) }} src="/img/search.svg" />
                                        </div>
                                    </div>
                                </span>
                                {
                                    !archive &&
                                    <span id="cellAddContactBtn">
                                        <img height={30} onClick={this.handleClickAddSite} title="Nouveau site" src="/img/add.svg" />
                                    </span>
                                }
                            </h3>
                        </div>
                        <div className="row-table">

                            <table className="fixed_header visible-scroll layout-fixed">
                                <thead>
                                    <tr>
                                        <th className="cellNom">Nom</th>
                                        <th className="cellProm">Prom</th>
                                        <th className="cellAdresse">
                                            Adresse
                                            <img src="/img/refresh_table.svg" onClick={() => { this.updateData(true, true) }} />
                                        </th>
                                    </tr>
                                </thead>
                                <tbody id="scrollableDiv" ref={el => (this.container = el)} style={{ 'height': (heightWindow - 160) + "px" }}>
                                    <InfiniteScroll
                                        scrollableTarget="scrollableDiv"
                                        dataLength={sites ? sites.length : 0}
                                        next={this.fetchMoreData}
                                        hasMore={!allDataLoaded}
                                        loader={<LoadingData />}
                                    >
                                        {
                                            sites.map((row) => {
                                                return (
                                                    <tr
                                                        key={row.idsite}
                                                        onDoubleClick={() => { this.handleClickSite(row.idsite) }}
                                                        className={((!archive && row.manque) ? "red" : (!archive && row.arm == "arme") ? "green" : "")
                                                            + " " + ((currentSite != null && currentSite.id == row.idsite) ? "selected-row" : "")}
                                                    >
                                                        <td className="cellNom" title={row.nom}>{row.nom}</td>
                                                        <td className="cellProm">{row.prom}</td>
                                                        <td className="cellAdresse" title={row.adresse}>{row.adresse}</td>
                                                    </tr>
                                                )
                                            })
                                        }
                                        {
                                            (allDataLoaded && sites.length == 0) &&
                                            <tr>
                                                <td className='center secondary'>Aucun données trouvé</td>
                                            </tr>
                                        }
                                    </InfiniteScroll>
                                </tbody>
                            </table>
                            {/* {!allDataLoaded ? <LoadingData /> : <></>} */}
                        </div>
                    </div>
                </div>
                {
                    showAddSiteModal && <EditSiteModal
                        title="Nouveau site"
                        action={'/api/sites/store'}
                        closeModal={this.closeSiteModal}
                        updateSite={this.updateSite}
                        height={heightWindow}
                    />
                }
                <div className={currentSite ? "box-shadow-left" : ""}
                    style={{ width: (widthWindow / 2.5) + 'px', maxWidth: (widthWindow / 2.5) + 'px', minWidth: (widthWindow / 2.5) + 'px' }}
                    id="overviewContainer">
                    {
                        currentSite ?
                            <div>
                                {showDiagSiteModal && <DiagSiteModal
                                    action={'/api/sites/diag/' + currentSite.id}
                                    closeModal={this.closeSiteModal}
                                    updateSite={this.updateSite}
                                    height={heightWindow}
                                    commentaire={currentSite.commentaire ? currentSite.commentaire : ''}
                                />}
                                {showEditSiteModal && <EditSiteModal
                                    archive={archive}
                                    title="Edition site"
                                    action={'/api/sites/update/' + currentSite.id}
                                    closeModal={this.closeSiteModal}
                                    updateSite={this.updateSite}
                                    height={heightWindow}
                                    site={{
                                        id: currentSite.id,
                                        nom: currentSite.nom,
                                        prom: currentSite.prom,
                                        puce: currentSite.puce,
                                        adresse: currentSite.adresse,
                                        centrale_drx: currentSite.centrale_drx,
                                        pointage_biometrique: currentSite.pointage_biometrique,
                                        pointage: currentSite.pointage,
                                        vigilance: currentSite.vigilance,
                                        pointeuse: currentSite.pointeuse,
                                        defaultPointeuse: currentSite.defaultPointeuse,
                                        group_id: currentSite.group_id,
                                        group_pointage_id: currentSite.group_pointage_id,
                                        centrale_id: currentSite.centrale_id,
                                        notify_arm: currentSite.notify_arm,
                                        client: currentSite.client,
                                        type_transmission: currentSite.transmission,
                                        type_vigilance: currentSite.type_vigilance,
                                        type_pointage: currentSite.type_pointage,
                                        without_system: currentSite.without_system,
                                        has_agent: currentSite.has_agent,
                                        nb_agent_day: currentSite.nb_agent_day,
                                        nb_agent_night: currentSite.nb_agent_night,
                                        phone_agent: currentSite.phone_agent,
                                        checkphone: currentSite.checkphone,
                                        do_checkphone: currentSite.do_checkphone,
                                        professionnel: currentSite ? currentSite.professionnel : '',
                                        group_diag_id: currentSite ? currentSite.group_diag_id : '',
                                        horaire_vigilance_id: currentSite ? currentSite.horaire_vigilance_id : '',
                                        horaire_pointage_id: currentSite ? currentSite.horaire_pointage_id : '',
                                        interval_test: currentSite ? currentSite.interval_test : '',
                                        commentaire: currentSite ? currentSite.commentaire : '',
                                        total_hour: currentSite ? currentSite.total_hour : '',
                                        secteur_id: currentSite ? currentSite.secteur_id : '',
                                        secteur: currentSite.secteur,
                                        intervention: currentSite.intervention,
                                        intervention_id: currentSite.intervention_id ? currentSite.intervention_id : '',
                                        group_intervention_id: currentSite.group_intervention_id ? currentSite.group_intervention_id : '',
                                    }}
                                />}
                                {
                                    showDeleteSiteModal &&
                                    <DeleteSiteModal
                                        action={'/api/sites/delete/' + currentSite.id}
                                        closeModal={this.closeSiteModal}
                                        nom={currentSite.nom}
                                        updateData={this.updateData} />
                                }
                                <div className="overview-container">
                                    <div className="head-title-overview" title={currentSite.nom + ' ' + currentSite.commentaire}>
                                        <div style={{ height: "40px", lineHeight: "40px" }}>
                                            <div
                                                className={"title-overview " + ((!archive && currentSite.manque) ? "red" : (!archive && currentSite.arm) == "arme" ? "green" : "")} >
                                                <span className={(!archive && currentSite.manque) ? "red" : (!archive && currentSite.arm == "arme") ? "green" : ""}>
                                                    {localStorage.getItem("username") == 'aro' && (currentSite.id + ' | ')}
                                                    {currentSite.nom}
                                                </span>
                                            </div>
                                            <div className="overview-edit-icon">
                                                <img onClick={() => { this.toggleEditSiteMenu(!showEditSiteMenu) }} className="overview-edit-img" src="/img/parametre.svg" />
                                                {
                                                    showEditSiteMenu &&
                                                    <div className="dropdown-overview-edit">
                                                        {!archive && <span onClick={this.handleClickDiagSite}>Faire la diagnostique</span>}
                                                        <span onClick={this.handleClickEditSite}>
                                                            {archive ? 'Restaurer le site' : 'Modifier le site'}
                                                        </span>
                                                        {!archive && <span onClick={this.handleClickDeleteSite}>Mettre en archive</span>}
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                    <span className="overview-break-overflow" title={currentSite.commentaire}>
                                        {currentSite.commentaire}
                                    </span>
                                    <span className="overview-break-overflow" title={currentSite.adresse}>
                                        <b>Adresse : </b>{currentSite.adresse}
                                    </span>
                                    {
                                        currentSite.prom &&
                                        <>
                                            <span><b>Prom : </b> {currentSite.prom}</span><br />
                                        </>
                                    }
                                    {
                                        (currentSite.prom && currentSite.prom != currentSite.puce) &&
                                        <>
                                            <span><b>Puce : </b> {currentSite.puce}</span><br />
                                        </>
                                    }
                                    {
                                        currentSite.prom &&
                                        <>
                                            <span>
                                                <b>Récépteur SMS : </b>
                                                {currentSite.transmitter_sms ? currentSite.transmitter_sms : "Non défini"}
                                            </span>
                                            {
                                                (currentSite.correct_transmitter && currentSite.correct_transmitter != currentSite.transmitter_sms) &&
                                                <span className="pink">
                                                    {" -> " + currentSite.correct_transmitter}
                                                </span>
                                            }
                                            <br />
                                        </>
                                    }
                                    {
                                        (currentSite.prom && currentSite.prom != currentSite.puce) &&
                                        <>
                                            <span>
                                                <b>Récépteur GPRS : </b>
                                                {currentSite.transmitter_gprs ? currentSite.transmitter_gprs : "Non défini"}
                                            </span>
                                            <br />
                                        </>
                                    }
                                    <span>
                                    </span>
                                    <div className="table">
                                        <span className="cell">
                                            <b>Dernière transmission : </b>
                                            {currentSite.last_transmission ? moment(currentSite.last_transmission).format("DD/MM/YYYY HH:mm") : <span className="red">Non définie</span>}
                                        </span>
                                        {
                                            currentSite.pointeuse ? <span className="cell right" style={{ height: "30px" }}>
                                                <span>
                                                    <span className="badge bg-purple">Pointeuse</span>
                                                </span>
                                            </span>
                                                : currentSite.vigilance ? <span className="cell right" style={{ height: "30px" }}>
                                                    <span>
                                                        <span className="badge bg-primary">Vigilance</span>
                                                    </span>
                                                </span>
                                                    : currentSite.pointage ? <span className="cell right" style={{ height: "30px" }}>
                                                        <span>
                                                            <span className="badge bg-dark">Pointage</span>
                                                        </span>
                                                    </span>
                                                        : <></>
                                        }
                                    </div>
                                </div>
                                <div style={{ position: 'relative', top: '2px' }}>
                                    <div className="table">
                                        <div className="cell">
                                            <div id="tabHeaderOverview">
                                                <ul>
                                                    <li id="habilite" className={activeTab == 'habilite' ? "active-tab" : ""} onClick={this.handleChangeTab}>Habilité</li>
                                                    <li id="historique" className={activeTab == 'historique' ? "active-tab" : ""} onClick={this.handleChangeTab}>Alarme</li>
                                                    <li id="zone" className={activeTab == 'zone' ? "active-tab" : ""} onClick={this.handleChangeTab}>Zone</li>
                                                    <li id="signale" className={activeTab == 'signale' ? "active-tab" : ""} onClick={this.handleChangeTab}>Signale</li>
                                                    {/*
                                                    currentSite.pointeuse &&
                                                    <li id="pointeuse" className={activeTab == 'pointeuse' ? "active-tab": ""} onClick={this.handleChangeTab}>Pointeuse</li>
                                                */}
                                                    <li id="operation" className={activeTab == 'operation' ? "active-tab" : ""} onClick={this.handleChangeTab}>Traçabilité</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="tabContentOverview">
                                    <div id="tabContainer">
                                        {
                                            activeTab == 'habilite' &&
                                            <Habilite archive={archive} user={user} idsite={currentSite.id} habilites={currentSite.habilites} updateCurrentSite={() => { this.updateSite(currentSite.id) }} prom={currentSite.prom} heightWindow={heightWindow} />
                                        }
                                        {
                                            activeTab == 'historique' &&
                                            <Historique data="site" nomSite={currentSite.nom} id={currentSite.id} heightWindow={heightWindow - 500} />
                                        }
                                        {
                                            activeTab == 'zone' &&
                                            <Zone archive={archive} userId={user.id} siteId={currentSite.id} heightWindow={heightWindow} />
                                        }
                                        {
                                            activeTab == 'signale' &&
                                            <Signale archive={archive} userId={user.id} siteId={currentSite.id} prom={currentSite.prom} heightWindow={heightWindow} />
                                        }
                                        {
                                            activeTab == 'pointeuse' &&
                                            <Pointeuse userId={user.id} idsite={currentSite.id} siteId={currentSite.id} heightWindow={heightWindow} />
                                        }
                                        {
                                            activeTab == 'operation' &&
                                            <Operation
                                                currentDate={currentDate}
                                                action={'/api/sites/operation/' + currentSite.id}
                                                heightTable={heightWindow - 500} />
                                        }
                                    </div>
                                </div>
                            </div>
                            :
                            <div className="img-bg-container">
                                <img className="img-bg-overview" src="/img/tls_background.svg" />
                            </div>
                    }
                </div>
            </div >
        )
    }
}
