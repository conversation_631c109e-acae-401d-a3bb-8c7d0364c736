import React, { Component } from 'react'
import axios from 'axios'

import LoadingData from '../../loading/LoadingData'
import EditClientModal from './EditClientModal'
import DeleteClientModal from './DeleteClientModal'
import InfiniteS<PERSON>roll from 'react-infinite-scroll-component'

export default class Client extends Component {
    constructor(props) {
        super(props)
        this.state = {
            selectedClient: null,
            searchValue: '',
            showAddModal: false,
            showEditModal: false,
            showDeleteModal: false,
            loading: true,
            clients: [],
            allDataLoaded: false,
        }
        this.handleSaveSelect = this.handleSaveSelect.bind(this)
        this.handleChangeSelected = this.handleChangeSelected.bind(this)
        this.setSelectedClient = this.setSelectedClient.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.closeModal = this.closeModal.bind(this)
        this.handleShowAddModal = this.handleShowAddModal.bind(this)
        this.handleShowEditModal = this.handleShowEditModal.bind(this)
        this.handleShowDeleteModal = this.handleShowDeleteModal.bind(this)
        this.handleSeachClient = this.handleSeachClient.bind(this)
        this.updateData = this.updateData.bind(this)
        this.handleEnterPress = this.handleEnterPress.bind(this)
        this.fetchMoreData = this.fetchMoreData.bind(this)
    }
    handleEnterPress(event) {
        if (event.key === 'Enter') {
            this.updateData(true)
        }
    }
    handleChangeSelected(client) {
        this.setState({
            selectedClient: client
        })
    }
    handleSaveSelect() {
        this.props.changeClient(this.state.selectedClient)
    }
    handleCancel() {
        this.props.closeModal()
    }
    handleCancel() {
        this.props.closeModal()
    }
    handleShowAddModal() {
        this.setState({
            showAddModal: true
        })
    }
    handleShowEditModal() {
        this.setState({
            showEditModal: true
        })
    }
    handleShowDeleteModal() {
        this.setState({
            showDeleteModal: true
        })
    }
    handleSeachClient(event) {
        this.setState({
            searchValue: event.target.value
        })
    }
    updateData(loading, clearSearch) {
        const { clients, searchValue } = this.state
        const params = new URLSearchParams()
        params.append('offset', (loading ? 0 : clients.length))
        if (loading)
            this.setState({
                allDataLoaded: false,
                clients: []
            })
        if (clearSearch)
            this.setState({
                searchValue: ''
            })
        else
            params.append('search', searchValue)

        axios.get('/api/clients?' + params)
            .then(({ data }) => {
                if (data) {
                    if (loading) {
                        this.container.scroll(0, 0)
                        this.setState({
                            clients: data
                        })
                    }
                    else {
                        const list = clients.slice().concat(data)
                        this.setState({
                            clients: list
                        })
                    }
                    this.setState({
                        allDataLoaded: (data.length < 50)
                    })
                }
            })
            .catch((e) => {
                setTimeout(() => {
                    this.updateData(loading, clearSearch)
                }, 10000)
            })
    }

    fetchMoreData() {
        setTimeout(() => {
            this.updateData()
        }, 300);
    }

    setSelectedClient(value) {
        this.setState({
            selectedClient: value
        })
    }

    componentDidMount() {
        this.updateData(true)
        this.setSelectedClient(this.props.defaultClient)
    }

    closeModal() {
        this.setState({
            showAddModal: false,
            showEditModal: false,
            showDeleteModal: false
        })
    }
    render() {
        const { allDataLoaded, clients, selectedClient, searchValue, showAddModal, showEditModal, showDeleteModal } = this.state
        return (
            <div style={{ zIndex: 200 }} className="fixed-front">
                {
                    showAddModal && <EditClientModal
                        action={"/api/clients/store"}
                        closeModal={this.closeModal}
                        updateClients={this.updateData}
                        setSelectedClient={this.setSelectedClient} />
                }
                {
                    (showEditModal && selectedClient) && <EditClientModal
                        action={"/api/clients/update/" + selectedClient.idClient}
                        client={selectedClient}
                        closeModal={this.closeModal}
                        updateClients={this.updateData}
                        setSelectedClient={this.setSelectedClient} />
                }
                {
                    (showDeleteModal && selectedClient) && <DeleteClientModal
                        action={"/api/clients/delete/" + selectedClient.idClient}
                        closeModal={this.closeModal}
                        nom={selectedClient.Societe}
                        updateClients={this.updateData}
                        setSelectedClient={this.setSelectedClient} />
                }
                <div className="table">
                    <div className="modal-container">
                        <div className="modal lg">
                            <div className="modal-content">
                                <div className="table">
                                    <div className="cell">
                                        <h3>Clients</h3>
                                    </div>
                                    <div className="cell right">
                                        <div id="searchSite">
                                            <div>
                                                <input onKeyDown={this.handleEnterPress} onChange={this.handleSeachClient} value={searchValue} type="text" />
                                                <img onClick={() => { this.updateData(true) }} src="/img/search.svg" />
                                            </div>
                                        </div>
                                    </div>
                                    <div id="cellAddContactBtn">
                                        <img onClick={this.handleShowAddModal} id="addClientBtn" src="/img/add.svg" />
                                    </div>
                                </div>
                                <table className="fixed_header default layout-fixed">
                                    <thead>
                                        <tr>
                                            <th className="cellClientRadio"></th>
                                            <th className="cellClientNom">Nom</th>
                                            <th className="cellClientPhone">Type</th>
                                            <th className="cellSiteAgent">
                                                Adresse
                                                <img src="/img/refresh_table_default.svg" onClick={() => { this.updateData(true) }} />
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody id="scrollableClient" ref={el => (this.container = el)} style={{ height: "400px" }}>
                                        <InfiniteScroll
                                            scrollableTarget="scrollableClient"
                                            dataLength={clients.length}
                                            next={this.fetchMoreData}
                                            hasMore={!allDataLoaded}
                                            loader={<LoadingData />}
                                        >
                                            {
                                                clients.map((client) => {
                                                    return (
                                                        <tr key={client.idClient} onClick={() => { this.handleChangeSelected(client) }}>
                                                            <td className="cellClientRadio">
                                                                <label className="checkbox-container">
                                                                    <input
                                                                        checked={(selectedClient && selectedClient.idClient == client.idClient)}
                                                                        name="clientRadio" type="checkbox" />
                                                                    <span className="radiomark-lg"></span>
                                                                </label>
                                                            </td>
                                                            <td className="cellClientNom">{client.Societe}</td>
                                                            <td className="cellClientPhone">{client.TypeClient ? 'Particulier' : 'Société'}</td>
                                                            <td>{client.Adresse}</td>
                                                        </tr>)
                                                })

                                            }
                                            {
                                                (allDataLoaded && clients.length == 0) &&
                                                <tr>
                                                    <td className='center secondary'>Aucun données trouvé</td>
                                                </tr>
                                            }
                                        </InfiniteScroll>
                                    </tbody>
                                </table>
                                {/* {!allDataLoaded && <LoadingData />} */}
                            </div>
                            <div className="modal-footer">
                                <div className="table">
                                    <div className="cell left">
                                        {selectedClient != null && <button onClick={this.handleShowEditModal} className="btn-default fix-width-right">Modifier</button>}
                                        {selectedClient != null && <button onClick={this.handleShowDeleteModal} className="btn-default fix-width-right">Supprimer</button>}
                                    </div>
                                    <div className="cell right">
                                        <button disabled={selectedClient == null} onClick={this.handleSaveSelect} className="btn-primary fix-width">Selectionner</button>
                                        <button onClick={this.handleCancel} className="btn-default fix-width">Annuler</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }
}
