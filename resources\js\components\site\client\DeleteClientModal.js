import React, { Component } from 'react'
import Modal from '../../modal/Modal'
import axios from 'axios'

export default class DeleteClientModal extends Component {
    constructor(props){
        super(props)
        this.state = {
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }
    handleSave(){
        const data = new FormData()
        axios.post(this.props.action, data)
        .then(({data}) => {
            this.props.updateClients(true)
            this.props.closeModal()
            this.props.setSelectedClient(null)
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        return (
            <Modal handleSave={this.handleSave} handleCancel={this.handleCancel}>
                <h3>Client</h3>
                <div>
                    Voulez-vous vraiment supprimer le client : <br/>
                    <center><b>{this.props.nom}</b></center>
                </div>
            </Modal>)
    }
}