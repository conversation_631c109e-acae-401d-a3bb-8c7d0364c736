import React, { Component } from 'react'
import axios from 'axios'

import Modal from '../../modal/Modal'

export default class EditClientModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            nom: '',
            type: false,
            adresse: '',
        }
        this.handleChangeNom = this.handleChangeNom.bind(this)
        this.handleChangeType = this.handleChangeType.bind(this)
        this.handleChangeAdresse = this.handleChangeAdresse.bind(this)
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }
    handleChangeNom(event){
        this.setState({
            nom: event.target.value
        })
    }
    handleChangeType(value){
        this.setState({
            type: value
        })
    }
    handleChangePhone(event){
        this.setState({
            phone: event.target.value
        })
    }
    handleChangeAdresse(event){
        this.setState({
            adresse: event.target.value
        })
    }
    componentDidMount(){
        const {client} = this.props
        this.setState({
            nom:  client ? client.Societe : '',
            type: client && (client.TypeClient ? true : false),
            adresse: client ? client.Adresse : '',
        })
    }
    toggleClient(value){
        this.setState({
            showClient: value
        })
    }
    handleSave(){
        const {nom, type, adresse} = this.state
        let data = new FormData()
        if(nom)
            data.append("nom", nom)
        if(type)
            data.append("type", 1)
        else
            data.append("type", 0)
        if(adresse)
            data.append("adresse", adresse)
        axios.post(this.props.action, data)
        .then(({data}) => {
            this.props.setSelectedClient(data)
            this.props.updateClients(true)
            this.props.closeModal()
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        const {nom, type, adresse} = this.state
        return (
            <div>
                <Modal handleSave={this.handleSave} handleCancel={this.handleCancel}>
                    <h3>Client</h3>
                    <div className="center">
                        <label className="checkbox-container">
                            Société
                            <input onChange={() => {this.handleChangeType(false)}} name="client" checked={!type} type="radio"/>
                            <span className="radiomark"></span>
                        </label>
                        <label className="checkbox-container">
                            Particulier
                            <input onChange={() => {this.handleChangeType(true)}} name="client" checked={type} type="radio"/>
                            <span className="radiomark"></span>
                        </label>
                    </div>
                    <div className="input-container">
                        <label>Nom</label>
                        <input onChange={this.handleChangeNom} value={nom}/>
                    </div>
                    <div className="input-container">
                        <label>Adresse</label>
                        <input onChange={this.handleChangeAdresse} value={adresse}/>
                    </div>
                </Modal>
            </div>
        )
    }
}