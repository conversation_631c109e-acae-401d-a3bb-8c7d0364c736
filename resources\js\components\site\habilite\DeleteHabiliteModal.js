import React, { Component } from 'react'
import Modal from '../../modal/Modal'
import axios from 'axios'

export default class DeleteHabiliteModal extends Component {
    constructor(props){
        super(props)
        this.state = {
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }
    handleSave(){
        const data = new FormData()
        axios.post(this.props.action, data)
        .then(({data}) => {
            this.props.updateCurrentSite()
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        return (
            <Modal handleSave={this.handleSave} handleCancel={this.handleCancel}>
                <h3>Habilité</h3>
                <div>Voulez-vous vraiment supprimer cette habilité ?</div>
            </Modal>)
    }
}