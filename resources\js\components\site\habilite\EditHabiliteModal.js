import React, { Component } from 'react'
import axios from 'axios'

import Modal from '../../modal/Modal'
import Contact from './contact/Contact'

export default class EditHabiliteModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            contact: null,
            quality: '',
            password: '',
            ordre: '',
            showContact: false,
        }
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.changeContact = this.changeContact.bind(this)
        this.handleChangeQuality = this.handleChangeQuality.bind(this)
        this.handleChangePassword = this.handleChangePassword.bind(this)
        this.handleChangeOrdre = this.handleChangeOrdre.bind(this)
    }
    changeContact(contact){
        this.setState({
            contact: contact,
            showContact: false
        })
    }
    handleChangeQuality(event){
        this.setState({
            quality: event.target.value
        })
    }
    handleChangePassword(event){
        this.setState({
            password: event.target.value
        })
    }
    handleChangeOrdre(event){
        this.setState({
            ordre: event.target.value
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    componentDidMount(){
        const {habilite} = this.props
        this.setState({
            contact: habilite ? habilite.contact : null,
            quality:  habilite ? habilite.quality : '',
            password: habilite ? habilite.password : '',
            ordre: habilite ? habilite.idordre: ''
        })
        axios.get('/api/events')
        .then(({data}) => {
            this.setState({
                events: data,
            })
        })
    }
    toggleContact(value){
        this.setState({
            showContact: value
        })
    }
    handleSave(){
        const {contact, password, quality, ordre} = this.state
        let data = new FormData()
        if(contact)
            data.append("idcontact", contact.idContact)
        if(password)
            data.append("password", password)
        if(quality)
            data.append("quality", quality)
        if(ordre)
            data.append("idordre", ordre)
        if(this.props.idsite)
            data.append("idsite", this.props.idsite)
        axios.post(this.props.action, data)
        .then(({data}) => {
            this.props.updateCurrentSite()
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        const {quality, password, ordre, showContact, contact} = this.state
        return (
            <div>
                <Modal handleSave={this.handleSave} handleCancel={this.handleCancel}>
                    <h3>Habilité</h3>
                    <div className="input-container">
                        <label>Contact</label>
                        <div className="table">
                            <div className="cell">
                                <input 
                                    disabled={true} 
                                    value={contact ? (contact.nom + (contact.prenom ? (' ' + contact.prenom) : '')) : ''}/>
                            </div>
                            <div id="cellContactBtn" onClick={()=>{this.toggleContact(true)}}>
                                <img id="contactImg" src="/img/profil.svg"/>
                            </div>
                        </div>
                    </div>
                    <div className="input-container">
                        <label>Qualité</label>
                        <input onChange={this.handleChangeQuality} value={quality}/>
                    </div>
                    <div className="input-container">
                        <label>Code vocal</label>
                        <input onChange={this.handleChangePassword} value={password}/>
                    </div>
                    <div className="input-container">
                        <label>Ordre</label>
                        <input onChange={this.handleChangeOrdre} value={ordre}/>
                    </div>
                </Modal>
                {
                    showContact && <Contact defaultContact={contact} changeContact={this.changeContact} closeModal={() => {this.toggleContact(false)}}/>
                }
            </div>
        )
    }
}