import React, { Component } from 'react'
import axios from 'axios'

import './contact.css'
import LoadingData from '../../../loading/LoadingData'
import EditContactModal from './EditContactModal'
import DeleteContactModal from './DeleteContactModal'
import InfiniteS<PERSON>roll from 'react-infinite-scroll-component'

export default class Contact extends Component {
    constructor(props) {
        super(props)
        this.state = {
            selectedContact: null,
            searchValue: '',
            showAddModal: false,
            showEditModal: false,
            showDeleteModal: false,
            loading: true,
            contacts: [],
            allDataLoaded: false,
            showArchived: false // New state to track if we're viewing archived contacts
        }
        this.handleSaveSelect = this.handleSaveSelect.bind(this)
        this.handleChangeSelected = this.handleChangeSelected.bind(this)
        this.setSelectedContact = this.setSelectedContact.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.closeModal = this.closeModal.bind(this)
        this.handleShowAddModal = this.handleShowAddModal.bind(this)
        this.handleShowEditModal = this.handleShowEditModal.bind(this)
        this.handleShowDeleteModal = this.handleShowDeleteModal.bind(this)
        this.handleSeachContact = this.handleSeachContact.bind(this)
        this.updateData = this.updateData.bind(this)
        this.handleEnterPress = this.handleEnterPress.bind(this)
        this.fetchMoreData = this.fetchMoreData.bind(this)
        this.handleArchiveToggle = this.handleArchiveToggle.bind(this) // New handler for archive toggle
    }

    handleEnterPress(event) {
        if (event.key === 'Enter') {
            this.updateData(true)
        }
    }

    handleChangeSelected(contact) {
        this.setState({
            selectedContact: contact
        })
    }

    handleSaveSelect() {
        this.props.changeContact(this.state.selectedContact)
        this.setState({
            selectedContact: this.state.selectedContact
        })
    }

    handleCancel() {
        this.props.closeModal()
    }

    handleShowAddModal() {
        this.setState({
            showAddModal: true
        })
    }

    handleShowEditModal() {
        this.setSelectedContact(this.state.selectedContact)
        this.setState({
            showEditModal: true
        })
    }

    handleShowDeleteModal() {
        this.setState({
            showDeleteModal: true
        })
    }

    handleSeachContact(event) {
        this.setState({
            searchValue: event.target.value
        })
    }

    // New handler for archive toggle
    handleArchiveToggle(event) {
        this.setSelectedContact(null)
        this.setState({
            showArchived: event.target.checked
        }, () => {
            this.updateData(true) // Reload data with new archive filter
        })
    }

    updateData(loading, clearSearch) {
        const { contacts, searchValue, showArchived } = this.state
        const params = new URLSearchParams()
        params.append('offset', (loading ? 0 : contacts.length))
        if (loading)
            this.setState({
                allDataLoaded: false,
                contacts: []
            })
        if (clearSearch)
            this.setState({
                searchValue: ''
            })
        else
            params.append('search', searchValue)

        // Add archive parameter to the request
        params.append('archived', showArchived ? '1' : '0')

        axios.get('/api/contacts?' + params)
            .then(({ data }) => {
                if (data) {
                    if (loading) {
                        this.container.scroll(0, 0)
                        this.setState({
                            contacts: data,
                        })
                    }
                    else {
                        const list = contacts.slice().concat(data)
                        this.setState({
                            contacts: list
                        })
                    }
                    this.setState({
                        allDataLoaded: (data.length < 50)
                    })
                }
            })
    }

    fetchMoreData() {
        setTimeout(() => {
            this.updateData()
        }, 300);
    }

    setSelectedContact(value) {
        this.setState({
            selectedContact: value
        })
    }

    componentDidMount() {
        this.updateData(true)
        this.setSelectedContact(this.props.defaultContact)
    }

    closeModal() {
        this.setState({
            showAddModal: false,
            showEditModal: false,
            showDeleteModal: false
        })
    }

    render() {
        const { contacts, selectedContact, searchValue, showAddModal, showEditModal, showDeleteModal, allDataLoaded, showArchived, loading } = this.state
        return (
            <div style={{ zIndex: 200 }} className="fixed-front">
                {
                    showAddModal && <EditContactModal
                        action={"/api/contacts/store"}
                        closeModal={this.closeModal}
                        updateContacts={this.updateData}
                        setSelectedContact={this.setSelectedContact} />
                }
                {
                    (showEditModal && selectedContact) && <EditContactModal
                        action={"/api/contacts/update/" + selectedContact.idContact}
                        contact={selectedContact}
                        closeModal={this.closeModal}
                        updateContacts={this.updateData}
                        setSelectedContact={this.setSelectedContact} />
                }
                {
                    (showDeleteModal && selectedContact) && <DeleteContactModal
                        action={"/api/contacts/delete/" + selectedContact.idContact}
                        closeModal={this.closeModal}
                        nom={selectedContact.nom}
                        updateContacts={this.updateData}
                        setSelectedContact={this.setSelectedContact}
                        isArchived={showArchived} // Pass the showArchived state
                    />
                }
                <div className="table">
                    <div className="modal-container">
                        <div className="modal lg">
                            <div className="modal-content">
                                <div className="table" style={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <div className="cell">
                                        <h3>Contacts {showArchived && "(Archives)"}</h3>
                                    </div>
                                    <div className='cell right' style={{ display: 'flex', alignItems: 'center' }}>
                                        <label style={{ display: 'flex', alignItems: 'center', marginRight: '10px' }}>
                                            <input
                                                type="checkbox"
                                                checked={showArchived}
                                                onChange={this.handleArchiveToggle}
                                            />
                                            <span style={{ marginLeft: '5px' }}>Archive</span>
                                        </label>
                                        <div id="searchSite" style={{ marginRight: '10px' }}>
                                            <div>
                                                <input onKeyDown={this.handleEnterPress} onChange={this.handleSeachContact} value={searchValue} type="text" />
                                                <img onClick={() => { this.updateData(true) }} src="/img/search.svg" alt="search" />
                                            </div>
                                        </div>
                                        <div id="cellAddContactBtn">
                                            <img onClick={this.handleShowAddModal} id="addContactBtn" src="/img/add.svg" alt="add contact" />
                                        </div>
                                    </div>
                                </div>
                                <table className="fixed_header default layout-fixed">
                                    <thead>
                                        <tr>
                                            <th className="cellContactRadio"></th>
                                            <th className="cellContactNom">Nom</th>
                                            <th className="cellContactPhone">Numéro</th>
                                            <th className="cellSiteAgent">
                                                Site
                                                <img src="/img/refresh_table_default.svg" onClick={() => { this.updateData(true) }} />
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody id="scrollableContact" ref={el => (this.container = el)} style={{ height: "400px" }}>
                                        <InfiniteScroll
                                            scrollableTarget="scrollableContact"
                                            dataLength={contacts.length}
                                            next={this.fetchMoreData}
                                            hasMore={!allDataLoaded}
                                            loader={<LoadingData />}
                                        >
                                            {
                                                contacts.map((contact, index) => {
                                                    return (
                                                        <tr key={contact.idContact + '_' + index} onClick={() => { this.handleChangeSelected(contact) }}>
                                                            <td className="cellContactRadio">
                                                                <label className="checkbox-container">
                                                                    <input checked={(selectedContact && selectedContact.idContact == contact.idContact)} name="contactRadio" type="checkbox" />
                                                                    <span className="radiomark"></span>
                                                                </label>
                                                            </td>
                                                            <td className="cellContactNom">{contact.nom && contact.nom + (contact.prenom ? (' ' + contact.prenom) : '')}</td>
                                                            <td className="cellContactPhone">
                                                                {contact.phones.map(phone => phone.numero).join(", ")}
                                                            </td>
                                                            <td>{contact.site}</td>
                                                        </tr>)
                                                })
                                            }
                                            {
                                                (allDataLoaded && contacts.length == 0) &&
                                                <tr>
                                                    <td colSpan="4" className='center secondary'>Aucun données trouvé</td>
                                                </tr>
                                            }
                                        </InfiniteScroll>
                                    </tbody>
                                </table>
                            </div>
                            <div className="modal-footer">
                                <div className="table">
                                    <div className="cell left">
                                        {selectedContact != null && !showArchived && (
                                            <>
                                                <button onClick={this.handleShowEditModal} className="btn-default fix-width-right">Modifier</button>
                                                <button onClick={this.handleShowDeleteModal} className="btn-default fix-width-right">Supprimer</button>
                                            </>
                                        )}
                                        {selectedContact != null && showArchived && (
                                            <button onClick={this.handleShowDeleteModal} className="btn-default fix-width-right">Restaurer</button>
                                        )}
                                    </div>
                                    <div className="cell right">
                                        {!showArchived && (
                                            <button disabled={selectedContact == null} onClick={this.handleSaveSelect} className="btn-primary fix-width">Selectionner</button>)}
                                        <button onClick={this.handleCancel} className="btn-default fix-width">Annuler</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }
}
