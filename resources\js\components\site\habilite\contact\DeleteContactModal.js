import React, { Component } from 'react'
import axios from 'axios'

export default class DeleteContactModal extends Component {
    constructor(props) {
        super(props)
        this.state = {
            loading: false
        }
        this.handleDelete = this.handleDelete.bind(this)
        this.handleRestore = this.handleRestore.bind(this)
    }

    handleDelete() {
        this.setState({
            loading: true
        })
        axios.post(this.props.action)
            .then(({ data }) => {
                if (data) {
                    this.props.closeModal()
                    this.props.updateContacts(true)
                    this.props.setSelectedContact(null)
                }
            })
            .catch(error => {
                console.error(error)
            })
            .finally(() => {
                this.setState({
                    loading: false
                })
            })
    }

    handleRestore() {
        this.setState({
            loading: true
        })
        axios.post(this.props.action.replace('delete', 'restore'))
            .then(({ data }) => {
                if (data) {
                    this.props.closeModal()
                    this.props.updateContacts(true)
                    this.props.setSelectedContact(null)
                }
            })
            .catch(error => {
                console.error(error)
            })
            .finally(() => {
                this.setState({
                    loading: false
                })
            })
    }

    render() {
        const { loading } = this.state
        const { nom, isArchived } = this.props

        const modalOverlayStyle = {
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000
        };

        return (
            <div style={modalOverlayStyle} className="fixed-front">
                <div className="modal-container">
                    <div className="modal sm">
                        <div className="modal-content">
                            <div className="table">
                                <div className="cell">
                                    <h3>{isArchived ? "Restaurer le contact" : "Supprimer le contact"}</h3>
                                </div>
                            </div>
                            <div className="table">
                                <div className="cell">
                                    {isArchived ? (
                                        <p>Voulez-vous restaurer le contact <b>{nom}</b> ?</p>
                                    ) : (
                                        <p>Voulez-vous supprimer le contact <b>{nom}</b> ?</p>
                                    )}
                                </div>
                            </div>
                        </div>
                        <div className="modal-footer">
                            <div className="table">
                                <div className="cell right">
                                    {isArchived ? (
                                        <button
                                            disabled={loading}
                                            onClick={this.handleRestore}
                                            className="btn-primary fix-width"
                                        >
                                            Restaurer
                                        </button>
                                    ) : (
                                        <button
                                            disabled={loading}
                                            onClick={this.handleDelete}
                                            className="btn-primary fix-width"
                                        >
                                            Supprimer
                                        </button>
                                    )}
                                    <button
                                        disabled={loading}
                                        onClick={this.props.closeModal}
                                        className="btn-default fix-width"
                                    >
                                        Annuler
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }
}
