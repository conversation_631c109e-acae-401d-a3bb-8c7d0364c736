import React, { Component } from 'react'
import axios from 'axios'

import Modal from '../../../modal/Modal'
import { error } from 'jquery'

export default class EditContactModal extends Component {
    constructor(props) {
        super(props)
        this.state = {
            nom: '',
            prenom: '',
            phones: [],
            adresse: '',
            error: '',
            newPhone: '',
            disableSave: false,
        }
        this.handleChangeNom = this.handleChangeNom.bind(this)
        this.handleChangePrenom = this.handleChangePrenom.bind(this)
        this.handleChangeAdresse = this.handleChangeAdresse.bind(this)
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.handleChange = this.handleChange.bind(this)
        this.handleAddPhone = this.handleAddPhone.bind(this)
        this.handleRemovePhone = this.handleRemovePhone.bind(this)
        this.handleKeyPress = this.handleKeyPress.bind(this)
    }

    handleChangeNom(event) {
        this.setState({ nom: event.target.value })
    }

    handleChangePrenom(event) {
        this.setState({ prenom: event.target.value })
    }

    handleChangeAdresse(event) {
        this.setState({ adresse: event.target.value })
    }

    componentDidMount() {
        const { contact } = this.props
        this.setState({
            nom: contact ? contact.nom : '',
            prenom: contact ? contact.prenom : '',
            phones: contact && contact.phones
                ? contact.phones.map(phone => {
                    return ({ numero: phone.numero })
                }) : [],
            adresse: contact ? contact.adresse : '',
        });
        if (this.state.phones.length < 1) {
            this.setState({ disableSave: true })
        } else {
            this.setState({ disableSave: false })
        }
    }

    handleAddPhone() {
        const { newPhone, phones } = this.state;
        const phoneRegex = /^(032|033|034|037|038|020)\d{7}$/;

        if (!newPhone) {
            this.setState({ error: { error: "Le numéro de téléphone ne peut pas être vide." } });
            return;
        }

        if (!phoneRegex.test(newPhone)) {
            this.setState({ error: { error: "Format de numéro invalide. Il doit contenir 10 chiffres et commencer par 032, 033, 034, 037, 038 ou 020." } });
            return;
        }

        this.setState({
            phones: [...phones, { numero: newPhone }],
            newPhone: '',
            error: ''
        });
    }

    handleKeyPress(event) {
        if (event.key === 'Enter') {
            event.preventDefault()
            this.handleAddPhone()
        }
    }
    handleRemovePhone(index) {
        const { phones } = this.state
        this.setState({
            phones: phones.filter((_, i) => i !== index)
        })
    }

    handleSave() {
        this.setState({ error: { error: '' } })
        const { nom, prenom, phones, adresse } = this.state
        let data = new FormData()
        if (nom)
            data.append("nom", nom)
        if (prenom)
            data.append("prenom", prenom)
        phones.forEach(phone => data.append("phones[]", phone.numero))
        if (adresse)
            data.append("adresse", adresse)
        axios.post(this.props.action, data)
            .then(({ data }) => {
                console.log(data)
                if (data.error) {
                    this.setState({ error: { error: data.error } })
                    if (data.existingMatches) {
                        this.setState({
                            error: {
                                error: data.error,
                                existingMatches: data.existingMatches,
                            }
                        })
                    }
                }
                else {
                    this.props.setSelectedContact(data)
                    this.props.updateContacts(true)
                    this.props.closeModal()
                }
            })
    }
    handleCancel() {
        this.props.closeModal()
    }
    handleChange(e) {
        this.setState({ newPhone: e.target.value });
    }

    componentDidUpdate(prevProps, prevState) {
        if ((prevState.phones !== this.state.phones)) {
            if (this.state.phones.length < 1) {
                this.setState({ disableSave: true })
            } else {
                this.setState({ disableSave: false })
            }
        }

    }

    render() {
        const { nom, prenom, phones, newPhone, adresse, error, disableSave } = this.state
        return (
            <div>
                <Modal handleSave={this.handleSave} handleCancel={this.handleCancel} disableSave={disableSave}>
                    <h3>Contact</h3>
                    <div className="input-container">
                        <label>Nom</label>
                        <input onChange={this.handleChangeNom} value={nom} />
                    </div>
                    <div className="input-container">
                        <label>Prénom</label>
                        <input onChange={this.handleChangePrenom} value={prenom} />
                    </div>
                    <div className="input-container">
                        <label>Téléphones</label>
                        <div style={{
                            display: "flex",
                            alignItems: "center",
                            flexWrap: "wrap",
                            padding: "5px",
                            border: "1px solid #ccc",
                            borderRadius: "5px"
                        }}>
                            {phones.map((phone, index) => (
                                <div key={index} style={{
                                    display: "flex",
                                    alignItems: "center",
                                    background: "#f5f5f5",
                                    padding: "5px",
                                    marginRight: "5px",
                                    borderRadius: "3px"
                                }}>
                                    <span>{phone.numero}</span>
                                    <button onClick={() => this.handleRemovePhone(index)} style={{
                                        marginLeft: "5px",
                                        background: "transparent",
                                        border: "none",
                                        cursor: "pointer"
                                    }}>X</button>
                                </div>
                            ))}

                            <input
                                type="text"
                                placeholder="Ajouter un numéro puis 'Entrer' pour valider"
                                value={newPhone}
                                onChange={this.handleChange}
                                onKeyDown={this.handleKeyPress}
                                style={{
                                    border: "none",
                                    outline: "none",
                                    flexGrow: 1,
                                    padding: "5px"
                                }}
                            />
                        </div>
                    </div>
                    <div className="input-container">
                        <label>Adresse</label>
                        <input onChange={this.handleChangeAdresse} value={adresse} />
                    </div>
                    {
                        error && (
                            <div style={{ padding: '10px', border: '1px solid #ddd', borderRadius: '5px', backgroundColor: '#f9f9f9' }}>
                                <div className="red" style={{ color: 'red', marginBottom: '10px' }}>{error.error}</div>
                                {error.existingMatches && (
                                    <div>
                                        <p style={{ fontWeight: 'bold', marginBottom: '5px' }}>Contacts existants avec ces numéros de téléphone :</p>
                                        <ul style={{ listStyleType: 'none', paddingLeft: '0' }}>
                                            {error.existingMatches.map((match, index) => (
                                                <li key={index} style={{ marginBottom: '5px', padding: '8px', borderBottom: '1px solid #eee' }}>
                                                    Contact: {match.contact.nom} , Numero: {match.phone_number}
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                )}
                            </div>
                        )
                    }
                </Modal>
            </div>
        )
    }
}
