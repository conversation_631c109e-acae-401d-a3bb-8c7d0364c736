import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'
import queryString from 'query-string'
import DatePicker  from 'react-datepicker'
import {saveAs} from "file-saver";
const Excel = require("exceljs")

import './historique.css'
import 'react-datepicker/dist/react-datepicker.css'
import LoadingData from '../../loading/LoadingData'

export default class Historique extends Component {
    constructor(props){
        super(props)
        this.state = {
            historiques: null,
            begin: moment().toDate(),
            end: moment().toDate(),
            loading: true
        }
        this.handleClickSearch = this.handleClickSearch.bind(this)
        this.handleClickExport = this.handleClickExport.bind(this)
        this.handleBeginDate = this.handleBeginDate.bind(this)
        this.handleEndDate = this.handleEndDate.bind(this)
    }
    async handleClickExport(){
        console.log('click export')
        const {begin, end, historiques} = this.state
        const {nomSite} = this.props
        const titleExport = 'Historique ' + nomSite + ' du ' + moment(begin).format('DD-MM-YYYY')
         + (moment(begin).format('DD-MM-YYYY') != moment(end).format('DD-MM-YYYY') ? (' au ' + moment(end).format('DD-MM-YYYY')): '')
        const workbook = new Excel.Workbook()
        const worksheet = workbook.addWorksheet(nomSite)

        worksheet.getCell('A1').value = nomSite
        worksheet.getCell('A1').font = {
            size: 20,
            bold: true
        }
        worksheet.getCell('A2').value = 'Historique du ' + moment(begin).format('DD-MM-YYYY')
        + (moment(begin).format('DD-MM-YYYY') != moment(end).format('DD-MM-YYYY') ? (' au ' + moment(end).format('DD-MM-YYYY')): '')
        worksheet.getCell('A2').font = {
            size: 14
        }
        
        worksheet.mergeCells('A1:C1')
        worksheet.mergeCells('A2:C2')
        worksheet.mergeCells('A3:C3')

        worksheet.getColumn('A').width = 20
        worksheet.getColumn('B').width = 50
        worksheet.getColumn('C').width = 40

        const headerFont = { bold: true }
        const borderStyle = {
            top: {style:'thin'},
            left: {style:'thin'},
            bottom: {style:'thin'},
            right: {style:'thin'}
        }
        worksheet.getCell('A4').value = 'Date'
        worksheet.getCell('A4').border = borderStyle
        worksheet.getCell('A4').font = headerFont
        worksheet.getCell('B4').value = 'Alarme'
        worksheet.getCell('B4').border = borderStyle
        worksheet.getCell('B4').font = headerFont
        worksheet.getCell('C4').value = 'Zone'
        worksheet.getCell('C4').border = borderStyle
        worksheet.getCell('C4').font = headerFont

        let line = 5
        historiques.map((row) =>{
            worksheet.getCell('A' + line).value = moment(row.dateArrived).format('DD/MM/YYYY HH:mm')
            worksheet.getCell('A' + line).border = borderStyle
            worksheet.getCell('B' + line).value = '['  + row.codeTevent + '] ' + this.getAlarm(row)
            worksheet.getCell('B' + line).border = borderStyle
            worksheet.getCell('C' + line).value = (row.numZone ? ('000' + row.numZone).slice(-3) : '') + (row.nomZone ? ' (' + row.nomZone + ')' : '')
            worksheet.getCell('C' + line).border = borderStyle
            line++
        })

        const buffer = await workbook.xlsx.writeBuffer();
        const fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        const fileExtension = '.xlsx'
        const blob = new Blob([buffer], {type: fileType});
        saveAs(blob, titleExport + fileExtension);
        
    }
    handleEndDate(date){
        this.setState({
            end: date
        })
    }
    handleBeginDate(date){
        this.setState({
            begin: date
        })
    }
    componentDidMount(){
        console.log(this.props.data)
        this.getHistorique(true)
    }
    getHistorique(first){
        this.setState({
            loading: true
        })
        const {id, data} = this.props
        const {begin, end} = this.state
        axios.get('/api/historiques/'+ data +'/' + 
            id + '/' + 
            moment(begin).format('YYYY-MM-DD') + '/' + 
            moment(end).format('YYYY-MM-DD') + (first ? "?first=1": "")
        )
        .then(({data})=> {
            this.setState({
                historiques: data,
                loading: false
            })
        })
        .catch((e) => {
            console.log(e)
            this.setState({
                loading: false
            })
        })
    }
    getLinks(){
        if(this.state.historiques){
            let historique = this.state.historiques
            let links = []
            let prev = null
            let next = null
            let currentPage = historique.current_page
            let lastPage = historique.last_page
            if(historique.prev_page_url){
                let prevUrlParam = historique.prev_page_url.split('?')
                if(prevUrlParam.length > 1)
                    prev = parseInt(queryString.parse(prevUrlParam[1]).page)
            }
            if(historique.next_page_url){
                let nextUrlParam = historique.next_page_url.split('?')
                if(nextUrlParam.length > 1)
                    next = (parseInt(queryString.parse(nextUrlParam[1]).page))
            }
            for(let i=currentPage-3; i<currentPage; i++){
                if(i>0)
                    links.push(i)
            }
            links.push(currentPage)
            for(let i=currentPage+1; i<=currentPage+3; i++){
                if(i<=lastPage)
                    links.push(i)
                else break;
            }
            return {
                current: currentPage,
                prev: prev,
                next: next,
                links: links
            }
        }
        return {}
    }
    handleClickSearch(event){
        this.getHistorique()
    }
    getColor(code){
        let color = (
            120 == code ? 'd50000':
            132 == code ? 'c62828':
            133 == code ? 'c62828':
            134 == code ? 'c62828':
            130 == code ? 'f44336':
            131 == code ? 'c51162':
            137 == code ? 'ad1457':
            140 == code ? 'd500f9':
            100 == code ? 'e91e63':
            101 == code ? 'ff6d00':
            110 == code ? 'ef6c00':
            151 == code ? '8d6e63':
            111 == code ? 'ff9800':
            117 == code ? 'ffab00':
            112 == code ? 'ff8f00':
            113 == code ? 'ffb300':
            102 == code ? 'dd2c00':
            139 == code ? 'd84315':
            384 == code ? '673ab7':
            301 == code ? '6200ea':
            302 == code ? '6200ea':
            350 == code ? '4527a0':
            1000 == code ? '78909c':
            [400, 401, 402, 403, 404, 405, 407, 406, 408, 409, 441, 442, 456, 454].includes(parseInt(code)) ? '7cb342': ''
        )
        return '#' + color
    }
    getAlarm(row){
        if(row.codeTevent == '301')
            if(row.eventQualify == 3)
                return 'Courant rétablit'
            else return 'Coupure de courant JIRAMA 220V'
        else if(row.alarm)
            if(row.eventQualify == 3)
                return row.alarm.replace("Armement/Désarmement", "Armement")
            else
                return row.alarm.replace("Armement/Désarmement", "Désarmement")
        else 
            return 'Alarme non définie'
    }
    render(){
        const {historiques, begin, end, loading} = this.state
        const {data, heightWindow} = this.props
        return (
            <div>
                <div id="searchBarHistorique">
                    <div className="table">
                        <div className="cell">
                            <DatePicker className="datepicker" dateFormat="dd-MM-yyyy" selected={begin} onChange={this.handleBeginDate}/>
                        </div>
                        <div className="cell">
                            <DatePicker className="datepicker" dateFormat="dd-MM-yyyy" selected={end} onChange={this.handleEndDate}/>
                        </div>
                        <div className="cell right">
                            <button disabled={loading} onClick={this.handleClickSearch} id="searchHistoriqueBtn">Rechercher</button>
                            <button disabled={loading || !historiques || historiques.length == 0} onClick={this.handleClickExport} id="exportHistoriqueBtn">Exporter</button>
                        </div>
                    </div>
                </div>
                {
                    loading ?
                        <LoadingData/>
                    :
                        <table className="fixed_header default layout-fixed">
                            <thead>
                                <tr>
                                    <th className="cellDate">Date</th>
                                    <th className="cellAlarm">Evènement</th>
                                    <th>
                                        {data == 'pointeuse' ? 'Agent' : 'Zone'}
                                    </th>
                                </tr>
                            </thead>
                            <tbody style={{height: heightWindow + "px"}}>
                                {
                                    historiques && historiques.map((row) =>{
                                        return (
                                            <tr style={{color: this.getColor(row.codeTevent)}}
                                                key={row.idademco}
                                                onDoubleClick={() => {this.handleClickRow(row)}}
                                            >
                                                <td className="cellDate">{moment(row.dateArrived).format('DD/MM/YYYY HH:mm')}</td>
                                                <td className="cellAlarm" title={this.getAlarm(row)}>
                                                    {'[' + row.codeTevent + '] ' + this.getAlarm(row)}
                                                </td>
                                                {
                                                    data == "pointeuse" ?
                                                        <td style={{color: '#888'}}>
                                                            {
                                                                row.agent_id ?
                                                                    (
                                                                        row.societe_id == 1 ? 'DGM-' + row.numero_employe :
                                                                        row.societe_id == 2 ? 'SOIT-' + row.num_emp_soit : 
                                                                        row.societe_id == 3 ? 'ST-' + row.numero_stagiaire :
                                                                        row.societe_id == 4 ? 'SM' :
                                                                        row.numero_employe ? row.numero_employe :
                                                                        row.numero_stagiaire ? row.numero_stagiaire :
                                                                        'Ndf'
                                                                    )
                                                                :
                                                                ''
                                                            } {row.nom_agent}
                                                        </td>
                                                    : row.sip ?
                                                        <td style={{color: '#888'}}>
                                                            Check-phone, SIP: {row.sip}
                                                        </td>
                                                    : row.panel_user ?
                                                        <td>
                                                            Area {row.panel_area} 
                                                            <span style={{color: '#888'}}>
                                                                {' (' + row.panel_user + ')'}
                                                            </span>
                                                        </td>
                                                    :
                                                        <td>
                                                            {row.numZone ? ('000' + row.numZone).slice(-3) : ''} 
                                                            <span style={{color: '#888'}}>
                                                                {row.nomZone ? ' (' + row.nomZone + ')' : ''}
                                                            </span>
                                                        </td>

                                                }
                                            </tr>)
                                    })
                                }
                            </tbody>
                        </table>
                }
            </div>
        )
    } 
}