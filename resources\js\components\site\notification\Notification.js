import React, { Component } from 'react'
import 'moment/locale/fr'

import './notification.css'

export default class Notification extends Component {
    constructor(props) {
        super(props)
    }
    capitalizeFirstLetter(string) {
        const arrayString = string.split(' ').map((s) => (
            s.trim().charAt(0).toUpperCase() + s.trim().slice(1).toLowerCase()
        ))
        return arrayString.join(' ')
    }
    render() {
        const { data } = this.props
        return (
            <div id="notificationContainer">
                <div className="table">
                    <div className="cell"><h4>Notification </h4></div>
                    <div className="cell right"><span className="badge bg-light">{data.length > 9 ? '+' : ''} {data.length}</span></div>
                </div>
                <ul id="notificationList">
                    {
                        data.map((site) => {
                            if (site.manque)
                                return <li key={site.idsite} className="lb-green" onClick={() => { this.props.clickItem(site.idsite) }}>
                                    Manque de transmission : {this.capitalizeFirstLetter(site.nom)}
                                </li>
                            else
                                return <li key={site.idsite} className="lb-red" onClick={() => { this.props.clickItem(site.idsite) }}>
                                    Bonton en panne : {this.capitalizeFirstLetter(site.nom)}
                                </li>
                        })
                    }
                </ul>
            </div>
        )
    }
}
