import React, { Component } from 'react'
import axios from 'axios'

import Modal from '../../modal/Modal'

export default class PointeuseModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            nom: '',
            id: '',
            edit_only: false
        }
        this.handlePromChange = this.handlePromChange.bind(this)
        this.handleNomChange = this.handleNomChange.bind(this)
        this.handleCapteurChange = this.handleCapteurChange.bind(this)
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }
    componentDidMount(){
        const {pointeuse} = this.props
        if(pointeuse){
            this.setState({
                nom: pointeuse.nom,
                id: pointeuse.id,
                edit_only: true
            })
        }
        else this.setState({
            edit_only: false
        })
    }
    togglePromList(value){
        this.setState({
            showPromList: value
        })
    }
    handlePromChange(value){
        this.setState({
            id: value,
            showPromList:false
        })
    }
    handleNomChange(event){
        this.setState({
            nom: event.target.value
        })
    }
    handleCapteurChange(event){
        this.setState({
            capteurId: event.target.value
        })
    }
    handlePointeuseChange(event){
        this.setState({
            pointeuse: event.target.value
        })
    }
    handleSave(){
        const {id, nom} = this.state
        const {siteId, action} = this.props
        let data = new FormData()
        data.append("site_id", siteId)
        data.append("id", id)
        data.append("nom", nom)
        axios.post(action, data)
        .then(() => {
            this.props.updatePointeuses()
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        const {id, nom, edit_only} = this.state
        return (
            <div>
                <Modal handleSave={this.handleSave} handleCancel={this.handleCancel}>
                    <h3>Pointeuse</h3>
                    <div className="input-container">
                        <label>Prom</label>
                        <div id="promInputTable" className="table">
                            <span className="cell">{id && ("000" + id).slice(-4)}</span>
                            {   !edit_only && <span id="cellProm">#ID</span>}
                        </div>
                    </div>
                    <div className="input-container">
                        <label>Nom</label>
                        <input value={nom} onChange={this.handleNomChange} type="text"/>
                    </div>
                </Modal>
            </div>
        )
    }
}