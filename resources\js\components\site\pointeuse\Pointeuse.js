import React, { Component } from 'react'
import axios from 'axios'
import LoadingData from '../../loading/LoadingData'

export default class Pointeuse extends Component {
    constructor(props){
        super(props)
        this.state = {
            isLoading: true,
            inputSearch: '',
            pointeuses: [],
            selectedPointeuse: null,
        }
        this.updateData = this.updateData.bind(this)
        this.handleSearchPointeuse = this.handleSearchPointeuse.bind(this)
        this.handleEnterPress = this.handleEnterPress.bind(this)
    }
    handleEnterPress(event){
        if(event.key === 'Enter'){
            this.updateData(true)
        }
    }
    handleChangeSelected(pointeuse){
        this.setState({
            selectedPointeuse: pointeuse
        })
    }
    handleSearchPointeuse(event){
        this.setState({
            inputSearch: event.target.value
        })
    }
    componentDidMount(){
        this.updateData(true)
        this.setState({
            selectedPointeuse: this.props.defaultPointeuse
        })
    }

    updateData(loading, clearSearch){
        const {pointeuses, inputSearch} = this.state
        this.setState({
            currentPointeuse: null
        })
        if(loading)
            this.setState({
                isLoading: true
            })
        if(clearSearch)
            this.setState({
                inputSearch: ''
            })
        axios.get('/api/pointeuses'
             + '?offset=' + (loading ? 0 : pointeuses.length)
             + ((!clearSearch && inputSearch) ? '&search=' + (inputSearch.replace('+', '%2B')) : ''))
        .then(({data}) => {
            if(data){
                console.log("pointeuse fetch:" + data.length)
                console.log(data)
                if(loading){
                    this.setState({
                        pointeuses: data
                    })
                }
                else {
                    const list = pointeuses ? pointeuses.slice().concat(data) : data
                    this.setState({
                        pointeuses: list
                    })
                }
                this.setState({
                    //allDataLoaded: (data.length < 50),
                    isLoading: false
                })
            }
        })
        .catch(() => {
            setTimeout(() => {
                this.updateData(loading, clearSearch)
            }, 10000)
        })
    }

    render(){
        const {selectedPointeuse, pointeuses, inputSearch, isLoading} = this.state
        return (
            <div style={{zIndex: 200}} className="fixed-front">
                <div className="table">
                    <div className="modal-container">
                        <div className="modal lg">
                            <div className="modal-content">
                                <div className="table">
                                    <div className="cell">
                                        <h3>Pointeuses</h3>
                                    </div>
                                    <div className="cell right">
                                        <input id="clientSearch"  onKeyDown={this.handleEnterPress} value={inputSearch} onChange={this.handleSearchPointeuse}/>
                                    </div>
                                </div>
                                <table className="fixed_header default layout-fixed">
                                    <thead>
                                        <tr>
                                            <th className="cellPointeuseNum">Num.</th>
                                            <th className="cellPointeuseNom">Nom</th>
                                            <th>Site</th>
                                        </tr>
                                    </thead>
                                    <tbody style={{height: "400px"}}>
                                        {
                                            isLoading ? 
                                                <tr>
                                                    <LoadingData/>
                                                </tr>
                                            : pointeuses.length == 0 ? 
                                                <tr>
                                                    <td className='center secondary'>Aucun données trouvé</td>
                                                </tr>
                                            :
                                            pointeuses.map((row) => {
                                                return (
                                                    <tr
                                                        key={row.id}
                                                        onClick={() => this.handleChangeSelected(row)}
                                                    >
                                                        <td className="cellClientRadio">
                                                            <label className="checkbox-container">
                                                                <input
                                                                    checked={(selectedPointeuse && selectedPointeuse.id == row.id)} 
                                                                    name="clientRadio" type="checkbox"/>
                                                                <span className="radiomark-lg"></span>
                                                            </label>
                                                        </td>
                                                        <td className="cellPointeuseNom" title={row.nom}>
                                                            [{("00" + row.id).slice(-3)}] {row.nom}
                                                        </td>
                                                        <td title={row.site}>
                                                            {row.site}
                                                        </td>
                                                    </tr>)
                                            })
                                        }
                                    </tbody>
                                </table>
                            </div>
                            <div className="modal-footer">
                                <div className="table">
                                    <div className="cell right">
                                        <button disabled={selectedPointeuse == null} 
                                            onClick={() => this.props.changePointeuse(selectedPointeuse)} 
                                            className="btn-primary fix-width">
                                                Selectionner
                                        </button>
                                        <button onClick={() => this.props.closeModal()} className="btn-default fix-width">Annuler</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    } 
}