.cellPointeuseNum{
    width: 80px;
    min-width: 80px;
    max-width: 80px;
}
.cellDetailPointeuse{
    width: 80px;
    min-width: 80px;
    max-width: 80px;
}
.cellPointeuseNom{
    width: 280px;
    min-width: 280px;
    max-width: 280px;
}
#cellProm{
    display: table-cell;
    width: 50px;
    min-width: 50px;
    max-width: 50px;
    text-align: center;
    background-color: #366666;
    color: whitesmoke;
}
#cellAgent{
    display: table-cell;
    width: 50px;
    min-width: 50px;
    max-width: 50px;
    text-align: center;
    background-color: #366666;
    color: whitesmoke;
}
#promInputTable > span {
    padding: 10px;
    border: solid .5px rgba(0, 0, 0, .1);
}
#agentInputTable > span {
    padding: 10px;
    border: solid .5px rgba(0, 0, 0, .1);
}
.cellPointeuseRadio{
    width: 70px;
    max-width: 70px;
    min-width: 70px;
}
.cellPointeuseProm{
    width: 80px;
    max-width: 80px;
    min-width: 80px;
    text-align: center;
}
#cellAddPointeuseBtn{
    display: table-cell;
    text-align: right;
    vertical-align: middle;
    width: 30px;
}
#addPointeuseBtn{
    height: 30px;
    padding: 3px;
}