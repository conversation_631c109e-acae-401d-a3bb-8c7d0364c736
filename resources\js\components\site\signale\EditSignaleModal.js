import React, { Component } from 'react'
import axios from 'axios'

import Modal from '../../modal/Modal'

export default class SignaleModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            signalReceived: 100,
            signalEmit: 100,
            zone: 0,
            events: null
        }
        this.handleEmitChange = this.handleEmitChange.bind(this)
        this.handleReceivedChange = this.handleReceivedChange.bind(this)
        this.handleZoneChange = this.handleZoneChange.bind(this)
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }
    componentDidMount(){
        this.setState({
            signalReceived: this.props.signale && this.props.signale.received && this.props.signale.received.code,
            signalEmit:  this.props.signale && this.props.signale.emit && this.props.signale.emit.code,
        })
        axios.get('/api/events')
        .then(({data}) => {
            this.setState({
                events: data,
            })
        })
    }
    handleEmitChange(event){
        this.setState({
            signalEmit: event.target.value
        })
    }
    handleReceivedChange(event){
        this.setState({
            signalReceived: event.target.value
        })
    }
    handleZoneChange(event){
        this.setState({
            zone: event.target.value
        })
    }
    handleSave(){
        let data = new FormData()
        const {signalReceived, signalEmit, zone} = this.state
        const {siteId, userId, action} = this.props
        if(signalReceived && signalEmit){
            data.append("received_code", signalReceived)
            data.append("emit_code", signalEmit)
            if(zone)
                data.append("zone", zone)
            data.append("site_id", siteId)
            data.append("user_id", userId)
            axios.post(action, data)
            .then(() => {
                this.props.updateSignales()
            })
        }
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        const {events, signalReceived, signalEmit, zone} = this.state
        return (
            <Modal handleSave={this.handleSave} handleCancel={this.handleCancel}>
                <h3>Signale</h3>
                <div className="input-container">
                    <label>Reçu</label>
                    <select value={signalReceived} onChange={this.handleReceivedChange}>
                        <option value=""/>
                        {
                        events && events.map( ev => 
                            <option value={ev.code} key={ev.IdCode}>
                                {'[' + ev.code + '] ' + ev.Description}
                            </option>)
                        }
                    </select>
                </div>
                <div className="input-container">
                    <label>Interprété</label>
                    <select value={signalEmit} onChange={this.handleEmitChange}>
                        <option value=""/>
                        {
                            events && 
                            events.map( ev => 
                                <option value={ev.code} key={ev.IdCode}>
                                    {'[' + ev.code + '] ' + ev.Description}
                                </option>)
                        }
                    </select>
                </div>
                <div className="input-container">
                    <label>Zone</label>
                    <input value={zone} onChange={this.handleZoneChange} type="text"/>
                </div>
            </Modal>
        )
    }
}