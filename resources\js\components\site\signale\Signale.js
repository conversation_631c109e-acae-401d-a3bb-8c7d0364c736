import React, { Component } from 'react'
import axios from 'axios'
import EditSignaleModal from './EditSignaleModal'
import DeleteSignaleModal from './DeleteSignaleModal'
import IconButton from '../../button/IconButton'

export default class Signale extends Component {
    constructor(props){
        super(props)
        this.state = {
            signale: null,
            signales: [],
            showAddModal: false,
            showEditModal: false,
            showDeleteModal: false,
            widthPx: ''
        }
        this.closeModal = this.closeModal.bind(this)
        this.addModal = this.addModal.bind(this)
        this.editModal = this.editModal.bind(this)
        this.updateData = this.updateData.bind(this)
    }
    componentDidMount(){
        this.updateData()
        this.resize()
    }
    resize() {
        if(this.container)
            this.setState({
                widthPx : ((this.container.offsetWidth - 170)  / 2) + "px"
            })
    }
    closeModal(){
        this.setState({
            showAddModal: false,
            showEditModal: false,
            showDeleteModal: false,
        })
    }
    addModal(){
        this.setState({
            showAddModal: true
        })
    }
    editModal(s){
        this.setState({
            signale: s,
            showEditModal: true
        })
    }
    deleteModal(s){
        this.setState({
            signale: s,
            showDeleteModal: true
        })
    }
    updateData(){
        axios.get("/api/signales/site/" + this.props.siteId)
        .then(({data})=>{
            this.setState({
                signales: data,
                showAddModal: false,
                showEditModal:false,
                showDeleteModal: false,
            })
        })
    }
    render(){
        const {signale, signales, showAddModal, showEditModal,  showDeleteModal, widthPx} = this.state
        const {heightWindow, userId, siteId, archive} = this.props
        return (
            <div ref={el => (this.container = el)}>
                {showAddModal && <EditSignaleModal 
                    action={"/api/signales/store"}
                    userId={userId}
                    siteId={siteId}
                    closeModal={this.closeModal}
                    updateSignales={this.updateData}/>}

                {showEditModal && <EditSignaleModal 
                    action={"/api/signales/update/"+ signale.id}
                    signale={signale}
                    userId={userId}
                    siteId={siteId}
                    closeModal={this.closeModal}
                    updateSignales={this.updateData}/>}

                {showDeleteModal && <DeleteSignaleModal 
                    action={"/api/signales/delete/"+ signale.id}
                    signale={signale}
                    closeModal={this.closeModal}
                    updateSignales={this.updateData}/>}


                <div className="btn-label-container right" onClick={this.addModal}>
                    {
                        !archive &&
                        <IconButton onClick={this.addModal} label="Ajouter une signale" src="/img/add.svg"/>
                    }
                </div>

                {
                    widthPx &&
                    <table className="fixed_header default layout-fixed">
                        <thead>
                            <tr>
                                <th style={{width: widthPx, minWidth: widthPx, maxWidth: widthPx}}>Reçu</th>
                                <th style={{width: widthPx, minWidth: widthPx, maxWidth: widthPx}}>Emit</th>
                                <th className="cellZoneSignal">Zone</th>
                                { !archive && <th></th> }
                            </tr>
                        </thead>
                        <tbody style={{height: (heightWindow - 490) + "px"}}>
                            {
                                signales && signales.map((row) => {
                                    return (
                                        <tr
                                            key={row.id}
                                        >
                                            <td style={{width: widthPx, minWidth: widthPx, maxWidth: widthPx}}  >
                                                <span 
                                                className={
                                                    120 == row.received.code ? 'red' :
                                                    110 == row.received.code ? 'pink':
                                                    151 == row.received.code ? 'purple':
                                                    602 == row.received.code ? 'indigo':
                                                    301 == row.received.code ? 'blue': ''}>
                                                        {'[' + row.received.code + '] '}
                                                </span>
                                                    {row.received.Description}
                                            </td>
                                            <td  style={{width: widthPx, minWidth: widthPx, maxWidth: widthPx}}>
                                                <span 
                                                className={
                                                    120 == row.emit.code ? 'red' :
                                                    110 == row.emit.code ? 'pink':
                                                    151 == row.emit.code ? 'purple':
                                                    602 == row.emit.code ? 'indigo':
                                                    301 == row.emit.code ? 'blue': ''}>
                                                        {'[' + row.emit.code + '] '}
                                                </span>
                                                {row.emit.Description}
                                            </td>
                                            <td className="cellZoneSignal">
                                                {row.zone && ("00" + row.zone).slice(-3)}
                                            </td>
                                            {
                                                !archive &&
                                                <td>
                                                    <img onClick={() => {this.editModal(row)}} className="img-btn" title="Modifier" src="/img/edit.svg"/>
                                                    <img onClick={() => {this.deleteModal(row)}} className="img-btn img-btn-margin" title="Supprimer" src="/img/delete.svg"/>
                                                </td>
                                            }
                                        </tr>)
                                })
                            }
                        </tbody>
                    </table>
                }
            </div>
        )
    } 
}