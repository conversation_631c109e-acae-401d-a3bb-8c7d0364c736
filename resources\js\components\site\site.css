.layout-fixed{
    table-layout: fixed;
}
.title-overview{
    display: inline-block;
    width: calc(100% - 170px);;
    font-size: 16pt;
    font-weight: bold;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    overflow-wrap: break-word;
    vertical-align: middle;
}
.head-title-overview{
    display: table;
    width: 100%;
    padding: 10px 0px;
}
.overview-edit-icon{
    display: inline-block;
    width: 170px;
    height: 40px;
    text-align: right;
    font-size: 12pt;
    font-weight: normal;
    vertical-align: middle;
}
.overview-edit-img{
    padding: 5px;
    height: 30px;
    vertical-align: middle;
    box-shadow: 1px 1px 1px rgba(24, 42, 42, .1);
}
.overview-edit-img:hover{
    background-color: rgba(24, 42, 42, .1);
}
.dropdown-overview-edit{
    list-style-type: none;
    position: relative;
    padding: 5px;
    border: solid .5px rgba(0, 0, 0, .2);
    text-align: right;
    background-color: white;
    text-align: left;
}
.dropdown-overview-edit > span{
    display: block;
    font-size: 12px;
    border-top: solid .5px rgba(0, 0, 0, .2);
    padding: 15px 10px 10px 10px;
    line-height: 12px;
    cursor: pointer;
}
.dropdown-overview-edit>span:first-child{
    border-top: none;
    padding: 10px 10px 15px 10px;
}
.overview-break-overflow{
    display: inline-block;
    width: 99%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.overview-container div span{
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;

}
.cellNom{
    width: 250px;
    max-width: 250px;
    min-width: 250px;
    overflow: hidden;
}
.cellProm{
    text-align: center;
    width: 110px;
    max-width: 110px;
    min-width: 110px;
}
#tabHeaderOverview{
    display: table-row;
    height: 30px;
}
#tabHeaderOverview > ul{
    padding: 0;
    margin: 0;
    display: inline-block;
}
#tabHeaderOverview > ul >li{
    display: inline-block;
    padding: 20px;
    list-style:none;
    cursor: pointer;
}
#tabContentOverview{
    padding-top: 10px;
    border-top: solid .5px rgba(0, 0, 0, .2);
}
.active-tab{
    color: #366666;
    font-weight: bold;
    border-top: solid .5px rgba(0, 0, 0, .2);
    border-left: solid .5px rgba(0, 0, 0, .2);
    border-right: solid .5px rgba(0, 0, 0, .2);
    border-bottom: solid 3px white;
}
#tabContainer{
    height: 100%;
    padding: 5px 0px;
    /*border: solid .5px rgba(0, 0 , 0, .2);*/
}
.default th{
    background-color: white;
    color: #444;
}
.img-btn{
    display: inline-block;
    height: 26px;
    cursor: pointer;
    padding: 3px;
}
.img-btn-margin{
    margin-left: 20px;
}
.img-btn:hover{
    background-color: rgba(24, 42, 42, .1);
}
#searchSite {
    display: flex;
    justify-content: right;
}
#searchSite > div{
    box-sizing: border-box; 
    width: 300px;
    padding: 10px;
    align-self: center;
    display: flex;
    vertical-align: middle;
    border: solid .5px rgba(0, 0, 0, .1);
}
#searchSite > div > input{
    border: none;
    outline: none;
    flex-grow: 1;
    padding: 0px;
}
#searchSite > div > img{
    height: 20px;
    
}
#newSiteBtn{
    vertical-align: middle;
    font-size: 13pt;
    font-weight: normal;
}
.fix-cell-site{
    width: 200px;
    min-width: 200px;
    max-width: 200px;
}
.fix-cell-add{
    width: 150px;
    min-width: 150px;
    max-width: 150px;
}
#vigilanceTypeContainer{
    padding-left: 20px;
}
.cellAdresse > img {
    width: 15px;
    float: right;
    cursor: pointer;
}
.cellDayWeek{
    width: 12%;
    max-width: 12%;
    min-width: 12%;
}
.cellBeginWeek{
    width: 5%;
    max-width: 5%;
    min-width: 5%;
}
.cellChecked{
    background-color: #336666;
}