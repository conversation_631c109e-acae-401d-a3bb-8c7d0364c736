import React, { Component } from 'react'
import axios from 'axios'

import Modal from '../../modal/Modal'

export default class ZoneModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            nom: '',
            num: '',
            capteurId: 0,
            capteurs: null,
            error: "",
        }
        this.handleNumChange = this.handleNumChange.bind(this)
        this.handleNomChange = this.handleNomChange.bind(this)
        this.handleCapteurChange = this.handleCapteurChange.bind(this)
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
    }
    componentDidMount(){
        const {zone} = this.props
        if(zone)
            this.setState({
                nom: zone.nomZone,
                num: zone.NumZone,
                capteurId: zone.capteur && zone.capteur.idcapteur,
            })
        axios.get('/api/capteurs')
        .then(({data}) => {
            this.setState({
                capteurs: data,
            })
        })
    }
    handleNumChange(event){
        this.setState({
            num: event.target.value
        })
    }
    handleNomChange(event){
        this.setState({
            nom: event.target.value
        })
    }
    handleCapteurChange(event){
        this.setState({
            capteurId: event.target.value
        })
    }
    handleZoneChange(event){
        this.setState({
            zone: event.target.value
        })
    }
    handleSave(){
        this.setState({error: ""})
        let data = new FormData()
        const {num, nom, capteurId} = this.state
        const {siteId, action} = this.props
        data.append("num", ("00" + num).slice(-3))
        data.append("nom", nom)
        data.append("idcapteur", capteurId)
        data.append("idsite", siteId)
        axios.post(action, data)
        .then(() => {
            if(data.error)
                this.setState({error: data.error})
            else
                this.props.updateZones()
        })
    }
    handleCancel(){
        this.props.closeModal()
    }
    render(){
        const {capteurs, num, nom, capteurId} = this.state
        return (
            <Modal handleSave={this.handleSave} handleCancel={this.handleCancel}>
                <h3>Zone</h3>
                <div className="input-container">
                    <label>Numéro de la zone</label>
                    <input value={num} onChange={this.handleNumChange} type="number"/>
                </div>
                <div className="input-container">
                    <label>Nom</label>
                    <input value={nom} onChange={this.handleNomChange} type="text"/>
                </div>
                <div className="input-container">
                    <label>Matériel</label>
                    <select value={capteurId} onChange={this.handleCapteurChange}>
                        <option value=""/>
                        {
                        capteurs && capteurs.map( cpt => 
                            <option value={cpt.idcapteur} key={cpt.idcapteur}>
                                {'[' + cpt.reference + '] ' + cpt.nom}
                            </option>)
                        }
                    </select>
                </div>
            </Modal>
        )
    }
}