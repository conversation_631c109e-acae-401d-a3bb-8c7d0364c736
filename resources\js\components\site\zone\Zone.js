import React, { Component } from 'react'
import axios from 'axios'
import EditZoneModal from './EditZoneModal'
import DeleteZoneModal from './DeleteZoneModal'
import IconButton from '../../button/IconButton'

export default class Zone extends Component {
    constructor(props){
        super(props)
        this.state = {
            zone: null,
            zones: [],
            showAddModal: false,
            showEditModal: false,
            showDeleteModal: false,
            widthPx: ''
        }
        this.closeModal = this.closeModal.bind(this)
        this.addModal = this.addModal.bind(this)
        this.editModal = this.editModal.bind(this)
        this.updateData = this.updateData.bind(this)
    }
    componentDidMount(){
        this.updateData()
        this.resize()
    }
    resize() {
        if(this.container)
            this.setState({
                widthPx : ((this.container.offsetWidth - 170) / 2) + "px"
            })
    }
    closeModal(){
        this.setState({
            showAddModal: false,
            showEditModal: false,
            showDeleteModal: false,
        })
    }
    addModal(){
        this.setState({
            showAddModal: true
        })
    }
    editModal(s){
        this.setState({
            zone: s,
            showEditModal: true
        })
    }
    deleteModal(s){
        this.setState({
            zone: s,
            showDeleteModal: true
        })
    }
    updateData(){
        axios.get("/api/zones/site/" + this.props.siteId)
        .then(({data})=>{
            this.setState({
                zones: data,
                showAddModal: false,
                showEditModal:false,
                showDeleteModal: false,
            })
        })
    }
    render(){
        const {zone, zones, showAddModal, showEditModal,  showDeleteModal, widthPx} = this.state
        const {heightWindow, userId, siteId, archive} = this.props
        return (
            <div ref={el => (this.container = el)}>
                {showAddModal && <EditZoneModal 
                    action={"/api/zones/store"}
                    userId={userId}
                    siteId={siteId}
                    closeModal={this.closeModal}
                    updateZones={this.updateData}/>}

                {showEditModal && <EditZoneModal 
                    action={"/api/zones/update/"+ zone.idzone}
                    zone={zone}
                    userId={userId}
                    siteId={siteId}
                    closeModal={this.closeModal}
                    updateZones={this.updateData}/>}

                {showDeleteModal && <DeleteZoneModal 
                    action={"/api/zones/delete/"+ zone.idzone}
                    zone={zone}
                    closeModal={this.closeModal}
                    updateZones={this.updateData}/>}


                <div className="btn-label-container right" onClick={this.addModal}>
                    {
                        !archive &&
                        <IconButton onClick={this.addModal} label="Ajouter une zone" src="/img/add.svg"/>
                    }
                </div>

                {
                    widthPx &&
                    <table className="fixed_header default layout-fixed">
                        <thead>
                            <tr>
                                <th className="cellZoneNum">Num.</th>
                                <th style={{width: widthPx, minWidth: widthPx, maxWidth: widthPx}}>Nom</th>
                                <th style={{width: widthPx, minWidth: widthPx, maxWidth: widthPx}}>Matériel</th>
                                { !archive && <th></th> }
                            </tr>
                        </thead>
                        <tbody style={{height: (heightWindow - 490) + "px"}}>
                            {
                                zones && zones.map((row) => {
                                    return (
                                        <tr
                                            key={row.idzone}
                                        >
                                            <td className="cellZoneNum">
                                                {('000' + row.NumZone).slice(-3) }
                                            </td>
                                            <td style={{width: widthPx, minWidth: widthPx, maxWidth: widthPx}} title={row.nomZone}>
                                                {row.nomZone}
                                            </td>
                                            <td style={{width: widthPx, minWidth: widthPx, maxWidth: widthPx}} title={row.capteur && row.capteur.nom}>
                                                {row.capteur && ('[' + row.capteur.reference + '] '+ row.capteur.nom)}
                                            </td>
                                            {
                                                !archive &&
                                                <td>
                                                    <img onClick={() => {this.editModal(row)}} className="img-btn" title="Modifier" src="/img/edit.svg"/>
                                                    <img onClick={() => {this.deleteModal(row)}} className="img-btn img-btn-margin" title="Supprimer" src="/img/delete.svg"/>
                                                </td>
                                            }
                                        </tr>)
                                })
                            }
                        </tbody>
                    </table>
                }
            </div>
        )
    } 
}