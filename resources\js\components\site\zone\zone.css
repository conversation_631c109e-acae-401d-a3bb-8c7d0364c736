.fixed-front{
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    z-index: 1000;
}
#zoneContainer{
    display: table-cell;
    background-color: rgba(0, 0, 0, .3);
    vertical-align: middle;
    text-align: center
}
#zoneModal{
    display: inline-block;
    width: 300px;
    padding: 20px;
    background-color: white;
}
.cellZoneSignal{
    width: 70px;
    min-width: 70px;
    max-width: 70px;
    text-align: center;
}
.cellZoneNum{
    width: 70px;
    min-width: 70px;
    max-width: 70px;

}