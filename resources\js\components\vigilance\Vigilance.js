import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'

import Pointage from './pointage/Pointage'
import EditCommentModal from './commentaire/EditCommentModal'
import AlarmLogModal from '../pointage/AlarmLogModal'
import LoadingCallModal from './LoadingCallModal'
import Notification from '../rappel/notification/Notification'
import { formDataOption } from '../../../../auth'
import './vigilance.css'

export default class Vigilance extends Component {
    constructor(props) {
        super(props)
        this.state = {
            vigilances: null,
            indiceVigilance: null,
            vigilanceSite: null,
            dateVigilance: '',
            datetimeVg: '',
            currentDate: '',
            timeoutId: null,
            showSelectItem: false,
            currentItem: null,
            heightWindow: null,
            widthWindow: null,
            showEditVigilance: false,
            currentCommentaire: '',
            nomSite: '',
            idSite: '',
            commentaireSite: '',
            showSelection: false,
            agents: null,
            dateLastSignal: '',
            phoneAgent: '',
            isCheckphone: false,
            groupSites: [],
            showAlarmModal: false,
            showLoadingCallModal: false,
            title: 'BtN - TLS',
            notifications: [],
            callReminders: [],
            filteredCallReminders: [],
            lastNotificationUpdate: 0,
        }
        this.updateData = this.updateData.bind(this)
        this.hideSelectItem = this.hideSelectItem.bind(this)
        this.clickItem = this.clickItem.bind(this)
        this.toggleSelectItem = this.toggleSelectItem.bind(this)
        this.updateVigilance = this.updateVigilance.bind(this)
        this.toggleSelectionAgent = this.toggleSelectionAgent.bind(this)
        this.toggleLoading = this.toggleLoading.bind(this)
        this.handleClickGroup = this.handleClickGroup.bind(this)
        this.handleClickSite = this.handleClickSite.bind(this)
        this.handleBack = this.handleBack.bind(this)
        this.toggleAlarmModal = this.toggleAlarmModal.bind(this)
        this.makeCall = this.makeCall.bind(this)
        this.toggleLoadingCallModal = this.toggleLoadingCallModal.bind(this)
        this.setTimeoutUpdateNotification = this.setTimeoutUpdateNotification.bind(this)
        this.updateNotification = this.updateNotification.bind(this)
        this.updateCallReminders = this.updateCallReminders.bind(this)
        this.filterCallReminders = this.filterCallReminders.bind(this)
        this.forceNotificationUpdate = this.forceNotificationUpdate.bind(this)
    }

    // Method to load call reminders from localStorage
    updateCallReminders() {
        try {
            const remindersData = localStorage.getItem('call_reminders');

            if (!remindersData) {
                this.setState({ callReminders: [] }, () => {
                    this.filterCallReminders();
                });
                return;
            }

            try {
                const reminders = JSON.parse(remindersData);

                if (!Array.isArray(reminders)) {
                    console.error('call_reminders is not an array');
                    localStorage.setItem('call_reminders', JSON.stringify([]));
                    this.setState({ callReminders: [] }, () => {
                        this.filterCallReminders();
                    });
                    return;
                }

                this.setState({ callReminders: reminders }, () => {
                    this.filterCallReminders();
                });
            } catch (parseError) {
                console.error('Error parsing call_reminders JSON');
                localStorage.setItem('call_reminders', JSON.stringify([]));
                this.setState({ callReminders: [] }, () => {
                    this.filterCallReminders();
                });
            }
        } catch (error) {
            console.error('Unexpected error in updateCallReminders');
            this.setState({ callReminders: [] }, () => {
                this.filterCallReminders();
            });
        }
    }

    // Method to filter call reminders based on current context
    filterCallReminders() {
        const { callReminders, currentItem, idSite, vigilances } = this.state;

        if (!callReminders || callReminders.length === 0) {
            this.setState({ filteredCallReminders: [] });
            return;
        }

        let filteredReminders = [];

        // if (idSite) {
        //     // Filter by specific site ID
        //     filteredReminders = callReminders.filter(reminder => {
        //         // Direct match by site ID
        //         if (reminder.site_id === idSite) {
        //             return true;
        //         }

        //         // Try to match by site name if we have the site name in state
        //         const { nomSite } = this.state;
        //         if (nomSite && reminder.site &&
        //             reminder.site.toLowerCase() === nomSite.toLowerCase()) {
        //             return true;
        //         }

        //         return false;
        //     });
        // } else
        if (currentItem && vigilances && vigilances.length > 0) {
            // When a group is selected, check all sites within that group
            filteredReminders = callReminders.filter(reminder => {
                // First check if the reminder matches the group directly
                const matchesGroupName = reminder.group &&
                    currentItem.nom &&
                    reminder.group.toLowerCase() === currentItem.nom.toLowerCase();

                const matchesGroupId = currentItem.id &&
                    reminder.group_id === currentItem.id;

                if (matchesGroupName || matchesGroupId) {
                    return true;
                }

                // Then check if the reminder matches any site within the group
                return vigilances.some(site => {
                    const matchesSiteName = reminder.site &&
                        site.nom &&
                        reminder.site.toLowerCase() === site.nom.toLowerCase();

                    const matchesSiteId = site.idsite &&
                        reminder.site_id === site.idsite;

                    return matchesSiteName || matchesSiteId;
                });
            });
        } else if (currentItem) {
            // If we have a currentItem but no vigilances loaded yet, match by group
            filteredReminders = callReminders.filter(reminder => {
                const matchesGroupName = reminder.group &&
                    currentItem.nom &&
                    reminder.group.toLowerCase() === currentItem.nom.toLowerCase();

                const matchesGroupId = currentItem.id &&
                    reminder.group_id === currentItem.id;

                return matchesGroupName || matchesGroupId;
            });
        }

        this.setState({ filteredCallReminders: filteredReminders }, () => {
            // Force a re-render to ensure CSS classes are applied correctly
            if (process.env.NODE_ENV === 'development') {
                console.log('Vigilance filterCallReminders - updated filteredCallReminders:', filteredReminders.length);
            }
            // Force notification positioning update
            this.forceNotificationUpdate();
        });
    }

    // Method to force notification positioning update
    forceNotificationUpdate() {
        // Force a re-render by updating a dummy state
        this.setState({
            lastNotificationUpdate: Date.now()
        }, () => {
            // Use setTimeout to ensure DOM has updated
            setTimeout(() => {
                if (this.overviewContainer) {
                    // Force a style recalculation
                    this.overviewContainer.style.display = 'none';
                    this.overviewContainer.offsetHeight; // Trigger reflow
                    this.overviewContainer.style.display = '';
                }
            }, 0);
        });
    }

    makeCall(to) {
        to = to.trim()
        if (/^0[0-9]{9}$/.test(to)) {
            this.toggleLoadingCallModal(true)
            const { user } = this.props
            console.log('from: ' + user.extension + ', to: ' + to)
            let data = new FormData()
            data.append("user_id", user.id)
            data.append("from", user.extension)
            data.append("to", to)
            axios.post("/api/call/make_call", data, formDataOption)
                .then(({ data }) => {
                    console.log(data)
                    this.toggleLoadingCallModal(false)

                    // After making a call, update call reminders
                    this.updateCallReminders();
                })
                .catch((e) => {
                    console.error(e)
                    this.toggleLoadingCallModal(false)
                })
        }
        else
            console.log("erreur de format")
    }
    setTimeoutUpdateNotification() {
        if (this.state.timeoutId)
            clearTimeout(this.state.timeoutId)
        let timeoutId = setTimeout(this.updateNotification, 9000)
        this.setState({
            timeoutId: timeoutId
        })
    }
    updateNotification() {
        // axios.get('api/call/get_recall_data', formDataOption)
        //     .then(({ data }) => {
        //         console.log('updateNotification')
        //         this.setState({
        //             notifications: data.data,
        //             // currentDate: data.now,
        //         }, () => {
        //             this.setTimeoutUpdateNotification()
        //         })
        //     })
        //     .catch(() => {
        //         this.setTimeoutUpdateNotification()
        //     })

    }
    toggleAlarmModal(v, siteId) {
        if (!v)
            this.setState({
                showAlarmModal: false
            })
        else if (siteId) {
            this.setState({
                showAlarmModal: true,
                siteId: siteId
            })
        }
    }
    toggleLoadingCallModal(value) {
        this.setState({
            showLoadingCallModal: value
        })
    }

    handleBack() {
        this.setState({
            currentItem: null,
            idSite: '',
            nomSite: '',
            title: "BtN - TLS"
        }, () => {
            this.updateData(true);
            this.filterCallReminders();
        })
    }
    handleClickGroup(g) {
        this.setState({
            currentItem: g,
            title: "BtN - " + g.nom + " - TLS"
        }, () => {
            this.updateData(true);
            this.filterCallReminders();
        });
    }
    capitalizeFirstLetter(string) {
        const arrayString = string.replace(/\s\s+/g, ' ').trim().split(' ').map((s) => (
            s.charAt(0).toUpperCase() + s.slice(1).toLowerCase()
        ))
        return arrayString.join(' ')
    }
    toggleSelectionAgent(value) {
        this.setState({
            showSelection: value
        })
    }
    handleClickCommentVigilance(commentaire) {
        this.setState({
            showEditVigilance: true,
            isCheckphone: false,
            currentCommentaire: commentaire
        })
    }
    toggleEditVigilance(value) {
        this.setState({
            showEditVigilance: value
        })
    }
    setVigilanceShowState(data) {
        const { currentDate } = this.state
        let vigilances = this.getVigilanceInterval(currentDate)
        data.vigilances.map(({ dtarrived, sip }) => {
            let date = moment(dtarrived)
            for (let i = 0; i < vigilances.length; i++) {
                if (!vigilances[i].date && date.isAfter(vigilances[i].begin) && date.isBefore(vigilances[i].end)) {
                    vigilances[i].date = date.format('HH:mm')
                    vigilances[i].dateMoment = date
                }
                if (date.isAfter(vigilances[i].begin) && date.isBefore(vigilances[i].end)) {
                    if (!vigilances[i].count) vigilances[i].count = 1
                    else vigilances[i].count = vigilances[i].count + 1
                    if (!sip)
                        vigilances[i].checkphone = 0
                }
            }
        })

        let indiceVigilances = []
        for (let i = 0; i < vigilances.length; i++) {
            vigilances[i].color = this.getColorCardSite(vigilances[i])
            data.commentaires.forEach(cmt => {
                if (cmt.date_vigilance == vigilances[i].begin.clone().add('10', 'minutes').format('YYYY-MM-DD HH:mm:ss')) {
                    vigilances[i].commentaire = cmt.commentaire
                    vigilances[i].objet = cmt.objet
                }
            })
            if (vigilances[i].count)
                indiceVigilances.push(vigilances[i].count)
        }
        let indice = 0
        if (indiceVigilances.length)
            indice = indiceVigilances.reduce((a, b) => a + b, 0) / indiceVigilances.length
        this.setState({
            indiceVigilance: indice,
            vigilanceSite: vigilances,
            agents: data.agents
        }, () => {
            this.toggleLoading(false)
        })
    }
    updateVigilance() {
        const { idSite } = this.state
        axios.get('/api/vigilances/show/' + idSite)
            .then(({ data }) => {
                this.setVigilanceShowState(data)
            })
            .catch(() => {
                this.toggleLoading(false)
            })
    }
    updateVigilanceWithDetail(nom, phone_agent, last_vigilance, commentaire) {
        const { idSite } = this.state
        axios.get('/api/vigilances/show/' + idSite)
            .then(({ data }) => {
                this.setState({
                    nomSite: nom,
                    phoneAgent: phone_agent,
                    dateLastSignal: last_vigilance,
                    commentaireSite: commentaire,
                })
                this.setVigilanceShowState(data)
            })
            .catch(() => {
                this.toggleLoading(false)
            })
    }
    handleClickSite(idsite, nom, phone_agent, last_vigilance, commentaire) {
        this.toggleLoading(true)
        this.setState({
            idSite: idsite,
            nomSite: nom,
            phoneAgent: phone_agent,
            dateLastSignal: last_vigilance,
            commentaireSite: commentaire,
            title: "BtN - " + nom + " - TLS"
        }, () => {
            this.updateVigilanceWithDetail(nom, phone_agent, last_vigilance, commentaire);
            this.filterCallReminders();
        })
    }
    getVigilanceInterval(time) {
        let currentVigilance = moment(time, "YYYY-MM-DD HH:mm:ss")
        let intervals = []
        if (currentVigilance.isAfter(moment(currentVigilance.format("YYYY-MM-DD") + " 05:50:00"))
            && currentVigilance.isBefore(moment(currentVigilance.format("YYYY-MM-DD") + " 17:50:00"))) {
            let vigilanceJour = moment(currentVigilance.format("YYYY-MM-DD") + " 05:50:00")
            while (vigilanceJour.isBefore(currentVigilance)) {
                let begin = vigilanceJour.clone()
                let nom = vigilanceJour.clone().add('10', 'minutes').format('HH:mm')
                let end = vigilanceJour.clone().add('1', 'hour').clone()
                intervals.push({
                    begin: begin,
                    nom: nom,
                    end: end,
                    checkphone: 1,
                })
                vigilanceJour.add('1', 'hour')
            }
        }
        else {
            let vigilanceNuit = moment(currentVigilance.format("YYYY-MM-DD") + " 17:50:00")
            if (currentVigilance.isBefore(moment(currentVigilance.format("YYYY-MM-DD") + " 05:50:00")))
                vigilanceNuit = vigilanceNuit.subtract("1", "day")
            while (vigilanceNuit.isBefore(currentVigilance)) {
                let begin = vigilanceNuit.clone()
                let nom = vigilanceNuit.clone().add('10', 'minutes').format('HH:mm')
                let end = vigilanceNuit.clone().add('30', 'minutes').clone()
                intervals.push({
                    begin: begin,
                    nom: nom,
                    end: end,
                    checkphone: 1,
                })
                vigilanceNuit.add('30', 'minutes')
            }
        }
        return intervals.reverse()
    }
    resize() {
        this.setState({
            heightWindow: window.innerHeight,
            widthWindow: window.innerWidth
        }, () => {
            // Re-filter call reminders after resize to ensure proper positioning
            this.filterCallReminders();
        });
    }
    clickItem(value) {
        if (value)
            document.title = value.nom + ' - Vigilance TLS'
        else
            document.title = 'Vigilance - TLS'
        this.setState({
            currentItem: value,
            vigilanceSite: null,
            showSelectItem: false,
            idSite: '',
            nomSite: ''
        })
    }
    toggleSelectItem() {
        this.setState({
            showSelectItem: !this.state.showSelectItem
        })
    }
    setShowSelectItem(value) {
        this.setState({
            showSelectItem: value,
            showSelection: value
        })
    }
    hideSelectItem() {
        this.setState({
            showSelectItem: false
        })
    }
    toggleLoading(load) {
        this.props.toggleLoading(load)
    }
    setTimeoutUpdateData() {
        if (this.state.timeoutId)
            clearTimeout(this.state.timeoutId)
        let timeoutId = setTimeout(() => this.updateData(), 60000)
        this.setState({
            timeoutId: timeoutId
        })
    }
    updateData(loading) {
        const { currentItem, timeoutId } = this.state
        const { idSite } = this.state
        if (timeoutId)
            clearTimeout(timeoutId)
        console.log('updateVigilance')
        this.setState({
            currentItem: currentItem
        })
        if (loading) {
            this.toggleLoading(true)
            this.setState({
                idSite: null
            })
        }

        let url = ''
        if (currentItem) {
            url = '/api/vigilances?group_id=' + currentItem.id
            if (idSite)
                url = url + '&site_id=' + idSite
            url = url + '&username=' + localStorage.getItem('username') + '&secret=' + localStorage.getItem('secret')
            axios.get(url)
                .then(({ data }) => {
                    for (let i = 0; i < data.vigilances.length; i++) {
                        data.vigilances[i].color = this.getColorCard(data.vigilances[i], data.datetime_vigilance)
                        data.vigilances[i].pointage_ok = false
                        for (let j = 0; j < data.commentaires.length; j++)
                            if (data.commentaires[j].site_id == data.vigilances[i].idsite) {
                                data.vigilances[i].commentaire = data.commentaires[j].commentaire
                                data.vigilances[i].objet = data.commentaires[j].objet
                            }
                        for (let j = 0; j < data.pointages.length; j++)
                            if (data.pointages[j].site_id == data.vigilances[i].idsite)
                                data.vigilances[i].pointage_ok = true
                    }
                    if (data.current_vigilance)
                        this.setVigilanceShowState(data.current_vigilance)
                    this.setState({
                        vigilances: data.vigilances,
                        dateVigilance: data.date_vigilance,
                        datetimeVg: data.datetime_vigilance,
                        currentDate: data.current_datetime
                    }, () => {
                        if (loading) {
                            this.toggleEditVigilance(false)
                        }
                        this.toggleLoading(false)
                        // After vigilances are loaded, filter call reminders again
                        this.filterCallReminders();
                        //this.setTimeoutUpdateData()
                    })
                })
                .catch(() => {
                    this.props.toggleLoading(false)
                    // setTimeout(() => {
                    //     this.updateData()
                    // }, 1000)
                })
        }
        else {
            url = '/api/group_sites'
            axios.get(url)
                .then(({ data }) => {
                    this.setState({
                        groupSites: data.groups,
                        dateVigilance: data.date_vigilance,
                        currentDate: data.current_datetime,
                    })
                    this.toggleLoading(false)
                })
                .catch(() => {
                    setTimeout(() => {
                        this.updateData()
                    }, 10000)
                })
        }
    }
    componentDidMount() {
        this.updateData(true)
        window.addEventListener("resize", this.resize.bind(this))
        this.resize()
        document.title = this.state.title
        this.updateNotification()

        // Load call reminders on mount
        this.updateCallReminders();

        // Set up interval to periodically check for new call reminders
        this.callRemindersInterval = setInterval(() => {
            this.updateCallReminders();
        }, 10000); // Check every 10 seconds
    }
    clearTimeoutVigilance() {
        if (this.state.timeoutId)
            clearTimeout(this.state.timeoutId)
        this.setState({
            timeoutId: null
        })
    }
    componentDidUpdate(prevProps, prevState) {
        if (prevState.title !== this.state.title) {
            document.title = this.state.title
        }

        // Re-filter call reminders if relevant state has changed
        if (prevState.callReminders !== this.state.callReminders ||
            prevState.currentItem !== this.state.currentItem ||
            prevState.vigilances !== this.state.vigilances ||
            prevState.idSite !== this.state.idSite) {
            this.filterCallReminders();
        }
    }
    componentWillUnmount() {
        this.clearTimeoutVigilance()
        const { timeoutId } = this.state
        if (timeoutId)
            clearTimeout(timeoutId)

        // Clear the call reminders interval
        if (this.callRemindersInterval) {
            clearInterval(this.callRemindersInterval);
        }
    }
    getColorCard(vigilance, dateVigilance) {
        let vigilanceArrived = vigilance.vigilance
        let color = 'pink'
        if (vigilanceArrived) {
            if (vigilance.sip)
                color = 'purple'
            else color = 'primary'
        }
        else if (!vigilance.last_vigilance || moment(vigilance.last_vigilance).isBefore(moment().subtract('1', 'day')))
            color = 'orange'
        return color
    }
    getColorCardSite(vigilance) {
        let color = 'card-pink'
        if (vigilance.dateMoment) {
            if (vigilance.checkphone)
                color = 'card-purple'
            else color = 'card-primary'
        }
        return color
    }
    render() {
        const { isCheckphone, vigilances, dateVigilance, showSelection, datetimeVg, commentaireSite,
            agents, currentDate, phoneAgent, dateLastSignal, groupSites, showEditVigilance, currentCommentaire,
            currentItem, vigilanceSite, indiceVigilance, nomSite, idSite, heightWindow, showAlarmModal, siteId,
            filteredCallReminders } = this.state;
        const { user } = this.props;

        // Determine if notifications are present
        const hasNotifications = (['root', 'room'].includes(user.role) && filteredCallReminders.length > 0);

        // Debug logging for notification positioning issues
        if (process.env.NODE_ENV === 'development') {
            console.log('Vigilance render - hasNotifications:', hasNotifications,
                'user.role:', user.role,
                'filteredCallReminders.length:', filteredCallReminders.length);
        }

        // Calculate height adjustment for notifications
        const notificationAdjustment = hasNotifications ? 120 : 0;

        return <>
            {/* {
                (['root', 'room'].includes(user.role) && notifications.length > 0) &&
                <Notification data={notifications} clickItem={this.makeCall} />
            } */}
            {
                (['root', 'room'].includes(user.role) && filteredCallReminders.length > 0) &&
                <Notification data={filteredCallReminders} clickItem={this.makeCall} type="call_reminder" />
            }
            {
                this.state.showLoadingCallModal &&
                <LoadingCallModal loading={this.state.showLoadingCallModal} />
            }
            {
                currentItem ?
                    <div className="table" onClick={() => { this.setShowSelectItem(false) }}>
                        <div id="vigilanceContainer">
                            <h3>
                                <div className="table">
                                    <div className="cell back-vigilance">
                                        <img onClick={this.handleBack} src="/img/back_arrow.svg" />
                                    </div>
                                    <div className="cell refresh-vigilance">
                                        <img onClick={() => this.updateData(true)} src="/img/refresh_arrow.svg" />
                                    </div>
                                    <div className="cell center" style={{ cursor: "pointer" }}>
                                        <div id="selectBox">
                                            <div id="itemSelected">
                                                <span className="item-selected">
                                                    {currentItem.nom}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="cell right heure-vigilance">
                                        <span className="hour-label">
                                            {dateVigilance}
                                        </span>
                                    </div>
                                </div>
                            </h3>
                            <div className="overflow-auto" style={{ height: (heightWindow - 120) + "px" }}>
                                {
                                    vigilances && vigilances.map(vg => (
                                        <div key={vg.idsite} className='card'>
                                            <div className={"card-" + vg.color}>
                                                <div className="right">
                                                    {
                                                        user.role != 'client' &&
                                                        <div>
                                                            {
                                                                (vg.objet) &&
                                                                <img style={{ height: '15px', cursor: "pointer" }}
                                                                    title={vg.commentaire}
                                                                    onClick={() => { this.handleClickCommentVigilance({ site_id: vg.idsite, objet: vg.objet, text: vg.commentaire, vigilance: datetimeVg, nom: vg.nom }) }}
                                                                    src={"/img/edit_" + vg.color + ".svg"} />
                                                            }
                                                        </div>
                                                    }
                                                </div>
                                                <h3 onClick={() => { this.handleClickSite(vg.idsite, vg.nom, vg.phone_agent, vg.last_vigilance, vg.commentaire) }} title={vg.nom + (vg.commentaire ? ' (' + vg.commentaire + ')' : '')}>
                                                    <span className={(user.role != 'client' && !vg.pointage_ok) ? "color-inverse" : ""}>{vg.nom && this.capitalizeFirstLetter(vg.nom)}</span>
                                                </h3>
                                                <div>
                                                    <div className="table">
                                                        <div className="cell">
                                                            {vg.vigilance && moment(vg.vigilance).format('HH:mm')}
                                                        </div>
                                                        <div className="cell right">
                                                            {
                                                                (vg.nb_vigilance && vg.nb_vigilance > 1) &&
                                                                <span className="badge bg-secondary">
                                                                    {vg.nb_vigilance}
                                                                </span>
                                                            }
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    ))
                                }
                                {
                                    (user.role != 'client' && showEditVigilance) &&
                                    <EditCommentModal
                                        isCheckphone={isCheckphone}
                                        updateData={this.updateData}
                                        action="/api/vigilances/save_commentaire"
                                        commentaire={currentCommentaire}
                                        closeModal={() => { this.toggleEditVigilance(false) }} />
                                }
                            </div>
                        </div>
                        <div ref={el => (this.overviewContainer = el)}
                            style={{ height: heightWindow + 'px' }}
                            id="vigilanceDetail"
                            key={`vigilanceDetail-${hasNotifications ? 'with-notifications' : 'no-notifications'}`}
                            className={hasNotifications ? 'notification-margin' : ''}>
                            {
                                showAlarmModal &&
                                <AlarmLogModal site_id={siteId} heightWindow={heightWindow} closeModal={() => this.toggleAlarmModal(false)} />
                            }
                            {
                                vigilanceSite ?
                                    <div>
                                        <h3>
                                            <div className="table">
                                                <div className="cell site-name-cell">
                                                    <div className="site-name-header">
                                                        <div className="site-name-text">{nomSite}</div>
                                                        <img onClick={() => { this.toggleAlarmModal(true, idSite) }} className="site-name-icon add-pointage-btn" src="/img/historique.svg" />
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="table">
                                                <div className="cell secondary">{commentaireSite}</div>
                                                {
                                                    indiceVigilance > 0 &&
                                                    <div id="cellIndice">
                                                        <span className={indiceVigilance > 3 ? "badge bg-pink" : "badge bg-dark"}>
                                                            {Number.parseFloat(indiceVigilance).toFixed(2)}
                                                        </span>
                                                    </div>
                                                }
                                            </div>
                                        </h3>

                                        <div className={`overflow-auto-vigilance ${hasNotifications ? 'with-notification' : ''}`}
                                            key={`overflow-vigilance-${hasNotifications ? 'with-notifications' : 'no-notifications'}`}
                                            style={{ height: (heightWindow - (hasNotifications ? 220 : 160)) + "px" }}>
                                            {
                                                <Pointage
                                                    user={user}
                                                    showSelection={showSelection}
                                                    agents={agents}
                                                    toggleLoading={this.toggleLoading}
                                                    updateData={this.updateVigilance}
                                                    toggleSelection={this.toggleSelectionAgent} />
                                            }
                                            {
                                                vigilanceSite.map((v, index) => (
                                                    <div key={index} className="v-card">
                                                        <div className={v.color}>
                                                            <div className="table ">
                                                                <div className="cell">
                                                                    {
                                                                        (user.role != 'client' && (index < 2 || (!dateLastSignal || moment(dateLastSignal).isBefore(moment().subtract('1', 'day'))))) &&
                                                                        <div>
                                                                            <img style={{ width: '10px', cursor: "pointer" }}
                                                                                onClick={() => { this.handleClickCommentVigilance({ site_id: idSite, objet: v.objet, text: v.commentaire, vigilance: (datetimeVg ? (datetimeVg.split(' ')[0] + ' ' + v.nom + ':00') : ''), nom: nomSite }) }}
                                                                                src="/img/edit_default.svg" />
                                                                        </div>
                                                                    }
                                                                    <h3>
                                                                        <span className={!v.date ? "fade" : ""}>
                                                                            {!v.date && v.nom} {v.date && " [ " + v.date + " ]"}
                                                                        </span>
                                                                    </h3>
                                                                </div>
                                                                <div className="cell right">
                                                                    {
                                                                        (v.count && v.count > 1) &&
                                                                        <span className="badge bg-light">
                                                                            {v.count}
                                                                        </span>
                                                                    }
                                                                </div>
                                                            </div>

                                                            <div className="vg-cmt-container">
                                                                <span>{v.objet}</span>
                                                                <div style={{ color: "#aaa" }}>
                                                                    <div>{v.commentaire}</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                ))
                                            }
                                        </div>
                                        <div className="center">
                                            <div id="phoneAgent">
                                                {
                                                    phoneAgent ?
                                                        phoneAgent.split(",").map((c, idx) => (
                                                            <span key={idx} className='phone-call' onClick={() => this.makeCall(c)}>{c}</span>
                                                        ))
                                                        :
                                                        ''
                                                }<br />
                                                <span id="h3Vigilance">{nomSite}</span>
                                            </div>
                                        </div>
                                    </div>
                                    :
                                    <div className="img-bg-container">
                                        <img className="img-bg-overview" src="/img/tls_background.svg" />
                                    </div>
                            }
                        </div>
                    </div>
                    :
                    <div className="table" onClick={() => { this.setShowSelectItem(false) }}>
                        <div id="vigilanceContainer">
                            <h3>
                                <div className="table">
                                    <div className="cell heure-vigilance">
                                        <span className="hour-label">
                                            {currentDate && moment(currentDate).format('HH:mm')}
                                        </span>
                                    </div>
                                    <div className="cell center" style={{ cursor: "pointer" }}>
                                        <div id="selectBox">
                                            <div id="itemSelected">
                                                <span className="item-selected">
                                                    BOUTON
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="cell right heure-vigilance">
                                        <span className="hour-label">
                                            {dateVigilance}
                                        </span>
                                    </div>
                                </div>
                            </h3>
                            <div className="overflow-auto" style={{ height: (heightWindow - 120) + "px" }}>
                                {
                                    groupSites.map(g => {
                                        return <div key={g.id} className='card'>
                                            <div className="card-secondary">
                                                <div>
                                                    <div style={{ display: "inline-block", width: "50%" }}>

                                                    </div>
                                                    <div style={{ display: "inline-block", width: "50%", textAlign: "right" }}>

                                                    </div>
                                                </div>
                                                <h3 onClick={() => { this.handleClickGroup(g) }} title={g.id + " " + g.nom}>
                                                    <span>
                                                        {g.nom}
                                                    </span>
                                                </h3>
                                                <div>
                                                    <div className="table">
                                                        <div className="cell right">
                                                            <span className="badge bg-light">
                                                                {g.nb_site}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                    )
                                }
                                {
                                    showEditVigilance &&
                                    <EditCommentModal isCheckphone={isCheckphone} updateData={this.updateData} action="/api/vigilances/save_commentaire" commentaire={currentCommentaire} closeModal={() => { this.toggleEditVigilance(false) }} />
                                }
                            </div>
                        </div>
                    </div>
            }
        </>
    }
}


