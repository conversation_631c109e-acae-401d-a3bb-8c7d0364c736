import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'

import './historique.css'
import 'react-datepicker/dist/react-datepicker.css'
import LoadingData from '../../loading/LoadingData'

export default class Historique extends Component {
    constructor(props){
        super(props)
        this.state = {
            historiques: null,
            begin: moment().toDate(),
            end: moment().toDate(),
            loading: true
        }
    }

    componentDidMount(){
        console.log(this.props.data)
        this.getHistorique()
    }

    getHistorique(){
        this.setState({
            loading: true
        })
        const {site} = this.props
        axios.get('/api/vigilances/alarm/' + site.idsite + '?username=' + localStorage.getItem("username") + '&secret=' + localStorage.getItem("secret"))
        .then(({data})=> {
            console.log(data)
            this.setState({
                historiques: data.alarms,
                loading: false
            })
        })
        .catch((e) => {
            console.log(e)
            this.setState({
                loading: false
            })
        })
    }
    getColor(code){
        let color = (
            120 == code ? 'd50000':
            132 == code ? 'c62828':
            133 == code ? 'c62828':
            134 == code ? 'c62828':
            130 == code ? 'f44336':
            131 == code ? 'c51162':
            137 == code ? 'ad1457':
            140 == code ? 'd500f9':
            100 == code ? 'e91e63':
            101 == code ? 'ff6d00':
            110 == code ? 'ef6c00':
            151 == code ? '8d6e63':
            111 == code ? 'ff9800':
            117 == code ? 'ffab00':
            112 == code ? 'ff8f00':
            113 == code ? 'ffb300':
            102 == code ? 'dd2c00':
            139 == code ? 'd84315':
            384 == code ? '673ab7':
            301 == code ? '6200ea':
            302 == code ? '6200ea':
            350 == code ? '4527a0':
            1000 == code ? '78909c':
            [400, 401, 402, 403, 404, 405, 407, 406, 408, 409, 441, 442, 456, 454].includes(parseInt(code)) ? '7cb342': ''
        )
        return '#' + color
    }

    getAlarm(row){
        if(row.codeTevent == '301')
            if(row.eventQualify == 3)
                return 'Courant rétablit'
            else return 'Coupure de courant JIRAMA 220V'
        else if(row.alarm)
            if(row.eventQualify == 3)
                return row.alarm.replace("Armement/Désarmement", "Armement")
            else
                return row.alarm.replace("Armement/Désarmement", "Désarmement")
        else 
            return 'Alarme non définie'
    }

    render(){
        const {historiques, loading} = this.state
        const {heightWindow, site} = this.props
        return (
            <div>
                {
                    loading ?
                        <LoadingData/>
                    : historiques.length == 0 ?
                        <div className='secondary'>Aucun signal trouvé durant ce service</div>
                    :
                        <table className="fixed_header default layout-fixed">
                            <thead>
                                <tr>
                                    <th className="cellDate">Date</th>
                                    <th className="cellAlarm">Alarme</th>
                                    <th>
                                        {site.pointeuse ? 'Agent' : 'Zone'}
                                    </th>
                                </tr>
                            </thead>
                            <tbody style={{height: (heightWindow / 1.8) + "px"}}>
                                {
                                    historiques && historiques.map((row) =>{
                                        return (
                                            <tr style={{color: this.getColor(row.codeTevent)}}
                                                key={row.idademco}
                                                onDoubleClick={() => {this.handleClickRow(row)}}
                                            >
                                                <td className="cellDate">{moment(row.dtarrived).format('DD/MM/YYYY HH:mm')}</td>
                                                <td className="cellAlarm" title={this.getAlarm(row)}>
                                                    {'[' + row.codeTevent + '] ' + this.getAlarm(row)}
                                                </td>
                                                {
                                                    site.pointeuse ?
                                                        <td style={{color: '#888'}}>
                                                            {
                                                                row.agent_id ?
                                                                    (
                                                                        row.societe_id == 1 ? 'DGM-' + row.numero_employe :
                                                                        row.societe_id == 2 ? 'SOIT-' + row.num_emp_soit : 
                                                                        row.societe_id == 3 ? 'ST-' + row.numero_stagiaire :
                                                                        row.societe_id == 4 ? 'SM' :
                                                                        row.numero_employe ? row.numero_employe :
                                                                        row.numero_stagiaire ? row.numero_stagiaire :
                                                                        'Ndf'
                                                                    )
                                                                :
                                                                ''
                                                            } {row.nom_agent}
                                                        </td>
                                                    : row.sip ?
                                                        <td style={{color: '#888'}}>
                                                            Check-phone, SIP: {row.sip}
                                                        </td>
                                                    :
                                                        <td>
                                                            {row.numZone ? ('000' + row.numZone).slice(-3) : ''} 
                                                            <span style={{color: '#888'}}>
                                                                {row.nomZone ? '(' + row.nomZone + ')' : ''}
                                                            </span>
                                                        </td>

                                                }
                                            </tr>)
                                    })
                                }
                            </tbody>
                        </table>
                }
            </div>
        )
    } 
}