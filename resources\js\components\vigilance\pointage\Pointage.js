import React, { Component } from 'react'
import axios from 'axios'

import './pointage.css'

export default class Pointage extends Component {
    constructor(props){
        super(props)
        this.state = {
        }
        this.handleCheckSelection = this.handleCheckSelection.bind(this)
        this.handleUncheckSelection = this.handleUncheckSelection.bind(this)
        this.toggleShowSelection = this.toggleShowSelection.bind(this)
    }
    displayNom(string){
        const  arrayString = string.replace(/\s\s+/g, ' ').trim().split(' ')
        let displayArray = []
        if(arrayString.length > 2){
            if(arrayString[0] != null)
                displayArray.push(arrayString[0].substring(0, 3).toUpperCase())
            if(arrayString[1] != null)
                displayArray.push(arrayString[1].substring(0, 7).toUpperCase())
        }
        else displayArray.push(arrayString[0].substring(0, 10).toUpperCase())
        /*if(arrayString[2] != null)
            displayArray.push(arrayString[2].substring(0, 1).toUpperCase())*/
        return displayArray.join(' ')
    }
    handleCheckSelection(agent_id, site_id){
        this.props.toggleLoading(true)
        let agents = []
        agents.push(agent_id)
        let data = {
            agents: agents,
            site_id: site_id,
            vigilance: 1,
            username: localStorage.getItem('username'),
            secret: localStorage.getItem('secret')
        }
        axios.post('/api/pointages/store', data)
        .then(() => {
            this.props.toggleSelection(false)
            this.props.updateData()
        })
        .catch((e) => {
            this.props.toggleLoading(false)
        })
    }
    handleUncheckSelection(e, id){
        e.stopPropagation()
        this.props.toggleLoading(true)
        console.log(e, id)
        let data = {
            username: localStorage.getItem('username'),
            secret: localStorage.getItem('secret')
        }
        axios.post('/api/pointages/delete/' + id, data)
        .then(() => {
            this.props.toggleSelection(false)
            this.props.updateData()
        })
        .catch(() => {
            this.props.toggleLoading(false)
        })
    }
    toggleShowSelection(e){
        e.stopPropagation()
        this.props.toggleSelection(!this.props.showSelection)
    }
    componentDidMount(){
    }
    render(){
        const {agents, showSelection, user} = this.props
        return (
            <div className="agent-pointage-container">
                <div onClick={this.toggleShowSelection} className="agent-selected">
                    {
                        (agents && agents.checked && agents.checked.length > 0) ? 
                        agents.checked.map((ag) => (
                            <span key={ag.agent_id} title={ag.numero_employe ? ag.numero_employe : ag.numero_stagiaire} className="agent-item">
                                {
                                    ag.nom && this.displayNom(ag.nom)
                                }
                                {
                                    user.role != 'client' && 
                                    <img onClick={(e) => {this.handleUncheckSelection(e, ag.pointage_id)}} src="/img/remove_item.svg"/>
                                }
                            </span>
                        ))
                        :
                        <span className="agent-item">
                            AGENT
                        </span>
                    }
                </div>
                { 
                    (user.role != 'client' && showSelection) &&
                    <div className="agent-not-selected">
                        {
                            (agents && agents.unchecked) &&
                            agents.unchecked.map((ag) => (
                                <div key={ag.agent_id} className="agent-item-line">
                                    <span onClick={() => this.handleCheckSelection(ag.agent_id, ag.site_id)}>
                                        {ag.nom && ag.nom.toUpperCase()} <span className="numero-agent">{ag.numero_employe ? ag.numero_employe : ag.numero_stagiaire}</span>
                                    </span>
                                </div>
                            ))
                        }
                    </div>
                }
            </div>
        )
    }
}