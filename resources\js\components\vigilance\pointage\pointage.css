.agent-pointage-container{
    margin-bottom: 10px;
    text-align: center;
}
.agent-selected{
    padding: 10px;
    border: solid .5px rgba(0, 0, 0, .1);
}
.agent-item{
    display: inline-block;
    margin: 0px 5px;
    padding: 5px;
    line-height: 14px;
    font-size: 14px;
    background-color: whitesmoke;
}
.agent-item > img{
    vertical-align: middle;
    height: 14px;
    padding-left: 5px;
    cursor: pointer;
}
.numero-agent{
    vertical-align: middle;
    opacity: .7;
}
.agent-not-selected{
    padding: 0px 7px;
    position: relative;
    background-color: white;
    border: solid .5px rgba(0, 0, 0, .1);
    box-shadow: 2px 2px 2px rgba(0, 0, 0, .1);
}
.agent-item-line{
    padding: 7px 0px;
}
.agent-item-line > span{
    cursor: pointer;
}
.agent-item-line:last-child{
    border-bottom: none;
}