import React, { Component } from 'react'
import axios from 'axios'

import './select.css'

export default class SelectGroupSite extends Component {
    constructor(props){
        super(props)
        this.state = {
            groups: null,
            showItem: false
        }
        this.toggleSelect = this.toggleSelect.bind(this)
    }
    setList(){
        axios.get('/api/group_sites?username=' + localStorage.getItem('username') + '&secret=' + localStorage.getItem('secret'))
        .then(({data}) => {
            this.setState({
                groups: data
            })
            this.props.updateData(data[0], true)
        })
        .catch(() => {
            setTimeout(() => {
                this.setList()
            }, 10000)
        })
    }
    componentDidMount(){
        this.setList()
    }
    toggleSelect(event){
        event.stopPropagation()
        this.props.toggleSelect()
    }
    render(){
        const {groups} = this.state
        const {showItem, currentItem, updateData} = this.props
        return (
            <div id="selectBox">
                <div onClick={this.toggleSelect} id="itemSelected">
                    <span className="item-selected">
                        {
                            currentItem ? currentItem.nom : 'Charging' 
                        }
                    </span>
                </div>
                {
                    showItem
                    &&
                    <div id="itemNotSelected">
                        <span className="border-bottom" onClick={() => updateData(currentItem, true)}>Actualiser</span>
                        {
                            (groups && groups.length > 1) &&
                            groups.map((item) => (
                                <span onClick={() => {this.props.clickItem(item);this.props.updateData(item, true)}} key={item.id}>{item.nom}</span>
                            ))
                        }
                    </div>
                }
            </div>
        )
    }
}