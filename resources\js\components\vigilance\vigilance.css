#vigilanceContainer {
    display: table-cell;
    padding: 20px;
    padding-top: 0px;
    vertical-align: top;
    text-align: justify;
    min-width: 350px;
    max-width: 70%;
    width: 70%;
    min-width: 70%;
}

#vigilanceDetail {
    display: table-cell;
    width: 150px;
    min-width: 150px;
    max-width: 150px;
    padding: 20px;
    box-shadow: -3px 0px 3px rgba(0, 0, 0, .1);
}

.card {
    display: inline-block;
    padding: 10px;
    vertical-align: top;
}

.card>div {
    border-top: .5px solid rgba(0, 0, 0, .1);
    border-right: .5px solid rgba(0, 0, 0, .1);
    border-bottom: .5px solid rgba(0, 0, 0, .1);
    padding: 10px;
    cursor: pointer;
}

.card>div>div {
    height: 20px;
    padding-bottom: 10px;
}

.card-primary {
    border-left: 5px solid #366666;
}

.card-primary h3 {
    color: #366666;
}

.card-primary h3 span.color-inverse {
    background-color: #366666;
    color: whitesmoke;
}

.card-secondary {
    border-left: 5px solid #888;
}

.card-secondary h3 {
    color: #888;
}

.card-secondary h3 span.color-inverse {
    background-color: #888;
    color: whitesmoke;
}

.card-pink {
    border-left: 5px solid #e91e63;
}

.card-pink h3 {
    color: #e91e63;
}

.card-pink h3 span.color-inverse {
    background-color: #e91e63;
    color: whitesmoke;
}

.card-amber {
    border-left: 5px solid #ffc107;
}

.card-amber h3 {
    color: #ffc107;
}

.card-amber h3 span.color-inverse {
    background-color: #ffc107;
    color: whitesmoke;
}

.card-orange {
    border-left: 5px solid #ef6c00;
}

.card-orange h3 {
    color: #ef6c00;
}

.card-orange h3 span.color-inverse {
    background-color: #ef6c00;
    color: whitesmoke;
}

.card-purple {
    border-left: 5px solid #9c27b0;
}

.card-purple h3 {
    color: #9c27b0;
}

.card-purple h3 span.color-inverse {
    background-color: #9c27b0;
    color: whitesmoke;
}

@media screen and (max-width: 1100px) {
    .card {
        width: 100%;
        min-width: 100%;
        max-width: 100%;
    }
}

@media screen and (min-width: 1100px) and (max-width: 1360px) {
    .card {
        width: 50%;
        min-width: 50%;
        max-width: 50%;
    }
}

@media screen and (min-width: 1360px) and (max-width: 1620px) {
    .card {
        width: 33.33%;
        min-width: 33.33%;
        max-width: 33.33%;
    }
}

@media screen and (min-width: 1620px) and (max-width: 1880px) {
    .card {
        width: 25%;
        min-width: 25%;
        max-width: 25%;
    }
}

@media screen and (min-width: 1880px) and (max-width: 2140px) {
    .card {
        width: 20%;
        min-width: 20%;
        max-width: 20%;
    }
}

@media all and (min-width: 2140px) {
    .card {
        width: 16.66%;
        min-width: 16.66%;
        max-width: 16.66%;
    }
}

.card>div>h3 {
    display: block;
    width: calc(100% - 30px);
    margin-top: 0px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    /* cursor: pointer; */
    /* vertical-align: middle; */
}

h3.border-pointage {
    border-left: solid 5px #e91e63;
    padding-left: 5px;
}

.comment-vigilance {
    width: 10px;
    cursor: pointer;
    opacity: .8;
}

.comment-vigilance:hover {
    opacity: 1;
}

.header-vigilance>img {
    display: inline-block;
    padding-left: 10px;
    width: 25px;
    vertical-align: middle;
}

.heure-vigilance {
    width: 100px;
}

.back-vigilance {
    padding: 0px 10px;
    padding-top: 9px;
    width: 40px;
    height: 100%;
}

.back-vigilance>img {
    width: 25px;
    cursor: pointer;
}

.refresh-vigilance {
    padding: 0px 10px;
    padding-top: 9px;
    width: 45px;
    height: 100%;
}

.refresh-vigilance>img {
    width: 30px;
    cursor: pointer;
}

.hour-label {
    border: solid .5px rgba(0, 0, 0, .1);
    height: 100%;
    padding: 20px;
}

.v-card {
    padding: 5px 0px;
}

.v-card>div {
    padding: 20px 10px;
    border-right: .5px solid rgba(0, 0, 0, .1);
    border-top: .5px solid rgba(0, 0, 0, .1);
    border-bottom: .5px solid rgba(0, 0, 0, .1);
}

h3 .fade {
    color: rgba(233, 30, 99, .4);
}

.v-card h3 {
    margin: 5px 0px;
}

.v-card>div>h3>span {
    color: #444;
}

.vg-cmt-container {
    /*white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    overflow-wrap: break-word;*/
    font-style: italic;
    font-size: 14px;
    color: #888;
    width: 100%;
}

.vg-cmt-container>div {
    display: none;
    color: #aaaaaa;
}

.v-card:hover>div>.vg-cmt-container>div {
    display: block;
}

.overflow-auto-vigilance {
    padding: 10px 5px;
    overflow: auto;
}

/* Add notification margin class */
.notification-margin {
    margin-bottom: 120px !important;
    /* Provide space for notification */
}

/* Adjust the overflow-auto-vigilance class to work with notifications */
.overflow-auto-vigilance.with-notification {
    padding-bottom: 130px;
}

#phoneAgent {
    padding: 10px;
    color: #444;
    background-color: whitesmoke;
}

.phone-call {
    cursor: pointer;
    padding: 5px;
    border: #aaaaaa solid 1px;
}

.phone-call:hover {
    background-color: #aaa;
}



div#cellIndice {
    display: table-cell;
    max-width: 50px;
    min-width: 50px;
    width: 50px;
}

/* Site name header layout fixes */
.site-name-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    min-height: 40px;
    flex-wrap: nowrap;
}

.site-name-text {
    flex: 1;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    line-height: 1.2;
    margin-right: 10px;
    min-width: 0;
    /* Allow text to shrink */
}

.site-name-icon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    cursor: pointer;
    align-self: flex-start;
    margin-top: 2px;
}

/* Ensure table cell contains the flexbox properly */
.table .cell.site-name-cell {
    padding: 0;
    vertical-align: top;
}

/* Phone agent section site name styling */
#h3Vigilance {
    font-weight: bold;
    font-size: 16px;
    color: #555;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    line-height: 1.3;
    text-align: center;
}

/* Responsive adjustments for very long site names */
@media screen and (max-width: 400px) {
    .site-name-text {
        font-size: 14px;
        line-height: 1.1;
    }

    #h3Vigilance {
        font-size: 14px;
        line-height: 1.2;
    }
}
