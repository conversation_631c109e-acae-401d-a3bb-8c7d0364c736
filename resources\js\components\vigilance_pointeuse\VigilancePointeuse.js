import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'

import './pointeuse.css'
import EditCommentModal from './commentaire/EditCommentModal'
import AlarmLogModal from '../pointage/AlarmLogModal'
import LoadingCallModal from '../vigilance/LoadingCallModal'

import Notification from '../rappel/notification/Notification'
import { formDataOption } from '../../../../auth'

export default class VigilancePointeuse extends Component {
    constructor(props) {
        super(props)
        this.state = {
            sites: [],
            indiceVigilance: null,
            vigilanceSite: null,
            currentVigilanceId: 0,
            dateVigilance: '',
            datetimeVg: '',
            currentDate: '',
            timeoutId: null,
            showSelectItem: false,
            currentItem: null,
            heightWindow: null,
            widthWindow: null,
            showEditVigilance: false,
            currentCommentaire: '',
            nomSite: '',
            currentPointeuse: null,
            showSelection: false,
            agents: null,
            dateLastSignal: '',
            phoneAgent: '',
            isCheckphone: false,
            currentItemType: 'all',
            groupSites: [],
            showAlarmModal: false,
            showLoadingCallModal: false,
            title: 'BioQ - TLS',
            notifications: [],
            callReminders: [],
            filteredCallReminders: [],
        }
        this.updateData = this.updateData.bind(this)
        this.hideSelectItem = this.hideSelectItem.bind(this)
        this.clickItem = this.clickItem.bind(this)
        this.toggleSelectItem = this.toggleSelectItem.bind(this)
        this.toggleSelectionAgent = this.toggleSelectionAgent.bind(this)
        this.toggleLoading = this.toggleLoading.bind(this)
        this.handleClickGroup = this.handleClickGroup.bind(this)
        this.handleBack = this.handleBack.bind(this)
        this.toggleAlarmModal = this.toggleAlarmModal.bind(this)
        this.makeCall = this.makeCall.bind(this)
        this.toggleLoadingCallModal = this.toggleLoadingCallModal.bind(this)
        this.setTimeoutUpdateNotification = this.setTimeoutUpdateNotification.bind(this)
        this.updateNotification = this.updateNotification.bind(this)
        this.updateCallReminders = this.updateCallReminders.bind(this)
        this.filterCallReminders = this.filterCallReminders.bind(this)
    }

    // Method to load call reminders from localStorage
    updateCallReminders() {
        try {
            const remindersData = localStorage.getItem('call_reminders');

            if (!remindersData) {
                this.setState({ callReminders: [] }, () => {
                    this.filterCallReminders();
                });
                return;
            }

            try {
                const reminders = JSON.parse(remindersData);

                if (!Array.isArray(reminders)) {
                    console.error('call_reminders is not an array');
                    localStorage.setItem('call_reminders', JSON.stringify([]));
                    this.setState({ callReminders: [] }, () => {
                        this.filterCallReminders();
                    });
                    return;
                }

                this.setState({ callReminders: reminders }, () => {
                    this.filterCallReminders();
                });
            } catch (parseError) {
                console.error('Error parsing call_reminders JSON');
                localStorage.setItem('call_reminders', JSON.stringify([]));
                this.setState({ callReminders: [] }, () => {
                    this.filterCallReminders();
                });
            }
        } catch (error) {
            console.error('Unexpected error in updateCallReminders');
            this.setState({ callReminders: [] }, () => {
                this.filterCallReminders();
            });
        }
    }

    // Method to filter call reminders based on current context
    filterCallReminders() {
        const { callReminders, currentPointeuse, currentItem, sites } = this.state;

        if (!callReminders || callReminders.length === 0) {
            this.setState({ filteredCallReminders: [] });
            return;
        }

        let filteredReminders = [];

        // if (currentPointeuse) {
        //     // Filter by pointeuse site name or ID
        //     filteredReminders = callReminders.filter(reminder => {
        //         const matchesSiteName = reminder.site &&
        //             currentPointeuse.nom &&
        //             reminder.site.toLowerCase() === currentPointeuse.nom.toLowerCase();

        //         const matchesSiteId = currentPointeuse.idsite &&
        //             reminder.site_id === currentPointeuse.idsite;

        //         return matchesSiteName || matchesSiteId;
        //     });
        // } else
        if (currentItem && sites && sites.length > 0) {
            // When a group is selected, check all sites within that group
            filteredReminders = callReminders.filter(reminder => {
                // First check if the reminder matches the group directly
                const matchesGroupName = reminder.group &&
                    currentItem.nom &&
                    reminder.group.toLowerCase() === currentItem.nom.toLowerCase();

                const matchesGroupId = currentItem.id &&
                    reminder.group_id === currentItem.id;

                if (matchesGroupName || matchesGroupId) {
                    return true;
                }

                // Then check if the reminder matches any site within the group
                return sites.some(site => {
                    const matchesSiteName = reminder.site &&
                        site.nom &&
                        reminder.site.toLowerCase() === site.nom.toLowerCase();

                    const matchesSiteId = site.idsite &&
                        reminder.site_id === site.idsite;

                    return matchesSiteName || matchesSiteId;
                });
            });
        } else if (currentItem) {
            // If we have a currentItem but no sites loaded yet, match by group
            filteredReminders = callReminders.filter(reminder => {
                const matchesGroupName = reminder.group &&
                    currentItem.nom &&
                    reminder.group.toLowerCase() === currentItem.nom.toLowerCase();

                const matchesGroupId = currentItem.id &&
                    reminder.group_id === currentItem.id;

                return matchesGroupName || matchesGroupId;
            });
        }

        this.setState({ filteredCallReminders: filteredReminders });
    }

    makeCall(to) {
        to = to.trim()
        if (/^0[0-9]{9}$/.test(to)) {
            this.toggleLoadingCallModal(true)
            const { user } = this.props
            console.log('from: ' + user.extension + ', to: ' + to)
            let data = new FormData()
            data.append("user_id", user.id)
            data.append("from", user.extension)
            data.append("to", to)
            axios.post("/api/call/make_call", data, formDataOption)
                .then(({ data }) => {
                    console.log(data)
                    this.toggleLoadingCallModal(false)

                    // After making a call, update call reminders
                    this.updateCallReminders();
                })
                .catch((e) => {
                    console.error(e)
                    this.toggleLoadingCallModal(false)
                })
        }
        else
            console.log("erreur de format")
    }

    toggleLoadingCallModal(value) {
        this.setState({
            showLoadingCallModal: value
        })
    }

    toggleAlarmModal(v, siteId) {
        if (!v)
            this.setState({
                showAlarmModal: false
            })
        else if (siteId) {
            this.setState({
                showAlarmModal: true,
                siteId: siteId
            })
        }
    }
    setTimeoutUpdateNotification() {
        if (this.state.timeoutId)
            clearTimeout(this.state.timeoutId)
        let timeoutId = setTimeout(this.updateNotification, 9000)
        this.setState({
            timeoutId: timeoutId
        })
    }
    updateNotification() {
        /*
        axios.get('api/call/get_recall_data', formDataOption)
        .then(({ data }) => {
            console.log('updateNotification')
            this.setState({
                notifications: data.data,
                // currentDate: data.now,
            }, () => {
                this.setTimeoutUpdateNotification()
            })
        })
        .catch(() => {
            this.setTimeoutUpdateNotification()
        })
        */
    }
    handleBack() {
        this.setState({
            currentItem: null,
            currentPointeuse: null,
            title: "BioQ - TLS"
        }, () => {
            this.updateData(true);
            this.filterCallReminders();
        })
    }
    handleClickGroup(g) {
        this.setState({
            currentItem: g,
            title: "BioQ - " + g.nom + " - TLS"
        }, () => {
            this.updateData(true);
            this.filterCallReminders();
        })
    }
    handleClickVigilance(index) {
        this.setState({
            currentVigilanceId: index
        })
    }
    capitalizeFirstLetter(string) {
        const arrayString = string.split(' ').map((s) => (
            s.trim().charAt(0).toUpperCase() + s.trim().slice(1).toLowerCase()
        ))
        return arrayString.join(' ')
    }
    toggleSelectionAgent(value) {
        this.setState({
            showSelection: value
        })
    }
    handleClickCommentVigilance(commentaire) {
        this.setState({
            showEditVigilance: true,
            isCheckphone: false,
            currentCommentaire: commentaire
        })
    }
    handleClickCheckphoneVigilance(commentaire) {
        this.setState({
            showEditVigilance: true,
            isCheckphone: true,
            currentCommentaire: commentaire
        })
    }
    toggleEditVigilance(value) {
        this.setState({
            showEditVigilance: value
        })
    }
    setVigilanceShowState(data) {
        const { currentDate } = this.state
        let sites = this.getVigilanceInterval(currentDate)
        for (let i = 0; i < sites.length; i++) {
            data.pointages.forEach(ptg => {
                let currentPtg = JSON.parse(JSON.stringify(ptg));
                currentPtg.manque = true
                sites[i].agents.push(currentPtg)
            })
            data.reclamations.forEach(rec => {
                let currentRec = JSON.parse(JSON.stringify(rec));
                sites[i].agents.push({
                    reclamation_id: currentRec.id,
                    nom: currentRec.agent_id ? currentRec.nom : currentRec.agent_not_registered,
                    societe_id: currentRec.societe_id,
                    numero_employe: currentRec.numero_employe,
                    num_emp_soit: currentRec.num_emp_soit,
                    numero_stagiaire: currentRec.numero_stagiaire,
                    // manque: true
                })
            })
            data.vigilances.map((vg) => {
                let dtarrived = moment(vg.dtarrived)
                if (dtarrived.isAfter(sites[i].begin) && dtarrived.isBefore(sites[i].end)) {
                    let found = false
                    for (let j = 0; j < sites[i].agents.length; j++) {
                        if (sites[i].agents[j].agent_id == vg.agent_id) {
                            found = true
                            sites[i].agents[j].ok = true
                            if (!sites[i].agents[j].dtarrived)
                                sites[i].agents[j].dtarrived = dtarrived.format('HH:mm:ss')
                            delete sites[i].agents[j].manque
                        }
                    }
                    if (!found) {
                        vg.not_pointed = true
                        sites[i].agents.push(vg)
                    }
                }
            })
            sites[i].nb_pointage = data.pointages.length + data.reclamations.length
            sites[i].nb_agent = 0
            sites[i].agents.forEach(a => {
                if (!a.manque && !a.reclamation_id) sites[i].nb_agent++
            })
            sites[i].color = this.getColorCardSite(sites[i].agents)
            data.commentaires.forEach(cmt => {
                if (cmt.date_vigilance == sites[i].begin.clone().add('10', 'minutes').format('YYYY-MM-DD HH:mm:ss')) {
                    if (!cmt.agent_id) {
                        sites[i].commentaire = cmt.commentaire
                        sites[i].objet = cmt.objet
                    }
                    else {
                        for (let j = 0; j < sites[i].agents.length; j++) {
                            if (sites[i].agents[j].agent_id == cmt.agent_id) {
                                sites[i].agents[j].commentaire = cmt.objet
                            }
                        }
                    }
                }
            })
        }
        this.setState({
            vigilanceSite: sites,
            pointages: data.pointages
        }, () => {
            this.toggleLoading(false)
        })
    }
    updateVigilanceWithDetail() {
        const { currentPointeuse } = this.state
        axios.get('/api/vigilances/pointeuse/show/' + currentPointeuse.pointeuse_id)
            .then(({ data }) => {
                this.setState({
                    nomSite: currentPointeuse.nom,
                    phoneAgent: currentPointeuse.phone_agent,
                    dateLastSignal: currentPointeuse.last_vigilance
                })
                this.setVigilanceShowState(data)
            })
            .catch(() => {
                this.toggleLoading(false)
            })
    }
    handleClickPointeuse(pt) {
        console.log("pointeuse: ", pt)
        this.toggleLoading(true)
        this.setState({
            currentPointeuse: pt,
            currentVigilanceId: 0,
        }, () => {
            this.updateVigilanceWithDetail();
            this.filterCallReminders();
        })
    }
    getVigilanceInterval(time) {
        let currentVigilance = moment(time, "YYYY-MM-DD HH:mm:ss")
        let intervals = []
        if (currentVigilance.isAfter(moment(currentVigilance.format("YYYY-MM-DD") + " 05:50:00"))
            && currentVigilance.isBefore(moment(currentVigilance.format("YYYY-MM-DD") + " 17:50:00"))) {
            let vigilanceJour = moment(currentVigilance.format("YYYY-MM-DD") + " 05:50:00")
            while (vigilanceJour.isBefore(currentVigilance)) {
                let begin = vigilanceJour.clone()
                let nom = vigilanceJour.clone().add('10', 'minutes').format('HH:mm')
                let end = vigilanceJour.clone().add('1', 'hour').clone()
                intervals.push({
                    begin: begin,
                    nom: nom,
                    end: end
                })
                vigilanceJour.add('1', 'hour')
            }
        }
        else {
            let vigilanceNuit = moment(currentVigilance.format("YYYY-MM-DD") + " 17:50:00")
            if (currentVigilance.isBefore(moment(currentVigilance.format("YYYY-MM-DD") + " 05:50:00")))
                vigilanceNuit = vigilanceNuit.subtract("1", "day")
            while (vigilanceNuit.isBefore(currentVigilance)) {
                let begin = vigilanceNuit.clone()
                let nom = vigilanceNuit.clone().add('10', 'minutes').format('HH:mm')
                let end = vigilanceNuit.clone().add('30', 'minutes').clone()
                intervals.push({
                    begin: begin,
                    nom: nom,
                    end: end,
                })
                vigilanceNuit.add('30', 'minutes')
            }
        }
        for (let i = 0; i < intervals.length; i++) {
            intervals[i].agents = []
        }
        return intervals.reverse()
    }
    resize() {
        this.setState({
            heightWindow: window.innerHeight,
            widthWindow: window.innerWidth
        });
        if (this.overviewContainer)
            this.setState({
                widthPx: this.overviewContainer.offsetWidth - 250
            })
    }
    clickItem(type, value) {
        if (value)
            document.title = value.nom + ' - Vigilance TLS'
        else
            document.title = 'Vigilance - TLS'
        this.setState({
            currentItemType: type,
            currentItem: value,
            vigilanceSite: null,
            showSelectItem: false,
            currentPointeuse: null,
            nomSite: ''
        })
    }
    toggleSelectItem() {
        this.setState({
            showSelectItem: !this.state.showSelectItem
        })
    }
    setShowSelectItem(value) {
        this.setState({
            showSelectItem: value,
            showSelection: value
        })
    }
    hideSelectItem() {
        this.setState({
            showSelectItem: false
        })
    }
    toggleLoading(load) {
        this.props.toggleLoading(load)
    }
    setTimeoutUpdateData() {
        if (this.state.timeoutId)
            clearTimeout(this.state.timeoutId)
        let timeoutId = setTimeout(this.updateData, 60000)
        this.setState({
            timeoutId: timeoutId
        })
    }
    updateData(loading) {
        const { timeoutId, currentPointeuse, currentItem } = this.state
        if (timeoutId)
            clearTimeout(timeoutId)
        console.log('updatePointeuse')
        if (loading) {
            this.toggleLoading(true)
        }
        let url = ''
        if (currentItem) {
            url = '/api/vigilances/pointeuse?group_id=' + currentItem.id
            if (currentPointeuse)
                url = url + '&pointeuse_id=' + currentPointeuse.pointeuse_id
            axios.get(url)
                .then(({ data }) => {
                    console.log(data)
                    for (let i = 0; i < data.sites.length; i++) {
                        data.sites[i].color = this.getColorCard(data.sites[i])
                        for (let j = 0; j < data.commentaires.length; j++)
                            if (data.commentaires[j].pointeuse_id == data.sites[i].id) {
                                data.sites[i].commentaire = data.commentaires[j].commentaire
                                data.sites[i].objet = data.commentaires[j].objet
                            }
                    }
                    if (data.current_vigilance)
                        this.setVigilanceShowState(data.current_vigilance)
                    this.setState({
                        sites: data.sites,
                        dateVigilance: data.date_vigilance,
                        datetimeVg: data.datetime_vigilance,
                        currentDate: data.current_datetime
                    }, () => {
                        if (loading) {
                            this.toggleEditVigilance(false)
                        }
                        // After sites are loaded, filter call reminders again
                        this.filterCallReminders();
                    })
                    this.toggleLoading(false)
                })
                .catch(() => {
                    this.props.toggleLoading(false)
                })
        }
        else {
            url = '/api/group_sites?biometrique=1'
            console.log(url)
            axios.get(url)
                .then(({ data }) => {
                    this.setState({
                        groupSites: data.groups,
                        dateVigilance: data.date_vigilance,
                        currentDate: data.current_datetime,
                    })
                    this.toggleLoading(false)
                })
                .catch(() => {
                    setTimeout(() => {
                        this.updateData()
                    }, 10000)
                })
        }

    }
    componentDidMount() {
        this.updateData(true)
        window.addEventListener("resize", this.resize.bind(this))
        this.updateCallReminders(); // Load call reminders on mount
        this.resize()
        document.title = this.state.title

        // Set up interval to periodically check for new call reminders
        this.callRemindersInterval = setInterval(() => {
            this.updateCallReminders();
        }, 10000); // Check every 10 seconds
    }
    componentDidUpdate(prevProps, prevState) {
        if (prevState.title !== this.state.title) {
            document.title = this.state.title
        }
    }
    clearTimeoutVigilance() {
        if (this.state.timeoutId)
            clearTimeout(this.state.timeoutId)
        this.setState({
            timeoutId: null
        })
    }
    componentWillUnmount() {
        this.clearTimeoutVigilance()
        const { timeoutId } = this.state
        if (timeoutId)
            clearTimeout(timeoutId)

        // Clear the call reminders interval
        if (this.callRemindersInterval) {
            clearInterval(this.callRemindersInterval);
        }
    }
    getColorCard(pointeuse) {
        const { currentDate } = this.state
        let color = 'secondary'
        if ((!pointeuse.last_connection || moment(pointeuse.last_connection).isBefore(moment(currentDate).subtract(6, 'hour'))))
            color = 'orange'
        else if (pointeuse.nb_ok == 0)
            color = 'pink'
        else if (pointeuse.nb_manque > 0)
            color = 'amber'
        else if (pointeuse.nb_not_pointed == 0)
            color = 'primary'
        else if (pointeuse.nb_not_pointed > 0)
            color = 'purple'
        return color
    }
    getColorCardSite(agents) {
        let color = ''
        let nb_ok = 0
        let nb_manque = 0
        let nb_not_pointed = 0
        agents.forEach(ag => {
            if (ag.manque) nb_manque++
            else if (ag.not_pointed) nb_not_pointed++
            else if (ag.ok) nb_ok++
        })
        if (nb_ok == 0)
            color = 'card-pink'
        else if (nb_manque > 0)
            color = 'card-amber'
        else if (nb_not_pointed == 0)
            color = 'card-primary'
        else if (nb_not_pointed > 0)
            color = 'card-purple'
        return color
    }
    showSiteItem(vg) {
        const { currentItem, currentItemType } = this.state
        if (currentItemType == 'all')
            return true
        else if (currentItemType == 'manque' && (vg.last_vigilance && moment(vg.last_vigilance).isAfter(moment().subtract('1', 'day'))) && !vg.vigilance)
            return true
        else if (currentItemType == 'panne' && (!vg.last_vigilance || moment(vg.last_vigilance).isBefore(moment().subtract('1', 'day'))))
            return true
        else if (currentItemType == 'item' && currentItem.id == vg.group_id)
            return true
        return false
    }
    render() {
        const { isCheckphone, sites, dateVigilance, datetimeVg, groupSites, currentDate, phoneAgent,
            dateLastSignal, showEditVigilance, currentCommentaire, vigilanceSite, notifications, nomSite,
            currentPointeuse, currentVigilanceId, heightWindow, widthPx, currentItem, showAlarmModal,
            filteredCallReminders } = this.state;
        const { user } = this.props;

        // Determine if notifications are present
        const hasNotifications = (['root', 'room'].includes(user.role) && filteredCallReminders.length > 0);

        // Calculate height adjustment for notifications
        const notificationAdjustment = hasNotifications ? 120 : 0;

        return <>
            {
                (['root', 'room'].includes(user.role) && filteredCallReminders.length > 0) &&
                <Notification data={filteredCallReminders} clickItem={this.makeCall} type="call_reminder" />
            }
            {
                this.state.showLoadingCallModal &&
                <LoadingCallModal loading={this.state.showLoadingCallModal} />
            }
            {
                currentItem ?
                    <div className="table" onClick={() => { this.setShowSelectItem(false) }}>
                        <div id="vigilanceContainer">
                            <h3>
                                <div className="table">
                                    <div className="cell back-vigilance">
                                        <img onClick={this.handleBack} src="/img/back_arrow.svg" />
                                    </div>
                                    <div className="cell refresh-vigilance">
                                        <img onClick={() => this.updateData(true)} src="/img/refresh_arrow.svg" />
                                    </div>
                                    <div className="cell center">
                                        <div id="selectBox">
                                            <div id="itemSelected">
                                                <span className="item-selected-primary">
                                                    {currentItem.nom}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="cell right heure-vigilance">
                                        <span className="hour-label">
                                            {dateVigilance}
                                        </span>
                                    </div>
                                </div>
                            </h3>
                            <div className="overflow-auto" style={{ height: (heightWindow - 120) + "px" }}>
                                {
                                    sites.map(s => {
                                        return (<div key={s.id} className='card'>
                                            <div onClick={() => { this.handleClickPointeuse(s) }} className={"card-" + this.getColorCard(s) + (
                                                this.getColorCard(s) != 'orange' && moment(s.last_connection).isBefore(moment(currentDate).subtract(20, 'minutes'))
                                                    ? " disconnected" : ""
                                            )}>

                                                <div>
                                                    <div style={{ display: "inline-block", width: "50%" }}>

                                                    </div>
                                                    <div style={{ display: "inline-block", width: "50%", textAlign: "right" }}>
                                                        {
                                                            s.objet &&
                                                            <img style={{ height: '15px', cursor: "pointer" }}
                                                                title={s.commentaire}
                                                                onClick={() => { this.handleClickCommentVigilance({ pointeuse_id: s.pointeuse_id, objet: s.objet, text: s.commentaire, vigilance: datetimeVg, nom: s.nom }) }}
                                                                src={"/img/edit_" + s.color + ".svg"} />
                                                        }
                                                        {
                                                            (!s.objet && this.getColorCard(s) == 'orange') &&
                                                            <img style={{ height: '15px', cursor: "pointer" }}
                                                                onClick={() => { this.handleClickCheckphoneVigilance({ pointeuse_id: s.pointeuse_id, objet: s.objet, text: s.commentaire, vigilance: datetimeVg, nom: s.nom }) }}
                                                                src={"/img/phone.svg"} />
                                                        }
                                                    </div>
                                                </div>
                                                <h3 title={s.pointeuse_id + " " + s.nom}>
                                                    <span>
                                                        {s.nom && this.capitalizeFirstLetter(s.nom)}
                                                    </span>
                                                </h3>
                                                <div>
                                                    <div className="table">
                                                        <div className="cell right">
                                                            {
                                                                s.nb_ok != 0 &&
                                                                <span className={"badge bg-" + (this.getColorCard(s) == 'primary' ? "light" : "secondary")}
                                                                    title={s.nb_ok + " / " + s.nb_pointage}>
                                                                    {s.nb_ok}
                                                                </span>
                                                            }
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>)
                                    }
                                    )
                                }
                                {
                                    showEditVigilance &&
                                    <EditCommentModal isCheckphone={isCheckphone} updateData={this.updateData} action="/api/vigilances/save_commentaire" commentaire={currentCommentaire} closeModal={() => { this.toggleEditVigilance(false) }} />
                                }
                            </div>
                        </div>
                        <div ref={el => (this.overviewContainer = el)}
                            style={{ height: heightWindow + 'px' }}
                            id="vigilanceDetail"
                            className={hasNotifications ? 'notification-margin' : ''}>
                            {
                                showAlarmModal &&
                                <AlarmLogModal site_id={currentPointeuse.idsite} heightWindow={heightWindow} closeModal={() => this.toggleAlarmModal(false)} />
                            }
                            {
                                vigilanceSite ?
                                    <div>
                                        <h3>
                                            <div className="table">
                                                <div className="cell">{nomSite}</div> <img onClick={() => { this.toggleAlarmModal(true, currentPointeuse.idsite) }} className="add-pointage-btn" src="/img/historique.svg" />
                                            </div>
                                        </h3>

                                        <div className={`overflow-auto-vigilance ${hasNotifications ? 'with-notification' : ''}`}
                                            style={{ height: (heightWindow - (hasNotifications ? 220 : 160)) + "px" }}>
                                            {
                                                vigilanceSite.map((v, index) => (
                                                    <div key={index} className="v-card">
                                                        <div className={v.color}>
                                                            <div className="table ">
                                                                <div className="cell">
                                                                    {
                                                                        (index < 2 || (!dateLastSignal || moment(dateLastSignal).isBefore(moment().subtract('1', 'day')))) &&
                                                                        <div>
                                                                            <img style={{ width: '10px', cursor: "pointer" }}
                                                                                onClick={() => { this.handleClickCommentVigilance({ pointeuse_id: currentPointeuse.pointeuse_id, objet: v.objet, text: v.commentaire, vigilance: (datetimeVg ? (datetimeVg.split(' ')[0] + ' ' + v.nom + ':00') : ''), nom: nomSite }) }}
                                                                                src="/img/edit_default.svg" />
                                                                        </div>
                                                                    }
                                                                    <h3>
                                                                        <span onClick={() => this.handleClickVigilance(index)} style={{ cursor: "pointer" }}>
                                                                            {v.nom}
                                                                        </span>
                                                                    </h3>
                                                                </div>
                                                                <div className="cell right">
                                                                    {
                                                                        (currentVigilanceId != index && v.nb_agent > 0) &&
                                                                        <span className={"badge bg-" + (
                                                                            v.color == 'card-primary' ? "light"
                                                                                : "secondary")}>
                                                                            {v.nb_agent}
                                                                        </span>
                                                                    }
                                                                </div>
                                                            </div>

                                                            <div className="vg-cmt-container">
                                                                <span>{v.objet}</span>
                                                                <div style={{ color: "#aaa" }}>
                                                                    <div>{v.commentaire}</div>
                                                                </div>
                                                            </div>
                                                            {
                                                                currentVigilanceId == index &&
                                                                <div>
                                                                    {
                                                                        (v.agents) &&
                                                                        <br />
                                                                    }
                                                                    <table className="fixed_header default layout-fixed">
                                                                        <tbody style={{ border: "none" }}>
                                                                            {
                                                                                v.agents &&
                                                                                v.agents.map((ag, ag_id) => (
                                                                                    <tr className={ag.reclamation_id ? 'grey-light' : ag.manque ? 'pink' : ag.not_pointed ? 'purple' : ''} key={'row' + index + ag_id}>
                                                                                        <td className="cellMatricule">
                                                                                            {
                                                                                                ag.societe_id == 1 ? 'DGM-' + ag.numero_employe :
                                                                                                    ag.societe_id == 2 ? 'SOIT-' + ag.num_emp_soit :
                                                                                                        ag.societe_id == 3 ? 'ST-' + ag.numero_stagiaire :
                                                                                                            ag.societe_id == 4 ? 'SM' :
                                                                                                                ag.numero_employe ? ag.numero_employe :
                                                                                                                    ag.numero_stagiaire ? ag.numero_stagiaire :
                                                                                                                        'Ndf'
                                                                                            }
                                                                                        </td>
                                                                                        <td title={ag.dtarrived ? ag.dtarrived : ''} style={{ width: widthPx, minWidth: widthPx, maxWidth: widthPx }}>
                                                                                            {ag.nom}
                                                                                        </td>
                                                                                        {
                                                                                            !ag.reclamation_id &&
                                                                                            <td className="center" title={ag.commentaire ? ag.commentaire : ""}>
                                                                                                <img style={{ width: '16px', cursor: "pointer" }}
                                                                                                    onClick={() => {
                                                                                                        this.handleClickCommentVigilance({
                                                                                                            agent_id: ag.agent_id,
                                                                                                            nom_agent: (
                                                                                                                ag.societe_id == 1 ? 'DGM-' + ag.numero_employe :
                                                                                                                    ag.societe_id == 2 ? 'SOIT-' + ag.num_emp_soit :
                                                                                                                        ag.societe_id == 3 ? 'ST-' + ag.numero_stagiaire :
                                                                                                                            ag.societe_id == 4 ? 'SM' :
                                                                                                                                ag.numero_employe ? ag.numero_employe :
                                                                                                                                    ag.numero_stagiaire ? ag.numero_stagiaire : '')
                                                                                                                + ' ' + ag.nom,
                                                                                                            pointeuse_id: currentPointeuse.pointeuse_id,
                                                                                                            agent_id: ag.agent_id,
                                                                                                            objet: ag.commentaire,
                                                                                                            vigilance: (datetimeVg ? (datetimeVg.split(' ')[0] + ' ' + v.nom + ':00') : ''),
                                                                                                            nom: nomSite
                                                                                                        })
                                                                                                    }}
                                                                                                    src={"/img/edit_" + (ag.commentaire ? "active" : "fade") + ".svg"} />
                                                                                            </td>
                                                                                        }
                                                                                    </tr>
                                                                                ))
                                                                            }
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            }
                                                        </div>
                                                    </div>
                                                ))
                                            }
                                        </div>
                                        <div className="center">
                                            <div id="phoneAgent">
                                                {
                                                    phoneAgent ?
                                                        phoneAgent.split(",").map((c, idx) => (
                                                            <span key={idx} className='phone-call' onClick={() => this.makeCall(c)}>{c}</span>
                                                        ))
                                                        : ''
                                                }<br />
                                                <span id="h3Vigilance">{nomSite}</span>
                                            </div>

                                        </div>
                                    </div>
                                    :
                                    <div className="img-bg-container">
                                        <img className="img-bg-overview" src="/img/tls_background.svg" />
                                    </div>
                            }
                        </div>
                    </div>
                    :
                    <div className="table" onClick={() => { this.setShowSelectItem(false) }}>
                        <div id="vigilanceContainer">
                            <h3>
                                <div className="table">
                                    <div className="cell heure-vigilance">
                                        <span className="hour-label">
                                            {currentDate && moment(currentDate).format('HH:mm')}
                                        </span>
                                    </div>
                                    <div className="cell center">
                                        <div id="selectBox">
                                            <div id="itemSelected">
                                                <span className="item-selected-primary">
                                                    BIOMETRIQUE
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="cell right heure-vigilance">
                                        <span className="hour-label">
                                            {dateVigilance}
                                        </span>
                                    </div>
                                </div>
                            </h3>
                            <div className="overflow-auto" style={{ height: (heightWindow - 120) + "px" }}>
                                {
                                    groupSites.map(g => {
                                        return <div key={g.id} className='card'>
                                            <div onClick={() => { this.handleClickGroup(g) }} className="card-secondary">
                                                <div>
                                                    <div style={{ display: "inline-block", width: "50%" }}>

                                                    </div>
                                                    <div style={{ display: "inline-block", width: "50%", textAlign: "right" }}>

                                                    </div>
                                                </div>
                                                <h3 title={g.id + " " + g.nom}>
                                                    <span>
                                                        {g.nom}
                                                    </span>
                                                </h3>
                                                <div>
                                                    <div className="table">
                                                        <div className="cell right">
                                                            <span className="badge bg-light">
                                                                {g.nb_site}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                    )
                                }
                                {
                                    showEditVigilance &&
                                    <EditCommentModal isCheckphone={isCheckphone} updateData={this.updateData} action="/api/vigilances/save_commentaire" commentaire={currentCommentaire} closeModal={() => { this.toggleEditVigilance(false) }} />
                                }
                            </div>
                        </div>
                    </div>
            }
        </>
    }
}


