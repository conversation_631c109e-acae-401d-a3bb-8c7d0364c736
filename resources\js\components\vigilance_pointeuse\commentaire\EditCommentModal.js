import React, { Component } from 'react'
import axios from 'axios'
import moment from 'moment'


export default class EditCommentModal extends Component {
    constructor(props){
        super(props)
        this.state = {
            pointeuse_id: '',
            agent_id: '',
            commentaire: '',
            objet: '',
            checkphone: '',
            currentField: '',
            vigilance: '',
            nom: '',
            nom_agent: '',
            site: '',
            dateVigilance: '',
            disableSave: false,
            objetList: [
                "Check-phone ____",
                "Bouton abusif.",
                "Bouton en panne.",
                "Rappelle de bouton.",
                "Problème de bouton.",
                "Problème de réseaux.",
                "Problème de transmission.",
                "Lancement de l'intervention.",
                "L’agent ne fait pas bien son bouton.",
                "Agent en mouvement.",
                "Agent injoignable.",
                "Ouverture à ____",
                "Fermeture à ____",
                "Réouverture à ____"
            ],
            commentaireList: [
                "Bouton en panne.",
                "Lancement de l'intervention.",
                "L'agent ne fait pas bien le bouton.",
                "Envoie d'intervention ____WWT.",
                "L'agent est averti.",
                "Rappel de bouton."
            ],
            errorLabel: ''
        }
        this.handleCommentaireChange = this.handleCommentaireChange.bind(this)
        this.handleObjetChange = this.handleObjetChange.bind(this)
        this.handleCheckphoneChange = this.handleCheckphoneChange.bind(this)
        this.handleObjetClick = this.handleObjetClick.bind(this)
        this.handleCommentaireClick = this.handleCommentaireClick.bind(this)
        this.handleSave = this.handleSave.bind(this)
        this.handleCancel = this.handleCancel.bind(this)
        this.hideSelectItem = this.hideSelectItem.bind(this)
        this.onKeyUp = this.onKeyUp.bind(this);
    }
    onKeyUp(event){
        if (event.charCode === 13) {
            this.handleSave()
        }
    }
    handleClickObjetItem(value){
        this.setState({
            objet: value,
            currentField: ''
        })
    }
    handleClickCommentaireItem(value){
        const {commentaire} = this.state
        this.setState({
            commentaire: (commentaire ? commentaire.trim() + ' ' : '') + value,
            currentField: ''
        })
    }
    hideSelectItem(){
        this.setState({
            currentField: ''
        })
    }
    handleObjetClick(e){
        e.stopPropagation()
        this.setState({
            currentField: (this.state.currentField == 'objet' ? '' : 'objet')
        })
    }
    handleCommentaireClick(e){
        e.stopPropagation()
        this.setState({
            currentField: (this.state.currentField == 'comment' ? '' : 'comment')
        })
    }
    handleCommentaireChange(event){
        this.setState({
            commentaire: event.target.value
        })
    }
    handleCheckphoneChange(event){
        this.setState({
            checkphone: event.target.value
        })
    }
    handleObjetChange(event){
        this.setState({
            objet: event.target.value
        })
    }
    handleSave(){
        const {checkphone, objet, commentaire, vigilance, agent_id, pointeuse_id} = this.state
        this.setState({
            disableSave: true,
            errorLabel: ''
        })
        const data = new FormData()
        var patt = new RegExp('____')
        if(objet && patt.test(objet))
            this.setState({
                errorLabel: 'Détail de l\'objet non définie'
            })
        else if(objet && commentaire && patt.test(commentaire))
            this.setState({
                errorLabel: 'Détail du commentaire non définie'
            })
        else{
            if(pointeuse_id)
                data.append("pointeuse_id", pointeuse_id)
            if(agent_id)
                data.append("agent_id", agent_id)

            data.append("date_vigilance", vigilance)
            data.append("commentaire", commentaire.trim())
            if(objet.trim())
                data.append("objet", objet.trim())
            else if(checkphone.trim())
                data.append("objet", "Check-phone " + checkphone.trim())
            axios.post(this.props.action, data)
            .then(({data}) => {
                if(data)
                    this.props.updateData(data)
            })
            .finally(()=>{
                this.setState({
                    disableSave: false
                })
            })
        }
    }
    handleCancel(){
        this.props.closeModal()
    }
    componentDidMount(){
        const {commentaire} = this.props
        if(commentaire){
            console.log(this.props.commentaire)
            const objet = commentaire.objet ? commentaire.objet : ''
            const comment = commentaire.text ? commentaire.text : ''
            const vg = commentaire.vigilance ? commentaire.vigilance : ''
            const nom = commentaire.nom ? commentaire.nom : ''
            const agent_id = commentaire.agent_id ? commentaire.agent_id : ''
            const nom_agent = commentaire.nom_agent ? commentaire.nom_agent: ''
            const pointeuse_id = commentaire.pointeuse_id ? commentaire.pointeuse_id : ''
            this.setState({
                commentaire: comment,
                vigilance: vg,
                nom: nom,
                nom_agent: nom_agent,
                agent_id: agent_id,
                pointeuse_id: pointeuse_id,
                objet: objet
            })
        }
    }
    render(){
        const {checkphone, objet, currentField, commentaire, nom, agent_id, nom_agent, vigilance, disableSave, objetList, commentaireList, errorLabel} = this.state
        const {isCheckphone} = this.props
        return (
            <div onClick={this.hideSelectItem}>
                <div style={{zIndex: 200}} className="fixed-front">
                    <div className="table">
                        <div className="modal-container">
                            <div className="modal md">
                                <div className="modal-content">
                                    <div className="table">
                                        <div className="cell"><h3>{nom}</h3></div>
                                        <div className="cell right"><h3>[{vigilance && moment(vigilance).format('HH:mm')}]</h3></div>
                                    </div>
                                    {
                                        nom_agent &&
                                        <div>
                                            <h5 style={{marginTop: '5px'}}>{nom_agent}</h5>
                                            <br/>
                                        </div>
                                    }
                                    {
                                        !agent_id && 
                                        <div>
                                            <label>Check-phone</label>
                                            <div className="input-container">
                                                <input autoFocus={true} autoComplete="off" onKeyPress={this.onKeyUp} onChange={this.handleCheckphoneChange} name="checkphone" type="text" value={checkphone}/>
                                            </div>
                                        </div>
                                    }
                                    {
                                        !isCheckphone &&
                                        <div>
                                            <div className="input-select-relative">
                                                <div>
                                                    <label>{agent_id ? 'Commentaire' : 'Objet'}</label>
                                                    <div className="input-container">
                                                        <input autoComplete="off" onClick={this.handleObjetClick} onChange={this.handleObjetChange} name="objet" type="text" value={objet}/>
                                                    </div>
                                                </div>
                                                {
                                                    (!agent_id && currentField == 'objet') &&
                                                    <ul className="comment-vg-list">
                                                        {
                                                            objetList.map((obj, index) =>(
                                                                <li key={index} onClick={() => {this.handleClickObjetItem(obj)}}>{obj}</li>
                                                            ))
                                                        }
                                                    </ul>
                                                }
                                            </div>
                                            {
                                                !agent_id &&
                                                <div className="input-select-relative">
                                                    <div>
                                                        <label>Commentaire</label>
                                                        <div className="input-container">
                                                            <textarea onClick={this.handleCommentaireClick} onChange={this.handleCommentaireChange} name="commentaire" type="text" value={commentaire} rows="2">
                                                            </textarea>
                                                        </div>
                                                    </div>
                                                    {
                                                        currentField == 'comment' &&
                                                        <ul className="comment-vg-list">
                                                            {
                                                                commentaireList.map((cmt, index) =>(
                                                                    <li key={index} value={cmt} onClick={() => {this.handleClickCommentaireItem(cmt)}}>{cmt}</li>
                                                                ))
                                                            }
                                                        </ul>
                                                    }
                                                </div>
                                            }
                                            <br/>
                                            <div className="pink">{errorLabel}</div>
                                        </div>
                                    }
                                </div>
                                <div className="table modal-footer">
                                    <div className="cell left">
                                        <span className="pink"></span>
                                    </div>
                                    <div className="cell right">
                                        {
                                            !isCheckphone &&
                                            <button disabled={disableSave} onClick={this.handleSave} className="btn-primary fix-width">Valider</button>
                                        }
                                        <button onClick={this.handleCancel} className="btn-default fix-width">Annuler</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }
}