import React, { Component } from 'react'
import axios from 'axios'

import './select.css'

export default class SelectGroupSite extends Component {
    constructor(props){
        super(props)
        this.state = {
            groups: null,
            showItem: false
        }
        this.toggleSelect = this.toggleSelect.bind(this)
        this.handleClickItem = this.handleClickItem.bind(this)
    }
    handleClickItem(item, value){
        this.props.clickItem(item, value)
    }
    setList(){
        axios.get('/api/group_sites')
        .then(({data}) => {
            this.setState({
                groups: data
            })
        })
        .catch(() => {
            setTimeout(() => {
                this.setList()
            }, 10000)
        })
    }
    componentDidMount(){
        this.setList()
    }
    toggleSelect(event){
        event.stopPropagation()
        this.props.toggleSelect()
    }
    render(){
        const {groups} = this.state
        const {showItem, currentItem, currentItemType} = this.props
        return (
            <div id="selectBox">
                <div onClick={this.toggleSelect} id="itemSelected">
                <span className="item-selected">
                    {
                        currentItemType == 'all' ? 'VIGILANCE' :
                        currentItemType == 'panne' ? 'EN PANNE' : 
                        currentItemType == 'manque' ? 'MANQUE' :
                        (currentItemType == 'item' && currentItem) ? currentItem.nom : '' 
                    }
                </span>
                </div>
                {
                    showItem
                    &&
                    <div id="itemNotSelected">
                        <span onClick={() => this.handleClickItem('all')}>Tous</span>
                        <span onClick={() => this.handleClickItem('manque')}>Manque</span>
                        <span className="border-bottom" onClick={() => this.handleClickItem('panne')}>En panne</span>
                        {
                            groups &&
                            groups.map((item) => (
                                <span onClick={() => this.handleClickItem('item', item)} key={item.id}>{item.nom}</span>
                            ))
                        }
                    </div>
                }
            </div>
        )
    }
}