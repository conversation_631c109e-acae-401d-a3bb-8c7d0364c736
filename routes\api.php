<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});

Route::group([
    'prefix' => 'auth'
], function () {
    Route::post('login', 'Auth\AuthController@login')->name('login');
    Route::post('register', 'Auth\AuthController@register');
    Route::group([
        'middleware' => 'auth:api'
    ], function () {
        Route::get('logout', 'Auth\AuthController@logout');
        Route::get('user', 'Auth\AuthController@user');
        Route::post('historique/site/{id}/{date}', 'HistoriqueController@site_client');
        Route::get('habilites/{site_id}', 'HabiliteController@client');
        Route::get('/vigilances/show/{site_id}', 'VigilanceController@client');
        Route::get('/rapports/site/{site_id}/{year}/{month}', 'RapportController@client');
        Route::get('/rapports/show/{id}', 'RapportController@client_show');
    });
});

Route::middleware(['local'])->group(function () {
    Route::get('/coupures', 'CoupureController@index');
    Route::post('/coupures/add', 'CoupureController@store');

    Route::get('/habilites/{site_id}', 'HabiliteController@index');
    Route::post('/habilites/store', 'HabiliteController@store');
    Route::post('/habilites/update/{id}', 'HabiliteController@update');
    Route::post('/habilites/delete/{site_id}', 'HabiliteController@delete');

    Route::get('/select_field_sites', 'SiteController@select_fields');

    Route::get('/vigilances', 'VigilanceController@vigilance')->middleware('secret');
    Route::get('/vigilances/alarm/{site_id}', 'VigilanceController@show_alarm')->middleware('secret');
    Route::get('/vigilances/pointeuse', 'VigilanceController@pointeuse');
    Route::get('/vigilances/pointeuse/show/{site_id}', 'VigilanceController@show_pointeuse');
    Route::get('/vigilances/show/{site_id}', 'VigilanceController@show');
    Route::get('/vigilances/get_vigilance/{prom}', 'VigilanceController@get_vigilance');
    Route::post('/vigilances/report_vigilance', 'VigilanceController@report_vigilance_all');
    Route::get('/vigilances/report_abusif', 'VigilanceController@report_abusif');
    Route::get('/vigilances/report_horaire', 'VigilanceController@report_vigilance_horaire');
    Route::post('/vigilances/save_commentaire', 'VigilanceController@save_commentaire');

    Route::post('/signales/store', 'SignaleController@store');
    Route::post('/signales/update/{id}', 'SignaleController@update');
    Route::post('/signales/delete/{id}', 'SignaleController@delete');
    Route::get('/signales/site/{prom}', 'SignaleController@site');

    Route::post('/zones/store', 'ZoneController@store');
    Route::post('/zones/update/{id}', 'ZoneController@update');
    Route::post('/zones/delete/{id}', 'ZoneController@delete');
    Route::get('/zones/site/{id}', 'ZoneController@site');

    Route::post('/pointeuses/new_prom', 'PointeuseController@new_prom');
    Route::post('/pointeuses/store', 'PointeuseController@store')->middleware('secret');
    Route::post('/pointeuses/update/{id}', 'PointeuseController@update')->middleware('secret');
    Route::post('/pointeuses/toggle_activation/{id}', 'PointeuseController@toggle_activation');
    Route::get('/pointeuses/show/{id}', 'PointeuseController@show');
    Route::post('/pointeuses/delete/{id}', 'PointeuseController@delete')->middleware('secret');
    Route::post('/pointeuses/set_site/{id}', 'PointeuseController@set_site');
    Route::get('/pointeuses/operation/{id}/{begin}/{end}', 'PointeuseController@operation');
    Route::get('/pointeuses', 'PointeuseController@index');

    Route::get('/capteurs', 'CapteurController@index');
    Route::get('/events', 'EventController@index');
    Route::get('/historiques/site/{id}/{begin}/{end}', 'HistoriqueController@site');
    Route::get('/historiques/pointeuse/{id}/{begin}/{end}', 'HistoriqueController@pointeuse');


    Route::get('/secteurs', 'SecteurController@index');
    Route::get('/horaires/show/{id}', 'HoraireController@show');
    Route::post('/sites/store', 'SiteController@store')->middleware('secret');
    Route::post('/sites/update/{id}', 'SiteController@update')->middleware('secret');
    Route::post('/sites/delete/{id}', 'SiteController@delete')->middleware('secret');
    Route::post('/sites/diag/{id}', 'SiteController@diag')->middleware('secret');
    Route::get('/sites/show/{id}', 'SiteController@show');
    Route::get('/sites/new_prom', 'SiteController@new_prom');
    Route::get('/sites/archive', 'SiteController@archive');
    Route::get('/sites/client', 'SiteController@client');
    Route::get('/sites/notification', 'SiteController@notification');
    Route::get('/sites/operation/{id}/{begin}/{end}', 'SiteController@operation');
    Route::get('/sites/pointeuse', 'SiteController@pointeuse');
    Route::get('/sites', 'SiteController@index');

    Route::post('/sim_gateways/update/{transmitter}', 'SimGatewayController@update')->middleware('secret');
    Route::get('/group_diag_sites', 'GroupDiagSiteController@index');
    Route::get('/group_sites', 'GroupSiteController@index');
    Route::post('/group_sites/store', 'GroupSiteController@store')->middleware('secret');
    Route::post('/group_sites/update/{id}', 'GroupSiteController@update')->middleware('secret');
    Route::post('/group_sites/delete/{id}', 'GroupSiteController@delete')->middleware('secret');
    Route::get('/group_vigilances_sites', 'GroupSiteController@vigilance')->middleware('secret');

    Route::get('/panics/find', 'PanicController@findPanic');
    Route::post('/panics/traite/{id}', 'PanicController@traitePanic');
    Route::post('/panics/verouiller', 'PanicController@verouillerPanic');
    Route::get('/panics/show/{id}', 'PanicController@show');

    Route::get('/logs/show/{id}', 'LogController@show');
    Route::get('/logs/traite_alarme/{id}', 'LogController@traiteAlarme');
    Route::get('/logs/if_traite_panic/{id}', 'LogController@ifTraitePanic');
    Route::get('/logs/nav_count', 'LogController@navCount')->middleware('secret');
    Route::post('/logs/aquiter', 'LogController@aquiter')->middleware('secret');
    Route::get('/logs/{id}', 'LogController@index');

    Route::get('/rapports/show_by_site/{site_id}', 'RapportController@show_by_site')->middleware('secret');
    Route::get('/rapports/show/{id}', 'RapportController@show');
    Route::get('/rapports/field_selection', 'RapportController@field_selection');
    Route::post('/rapports/store', 'RapportController@store')->middleware('secret');
    Route::post('/rapports/attach_alarm/{id}', 'RapportController@attach_alarm')->middleware('secret');
    Route::post('/rapports/update/{id}', 'RapportController@update')->middleware('secret');
    Route::post('/rapports/soft_delete/{id}', 'RapportController@soft_delete')->middleware('secret');
    Route::post('/rapports/restaure/{id}', 'RapportController@restaure')->middleware('secret');
    Route::post('/rapports/cancel/{id}', 'RapportController@cancel')->middleware('secret');
    Route::get('/rapports', 'RapportController@index')->middleware('secret');

    Route::get('/actions/by_rapport_id/{id}', 'ActionController@show_by_rapport_id');
    Route::post('/actions/store', 'ActionController@store')->middleware('secret');
    Route::post('/actions/delete/{id}', 'ActionController@delete')->middleware('secret');

    Route::get('/contacts', 'ContactController@index');
    Route::post('/contacts/store', 'ContactController@store');
    Route::post('/contacts/update/{id}', 'ContactController@update');
    Route::post('/contacts/delete/{id}', 'ContactController@delete');
    Route::post('/contacts/restore/{id}', 'ContactController@restore');

    Route::get('/numeros', 'NumeroController@index');
    Route::get('/numeros/comprehensive', 'NumeroController@comprehensive');

    Route::get('/clients', 'ClientController@index');
    Route::post('/clients/store', 'ClientController@store');
    Route::post('/clients/update/{id}', 'ClientController@update');
    Route::post('/clients/delete/{id}', 'ClientController@delete');

    Route::get('/type_pointages', 'TypePointageController@index');

    Route::get('/get_date', 'PointageController@get_current_date');
    Route::post('/pointages/store', 'PointageController@store')->middleware('secret');
    Route::post('/pointages/reclamation_to_pointage/{id}', 'PointageController@reclamation_to_pointage')->middleware('secret');
    Route::post('/pointages/update/{id}', 'PointageController@update')->middleware('secret');
    Route::post('/pointages/delete/{id}', 'PointageController@delete')->middleware('secret');
    Route::post('/pointages/cancel_delete/{id}', 'PointageController@cancel_delete')->middleware('secret');
    Route::post('/pointages/change_motif/{id}', 'PointageController@change_motif')->middleware('secret');
    Route::get('/pointages/site', 'PointageController@pointage_par_site');
    Route::get('/pointages/show/{id}', 'PointageController@show');
    Route::get('/pointages/select_agents/{site_id}', 'PointageController@select_agents');
    Route::get('/pointages/late', 'PointageController@index_late')->middleware('secret');
    Route::post('/pointages/late/store', 'PointageController@store_late')->middleware('secret');
    Route::post('/pointages/comment', 'PointageController@add_comment')->middleware('secret');
    Route::get('/pointages/operation/{id}/{begin}/{end}', 'PointageController@operation');
    Route::get('pointages/operation/{id}/{begin}/{end}', 'PointageController@operation');
    Route::post('pointages/store_reclamation', 'PointageController@store_reclamation')->middleware('secret');
    Route::get('pointages/get_admin_user', 'PointageController@get_admin_user');
    Route::get('/pointages', 'PointageController@index');

    Route::get('/reclamations/show/{id}', 'ReclamationController@show');
    Route::post('/reclamations/delete/{id}', 'ReclamationController@delete')->middleware('secret');
    Route::get('/reclamations', 'ReclamationController@index');

    Route::get('/service24/show/{id}', 'Service24Controller@show');
    Route::get('/service24', 'Service24Controller@index');

    Route::post('/pointeuses/update_sim/{id}', 'ClientBiometriqueController@update_sim')->middleware('secret');
    Route::post('/pointeuses/cancel_register/{id}', 'ClientBiometriqueController@cancel_register')->middleware('secret');
    Route::post('/pointeuses/list_id/{id}', 'ClientBiometriqueController@list_id')->middleware('secret');
    Route::post('/pointeuses/get_template/{id}', 'ClientBiometriqueController@get_template')->middleware('secret');
    Route::post('/pointeuses/register/{id}', 'ClientBiometriqueController@register')->middleware('secret');
    Route::post('/pointeuses/enroll/{pointeuse_id}', 'ClientBiometriqueController@add_empreinte')->middleware('secret');
    Route::post('/pointeuses/remove_digit/{pointeuse_id}/{empreinte_id}', 'ClientBiometriqueController@remove_empreinte')->middleware('secret');

    Route::post('/client_users/store', 'ClientUserController@store')->middleware('secret');
    Route::post('/client_users/update/{id}', 'ClientUserController@update')->middleware('secret');
    Route::post('/client_users/delete/{id}', 'ClientUserController@delete')->middleware('secret');
    Route::post('/client_users/add_site/{id}', 'ClientUserController@add_site')->middleware('secret');
    Route::post('/client_users/remove_site/{id}', 'ClientUserController@remove_site')->middleware('secret');
    Route::get('/client_users/show/{id}', 'ClientUserController@show')->middleware('secret');
    Route::get('/client_users', 'ClientUserController@index')->middleware('secret');

    Route::post('/call/store', 'CallController@store')->middleware('secret');
    Route::get('/call/get_recall_data', 'CallController@get_recall_data')->name('api.call.get_recall_data');
    Route::post('call/make_call', 'CallController@make_call');

    Route::get('appelles', 'AppelleController@index');
    Route::get('appelles/a_rappeler', 'AppelleController@get_a_rappler');

    Route::get('/anomalie', 'AnomalieController@getAnomalieData');
    Route::get('/anomalie/count', 'AnomalieController@getAnomalieDataCount');
});

Route::get('/phpinfo', function (Request $request) {
    return ini_get('max_execution_time');
});

Route::get('/selection_sites', 'SiteController@selection');
Route::get('/select_diag_sites', 'SiteController@select_diag');
Route::get('/all_site', 'SiteController@all_site');
Route::get('/selectable_sites/{id}', 'SiteController@selectable');
Route::get('/group_pointage_sites', 'GroupPointageSiteController@index');

Route::get('/logs/ip', 'LogController@ip');
Route::post('/login', 'UserController@login');
Route::post('/logout', 'UserController@logout');
Route::post('/change_password', 'UserController@change_password');
Route::get('/getAuthUser', 'UserController@getAuthUser');
Route::post('/update_extension/{id}', 'UserController@update_extension');

Route::post('/group_sim/store', 'GroupSimController@store')->middleware('secret');
Route::post('/group_sim/update/{id}', 'GroupSimController@update')->middleware('secret');
Route::post('/group_sim/delete/{id}', 'GroupSimController@delete')->middleware('secret');
Route::get('/group_sim', 'GroupSimController@index');

Route::get('/monitor/repartition', 'MonitorController@repartition');
Route::get('/monitor/configuration', 'MonitorController@configuration');
Route::get('/monitor/show/{transmitter}', 'MonitorController@show');

Route::get('/centrales', 'CentraleController@index');


Route::post('/sanctions/store', 'SanctionController@store')->middleware('secret');
Route::post('/sanctions/update/{id}', 'SanctionController@update')->middleware('secret');
Route::post('/sanctions/delete/{id}', 'SanctionController@delete')->middleware('secret');
Route::get('/sanctions/all/{agent_id}', 'SanctionController@all');
Route::get('/sanctions/{agent_id}', 'SanctionController@index');

Route::post('/primes/store', 'PrimeController@store')->middleware('secret');
Route::post('/primes/update/{id}', 'PrimeController@update')->middleware('secret');
Route::post('/primes/delete/{id}', 'PrimeController@delete')->middleware('secret');
Route::get('/primes/month/{agent_id}', 'PrimeController@month');
Route::get('/primes/all/{agent_id}', 'PrimeController@all');
Route::get('/primes/{agent_id}', 'PrimeController@index');

Route::post('/conges/store', 'CongeController@store')->middleware('secret');
Route::post('/conges/update/{id}', 'CongeController@update')->middleware('secret');
Route::post('/conges/delete/{id}', 'CongeController@delete')->middleware('secret');
Route::get('/conges/{agent_id}', 'CongeController@index');

Route::post('/agents/change_site', 'AgentController@change_site');
Route::get('/agents/pointage/{id}', 'AgentController@pointage');
Route::get('/agents/notification', 'AgentController@notification');
Route::post('/agents/reset_digit/{id}', 'AgentController@reset_digit')->middleware('secret');
Route::get('/list_enroll_agents', 'AgentController@list_enroll_agents');
Route::get('/agents/archive', 'AgentController@archive')->middleware('secret');
Route::get('/agents', 'AgentController@index')->middleware('secret');
Route::get('/agents/modal', 'AgentController@index_modal');
Route::get('/agents/modal_sd', 'AgentController@index_modal_sd');
Route::get('/agents/pointeuse', 'AgentController@index_pointeuse');
Route::get('/agents/get_empreinte/{id}', 'AgentController@get_empreinte');
Route::get('/agents/get_site/{agent_id}', 'AgentController@get_site');
Route::get('/agents/show/{id}', 'AgentController@show')->middleware('secret');

Route::post('/empreintes/define/{id}', 'EmpreinteController@define');

Route::get('/pointeuses/new_id', 'PointeuseController@new_id');

Route::get('/paies/show/{agent_id}/{year}/{month}', 'PaieController@show')->middleware('secret');
Route::post('/paies/confirm/{agent_id}/{year}/{month}', 'PaieController@confirm')->middleware('secret');
Route::post('/paies/update/{agent_id}/{year}/{month}', 'PaieController@update')->middleware('secret');
Route::get('/paies', 'PaieController@index');

Route::get('/hours/show/{agent_id}/{year}/{month}', 'HourController@show')->middleware('secret');
Route::post('/hours/confirm/{agent_id}/{year}/{month}', 'HourController@confirm')->middleware('secret');
Route::post('/hours/cancel_confirmation/{paie_id}', 'HourController@cancel_confirmation')->middleware('secret');
Route::post('/hours/add_pointage', 'HourController@add_pointage')->middleware('secret');
Route::post('/hours/update_pointage/{pointage_id}', 'HourController@update_pointage')->middleware('secret');
Route::post('/hours/delete_pointage/{pointage_id}', 'HourController@delete_pointage')->middleware('secret');
Route::post('/hours/restore_pointage/{pointage_id}', 'HourController@restore_pointage')->middleware('secret');
Route::get('/hours', 'HourController@index');

Route::post('/digits/new_agent', 'AgentController@digit_to_new_agent');
Route::post('/digits/existing_agent/{id}', 'AgentController@digit_to_existing_agent');
Route::post('/digits/optic_agent/{id}', 'AgentController@digit_to_optic_agent');

Route::get('/reports', 'ReportController@index')->middleware('secret');
Route::get('/reports/pointage_agent', 'ReportController@pointage_agent');
Route::get('/reports/confirm_repartition_tache', 'ReportController@confirm_repartition_tache');
Route::post('/reports/indice_tache', 'ReportController@indice_tache');
Route::post('/reports/pointage_mensuel', 'ReportController@pointage_mensuel');

Route::post('/repartitions/assign_task', 'RepartitionTacheController@assign_task')->middleware('secret');
Route::post('/repartitions/cancel_task', 'RepartitionTacheController@cancel_task')->middleware('secret');
Route::post('/repartitions/lock_task', 'RepartitionTacheController@lock_task')->middleware('secret');
