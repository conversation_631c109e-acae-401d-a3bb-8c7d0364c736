START TRANSACTION;

UPDATE contacts 
SET nom = TRIM(nom), 
    prenom = TRIM(prenom), 
    adresse = TRIM(adresse);

CREATE TEMPORARY TABLE duplicate_contacts AS
SELECT
    MIN(c.idContact) AS master_id, 
    CAST(TRIM(COALESCE(n.numero, '')) AS BINARY) AS numero, 
    COALESCE(TRIM(c.nom), '') AS nom, 
    COALESCE(TRIM(c.prenom), '') AS prenom, 
    COALESCE(TRIM(c.adresse), '') AS adresse 
FROM contacts c
LEFT JOIN numeros n ON c.idContact = n.id_contact 
WHERE c.soft_delete = 0
GROUP BY 
    CAST(TRIM(COALESCE(n.numero, '')) AS BINARY), 
    COALESCE(TRIM(c.nom), ''), 
    COALESCE(TRIM(c.prenom), ''), 
    COALESCE(TRIM(c.adresse), '') 
HAVING COUNT(*) > 1; 

SELECT * FROM duplicate_contacts LIMIT 100;

CREATE TEMPORARY TABLE contacts_to_update AS
SELECT 
    c.idContact, 
    d.master_id
FROM contacts c
LEFT JOIN numeros n ON c.idContact = n.id_contact 
JOIN duplicate_contacts d 
    ON CAST(TRIM(COALESCE(n.numero, '')) AS BINARY) = d.numero 
    AND COALESCE(TRIM(c.nom), '') = d.nom 
    AND COALESCE(TRIM(c.prenom), '') = d.prenom 
    AND COALESCE(TRIM(c.adresse), '') = d.adresse 
WHERE c.idContact != d.master_id; 

SELECT * FROM contacts_to_update LIMIT 100;

UPDATE actioncontact 
JOIN contacts_to_update ctu 
    ON actioncontact.idcontact = ctu.idContact 
SET actioncontact.idcontact = ctu.master_id;


UPDATE clientcontact 
JOIN contacts_to_update ctu 
    ON clientcontact.IdContact = ctu.idContact 
SET clientcontact.IdContact = ctu.master_id;

UPDATE email 
JOIN contacts_to_update ctu 
    ON email.idcontact = ctu.idContact 
SET email.idcontact = ctu.master_id;

UPDATE habilites 
JOIN contacts_to_update ctu 
    ON habilites.idcontact = ctu.idContact 
SET habilites.idcontact = ctu.master_id;

UPDATE interventions 
JOIN contacts_to_update ctu 
    ON interventions.idcontact = ctu.idContact 
SET interventions.idcontact = ctu.master_id;

UPDATE contacts
SET soft_delete = 1
WHERE idContact IN (SELECT idContact FROM contacts_to_update WHERE idContact != master_id);

SELECT idContact, nom, prenom, adresse, soft_delete 
FROM contacts 
WHERE idContact IN (SELECT idContact FROM contacts_to_update WHERE idContact != master_id)
LIMIT 100;

CREATE TEMPORARY TABLE unused_contacts AS
SELECT c.idContact
FROM contacts c
LEFT JOIN actioncontact ac ON c.idContact = ac.idcontact
LEFT JOIN clientcontact cc ON c.idContact = cc.IdContact
LEFT JOIN email e ON c.idContact = e.idcontact
LEFT JOIN habilites h ON c.idContact = h.idcontact
LEFT JOIN interventions i ON c.idContact = i.idcontact
LEFT JOIN numeros n ON c.idContact = n.id_contact 
WHERE ac.idcontact IS NULL 
  AND cc.IdContact IS NULL 
  AND e.idcontact IS NULL 
  AND h.idcontact IS NULL 
  AND i.idcontact IS NULL
  AND n.id_contact IS NULL 
  AND c.soft_delete = 0; 
  
SELECT * FROM unused_contacts LIMIT 100;

UPDATE contacts
SET soft_delete = 1
WHERE idContact IN (SELECT idContact FROM unused_contacts);

SELECT idContact, nom, prenom, adresse, soft_delete 
FROM contacts 
WHERE idContact IN (SELECT idContact FROM unused_contacts)
LIMIT 100;

DROP TEMPORARY TABLE IF EXISTS duplicate_contacts;
DROP TEMPORARY TABLE IF EXISTS contacts_to_update;
DROP TEMPORARY TABLE IF EXISTS unused_contacts;

COMMIT;