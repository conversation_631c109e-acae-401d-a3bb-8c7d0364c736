const axios = require('axios')
const mysql = require('mysql')

let currentPort = Math.floor(Math.random() * 15 + 2)
console.log("currentPort: " + currentPort)
const querysend = {
	'event': 'txsms',
	'userid': '0',
	'num': '',
	'port': '',
	'encoding': '0',
}

const auth = {
    username: 'ApiUserAdmin',
    password: '1234'
}

const db_config = require("./auth").db_config_ovh
const pool = mysql.createPool(db_config);

const sqlSelectPointeuse = "SELECT id, sim FROM pointeuses "
    + "where sim is not null and (soft_delete is null or soft_delete = 0)"

function sendConfigPwd(pointeuses, index){
    if(index < pointeuses.length){
        if(currentPort == 16)
            currentPort = 3
        else
            currentPort++
        const pointeuse = pointeuses[index]
        querysend.num = pointeuse.sim
        querysend.port = 3
        querysend.smsinfo = "addrIp" + pointeuse.id + "123456" + process.argv[2] + ":" + process.argv[3]
        console.log("-------------")
        if(pointeuse.sim.match(/^03[2|7]\d{7}$/)) {
            axios.post('http://************:80/API/TaskHandle', querysend, {auth: auth})
            .then(({data}) => {
                console.log(data)
                waitBeforeConfig(pointeuses, index+1)
            })
            .catch((e) => {
                console.error(e)&
                waitBeforeConfig(pointeuses, index)
            })
        }
        else {
            console.log("different sim : " + pointeuse.sim)
        }
    }
    else {
        console.log("change transmitter done")
    }
}

function waitBeforeConfig(pointeuses, index){
	setTimeout(() => sendConfigPwd(pointeuses, index), 200)
}

if(!process.argv[2])
    console.log("ip requis.")
else if(!process.argv[2].match(/^\d+\.\d+\.\d+$/))
    console.log("ip incorrect")
else if(!process.argv[3])
    console.log("port requis.")
else if(!process.argv[3].match(/^\d+$/))
    console.log("port incorrect")
else {
pool.query(sqlSelectPointeuse, [], (err, pointeuses) => {
    if(err)
        console.error(err)
    else {
        sendConfigPwd(pointeuses, 0)
    }
})
}