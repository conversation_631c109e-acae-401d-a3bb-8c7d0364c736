const axios = require('axios')
const mysql = require('mysql')

let currentPort = Math.floor(Math.random() * 15 + 2)
console.log("currentPort: " + currentPort)
const querysend = {
	'event': 'txsms',
	'userid': '0',
	'num': '',
	'port': '',
	'encoding': '0',
}

const auth = {
    username: 'ApiUserAdmin',
    password: '1234'
}

const db_config = require("../auth").db_config_ovh
const pool = mysql.createPool(db_config);

const serverDB = process.argv[2]
const passerelleIp = process.argv[3]
const passerellePort = process.argv[4]

const sqlSelectSite = "SELECT idsite, prom, numeropuces, group_diag_id, transmitter FROM sites "
    + "where sms = 1 and idcentrale=7 and prom is not null and (soft_delete is null or soft_delete = 0) "
    + "and transmitter in ('0321134413', '0321154685')"

function sendConfigPwd(sites, index){
    if(index < sites.length){
        if(currentPort > passerellePort)
            currentPort = 3
        else
            currentPort++
        const site = sites[index]
        querysend.num = site.prom
        querysend.port = 3
        if(serverDB == "atelier")
            querysend.smsinfo = "12341A2#1#0321154686#"
        else {
            if(site.group_diag_id == 1)
                querysend.smsinfo = "12341A2#1#" + site.transmitter + "#"
            else
                querysend.smsinfo = "12341A2#1#" + site.transmitter + "#"
        }
        console.log("-------------")
        if(site.prom.match(/^03[2|7]\d{7}$/)) {
            axios.post('http://'+ passerelleIp +':80/API/TaskHandle', querysend, {auth: auth})
            .then(({data}) => {
                console.log(data)
                waitBeforeConfig(sites, index+1)
            })
            .catch((e) => {
                console.error(e)&
                waitBeforeConfig(sites, index)
            })
        }
        else {
            console.log("different sim : " + site.prom)
        }
    }
    else {
        console.log("change transmitter done")
        process.exit(1)
    }
}

function waitBeforeConfig(sites, index){
	setTimeout(() => sendConfigPwd(sites, index), 200)
}

if(!["atelier", "maroho"].includes(serverDB))
    console.log("db serveur requis : atelier | maroho")
else if(!["************", "************", "*************"].includes(passerelleIp))
    console.log("passerelle ip requis : ************ | ************ | *************")
else if(!["16", "8"].includes(passerellePort))
    console.log("passerelle port requis : 16 | 8")
else {
    pool.query(sqlSelectSite, [], (err, sites) => {
        if(err)
            console.error(err)
        else {
            sendConfigPwd(sites, 0)
        }
    })
}