#!/bin/sh
while true 
do 
    php delete_vigilance.php
    node task/do_aquittement.js
	node export/appelle.js task
	node export/diagnostique.js task
	node export/diag_biometrique.js task
	node export/biometrique.js task
	node export/rapport.js task
	node export/pointage.js task
	node export/pointage_tmv.js task
	node export/pointage_eclosia.js task
	node export/transmitter.js task
	node export/bouton.js tana task
	node export/bouton.js province task
	node export/rapport_mailing.js task
	node export/sanction/manque.js task
	sleep 300
done 
