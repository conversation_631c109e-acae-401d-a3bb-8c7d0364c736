FROM node:16-alpine

ENV TZ=Indian/Antananarivo

RUN apk add --no-cache \
    tzdata \
    && cp /usr/share/zoneinfo/Indian/Antananarivo /etc/localtime \
    && echo "Indian/Antananarivo" > /etc/timezone

WORKDIR /opt/app/tls

RUN mkdir -p script

RUN echo '{}' > package.json && \
    echo '{}' > package-lock.json

COPY auth.js ./

COPY export/pointage_eclosia.js ./script/

RUN npm install moment mysql2 exceljs nodemailer axios

CMD ["sh", "-c", "while true; do node script/pointage_eclosia.js task; sleep 300; done"]