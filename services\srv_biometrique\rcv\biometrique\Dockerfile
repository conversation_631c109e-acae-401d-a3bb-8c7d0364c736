FROM node:16-alpine

ENV TZ=Indian/Antananarivo

RUN apk add --no-cache \
    tzdata \
    && cp /usr/share/zoneinfo/Indian/Antananarivo /etc/localtime \
    && echo "Indian/Antananarivo" > /etc/timezone

WORKDIR /opt/app/tls

RUN echo '{}' > package.json && \
    echo '{}' > package-lock.json

COPY auth.js ./

COPY rcv/biometrique.js ./rcv/biometrique.js

RUN npm install lodash process fs moment mysql2 axios

CMD ["node", "rcv/biometrique.js"]