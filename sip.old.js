const net = require('net')
const moment = require('moment')
const mysql = require('mysql')
const fs = require('fs')
const recoveryPath = 'recovery/sip/'

let lastReception = {}

const {db_config_ovh, db_config_maroho} = require("./auth")
const pool = mysql.createPool(db_config_ovh)
const pool_tls = mysql.createPool(db_config_maroho)
const sqlSelectCheckphoneStatus = "SELECT do_checkphone FROM sites WHERE checkphone = ? limit 1"
const sqlInsertLog = "INSERT INTO ademcomemlog(checkphone, dtarrived, codeevent, sip, istraite) VALUES(?, ?, ?, ?, 1)"

var sockets = {}
var server = net.createServer(function(socket) {
	socket.on('error', (err) => {
		//if(err ==='ERCONNRESET')
		Object.keys(sockets).map((key) => {
			if(sockets[key] == socket) delete sockets[key]
		})
		socket.end()
		console.log("*** erreur reset ***")
	})
	socket.on('timeout', () => {
		console.log("TIMEOUT close")
		socket.end()
	})
	socket.on('data', (data) => {
        const message = data.toString()
        console.log(message)
        const rows = message.split('\n')
        let call = {}
        rows.forEach(r => {
          if(/^INVITE sip:\d+@.+/.test(r)){
            const group = /^INVITE sip:(\d+)@.+/.exec(r)
            call.sip = group[1]
          }
          else if(/^Contact: <sip:\+261\d+@.+/.test(r)){
            const group = /^Contact: <sip:\+261(\d+)@.+/.exec(r)
            call.number = "0" + group[1]
          }
        })
        if(call.sip && call.number && (!lastReception[call.number] || moment().isAfter(moment(lastReception[call.number]).add(1, 'minute')))){
          console.log("\n-----------------")
          console.log(call.number + " " + moment().format("DD-MM-YY HH:mm:ss"))
          lastReception[call.number] = moment()
          call.datetime = lastReception[call.number].format("YYYY-MM-DD HH:mm:ss")
          const queryValue = [call.number, call.datetime, 1000, call.sip]
          pool.query(sqlSelectCheckphoneStatus, [call.number], (err, result) => {
            if(err){
              console.error(err)
              fs.writeFile(
                recoveryPath +  moment().format('YYMMDDHHmmss') + '_' + queryValue[0] + '.json', 
                JSON.stringify(queryValue), 
                (err) => {
                  console.error(err)
                }
              )
            }
            else if(result.length > 0 && result[0]['do_checkphone']){
              pool_tls.query(sqlInsertLog, queryValue, (err) => {
                if(err){
                  console.error(err)
                  fs.writeFile(
                    recoveryPath +  moment().format('YYMMDDHHmmss') + '_' + queryValue[0] + '.json', 
                    JSON.stringify(queryValue), 
                    (err) => {
                      console.error(err)
                    }
                  )
                }
                else {
                  console.log("Insert succefully")
                }
              })
            }
            else if(result.length > 0)
              console.log("checkphone not allow...")
            else 
              console.log("site not found, number: " + call.number)
          })
        }
      });
})


setTimeout(() => {
	server.listen(5060, "0.0.0.0", ()=>{
	  const address = server.address();
	  const port = address.port;
	  const family = address.family;
	  const ipaddr = address.address;
	  console.log('Server '+ family + ' is listening at ' + ipaddr + ':' + port);
	});
}, 2000)
