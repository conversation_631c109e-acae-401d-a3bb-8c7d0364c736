var smpp = require('smpp');
const moment = require('moment')
const fs = require('fs');

var session = smpp.connect({
	url: 'smpp://10.249.2.15:9500',
	auto_enquire_link_period: 10000,
	debug: true
})

const { decryptSMS } = require('./decryptSms');

const smpp1 = {
    id: "ssoit",
    pwd: "sso@it2021"
}
const smpp2 = {
    id: "ssoit2",
    pwd: "s@oi201"
}
const smpp3 = {
    id: "ssoit3",
    pwd: "ssoit3"
}

const auth = process.argv[2] == "3" ? smpp3 : process.argv[2] == "2" ? smpp2 : smpp1

const pathname = '/opt/app/tls/logs/sms/virtual/' + moment().format('YYYYMMDDHHmmss') + '.log'
const recoveryPath = '/opt/app/tls/recovery/sms/virtual/'

fs.writeFile(pathname, moment().format('LLLL') + '\n\n', (err) => {
	console.error(err)
})

let connected = false
session.on('connect', function () {
    connected = true
    session.bind_transceiver({
        system_id: auth.id,
        password: auth.pwd
    }, function (pdu) {
        if (pdu.command_status === 0) {
            connected = true
            console.log(moment().format('HH:mm:ss') + ' # connected!');
        }
    });

});

session.on('close', function () {
    if (connected) { 
        fs.appendFile(pathname, '\n!!!!!\nClosed at ' +  moment().format('DD-MM-YY HH:mm:ss') + '\n----------\n', (err) => {
            if(err) console.error(err);
        })
        console.log(moment().format('HH:mm:ss') + " # try to reconnect.")
        session.connect()
    }
    else {
        console.log(moment().format('HH:mm:ss') + " # closed.")
    }
});

session.on('debug', function(type, msg, payload) {
	console.log({type: type, msg: msg, payload: payload});
});

session.on('error', function (error) {
    console.log(moment().format('HH:mm:ss') + " # error: "  + error.code)
    fs.appendFile(pathname, '\nxxxxx\nError ' +  moment().format('DD-MM-YY HH:mm:ss') + "\n" + error + '\n----------\n', (err) => {
        if(err) console.error(err);
    })
    connected = false
});

session.on('pdu', function (pdu) {
    if (pdu.command == 'deliver_sm') {
        var from = pdu.source_addr.toString();
        var to = pdu.destination_addr.toString();
        var message = '';
        if (pdu.short_message && pdu.short_message.message) {
          message = pdu.short_message.message;
        }
        console.log(moment().format('HH:mm:ss') + " # sms from "  + from + " to " + to + '->\n' + message)
        session.deliver_sm_resp({ sequence_number: pdu.sequence_number });
        
        let rowData = decryptSMS(message, "0" + to.slice(3), "0" + from.slice(3), moment().format('YYYYMMDDHHmmss'), null, pathname)
        
        if(rowData.length == 0)
            fs.appendFile(pathname, '\n' + moment().format("DD-MM-YY HH:mm:ss") + ' prom: ' + from + ' -> transmitter: ' + to + '\n' + message  + '\n----------\n', (err) => {
                if(err) console.error(err);
            })

        let queryValues = []
        rowData.forEach(row => {
            row.zones = row.zones ? row.zones : 0 
            if(row.codeevent && !queryValues.map(v => (v[0] + v[1] + v[4] + v[5] + v[6])).includes(row.dtarrived + row.prom + row.codeevent + row.eventQualify + row.zones)){
                const rowValue = [row.dtarrived, row.prom, row.pointeuse_id, null, row.codeevent, row.eventQualify, row.zones, 
                    row.transmitter, "1"]
                console.log(rowValue)
                queryValues.push(rowValue)
            }
        })
        console.log("Nb row: " + queryValues.length)
        if(queryValues.length > 0){
            const firstRow = queryValues[0]
            fs.writeFile(
                recoveryPath +  moment(firstRow[0], "YYYY/MM/DD HH:mm:ss").format('YYYYMMDDHHmmss')
                        + '_' + firstRow[1] + '_' + firstRow[4] + '_' + firstRow[5] + '_' + firstRow[6] + '.json', 
                JSON.stringify(queryValues),
                (err) => {
                    console.error(err)
                }
            )
        }
    }

});