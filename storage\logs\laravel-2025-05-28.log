[2025-05-28 16:41:58] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 16:42:36] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 16:43:36] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 16:44:36] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 16:45:36] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 16:46:36] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 16:47:36] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 16:48:36] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 16:49:30] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 16:50:30] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 16:51:30] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 16:52:30] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 16:53:30] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 16:54:30] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 16:55:30] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 16:56:30] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 16:57:30] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 16:58:30] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 16:59:30] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 17:00:30] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 17:01:30] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 17:02:30] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 17:03:30] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 17:04:30] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 17:05:30] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 17:06:30] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 17:07:30] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 17:08:30] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 17:09:30] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
[2025-05-28 17:10:30] local.ERROR: Uncaught ReflectionException: Class App\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Policies\\Mo...')
#1 [internal function]: {closure}('App\\Policies\\Mo...', 'App\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class App\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Policies\\\\Mo...')
#1 [internal function]: {closure}('App\\\\Policies\\\\Mo...', 'App\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(148): Symfony\\Component\\Debug\\Exception\\FatalErrorException->__construct(''Uncaught Refle...', '1', '0', ''Command line c...', '1', '0', '???', '???', '???')
#1 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalExceptionFromError('['type' => 1, '...', '0')
#2 D:\\laravel\\DRX\\tls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 {main}
"} 
