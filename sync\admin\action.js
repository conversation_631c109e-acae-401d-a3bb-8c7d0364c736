const moment = require('moment')
const mysql = require('mysql2')
const fs = require("fs");

moment.locale('fr')
const auth = require("../../auth");
const { argv } = require('process');

const { db_config_zo, db_config_admin } = auth
const pool_tls = mysql.createPool(db_config_zo)
const pool_admin = mysql.createPool(db_config_admin)

const pathname = 'logs/sync/action/' + moment().format('YYYYMMDDHHmmss') + '.log'
fs.writeFile(pathname, moment().format('LLLL') + '\n\n', (err) => {
    console.error(err)
})

const sqlSelectAction = "SELECT id, type_action_id, rapport_id, soft_delete, created_at, updated_at from actions " +
    "where synchronized_at is null or (admin_updated_at is not null and synchronized_at <= admin_updated_at) " +
    (argv[2] == 'reverse' ? " order by id desc  limit 100 " : " limit 50 ")
const sqlInsertOrUpdateAction = "INSERT INTO actions(id, type_action_id, rapport_id, soft_delete, created_at, updated_at) " +
    "VALUES (?, ?, ?, ?, ?, ?) " +
    "ON DUPLICATE KEY UPDATE type_action_id=?, rapport_id=?, soft_delete=?, created_at=?, updated_at=?"
const sqlDeleteAction = "DELETE FROM actions WHERE id=?"
const sqlUpdateAction = "UPDATE actions SET synchronized_at = now() WHERE id=?"
const sqlInsertLastSync = "UPDATE synchronisations SET last_sync_update = now() WHERE service = 'action'"

function syncActionById(actions, index) {
    if (index < actions.length) {
        const action = actions[index]
        const params = [action.id, action.type_action_id, action.rapport_id, action.soft_delete, action.created_at, action.updated_at]
        if (action.soft_delete) {
            pool_admin.query(sqlDeleteAction, [action.id], async (err, res) => {
                if (err) {
                    console.log("err found")
                    console.error(err)
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if (err) console.error(err);
                    })
                    waitBeforeUpdate()
                }
                else {
                    console.log("sync action: " + action.id + " deleted!")
                    pool_tls.query(sqlUpdateAction, [action.id], async (err, res) => {
                        if (err) {
                            fs.appendFile(pathname, err.toString(), (err) => {
                                if (err) console.error(err);
                            })
                            console.error(err)
                        }

                        pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                            if (err) {
                                fs.appendFile(pathname, err.toString(), (err) => {
                                    if (err) console.error(err);
                                })
                                console.error(err)
                            }
                        })
                    })
                    setTimeout(() => {
                        syncActionById(actions, index + 1)
                    }, 200)
                }
            })
        }
        else {
            pool_admin.query(sqlInsertOrUpdateAction, [...params, ...params.slice(1)], async (err, res) => {
                if (err) {
                    console.log("err found")
                    console.error(err)
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if (err) console.error(err);
                    })
                    waitBeforeUpdate()
                }
                else {
                    console.log("sync action: " + action.id)
                    pool_tls.query(sqlUpdateAction, [action.id], async (err, res) => {
                        if (err) {
                            fs.appendFile(pathname, err.toString(), (err) => {
                                if (err) console.error(err);
                            })
                            console.error(err)
                        }

                        pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                            if (err) {
                                fs.appendFile(pathname, err.toString(), (err) => {
                                    if (err) console.error(err);
                                })
                                console.error(err)
                            }
                        })
                    })
                    setTimeout(() => {
                        syncActionById(actions, index + 1)
                    }, 200)
                }
            })
        }
    }
    else
        waitBeforeUpdate()
}

function updateData() {
    pool_tls.query(sqlSelectAction, [], async (err, actions) => {
        if (err) {
            fs.appendFile(pathname, err.toString(), (err) => {
                if (err) console.error(err);
            })
            waitBeforeUpdate()
            console.error(err)
        }
        else {

            pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                if (err) {
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if (err) console.error(err);
                    })
                    console.error(err)
                }
            })
            if (actions.length > 0) {
                console.log("action to sync: " + actions.length)
                syncActionById(actions, 0)
            }
            else {
                console.log(moment().format("YYYY-MM-DD HH:mm:ss"))
                waitBeforeUpdate()
            }
        }
    })
}

let count = 1
function waitBeforeUpdate() {
    console.log("-----" + (count > 1 ? "-----" : "") + (count > 2 ? "-----" : "") + (count > 3 ? "-----" : ""))
    setTimeout(() => {
        updateData()
    }, 3000)
    if (count > 3) count = 1
    else count++
}

updateData()
