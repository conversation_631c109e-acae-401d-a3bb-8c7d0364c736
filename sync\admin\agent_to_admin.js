const moment = require('moment')
const mysql = require('mysql2')
const fs = require("fs");

moment.locale('fr')
const auth = require("../../auth");
const { argv } = require('process');

const {db_config_zo, db_config_admin} = auth
const pool_tls = mysql.createPool(db_config_zo)
const pool_admin = mysql.createPool(db_config_admin)

const pathname = 'logs/sync/agent/' + moment().format('YYYYMMDDHHmmss') + '.log'
fs.writeFile(pathname, moment().format('LLLL') + '\n\n', (err) => {
    console.error(err)
})

const sqlSelectAgent = "SELECT id, societe_id, numero_stagiaire, numero_employe, num_emp_soit, num_emp_saoi, nom, " +
    "site_id , real_site_id, date_embauche, date_confirmation, date_conf_soit, date_sortie, soft_delete, " +
    "sal_forfait, fonction_id, observation, agence_id, last_update, created_at " +
    "from agents " +
    "where id > 27387 " 
const sqlInsertOrUpdate = "INSERT INTO employes(id, societe_id, numero_stagiaire, numero_employe, num_emp_soit, num_emp_saoi, nom, site_id, " +
    "real_site_id, date_embauche, date_confirmation, date_conf_soit, date_sortie, soft_delete, fonction_id, " +
    "sal_forfait, observation, agence_id, last_update, created_at " +
    ") VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) " +
    "ON DUPLICATE KEY UPDATE societe_id=?, numero_stagiaire=?, numero_employe=?, num_emp_soit=?, num_emp_saoi=?, nom=?, site_id=?, real_site_id=?, " +
    "date_embauche=?, date_confirmation=?, date_conf_soit=?, date_sortie=?, soft_delete=?, fonction_id=?, " +
    "sal_forfait=?, observation=?, agence_id=?, last_update=?, created_at=? "

function updateAgentById(agents, index) {
    if (index < agents.length) {
        const agent = agents[index]
        const params = [agent.id, agent.societe_id, agent.numero_stagiaire, agent.numero_employe, agent.num_emp_soit, agent.num_emp_saoi, agent.nom,
        agent.site_id, agent.real_site_id, agent.date_embauche, agent.date_confirmation, agent.date_conf_soit,
        agent.date_sortie, agent.soft_delete, agent.fonction_id, agent.sal_forfait,
        agent.observation, agent.agence_id, agent.last_update, agent.created_at
        ]
        pool_admin.query(sqlInsertOrUpdate, [...params, ...params.slice(1)], async (err, res) => {
            if (err) {
                console.log("err found")
                console.error(err)
                fs.appendFile(pathname, err.toString(), (err) => {
                    if (err) console.error(err);
                })
                waitBeforeUpdate()
            }
            else {
                setTimeout(() => {
                    updateAgentById(agents, index + 1)
                }, 200)
            }
        })
    }
    else
        process.exit(1)
}
function updateData() {
    pool_tls.query(sqlSelectAgent, [], async (err, agents) => {
        if (err) {
            console.error(err)
            process.exit(1)
        }
        else {
            if (agents.length > 0) {
                console.log("agent to sync: " + agents.length)
                updateAgentById(agents, 0)
            }
            else {
                console.log(moment().format("YYYY-MM-DD HH:mm:ss"))
                waitBeforeUpdate()
            }
        }
    })
}

let count = 1
function waitBeforeUpdate() {
    console.log("-----" + (count > 1 ? "-----" : "") + (count > 2 ? "-----" : "") + (count > 3 ? "-----" : ""))
    setTimeout(() => {
        updateData()
    }, 3000)
    if (count > 3) count = 1
    else count++
}

updateData()
