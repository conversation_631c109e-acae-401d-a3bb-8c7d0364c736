const moment = require('moment')
const mysql = require('mysql2')
const fs = require("fs");

moment.locale('fr')
const auth = require("../../auth")

const {db_config_zo, db_config_admin} = auth
const pool_tls = mysql.createPool(db_config_zo)
const pool_admin = mysql.createPool(db_config_admin)

const pathname = 'logs/sync/vigilance/' + moment().format('YYYYMMDDHHmmss') + '.log'
fs.writeFile(pathname, moment().format('LLLL') + '\n\n', (err) => {
    console.error(err)
})

const sqlSelectAdemco = "SELECT idademco, rapport_id, zones, dtarrived, site_id, agent_id, pointeuse_id, eventQualify, codeTevent " +
    "from ademcotemp " +
    "where synchronized_at is null or (admin_updated_at is not null and synchronized_at  <= admin_updated_at)" +
    (process.argv[2] == 'reverse' ? " order by idademco desc  limit 300 " : " limit 100 ")
const sqlInsertAdemco = "INSERT INTO alarms(idademco, rapport_id, zones, dtarrived, site_id, employe_id, pointeuse_id, eventQualify, codeTevent) " +
    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) " +
    "ON DUPLICATE KEY UPDATE  rapport_id=?, zones=?, dtarrived=?, site_id=?, employe_id=?, pointeuse_id=?, eventQualify=?, codeTevent=?"
const sqlUpdateAdemco = "UPDATE ademcotemp SET synchronized_at = now() WHERE idademco = ?"
const sqlInsertLastSync = "UPDATE synchronisations SET last_sync_update = now() WHERE service = 'alarm'"

function syncVigilanceById(vigilances, index) {
    if (index < vigilances.length) {
        const vigilance = vigilances[index]
        const params = [vigilance.idademco, vigilance.rapport_id, vigilance.zones, vigilance.dtarrived,
        vigilance.site_id, vigilance.agent_id, vigilance.pointeuse_id, vigilance.eventQualify, vigilance.codeTevent]
        pool_admin.query(sqlInsertAdemco, [...params, ...params.slice(1)], async (err, res) => {
            if (err) {
                console.log("err found")
                console.error(err)
                fs.appendFile(pathname, err.toString(), (err) => {
                    if (err) console.error(err);
                })
                waitBeforeUpdate()
            }
            else {
                console.log("sync alarm: " + vigilance.idademco)
                pool_tls.query(sqlUpdateAdemco, [vigilance.idademco], async (err, res) => {
                    if (err) {
                        fs.appendFile(pathname, err.toString(), (err) => {
                            if (err) console.error(err);
                        })
                        console.error(err)
                    }
                    pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                        if (err) {
                            fs.appendFile(pathname, err.toString(), (err) => {
                                if (err) console.error(err);
                            })
                            console.error(err)
                        }
                    })
                })
                setTimeout(() => {
                    syncVigilanceById(vigilances, index + 1)
                }, 200)
            }
        })
    }
    else
        waitBeforeUpdate()
}

function updateData() {
    pool_tls.query(sqlSelectAdemco, [], async (err, vigilances) => {
        if (err) {
            fs.appendFile(pathname, err.toString(), (err) => {
                if (err) console.error(err);
            })
            setTimeout(() => {
                updateData()
            }, 60000)
            console.error(err)
        }
        else {
            pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                if (err) {
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if (err) console.error(err);
                    })
                    console.error(err)
                }
            })
            if (vigilances.length > 0) {
                console.log("vigilance to sync: " + vigilances.length)
                syncVigilanceById(vigilances, 0)
            }
            else {
                console.log(moment().format("YYYY-MM-DD HH:mm:ss"))
                waitBeforeUpdate()
            }
        }
    })
}

let count = 1
function waitBeforeUpdate() {
    console.log("-----" + (count > 1 ? "-----" : "") + (count > 2 ? "-----" : "") + (count > 3 ? "-----" : ""))
    setTimeout(() => {
        updateData()
    }, 3000)
    if (count > 3) count = 1
    else count++
}

updateData()
