const moment = require('moment')
const mysql = require('mysql2')
const fs = require("fs");

moment.locale('fr')
const auth = require("../../auth");
const { argv } = require('process');

const db_config_zo = auth.db_config_zo
const pool_tls = mysql.createPool(db_config_zo)

const db_config_admin = auth.db_config_admin
const pool_admin = mysql.createPool(db_config_admin)

const sendMail = auth.sendMail

const dest = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]

const pathname = `logs/sync/contact/${moment().format('YYYYMMDDHHmmss')}.log`;

const sqlSelectContacts = `
    SELECT idContact, nom, prenom, adresse, lastupdate, soft_delete
    FROM contacts
    WHERE synchronized_at is NULL OR (admin_updated_at IS NOT NULL AND synchronized_at <= admin_updated_at)
    LIMIT 50;
`;

const sqlInsertOrUpdateContact = `
    INSERT INTO contacts (idContact, nom, prenom, adresse, lastupdate, soft_delete)
    VALUES (?, ?, ?, ?, ?, ?)
    ON DUPLICATE KEY UPDATE
        nom = VALUES(nom),
        prenom = VALUES(prenom),
        adresse = VALUES(adresse),
        lastupdate = VALUES(lastupdate),
        soft_delete = VALUES(soft_delete);
`;

const sqlUpdateSynchronizedAt = `
    UPDATE contacts
    SET synchronized_at = NOW()
    WHERE idContact = ?;
`;

const sqlInsertLastSync = "UPDATE synchronisations SET last_sync_update = now() WHERE service = 'contact'"

fs.writeFile(pathname, `${moment().format('LLLL')}\n\n`, (err) => {
    if (err) console.error(err);
});


function formatErrorForApp(err) {
    const stackTrace = err.stack ? err.stack.replace(/\n/g, '<br>') : 'No stack trace available';
    const otherProperties = Object.getOwnPropertyNames(err)
        .filter(prop => !['message', 'name', 'stack'].includes(prop))
        .map(prop => `<strong>${prop}:</strong> ${JSON.stringify(err[prop])}`)
        .join('<br>') || 'None';

    return `
        <div style="font-family: Arial, sans-serif; color: #333;">
            <h2 style="color: #d9534f;">Error Report</h2>
            <div>
                <h4 style="margin-bottom: 5px;">Message:</h4>
                <p style="background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px;">${err.message || 'N/A'}</p>
            </div>
            <div>
                <h4 style="margin-bottom: 5px;">Error Type:</h4>
                <p style="background: #e2e3e5; color: #383d41; padding: 10px; border-radius: 5px;">${err.name || 'N/A'}</p>
            </div>
            <div>
                <h4 style="margin-bottom: 5px;">Stack Trace:</h4>
                <p style="background: #f1f1f1; color: #555; padding: 10px; border-radius: 5px; font-family: monospace; overflow-x: auto;">${stackTrace}</p>
            </div>
            <div>
                <h4 style="margin-bottom: 5px;">Other Properties:</h4>
                <p style="background: #f1f1f1; color: #555; padding: 10px; border-radius: 5px;">${otherProperties}</p>
            </div>
        </div>`;
}

function syncContact(contacts, index) {
    if (index < contacts.length) {
        const contact = contacts[index];

        const softDelete = contact.soft_delete !== null ? contact.soft_delete : 0;
        pool_admin.query(sqlInsertOrUpdateContact, [
            contact.idContact, contact.nom, contact.prenom, contact.adresse, contact.lastupdate, softDelete
        ], (err, result) => {
            if (err) {
                logError(`Error syncing contact ${contact.idContact}: ${err}`);
                sendMail(pool_admin, dest, "Erreur Synchronisation Contact (Admin) insert or update", formatErrorForApp(err), [], () => { })
                retrySyncContact(contacts, index);
            } else {
                console.log(`Synced contact: ${contact.idContact}`);
                pool_tls.query(sqlUpdateSynchronizedAt, [contact.idContact], (err, result) => {
                    if (err) {
                        logError(`Error updating synchronized_at for contact ${contact.idContact}: ${err}`);
                        sendMail(pool_tls, dest, "Erreur Synchronisation Contact (TLS) Update synchronized_at", formatErrorForApp(err), [], () => { })
                        retrySyncContact(contacts, index);
                    } else {
                        console.log(`Updated synchronized_at for contact: ${contact.idContact}`);
                        pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                            if (err) {
                                fs.appendFile(pathname, err.toString(), (err) => {
                                    if (err) console.error(err);
                                })
                                console.error(err)
                            }
                            setTimeout(() => {
                                syncContact(contacts, index + 1);
                            }, 200)
                        })
                    }
                });
            }
        });
    } else {
        waitBeforeUpdate();
    }
}

function retrySyncContact(contacts, index) {
    setTimeout(() => {
        syncContact(contacts, index);
    }, 3000);
}

function logError(message) {
    console.error(message);
    fs.appendFile(pathname, `\n${moment().format('YY-MM-DD HH:mm:ss')}> ${message}`, (err) => {
        if (err) console.error(err);
    });
}

function waitBeforeUpdate() {
    setTimeout(() => {
        fetchAndSyncContacts();
    }, 3000);
}

function fetchAndSyncContacts() {
    pool_tls.query(sqlSelectContacts, (err, contacts) => {
        if (err) {
            logError(`Error fetching contacts: ${err}`);
            sendMail(pool_tls, dest, "Erreur Synchronisation Contact (TLS) fetch contacts", formatErrorForApp(err), [], () => { })
            setTimeout(() => {
                fetchAndSyncContacts();
            }, 60000);
        } else if (contacts.length > 0) {
            console.log(`Found ${contacts.length} contacts to sync`);
            syncContact(contacts, 0);
            pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                if (err) {
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if (err) console.error(err);
                    })
                    console.error(err)
                }
            })
        } else {
            console.log('No contacts to sync');
            waitBeforeUpdate();
            pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                if (err) {
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if (err) console.error(err);
                    })
                    console.error(err)
                }
            })
        }
    });
}

fetchAndSyncContacts();
