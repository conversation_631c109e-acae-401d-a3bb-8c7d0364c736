const moment = require('moment');
const mysql = require('mysql2');
const fs = require("fs");

moment.locale('fr');
const auth = require("../../auth");
const { argv } = require('process');

const db_config_zo = auth.db_config_zo;
const pool_tls = mysql.createPool(db_config_zo);

const db_config_admin = auth.db_config_admin;
const pool_admin = mysql.createPool(db_config_admin);

const sendMail = auth.sendMail; // Assuming sendMail is correctly imported from auth

const dest = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"];

const pathname = `logs/sync/habilites/${moment().format('YYYYMMDDHHmmss')}.log`;

const sqlSelectHabilites = `
    SELECT idhabilite, idcontact, idsite, idordre, Timedisponible, starttime, stoptime, password, quality, DayDisponible, startdate, stopdate, code, idPartition, lastupdate, synchronized_at
    FROM habilites
    WHERE synchronized_at is NULL OR (admin_updated_at IS NOT NULL AND synchronized_at <= admin_updated_at)
    LIMIT 50;
`;

const sqlInsertOrUpdateHabilite = `
    INSERT INTO habilites(idhabilite, idcontact, idsite, idordre, Timedisponible, starttime, stoptime, password, quality, DayDisponible, startdate, stopdate, code, idPartition, lastupdate)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ON DUPLICATE KEY UPDATE
        idcontact = VALUES(idcontact),
        idsite = VALUES(idsite),
        idordre = VALUES(idordre),
        Timedisponible = VALUES(Timedisponible),
        starttime = VALUES(starttime),
        stoptime = VALUES(stoptime),
        password = VALUES(password),
        quality = VALUES(quality),
        DayDisponible = VALUES(DayDisponible),
        startdate = VALUES(startdate),
        stopdate = VALUES(stopdate),
        code = VALUES(code),
        idPartition = VALUES(idPartition),
        lastupdate = VALUES(lastupdate);
`;

const sqlUpdateSynchronizedAt = `
    UPDATE habilites
    SET synchronized_at = NOW()
    WHERE idhabilite = ?;
`;
const sqlInsertLastSync = "UPDATE synchronisations SET last_sync_update = now() WHERE service = 'habilite'"

// Log initialization
fs.writeFile(pathname, `${moment().format('LLLL')}\n\n`, (err) => {
    if (err) console.error(err);
});

function formatErrorForApp(err) {
    const stackTrace = err.stack ? err.stack.replace(/\n/g, '<br>') : 'No stack trace available';
    const otherProperties = Object.getOwnPropertyNames(err)
        .filter(prop => !['message', 'name', 'stack'].includes(prop))
        .map(prop => `<strong>${prop}:</strong> ${JSON.stringify(err[prop])}`)
        .join('<br>') || 'None';

    return `
        <div style="font-family: Arial, sans-serif; color: #333;">
            <h2 style="color: #d9534f;">Error Report</h2>
            <div>
                <h4 style="margin-bottom: 5px;">Message:</h4>
                <p style="background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px;">${err.message || 'N/A'}</p>
            </div>
            <div>
                <h4 style="margin-bottom: 5px;">Error Type:</h4>
                <p style="background: #e2e3e5; color: #383d41; padding: 10px; border-radius: 5px;">${err.name || 'N/A'}</p>
            </div>
            <div>
                <h4 style="margin-bottom: 5px;">Stack Trace:</h4>
                <p style="background: #f1f1f1; color: #555; padding: 10px; border-radius: 5px; font-family: monospace; overflow-x: auto;">${stackTrace}</p>
            </div>
            <div>
                <h4 style="margin-bottom: 5px;">Other Properties:</h4>
                <p style="background: #f1f1f1; color: #555; padding: 10px; border-radius: 5px;">${otherProperties}</p>
            </div>
        </div>`;
}

function syncHabilite(habilites, index) {
    if (index < habilites.length) {
        const habilite = habilites[index];
        pool_admin.query(sqlInsertOrUpdateHabilite, [
            habilite.idhabilite, habilite.idcontact, habilite.idsite, habilite.idordre, habilite.Timedisponible, habilite.starttime, habilite.stoptime, habilite.password, habilite.quality, habilite.DayDisponible, habilite.startdate, habilite.stopdate, habilite.code, habilite.idPartition, habilite.lastupdate
        ], (err, result) => {
            if (err) {
                logError(`Error syncing habilite ${habilite.idhabilite}: ${err}`);
                sendMail(pool_admin, dest, "Erreur Synchronisation Habilite (Admin) insert or update", formatErrorForApp(err), [], () => { });
                retrySyncHabilite(habilites, index);
            } else {
                console.log(`Synced habilite: ${habilite.idhabilite}`);
                pool_tls.query(sqlUpdateSynchronizedAt, [habilite.idhabilite], (err, result) => {
                    if (err) {
                        logError(`Error updating synchronized_at for habilite ${habilite.idhabilite}: ${err}`);
                        sendMail(pool_tls, dest, "Erreur Synchronisation Habilite (TLS) Update synchronized_at", formatErrorForApp(err), [], () => { });
                        retrySyncHabilite(habilites, index);
                    } else {
                        console.log(`Updated synchronized_at for habilite: ${habilite.idhabilite}`);
                        pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                            if (err) {
                                fs.appendFile(pathname, err.toString(), (err) => {
                                    if (err) console.error(err);
                                })
                                console.error(err)
                            }
                            setTimeout(() => {
                                syncHabilite(habilites, index + 1);
                            }, 200)
                        })
                    }
                });
            }
        });
    } else {
        waitBeforeUpdate();
    }
}

function retrySyncHabilite(habilites, index) {
    setTimeout(() => {
        syncHabilite(habilites, index);
    }, 3000);
}

function logError(message) {
    console.error(message);
    fs.appendFile(pathname, `\n${moment().format('YY-MM-DD HH:mm:ss')}> ${message}`, (err) => {
        if (err) console.error(err);
    });
}

function waitBeforeUpdate() {
    setTimeout(() => {
        fetchAndSyncHabilites();
    }, 3000);
}

function fetchAndSyncHabilites() {
    pool_tls.query(sqlSelectHabilites, (err, habilites) => {
        if (err) {
            logError(`Error fetching habilites: ${err}`);
            sendMail(pool_tls, dest, "Erreur Synchronisation Habilite (TLS) fetch habilites", formatErrorForApp(err), [], () => { });
            setTimeout(() => {
                fetchAndSyncHabilites();
            }, 60000);
        } else if (habilites.length > 0) {
            console.log(`Found ${habilites.length} habilites to sync`);
            syncHabilite(habilites, 0);
            pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                if (err) {
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if (err) console.error(err);
                    })
                    console.error(err)
                }
            })
        } else {
            console.log('No habilites to sync');
            waitBeforeUpdate();
            pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                if (err) {
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if (err) console.error(err);
                    })
                    console.error(err)
                }
            })
        }
    });
}

fetchAndSyncHabilites();
