const moment = require('moment')
const mysql = require('mysql2')
const fs = require("fs");

moment.locale('fr')
const auth = require("../../auth");
const { argv } = require('process');

const db_config_zo = auth.db_config_zo
const pool_tls = mysql.createPool(db_config_zo)

const db_config_admin = auth.db_config_admin
const pool_admin = mysql.createPool(db_config_admin)

const pathname = 'logs/sync/horaire/' + moment().format('YYYYMMDDHHmmss') + '.log'
fs.writeFile(pathname, moment().format('LLLL') + '\n\n', (err) => {
    console.error(err)
})

const sqlSelectHoraire = "SELECT id, site_id, day_1, night_1, day_2, night_2, day_3, night_3, day_4, night_4, day_5, night_5, " +
    "day_6, night_6, day_0, night_0, day_ferie, night_ferie " +
    "from horaire_effectifs " +
    "where synchronized_at is null or (admin_updated_at is not null and synchronized_at <= admin_updated_at) " +
    (argv[2] == 'reverse' ? " order by id desc  limit 100 " : " limit 50 ")
const sqlInsertOrUpdateHoraire = "INSERT INTO horaire_effectifs(id, site_id, day_1, night_1, day_2, night_2, day_3, night_3, day_4, night_4, day_5, night_5, " +
    "day_6, night_6, day_0, night_0, day_ferie, night_ferie) " +
    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) " +
    "ON DUPLICATE KEY UPDATE site_id=?, day_1=?, night_1=?, day_2=?, night_2=?, day_3=?, night_3=?, day_4=?, night_4=?, " +
    "day_5=?, night_5=?, day_6=?, night_6=?, day_0=?, night_0=?, day_ferie=?, night_ferie=?"
const sqlUpdateHoraire = "UPDATE horaire_effectifs SET synchronized_at = now() WHERE id = ?"
const sqlInsertLastSync = "UPDATE synchronisations SET last_sync_update = now() WHERE service = 'horaire'"

function syncHoraireById(horaires, index) {
    if (index < horaires.length) {
        const horaire = horaires[index]
        const params = [horaire.id, horaire.site_id, horaire.day_1, horaire.night_1, horaire.day_2, horaire.night_2, horaire.day_3, horaire.night_3, horaire.day_4, horaire.night_4, 
            horaire.day_5, horaire.night_5, horaire.day_6, horaire.night_6, horaire.day_0, horaire.night_0, horaire.day_ferie, horaire.night_ferie]
        pool_tls.query(sqlInsertOrUpdateHoraire, [...params, ...params.slice(1)], async (err, res) => {
            if(err){
                console.log("err found")
                console.error(err)
                fs.appendFile(pathname, err.toString(), (err) => {
                    if (err) console.error(err);
                })
                waitBeforeUpdate()
            }
            else {
                console.log("sync horaire: " + horaire.id)
                pool_admin.query(sqlUpdateHoraire, [horaire.id], async (err, res) => {
                    if(err){
                        fs.appendFile(pathname, err.toString(), (err) => {
                            if (err) console.error(err);
                        })
                        console.error(err)
                    }
                    pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                        if (err) {
                            fs.appendFile(pathname, err.toString(), (err) => {
                                if (err) console.error(err);
                            })
                            console.error(err)
                        }
                    })
                })
                setTimeout(() => {
                    syncHoraireById(horaires, index + 1)
                }, 200)
            }
        })
    }
    else
        waitBeforeUpdate()
}

function updateData(){
    pool_admin.query(sqlSelectHoraire, [], async (err, horaires) => {
        if(err){
            fs.appendFile(pathname, err.toString(), (err) => {
                if (err) console.error(err);
            })
            waitBeforeUpdate()
            console.error(err)
        }
        else {
            if (horaires.length > 0) {
                console.log("horaire to sync: " + horaires.length)
                syncHoraireById(horaires, 0)
            }
            else {
                console.log(moment().format("YYYY-MM-DD HH:mm:ss"))
                waitBeforeUpdate()
            }
            pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                if (err) {
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if (err) console.error(err);
                    })
                    console.error(err)
                }
            })
        }
    })
}

let count = 1
function waitBeforeUpdate() {
    console.log("-----" + (count > 1 ? "-----" : "") + (count > 2 ? "-----" : "") + (count > 3 ? "-----" : ""))
    setTimeout(() => {
        updateData()
    }, 3000)
    if (count > 3) count = 1
    else count++
}

updateData()
