const moment = require('moment')
const mysql = require('mysql')
const fs = require("fs");

moment.locale('fr')
// const auth = require("../../auth");
const { argv } = require('process');

// const db_config_zo = auth.db_config_zo
const db_config_zo = {
    host: "127.0.0.1",
    port: "3306",
    user: "root",
    database: "last_tls",
    password: ""
}
const pool_tls = mysql.createPool(db_config_zo)

// const db_config_admin = auth.db_config_admin
const db_config_admin = {
    host: "127.0.0.1",
    port: "3306",
    user: "root",
    database: "adm_nov",
    password: ""
}
const pool_admin = mysql.createPool(db_config_admin)

const pathname = 'logs/sync/call/' + moment().format('YYYYMMDDHHmmss') + '.log'
fs.writeFile(pathname, moment().format('LLLL') + '\n\n', (err) => {
    console.error(err)
})

const sqlUpdateLastSyncCall = "UPDATE call_users SET synchronized_at = now() WHERE id = ?"

const sqlSelectCall = "SELECT id, user_id, call_id, sip, destination, created_at " +
    "from call_users " +
    "where synchronized_at is null or (admin_updated_at is not null and synchronized_at <= admin_updated_at) " +
    (argv[2] == 'reverse' ? "order by id desc limit 100 " : " limit 50")
const sqlInsertOrUpdateCall = "INSERT INTO call_users (id, user_id, call_id, sip, destination, created_at) VALUES(?, ?, ?, ?, ?, ?) " +
    "ON DUPLICATE KEY UPDATE user_id=?, call_id=?, sip=?, destination=?, created_at=?"
const sqlInsertLastSync = "UPDATE synchronisations SET last_sync_update = now() WHERE service = 'indice appel'"

function syncCallById(calls, index) {
    if (index < calls.length) {
        const call = calls[index]
        pool_admin.query(sqlInsertOrUpdateCall, [
            call.id, call.user_id, call.call_id, call.sip, call.destination, call.created_at,
            call.user_id, call.call_id, call.sip, call.destination, call.created_at
        ], async (err, res) => {
            if (err) {
                console.log("err found")
                console.error(err)
                fs.appendFile(pathname, "\n" + moment().format("YY-MM-DD HH:mm:ss") + "> INSERT OR UPDATE SINGLE CALL: " + err.toString(), (err) => {
                    if (err) console.error(err);
                })
                setTimeout(() => {
                    syncCallById(calls, index)
                }, 300)
            }
            else {
                console.log("sync call: " + call.id)
                pool_tls.query(sqlUpdateLastSyncCall, [call.id], async (err, res) => {
                    if (err) {
                        fs.appendFile(pathname, "\n" + moment().format("YY-MM-DD HH:mm:ss") + "> UPDATE SYNCHRONIZED_AT: " + err.toString(), (err) => {
                            if (err) console.error(err);
                        })
                        console.error(err)
                        setTimeout(() => {
                            syncCallById(calls, index)
                        }, argv[2] == 'reverse' ? 100 : 300)
                    }
                    else {
                        setTimeout(() => {
                            syncCallById(calls, index + 1)
                        }, argv[2] == 'reverse' ? 30 : 100)
                    }
                    pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                        if (err) {
                            fs.appendFile(pathname, err.toString(), (err) => {
                                if (err) console.error(err);
                            })
                            console.error(err)
                        }
                    })
                })
            }
        })
    }
    else
        waitBeforeUpdate()
}

function updateData() {
    pool_tls.query(sqlSelectCall, [], async (err, calls) => {
        if (err) {
            fs.appendFile(pathname, "\n" + moment().format("YY-MM-DD HH:mm:ss") + "> SELECT CALL LIMIT 50: " + err.toString(), (err) => {
                if (err) console.error(err);
            })
            waitBeforeUpdate()
            console.error(err)
        }
        else {
            if (calls.length > 0) {
                console.log("call to sync: " + calls.length)
                syncCallById(calls, 0)
            }
            else {
                console.log(moment().format("YYYY-MM-DD HH:mm:ss"))
                waitBeforeUpdate()
            }
            pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                if (err) {
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if (err) console.error(err);
                    })
                    console.error(err)
                }
            })
        }
    })
}

let count = 1
function waitBeforeUpdate() {
    console.log("-----" + (count > 1 ? "-----" : "") + (count > 2 ? "-----" : "") + (count > 3 ? "-----" : ""))
    setTimeout(() => {
        updateData()
    }, 3000)
    if (count > 3) count = 1
    else count++
}

updateData()
