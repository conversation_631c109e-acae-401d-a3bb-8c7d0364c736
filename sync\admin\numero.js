const moment = require('moment');
const mysql = require('mysql2');
const fs = require("fs");

moment.locale('fr');
const auth = require("../../auth");
const { argv } = require('process');

const db_config_zo = auth.db_config_zo;
const pool_tls = mysql.createPool(db_config_zo);

const db_config_admin = auth.db_config_admin;
const pool_admin = mysql.createPool(db_config_admin);

const sendMail = auth.sendMail;

const dest = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"];

const pathname = `logs/sync/numeros/${moment().format('YYYYMMDDHHmmss')}.log`;

const sqlSelectNumeros = `
    SELECT id, id_contact, id_site, numero, soft_delete, lastupdate, synchronized_at
    FROM numeros
    WHERE synchronized_at is NULL OR (admin_updated_at IS NOT NULL AND synchronized_at <= admin_updated_at)
    LIMIT 50;
`;

const sqlInsertOrUpdateNumero = `
    INSERT INTO numeros (id, id_contact, id_site, numero, soft_delete)
    VALUES (?, ?, ?, ?, ?)
    ON DUPLICATE KEY UPDATE
        id_contact = VALUES(id_contact),
        id_site = VALUES(id_site),
        numero = VALUES(numero),
        soft_delete = VALUES(soft_delete);
`;

const sqlUpdateSynchronizedAt = `
    UPDATE numeros
    SET synchronized_at = NOW()
    WHERE id = ?;
`;
const sqlInsertLastSync = "UPDATE synchronisations SET last_sync_update = now() WHERE service = 'numero'"

fs.writeFile(pathname, `${moment().format('LLLL')}\n\n`, (err) => {
    if (err) console.error(err);
});

function formatErrorForApp(err) {
    const stackTrace = err.stack ? err.stack.replace(/\n/g, '<br>') : 'No stack trace available';
    const otherProperties = Object.getOwnPropertyNames(err)
        .filter(prop => !['message', 'name', 'stack'].includes(prop))
        .map(prop => `<strong>${prop}:</strong> ${JSON.stringify(err[prop])}`)
        .join('<br>') || 'None';

    return `
        <div style="font-family: Arial, sans-serif; color: #333;">
            <h2 style="color: #d9534f;">Error Report</h2>
            <div>
                <h4 style="margin-bottom: 5px;">Message:</h4>
                <p style="background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px;">${err.message || 'N/A'}</p>
            </div>
            <div>
                <h4 style="margin-bottom: 5px;">Error Type:</h4>
                <p style="background: #e2e3e5; color: #383d41; padding: 10px; border-radius: 5px;">${err.name || 'N/A'}</p>
            </div>
            <div>
                <h4 style="margin-bottom: 5px;">Stack Trace:</h4>
                <p style="background: #f1f1f1; color: #555; padding: 10px; border-radius: 5px; font-family: monospace; overflow-x: auto;">${stackTrace}</p>
            </div>
            <div>
                <h4 style="margin-bottom: 5px;">Other Properties:</h4>
                <p style="background: #f1f1f1; color: #555; padding: 10px; border-radius: 5px;">${otherProperties}</p>
            </div>
        </div>`;
}

function syncNumero(numeros, index) {
    if (index < numeros.length) {
        const numero = numeros[index];

        const softDelete = numero.soft_delete !== null ? numero.soft_delete : 0;

        pool_admin.query(sqlInsertOrUpdateNumero, [
            numero.id, numero.id_contact, numero.id_site, numero.numero, softDelete
        ], (err, result) => {
            if (err) {
                logError(`Error syncing numero ${numero.id}: ${err}`);
                sendMail(pool_admin, dest, "Erreur Synchronisation Numero (Admin) insert or update", formatErrorForApp(err), [], () => { });
                retrySyncNumero(numeros, index);
            } else {
                console.log(`Synced numero: ${numero.id}`);
                pool_tls.query(sqlUpdateSynchronizedAt, [numero.id], (err, result) => {
                    if (err) {
                        logError(`Error updating synchronized_at for numero ${numero.id}: ${err}`);
                        sendMail(pool_tls, dest, "Erreur Synchronisation Numero (TLS) Update synchronized_at", formatErrorForApp(err), [], () => { });
                        retrySyncNumero(numeros, index);
                    } else {
                        console.log(`Updated synchronized_at for numero: ${numero.id}`);
                        pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                            if (err) {
                                fs.appendFile(pathname, err.toString(), (err) => {
                                    if (err) console.error(err);
                                })
                                console.error(err)
                            }
                            setTimeout(() => {
                                syncNumero(numeros, index + 1);
                            }, 200)
                        })
                    }
                });
            }
        });
    } else {
        waitBeforeUpdate();
    }
}

function retrySyncNumero(numeros, index) {
    setTimeout(() => {
        syncNumero(numeros, index);
    }, 3000);
}

function logError(message) {
    console.error(message);
    fs.appendFile(pathname, `\n${moment().format('YY-MM-DD HH:mm:ss')}> ${message}`, (err) => {
        if (err) console.error(err);
    });
}

function waitBeforeUpdate() {
    setTimeout(() => {
        fetchAndSyncNumeros();
    }, 3000);
}

function fetchAndSyncNumeros() {
    pool_tls.query(sqlSelectNumeros, (err, numeros) => {
        if (err) {
            logError(`Error fetching numeros: ${err}`);
            sendMail(pool_tls, dest, "Erreur Synchronisation Numero (TLS) fetch numeros", formatErrorForApp(err), [], () => { });
            setTimeout(() => {
                fetchAndSyncNumeros();
            }, 60000);
        } else if (numeros.length > 0) {
            console.log(`Found ${numeros.length} numeros to sync`);
            syncNumero(numeros, 0);
            pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                if (err) {
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if (err) console.error(err);
                    })
                    console.error(err)
                }
            })
        } else {
            console.log('No numeros to sync');
            waitBeforeUpdate();
            pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                if (err) {
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if (err) console.error(err);
                    })
                    console.error(err)
                }
            })
        }
    });
}

fetchAndSyncNumeros();
