const moment = require('moment')
const mysql = require('mysql2')
const fs = require("fs");

moment.locale('fr')
const auth = require("../../auth");
const { argv } = require('process');

const {db_config_zo, db_config_admin} = auth
const pool_tls = mysql.createPool(db_config_zo)
const pool_admin = mysql.createPool(db_config_admin)

const pathname = 'logs/sync/pointage/' + moment().format('YYYYMMDDHHmmss') + '.log'
fs.writeFile(pathname, moment().format('LLLL') + '\n\n', (err) => {
    console.error(err)
})

const sqlUpdateLastSyncPointage = "UPDATE pointages SET synchronized_at = now() WHERE id = ?"

const sqlSelectPointage = "SELECT id, site_id, agent_id, date_pointage, vigilance, dtarrived, pointeuse_id, user_id, soft_delete " +
    "from pointages " +
    "where synchronized_at is null or (admin_updated_at is not null and synchronized_at <= admin_updated_at) " +
    (argv[2] == 'reverse' ? "order by id desc limit 100 " : " limit 50")
const sqlInsertOrUpdatePointage = "INSERT INTO pointages (id, site_id, employe_id, date_pointage, vigilance, dtarrived, pointeuse_id, user_id, soft_delete) VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?) " +
    "ON DUPLICATE KEY UPDATE site_id=?, employe_id=?, date_pointage=?, vigilance=?, dtarrived=?, pointeuse_id=?,  user_id=?, soft_delete=?"
const sqlInsertLastSync = "UPDATE synchronisations SET last_sync_update = now() WHERE service = 'pointage'"

function syncPointageById(pointages, index) {
    if (index < pointages.length) {
        const pointage = pointages[index]
        pool_admin.query(sqlInsertOrUpdatePointage, [
            pointage.id, pointage.site_id, pointage.agent_id, pointage.date_pointage, pointage.vigilance, pointage.dtarrived, pointage.pointeuse_id, pointage.user_id, pointage.soft_delete,
            pointage.site_id, pointage.agent_id, pointage.date_pointage, pointage.vigilance, pointage.dtarrived, pointage.pointeuse_id, pointage.user_id, pointage.soft_delete
        ], async (err, res) => {
            if (err) {
                console.log("err found")
                console.error(err)
                fs.appendFile(pathname, "\n" + moment().format("YY-MM-DD HH:mm:ss") + "> INSERT OR UPDATE SINGLE POINTAGE: " + err.toString(), (err) => {
                    if (err) console.error(err);
                })
                setTimeout(() => {
                    syncPointageById(pointages, index)
                }, 300)
            }
            else {
                console.log("sync pointage: " + pointage.id)
                pool_tls.query(sqlUpdateLastSyncPointage, [pointage.id], async (err, res) => {
                    if (err) {
                        fs.appendFile(pathname, "\n" + moment().format("YY-MM-DD HH:mm:ss") + "> UPDATE SYNCHRONIZED_AT: " + err.toString(), (err) => {
                            if (err) console.error(err);
                        })
                        console.error(err)
                        setTimeout(() => {
                            syncPointageById(pointages, index)
                        }, argv[2] == 'reverse' ? 100 : 300)
                    }
                    else {
                        setTimeout(() => {
                            syncPointageById(pointages, index + 1)
                        }, argv[2] == 'reverse' ? 30 : 100)
                    }
                    pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                        if (err) {
                            fs.appendFile(pathname, err.toString(), (err) => {
                                if (err) console.error(err);
                            })
                            console.error(err)
                        }
                    })
                })
            }
        })
    }
    else
        waitBeforeUpdate()
}

function updateData() {
    pool_tls.query(sqlSelectPointage, [], async (err, pointages) => {
        if (err) {
            fs.appendFile(pathname, "\n" + moment().format("YY-MM-DD HH:mm:ss") + "> SELECT POINTAGE LIMIT 50: " + err.toString(), (err) => {
                if (err) console.error(err);
            })
            console.error(err)
            setTimeout(() => {
                updateData()
            }, 60000)
        }
        else {
            if (pointages.length > 0) {
                console.log("pointage to sync: " + pointages.length)
                syncPointageById(pointages, 0)
            }
            else {
                console.log(moment().format("YYYY-MM-DD HH:mm:ss"))
                waitBeforeUpdate()
            }
            pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                if (err) {
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if (err) console.error(err);
                    })
                    console.error(err)
                }
            })
        }
    })
}

let count = 1
function waitBeforeUpdate() {
    console.log("-----" + (count > 1 ? "-----" : "") + (count > 2 ? "-----" : "") + (count > 3 ? "-----" : ""))
    setTimeout(() => {
        updateData()
    }, 3000)
    if (count > 3) count = 1
    else count++
}

updateData()
