const moment = require('moment')
const mysql = require('mysql2')
const fs = require("fs");

moment.locale('fr')
const auth = require("../../auth");
const { argv } = require('process');

const db_config_zo = auth.db_config_zo
const pool_tls = mysql.createPool(db_config_zo)

const db_config_admin = auth.db_config_admin
const pool_admin = mysql.createPool(db_config_admin)

const pathname = 'logs/sync/rapport/' + moment().format('YYYYMMDDHHmmss') + '.log'
fs.writeFile(pathname, moment().format('LLLL') + '\n\n', (err) => {
    console.error(err)
})

const sqlSelectRapport = "SELECT id, type_rapport_id, site_id, eventcode, zone, commentaire, dtarrived, user_id, soft_delete, " +
    "created_at, updated_at, debut, fin, depart, arrivee, technicien, tache, exported, idademco, intervention_id, admin_updated_at, synchronized_at " +
    "from rapports " +
    "where synchronized_at is null or (admin_updated_at is not null and synchronized_at <= admin_updated_at) " +
    (argv[2] == 'reverse' ? " order by id desc  limit 100 " : " limit 50 ")
const sqlInsertOrUpdateRapport = "INSERT INTO rapports(id, type_rapport_id, site_id, eventcode, zone, commentaire, dtarrived, user_id, soft_delete, " +
    "created_at, updated_at, debut, fin, depart, arrivee, technicien, tache, exported, idademco, intervention_id) " +
    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) " +
    "ON DUPLICATE KEY UPDATE type_rapport_id=?, site_id=?, eventcode=?, zone=?, commentaire=?, dtarrived=?, user_id=?, soft_delete=?, " +
    "created_at=?, updated_at=?, debut=?, fin=?, depart=?, arrivee=?, technicien=?, tache=?, exported=?, idademco=?, intervention_id=?"
const sqlUpdateRapport = "UPDATE rapports SET synchronized_at = now() WHERE id = ?"
const sqlInsertLastSync = "UPDATE synchronisations SET last_sync_update = now() WHERE service = 'rapport'"

function syncRapportById(rapports, index) {
    if (index < rapports.length) {
        const rapport = rapports[index]
        const params = [rapport.id, rapport.type_rapport_id, rapport.site_id, rapport.eventcode, rapport.zone, rapport.commentaire,
        rapport.dtarrived, rapport.user_id, rapport.soft_delete, rapport.created_at, rapport.updated_at, rapport.debut, rapport.fin,
        rapport.depart, rapport.arrivee, rapport.technicien, rapport.tache, rapport.exported, rapport.idademco, rapport.intervention_id]
        pool_admin.query(sqlInsertOrUpdateRapport, [...params, ...params.slice(1)], async (err, res) => {
            if (err) {
                console.log("err found")
                console.error(err)
                fs.appendFile(pathname, err.toString(), (err) => {
                    if (err) console.error(err);
                })
                waitBeforeUpdate()
            }
            else {
                console.log("sync rapport: " + rapport.id)
                pool_tls.query(sqlUpdateRapport, [rapport.id], async (err, res) => {
                    if (err) {
                        fs.appendFile(pathname, err.toString(), (err) => {
                            if (err) console.error(err);
                        })
                        console.error(err)
                    }
                    pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                        if (err) {
                            fs.appendFile(pathname, err.toString(), (err) => {
                                if (err) console.error(err);
                            })
                            console.error(err)
                        }
                    })
                })
                setTimeout(() => {
                    syncRapportById(rapports, index + 1)
                }, 200)
            }
        })
    }
    else
        waitBeforeUpdate()
}

function updateData() {
    pool_tls.query(sqlSelectRapport, [], async (err, rapports) => {
        if (err) {
            fs.appendFile(pathname, err.toString(), (err) => {
                if (err) console.error(err);
            })
            waitBeforeUpdate()
            console.error(err)
        }
        else {
            if (rapports.length > 0) {
                console.log("rapport to sync: " + rapports.length)
                syncRapportById(rapports, 0)
            }
            else {
                console.log(moment().format("YYYY-MM-DD HH:mm:ss"))
                waitBeforeUpdate()
            }
            pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                if (err) {
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if (err) console.error(err);
                    })
                    console.error(err)
                }
            })
        }
    })
}

let count = 1
function waitBeforeUpdate() {
    console.log("-----" + (count > 1 ? "-----" : "") + (count > 2 ? "-----" : "") + (count > 3 ? "-----" : ""))
    setTimeout(() => {
        updateData()
    }, 3000)
    if (count > 3) count = 1
    else count++
}

updateData()
