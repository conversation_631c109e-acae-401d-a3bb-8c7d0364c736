const moment = require('moment');
const mysql = require('mysql');

moment.locale('fr');

const db_config_admin = require("../auth").db_config_admin;
const pool_admin = mysql.createPool(db_config_admin);

const db_tls = require("../auth").db_config_zo;
const pool_tls = mysql.createPool(db_tls);

const sqlSelectSecteur = `
    SELECT id, nom, heure_contrat, group_pointage_id FROM secteurs
    WHERE updated_at > synchronized_at OR synchronized_at IS NULL
`;

const insertSecteurToAdmin = `
    INSERT INTO secteurs (id, nom, heure_contrat, group_pointage_id)
    VALUES (?, ?, ?, ?)
    ON DUPLICATE KEY UPDATE
        nom = VALUES(nom),
        heure_contrat = VALUES(heure_contrat),
        group_pointage_id = VALUES(group_pointage_id)
`;

const updateSynchronizedAt = `
    UPDATE secteurs SET synchronized_at = ? WHERE id = ?
`;
const sqlInsertLastSync = `UPDATE synchronisations SET last_sync_update = now() WHERE service = 'secteur'`;

const insertRow = async (secteurs, index) => {
    if (index >= secteurs.length) {
        syncDataTimeout();
        return;
    }

    const currentSecteur = secteurs[index];
    const currentDate = moment().format('YYYY-MM-DD HH:mm:ss');

    pool_admin.query(
        insertSecteurToAdmin,
        [
            currentSecteur.id,
            currentSecteur.nom,
            currentSecteur.heure_contrat,
            currentSecteur.group_pointage_id,
        ],
        (err) => {
            if (err) {
                console.error(`Erreur d'insertion pour ID ${currentSecteur.id}:`, err);
                insertRow(secteurs, index + 1);
            } else {
                pool_tls.query(
                    updateSynchronizedAt,
                    [currentDate, currentSecteur.id],
                    (updateErr) => {
                        if (updateErr) {
                            console.error(`Erreur lors de la mise à jour de synchronized_at pour ID ${currentSecteur.id}:`, updateErr);
                        }
                        insertRow(secteurs, index + 1);
                        pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                            if (err) {
                                console.error(err)
                            }
                        })
                    }
                );
            }
        }
    );
};

const syncDataTimeout = () => {
    setTimeout(() => syncDataToAdmin(), 200);
};

const syncDataToAdmin = async () => {
    console.log("---------\n", moment().format("YY-MM-DD HH:mm:ss"));
    console.log("Sélection des données à insérer");

    pool_tls.query(sqlSelectSecteur, (err, results) => {
        if (err) {
            console.error("Erreur lors de la récupération des secteurs:", err);
            syncDataTimeout();
            return;
        }
        else {
            if (results.length > 0) {
                console.log(`${results.length} secteurs trouvés.`);
                insertRow(results, 0);
            } else {
                console.log("Aucun secteur à synchroniser.");
                syncDataTimeout();
            }
            pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                if (err) {
                    console.error(err)
                }
            })
        }
    });
};

syncDataToAdmin();
