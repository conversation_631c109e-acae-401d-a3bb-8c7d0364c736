const moment = require('moment')
const mysql = require('mysql2')
const fs = require("fs");

moment.locale('fr')
const auth = require("../../auth");
const { argv } = require('process');

const { db_config_zo, db_config_admin } = auth
const pool_tls = mysql.createPool(db_config_zo)
const pool_admin = mysql.createPool(db_config_admin)

const pathname = 'logs/sync/site/' + moment().format('YYYYMMDDHHmmss') + '.log'
fs.writeFile(pathname, moment().format('LLLL') + '\n\n', (err) => {
    console.error(err)
})

const sqlSelectSite = "SELECT idsite, prom, nom, adresse, pointage, vigilance, pointeuse, pointeuse_id, intervention, intervention_id, soft_delete, " +
    "group_pointage_id, group_id, horaire_pointage_id, secteur_id, total_hour, nb_agent_night, nb_agent_day " +
    "from sites " +
    "where synchronized_at is null or (admin_updated_at is not null and synchronized_at <= admin_updated_at) " +
    (argv[2] == 'reverse' ? " order by idsite desc  limit 100 " : " limit 50 ")
const sqlInsertOrUpdateSite = "INSERT INTO sites(idsite, prom, nom, adresse, pointage, vigilance, pointeuse, pointeuse_id, intervention, intervention_id, soft_delete, " +
    "group_pointage_id, group_id, horaire_pointage_id, secteur_id, total_hour, nb_agent_night, nb_agent_day) " +
    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) " +
    "ON DUPLICATE KEY UPDATE prom=?, nom=?, adresse=?, pointage=?, vigilance=?, pointeuse=?, pointeuse_id=?, intervention=?, intervention_id=?, soft_delete=?, " +
    "group_pointage_id=?, group_id=?, horaire_pointage_id=?, secteur_id=?, total_hour=?, nb_agent_night=?, nb_agent_day=?"
const sqlUpdateSite = "UPDATE sites SET synchronized_at = now() WHERE idsite = ?"
const sqlInsertLastSync = "UPDATE synchronisations SET last_sync_update = now() WHERE service = 'site_to_admin'"

function syncSiteById(sites, index) {
    if (index < sites.length) {
        const site = sites[index]
        const params = [site.idsite, site.prom, site.nom, site.adresse, site.pointage, site.vigilance, site.pointeuse,
        site.pointeuse_id, site.intervention, site.intervention_id, site.soft_delete, site.group_pointage_id,
        site.group_id, site.horaire_pointage_id, site.secteur_id, site.total_hour, site.nb_agent_night, site.nb_agent_day]
        pool_admin.query(sqlInsertOrUpdateSite, [...params, ...params.slice(1)], async (err, res) => {
            if (err) {
                console.log("err found")
                console.error(err)
                fs.appendFile(pathname, err.toString(), (err) => {
                    if (err) console.error(err);
                })
                waitBeforeUpdate()
            }
            else {
                console.log("sync site: " + site.idsite)
                pool_tls.query(sqlUpdateSite, [site.idsite], async (err, res) => {
                    if (err) {
                        fs.appendFile(pathname, err.toString(), (err) => {
                            if (err) console.error(err);
                        })
                        console.error(err)
                    }
                    pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                        if (err) {
                            fs.appendFile(pathname, err.toString(), (err) => {
                                if (err) console.error(err);
                            })
                            console.error(err)
                        }
                        setTimeout(() => {
                            syncSiteById(sites, index + 1)
                        }, 200)
                    })
                })
            }
        })
    }
    else
        waitBeforeUpdate()
}

function updateData() {
    pool_tls.query(sqlSelectSite, [], async (err, sites) => {
        if (err) {
            fs.appendFile(pathname, err.toString(), (err) => {
                if (err) console.error(err);
            })
            console.error(err)
            setTimeout(() => {
                updateData()
            }, 6000)
        }
        else {
            if (sites.length > 0) {
                console.log("site to sync: " + sites.length)
                syncSiteById(sites, 0)
            }
            else {
                console.log(moment().format("YYYY-MM-DD HH:mm:ss"))
                waitBeforeUpdate()
            }
            pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                if (err) {
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if (err) console.error(err);
                    })
                    console.error(err)
                }
            })
        }
    })
}

let count = 1
function waitBeforeUpdate() {
    console.log("-----" + (count > 1 ? "-----" : "") + (count > 2 ? "-----" : "") + (count > 3 ? "-----" : ""))
    setTimeout(() => {
        updateData()
    }, 3000)
    if (count > 3) count = 1
    else count++
}

updateData()
