const moment = require('moment')
const mysql = require('mysql2')
const fs = require("fs");

moment.locale('fr')
const auth = require("../../auth");
const { argv } = require('process');

const { db_config_zo, db_config_admin } = auth
const pool_tls = mysql.createPool(db_config_zo)
const pool_admin = mysql.createPool(db_config_admin)

const pathname = 'logs/sync/zone/' + moment().format('YYYYMMDDHHmmss') + '.log'
fs.writeFile(pathname, moment().format('LLLL') + '\n\n', (err) => {
    console.error(err)
})

const sqlSelectZone = "SELECT NumZone, idsite, idzone, nomZone, idcapteur, soft_delete from zonesites " +
    "where synchronized_at is null or (admin_updated_at is not null and synchronized_at <= admin_updated_at) " +
    (argv[2] == 'reverse' ? " order by idzone desc  limit 100 " : " limit 50 ")
const sqlInsertOrUpdateZone = "INSERT INTO zones(NumZone, idsite, idzone, nomZone, idcapteur, soft_delete) " +
    "VALUES (?, ?, ?, ?, ?, ?) " +
    "ON DUPLICATE KEY UPDATE idzone=?, nomZone=?, idcapteur=?, soft_delete=?"
const sqlDeleteZone = "DELETE FROM zones WHERE NumZone=? and idsite=?"
const sqlUpdateZone = "UPDATE zonesites SET synchronized_at = now() WHERE NumZone=? and idsite=?"
const sqlInsertLastSync = "UPDATE synchronisations SET last_sync_update = now() WHERE service = 'zone'"

function syncZoneById(zones, index) {
    if (index < zones.length) {
        const zone = zones[index]
        const params = [zone.NumZone, zone.idsite, zone.idzone, zone.nomZone, zone.idcapteur, zone.soft_delete]
        if (zone.soft_delete) {
            pool_admin.query(sqlDeleteZone, [zone.NumZone, zone.idsite], async (err, res) => {
                if (err) {
                    console.log("err found")
                    console.error(err)
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if (err) console.error(err);
                    })
                    waitBeforeUpdate()
                }
                else {
                    console.log("sync zone: " + zone.idzone + " deleted!")
                    pool_tls.query(sqlUpdateZone, [zone.NumZone, zone.idsite], async (err, res) => {
                        if (err) {
                            fs.appendFile(pathname, err.toString(), (err) => {
                                if (err) console.error(err);
                            })
                            console.error(err)
                        }
                        pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                            if (err) {
                                fs.appendFile(pathname, err.toString(), (err) => {
                                    if (err) console.error(err);
                                })
                                console.error(err)
                            }
                        })
                    })
                    setTimeout(() => {
                        syncZoneById(zones, index + 1)
                    }, 200)
                }
            })
        }
        else {
            pool_admin.query(sqlInsertOrUpdateZone, [...params, ...params.slice(2)], async (err, res) => {
                if (err) {
                    console.log("err found")
                    console.error(err)
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if (err) console.error(err);
                    })
                    waitBeforeUpdate()
                }
                else {
                    console.log("sync zone: " + zone.NumZone + "_" + zone.idsite)
                    pool_tls.query(sqlUpdateZone, [zone.NumZone, zone.idsite], async (err, res) => {
                        if (err) {
                            fs.appendFile(pathname, err.toString(), (err) => {
                                if (err) console.error(err);
                            })
                            console.error(err)
                        }
                        pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                            if (err) {
                                fs.appendFile(pathname, err.toString(), (err) => {
                                    if (err) console.error(err);
                                })
                                console.error(err)
                            }
                        })
                    })
                    setTimeout(() => {
                        syncZoneById(zones, index + 1)
                    }, 200)
                }
            })
        }
    }
    else
        waitBeforeUpdate()
}

function updateData() {
    pool_tls.query(sqlSelectZone, [], async (err, zones) => {
        if (err) {
            fs.appendFile(pathname, err.toString(), (err) => {
                if (err) console.error(err);
            })
            waitBeforeUpdate()
            console.error(err)
        }
        else {
            if (zones.length > 0) {
                console.log("zone to sync: " + zones.length)
                syncZoneById(zones, 0)
            }
            else {
                console.log(moment().format("YYYY-MM-DD HH:mm:ss"))
                waitBeforeUpdate()
            }
            pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                if (err) {
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if (err) console.error(err);
                    })
                    console.error(err)
                }
            })
        }
    })
}

let count = 1
function waitBeforeUpdate() {
    console.log("-----" + (count > 1 ? "-----" : "") + (count > 2 ? "-----" : "") + (count > 3 ? "-----" : ""))
    setTimeout(() => {
        updateData()
    }, 3000)
    if (count > 3) count = 1
    else count++
}

updateData()
