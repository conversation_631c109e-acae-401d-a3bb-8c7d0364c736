const moment = require('moment')
const mysql = require('mysql2')
const fs = require("fs");

moment.locale('fr')
const auth = require("../../auth");
const { argv } = require('process');

const db_config_zo = auth.db_config_zo
const pool_tls = mysql.createPool(db_config_zo)

const db_config_admin = auth.db_config_admin
const pool_admin = mysql.createPool(db_config_admin)

const pathname = 'logs/sync/absence/' + moment().format('YYYYMMDDHHmmss') + '.log'
fs.writeFile(pathname, moment().format('LLLL') + '\n\n', (err) => {
    console.error(err)
})

const sqlSelectAbsence = "SELECT id, type_absence, employe_id, site_id, depart, retour, " +
    "motif , superviseur_id, status " +
    "from absences " +
    "where synchronized_at is null or (admin_updated_at is not null and synchronized_at <= admin_updated_at) " +
    (argv[2] == 'reverse' ? " order by id desc  limit 100 " : " limit 50 ")
const sqlInsertOrUpdate = "INSERT INTO absences(id, type_absence, employe_id, site_id, depart, retour, " +
    "motif , superviseur_id, status " +
    ") VALUES (?,?,?,?,?,?,?,?,?) " +
    "ON DUPLICATE KEY UPDATE type_absence=?, employe_id=?, site_id=?, depart=?, retour=?, motif=?, superviseur_id=?, " +
    "status=? "
const sqlUpdateAbsence = "UPDATE absences SET synchronized_at = now() WHERE id = ?"
const sqlInsertLastSync = "UPDATE synchronisations SET last_sync_update = now() WHERE service = 'absence'"

function updateAbsenceById(absences, index) {
    if (index < absences.length) {
        const absence = absences[index]
        const params = [absence.id, absence.type_absence, absence.employe_id, absence.site_id, absence.depart, absence.retour,
        absence.motif, absence.superviseur_id, absence.status
        ]
        pool_tls.query(sqlInsertOrUpdate, [...params, ...params.slice(1)], async (err, res) => {
            if (err) {
                console.log("err found")
                console.error(err)
                fs.appendFile(pathname, err.toString(), (err) => {
                    if (err) console.error(err);
                })
                waitBeforeUpdate()
            }
            else {
                console.log("sync absence: " + absence.id)
                pool_admin.query(sqlUpdateAbsence, [absence.id], async (err, res) => {
                    if (err) {
                        fs.appendFile(pathname, err.toString(), (err) => {
                            if (err) console.error(err);
                        })
                        console.error(err)
                    }
                    pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                        if (err) {
                            fs.appendFile(pathname, err.toString(), (err) => {
                                if (err) console.error(err);
                            })
                            console.error(err)
                        }
                    })
                })
                setTimeout(() => {
                    updateAbsenceById(absences, index + 1)
                }, 200)
            }
        })
    }
    else
        waitBeforeUpdate()
}
function updateData() {
    pool_admin.query(sqlSelectAbsence, [], async (err, absences) => {
        if (err) {
            fs.appendFile(pathname, err.toString(), (err) => {
                if (err) console.error(err);
            })
            waitBeforeUpdate()
            console.error(err)
        }
        else {
            pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                if (err) {
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if (err) console.error(err);
                    })
                    console.error(err)
                }
            })
            if (absences.length > 0) {
                console.log("absence to sync: " + absences.length)
                updateAbsenceById(absences, 0)
            }
            else {
                console.log(moment().format("YYYY-MM-DD HH:mm:ss"))
                waitBeforeUpdate()
            }
        }
    })
}

let count = 1
function waitBeforeUpdate() {
    console.log("-----" + (count > 1 ? "-----" : "") + (count > 2 ? "-----" : "") + (count > 3 ? "-----" : ""))
    setTimeout(() => {
        updateData()
    }, 3000)
    if (count > 3) count = 1
    else count++
}

updateData()
