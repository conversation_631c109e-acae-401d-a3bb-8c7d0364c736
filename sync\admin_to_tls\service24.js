const moment = require('moment')
const mysql = require('mysql2')
const fs = require("fs");

moment.locale('fr')
const auth = require("../../auth");
const { argv } = require('process');

const {db_config_zo, db_config_admin} = auth
const pool_tls = mysql.createPool(db_config_zo)
const pool_admin = mysql.createPool(db_config_admin)

const pathname = 'logs/sync/service24/' + moment().format('YYYYMMDDHHmmss') + '.log'
fs.writeFile(pathname, moment().format('LLLL') + '\n\n', (err) => {
    console.error(err)
})

const sqlSelectService24 = "SELECT id, employe_id, site_id, date_pointage, motif, status " +
    "from service24s " +
    "where synchronized_at is null or (admin_updated_at is not null and synchronized_at <= admin_updated_at) " +
    (argv[2] == 'reverse' ? " order by id desc  limit 100 " : " limit 50 ")
const sqlInsertOrUpdate = "INSERT INTO service24s(id, employe_id, site_id, date_pointage, motif, status " +
    ") VALUES (?,?,?,?,?,?) " +
    "ON DUPLICATE KEY UPDATE employe_id=?, site_id=?, date_pointage=?, motif=?, status=? "
const sqlUpdateService24 = "UPDATE service24s SET synchronized_at = now() WHERE id = ?"
const sqlInsertLastSync = "UPDATE synchronisations SET last_sync_update = now() WHERE service = 'service24'"

function updateService24ById(service24s, index) {
    if (index < service24s.length) {
        const service24 = service24s[index]
        const params = [service24.id, service24.employe_id, service24.site_id, service24.date_pointage, service24.motif.replace(/[\u200E\u200F\u202A-\u202E]/g, ''), service24.status]
        pool_tls.query(sqlInsertOrUpdate, [...params, ...params.slice(1)], async (err, res) => {
            if (err) {
                console.log("err found")
                console.error(err)
                fs.appendFile(pathname, err.toString(), (err) => {
                    if (err) console.error(err);
                })
                waitBeforeUpdate()
            }
            else {
                console.log("sync service24: " + service24.id)
                pool_admin.query(sqlUpdateService24, [service24.id], async (err, res) => {
                    if (err) {
                        fs.appendFile(pathname, err.toString(), (err) => {
                            if (err) console.error(err);
                        })
                        console.error(err)
                    }
                    pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                        if (err) {
                            fs.appendFile(pathname, err.toString(), (err) => {
                                if (err) console.error(err);
                            })
                            console.error(err)
                        }
                    })
                })
                setTimeout(() => {
                    updateService24ById(service24s, index + 1)
                }, 200)
            }
        })
    }
    else
        waitBeforeUpdate()
}
function updateData() {
    pool_admin.query(sqlSelectService24, [], async (err, service24s) => {
        if (err) {
            fs.appendFile(pathname, err.toString(), (err) => {
                if (err) console.error(err);
            })
            waitBeforeUpdate()
            console.error(err)
        }
        else {
            if (service24s.length > 0) {
                console.log("service24 to sync: " + service24s.length)
                updateService24ById(service24s, 0)
            }
            else {
                console.log(moment().format("YYYY-MM-DD HH:mm:ss"))
                waitBeforeUpdate()
            }
            pool_tls.query(sqlInsertLastSync, [], (err, res) => {
                if (err) {
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if (err) console.error(err);
                    })
                    console.error(err)
                }
            })
        }
    })
}

let count = 1
function waitBeforeUpdate() {
    console.log("-----" + (count > 1 ? "-----" : "") + (count > 2 ? "-----" : "") + (count > 3 ? "-----" : ""))
    setTimeout(() => {
        updateData()
    }, 3000)
    if (count > 3) count = 1
    else count++
}

updateData()
