const moment = require('moment')
const mysql = require('mysql2')
const fs = require("fs");

moment.locale('fr')
const auth = require("../../auth");

const {db_config_zo, db_config_cdr} = auth

const pool_tls = mysql.createPool(db_config_zo)
const pool_cdr = mysql.createPool(db_config_cdr)

const pathname = 'logs/sync/cdr/' + moment().format('YYYYMMDDHHmmss') + '.log'
fs.writeFile(pathname, moment().format('LLLL') + '\n\n', (err) => {
    console.error(err)
})

const sqlSelectCdr = "SELECT datetime, uniqueid, clid, src, dst, duration, billable, disposition, " +
    "calltype, recordfile, recordpath " +
    "from cdr " +
    "where datetime > ? order by datetime asc limit 50 "
const sqlInsert = "INSERT INTO cdr(datetime, uniqueid, clid, src, dst, duration, billable, disposition, " +
    "calltype, recordfile, recordpath " +
    ") VALUES (?,?,?,?,?,?,?,?,?,?,?) " 

const sqlSelectLastDataSync = "SELECT value FROM params p WHERE p.key = 'last_date_sync_cdr'"

const sqlUpdateLastSync = "UPDATE params p SET p.value = ? WHERE p.key = 'last_date_sync_cdr'"

function insertCdr(cdrs, lastDateSync,index) {
    if (index < cdrs.length) {
        const cdr = cdrs[index]
        const params = [cdr.datetime, cdr.uniqueid, cdr.clid, cdr.src, cdr.dst, cdr.duration, cdr.billable, cdr.disposition, cdr.calltype, cdr.recordfile, cdr.recordpath ]
        pool_tls.query(sqlInsert, [...params], async (err, res) => {
            if (err) {
                console.log("err found")
                console.error(err)
                fs.appendFile(pathname, err.toString(), (err) => {
                    if (err) console.error(err);
                })
                waitBeforeUpdate()
            }
            else {
                lastDateSync = moment(cdr.datetime).format("YYYY-MM-DD HH:mm:ss")
                console.log("sync cdr: " + lastDateSync)
                setTimeout(() => {
                    insertCdr(cdrs, lastDateSync, index + 1)
                }, 200)
            }
        })
    }
    else{
        console.log("sync cdr done", lastDateSync)
        if(lastDateSync) {
            pool_tls.query(sqlUpdateLastSync, [lastDateSync], (err, res) => {
                if (err) {
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if (err) console.error(err);
                    })
                    console.error(err)
                }
            })
        }
        waitBeforeUpdate()
    }
}

function updateData() {
    pool_tls.query(sqlSelectLastDataSync, [], async (err, res) => {
        if (err) {
            console.error(err)
        }
        else{
            let lastDateSync  = res[0].value
            pool_cdr.query(sqlSelectCdr, [lastDateSync], async (err, cdrs) => {
                if (err) {
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if (err) console.error(err);
                    })
                    waitBeforeUpdate()
                    console.error(err)
                }
                else {
                    if (cdrs.length > 0) {
                        console.log("cdrs to sync: " + cdrs.length)
                        insertCdr(cdrs, lastDateSync, 0)
                    }
                    else {
                        console.log(moment().format("YYYY-MM-DD HH:mm:ss"))
                        waitBeforeUpdate()
                    }
                }
            })
        }
    })
}

let count = 1
function waitBeforeUpdate() {
    console.log("-----" + (count > 1 ? "-----" : "") + (count > 2 ? "-----" : "") + (count > 3 ? "-----" : ""))
    setTimeout(() => {
        updateData()
    }, 3000)
    if (count > 3) count = 1
    else count++
}

updateData()
