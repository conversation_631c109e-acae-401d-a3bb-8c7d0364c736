const moment = require('moment')
const mysql = require('mysql2')
const fs = require("fs");
const crypto = require('crypto');

moment.locale('fr')
const auth = require("../../auth");

const {db_config_zo, db_config_cdr} = auth

const pool_tls = mysql.createPool(db_config_zo)
const pool_cdr = mysql.createPool(db_config_cdr)

const pathname = 'logs/sync/cdr/' + moment().format('YYYYMMDDHHmmss') + '.log'
fs.writeFile(pathname, moment().format('LLLL') + '\n\n', (err) => {
    console.error(err)
})

const sqlSelectCdr = "SELECT ADDTIME(datetime, SEC_TO_TIME(duration)) as 'datetime_duration', datetime, uniqueid, clid, src, dst, duration, billable, disposition, " +
    "calltype, recordfile, recordpath " +
    "from cdr " +
    "where  ADDTIME(datetime, SEC_TO_TIME(duration)) >= ? and ADDTIME(datetime, SEC_TO_TIME(duration)) < ? " +
    (process.argv[2] ? " and duration >= 120 " : "") +
    "ORDER BY ADDTIME(datetime, SEC_TO_TIME(duration)) asc limit 50 "

const sqlInsert = "INSERT IGNORE INTO cdr(datetime, uniqueid, clid, src, dst, duration, billable, disposition, " +
    "calltype, recordfile, recordpath, created_at, data_hash " +
    ") VALUES ? " 

const sqlSelectLastDataSync = "SELECT value FROM params p WHERE p.key = " + (process.argv[2] ? "'last_date_sync_cdr_late'" : "'last_date_sync_cdr'")
// last_date_sync_cdr_late
const sqlUpdateLastSync = "UPDATE params p SET p.value = ? WHERE p.key = " + (process.argv[2] ? "'last_date_sync_cdr_late'" : "'last_date_sync_cdr'")

function insertCdr(cdrs, lastDateSync) {
    if (cdrs.length > 0) {

        let valueToInsert = cdrs.map(cdr =>{
            const rowString = `${cdr.datetime}-${cdr.uniqueid}-${cdr.clid}-${cdr.src}-${cdr.dst}-${cdr.duration}-${cdr.billable}-${cdr.disposition}-${cdr.calltype}-${cdr.recordfile}-${cdr.recordpath}`
            const hash = crypto.createHash('sha256').update(rowString).digest('hex');
            return [
                cdr.datetime, 
                cdr.uniqueid, 
                cdr.clid, 
                cdr.src, 
                cdr.dst, 
                cdr.duration, 
                cdr.billable, 
                cdr.disposition, 
                cdr.calltype, 
                cdr.recordfile, 
                cdr.recordpath,
                moment().format("YYYY-MM-DD HH:mm:ss"),
                hash
            ]
        })
        pool_tls.query(sqlInsert, [valueToInsert], async (err, res) => {
            if (err) {
                console.log("err found")
                console.error(err)
                fs.appendFile(pathname, err.toString(), (err) => {
                    if (err) console.error(err);
                })
                waitBeforeUpdate()
            }
            else {
                console.log("last date: " + cdrs[cdrs.length - 1].datetime)
                lastDateSync = moment(cdrs[cdrs.length - 1].datetime_duration).format("YYYY-MM-DD HH:mm:ss")
                pool_tls.query(sqlUpdateLastSync, [lastDateSync], (err, res) => {
                    if (err) {
                        fs.appendFile(pathname, err.toString(), err => {
                            if (err) console.error(err);
                        });
                    }
                    else {
                        console.log("sync cdr: " + lastDateSync)
                        waitBeforeUpdate()
                    }
                })
                
            }
        })
    }
    else{
        console.log("Aucune donnée à insérer");
        waitBeforeUpdate();
    }
}

function updateData() {
    console.log("updateData")
    pool_tls.query(sqlSelectLastDataSync, [], async (err, res) => {
        if (err) {
            console.error(err)
            console.log(sqlSelectLastDataSync)
            waitBeforeUpdate()
        }
        else{
            let lastDateSync  = res[0].value
            pool_cdr.query(sqlSelectCdr, [lastDateSync, moment().subtract(process.argv[2] ?? 4, 'minute').format('YYYY-MM-DD HH:mm:ss')], async (err, cdrs) => {
                if (err) {
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if (err) console.error(err);
                    })
                    console.error(err)
                    console.log("err found")
                    waitBeforeUpdate()
                }
                else {
                    if (cdrs.length > 0) {
                        console.log("cdrs to sync: " + cdrs.length)
                        insertCdr(cdrs, lastDateSync)
                    }
                    else {
                        console.log(moment().format("YYYY-MM-DD HH:mm:ss"))
                        waitBeforeUpdate()
                    }
                }
            })
        }
    })
}

let count = 1
function waitBeforeUpdate() {
    console.log("-----" + (count > 1 ? "-----" : "") + (count > 2 ? "-----" : "") + (count > 3 ? "-----" : ""))
    setTimeout(() => {
        updateData()
    }, process.argv[2] ? 10000 : 3000)
    if (count > 3) count = 1
    else count++
}

updateData()
