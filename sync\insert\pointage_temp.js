const moment = require('moment')
const mysql = require('mysql')
const nodemailer = require("nodemailer")
const pdf = require("pdf-creator-node");
const fs = require("fs");

moment.locale('fr')
const auth = require("../../auth")

const db_config_zo = auth.db_config_zo
const pool_tls = mysql.createPool(db_config_zo)

const db_config_admin = auth.db_config_admin
const pool_admin = mysql.createPool(db_config_admin)

const pathname = 'logs/sync/' +  moment().format('YYYYMMDDHHmmss') + '.log'
fs.writeFile(pathname, moment().format('LLLL') + '\n\n', (err) => {
	console.error(err)
})

const sqlUpdateLastSyncPointage = (ids) => "UPDATE pointages SET synchronized_at = now() WHERE id in (" + ids.join(',') + ")"

const sqlInsertPointage = "INSERT INTO pointages(id, site_id, agent_id, date_pointage, vigilance, dtarrived, pointeuse_id, user_id, soft_delete) VALUES ?"

const sqlSelectPointage = "SELECT id, site_id, agent_id, date_pointage, vigilance, dtarrived, pointeuse_id, user_id, soft_delete " +
    "from pointages " +
    "where synchronized_at is null " +
    "limit 500"
const sqlInsertOrUpdatePointage = "INSERT INTO pointages (id, site_id, agent_id, date_pointage, vigilance, dtarrived, pointeuse_id, user_id, soft_delete) VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?) " +
    "ON DUPLICATE KEY UPDATE site_id=?, agent_id=?, date_pointage=?, vigilance=?, dtarrived=?, pointeuse_id=?,  user_id=?, soft_delete=?"

function syncDataPointage(){
    pool_tls.query(sqlSelectPointage, [], async (err, pointages) => {
        if(err){
            fs.appendFile(pathname, err.toString(), (err) => {
                if(err) console.error(err);
            })
            updateData()
            console.error(err)
        }
        else {
            if(pointages.length > 0){
                console.log("select new pointage: " + pointages.length)
                const values = []
                const ids = []
                pointages.map(p => {
                    ids.push(p.id)
                    values.push([p.id, p.site_id, p.agent_id, p.date_pointage, p.vigilance, p.dtarrived, p.pointeuse_id, p.user_id, p.soft_delete])
                })
                pool_admin.query(sqlInsertPointage, [values], async (err, res) => {
                    if(err){
                        fs.appendFile(pathname, err.toString(), (err) => {
                            if(err) console.error(err);
                        })
                        updateData()
                        console.error(err)
                    }
                    else {
                        console.log("insert new agent")
                        pool_tls.query(sqlUpdateLastSyncPointage(ids), [], async (err, res) => {
                            if(err){
                                fs.appendFile(pathname, err.toString(), (err) => {
                                    if(err) console.error(err);
                                })
                                updateData(false)
                                console.error(err)
                            }
                            else {
                                console.log("update sync date")
                                updateData(false)
                            }
                        })
                    }
                })
            }
            else {
                console.log("no pointage to insert")
                updateData()
            }
        }
    })
}

function updateData(){
    console.log("----------------------")
    setTimeout(() => {
        syncDataPointage()
    }, 1500)
}

syncDataPointage()