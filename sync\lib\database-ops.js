/**
 * Database operations for the synchronization service
 */

const mysql = require('mysql2/promise');
const moment = require('moment');

/**
 * Connect to a database
 * @param {Object} dbConfig - Database configuration
 * @param {string} dbName - Database name for logging
 * @param {Function} logFn - Logging function
 * @returns {Promise<Object>} - MySQL connection pool
 */
async function connectToDatabase(dbConfig, dbName, logFn) {
    let attempts = 0;
    const maxAttempts = 5;

    while (attempts < maxAttempts) {
        attempts++;
        try {
            logFn(`Connecting to ${dbName} database (attempt ${attempts}/${maxAttempts})...`);

            // Create connection pool
            const pool = mysql.createPool(dbConfig);

            // Test connection
            await pool.query('SELECT 1');

            logFn(`Successfully connected to ${dbName} database`);
            return pool;
        } catch (err) {
            logFn(`Failed to connect to ${dbName} database: ${err.message}`, 'error');

            // If this is the last attempt, throw the error
            if (attempts >= maxAttempts) {
                throw err;
            }

            // Wait before retrying
            const waitTime = 1000 * attempts; // Increase wait time with each attempt
            logFn(`Waiting ${waitTime / 1000} seconds before next attempt...`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }
    }
}

/**
 * Initialize database connections
 * @param {Object} dbConfigs - Database configurations
 * @param {string} sourceDbName - Source database name
 * @param {string} targetDbName - Target database name
 * @param {Function} logFn - Logging function
 * @returns {Promise<Object>} - Database connection pools
 */
async function initializeDatabases(dbConfigs, sourceDbName, targetDbName, logFn) {
    try {
        // Connect to source database
        const sourcePool = await connectToDatabase(dbConfigs[sourceDbName], sourceDbName, logFn);

        // Connect to target database
        const targetPool = await connectToDatabase(dbConfigs[targetDbName], targetDbName, logFn);

        // Connect to configuration database (always zo)
        const configPool = await connectToDatabase(dbConfigs.zo, 'config (zo)', logFn);

        return { sourcePool, targetPool, configPool };
    } catch (err) {
        logFn(`Failed to initialize databases: ${err.message}`, 'error');
        throw err;
    }
}

/**
 * Fetch entities from source database
 * @param {Object} sourcePool - Source database connection pool
 * @param {string} selectQuery - Select query
 * @param {Function} logFn - Logging function
 * @returns {Promise<Array>} - Fetched entities
 */
async function fetchEntities(sourcePool, selectQuery, logFn) {
    try {
        const [entities] = await sourcePool.query(selectQuery);
        return entities;
    } catch (err) {
        logFn(`Error fetching entities: ${err.message}`, 'error');
        throw err;
    }
}

/**
 * Synchronize a single entity
 * @param {Object} entity - Entity to synchronize
 * @param {Object} targetPool - Target database connection pool
 * @param {Object} sourcePool - Source database connection pool
 * @param {Object} configPool - Configuration database connection pool
 * @param {string} insertOrUpdateQuery - Insert or update query
 * @param {string} updateSyncQuery - Update sync query
 * @param {string} insertLastSyncQuery - Insert last sync query
 * @param {string} syncKey - Sync key field name
 * @param {Function} prepareParams - Function to prepare parameters
 * @param {boolean} dryRun - Whether to run in dry run mode
 * @param {Function} logFn - Logging function
 * @returns {Promise<boolean>} - Whether the synchronization was successful
 */
async function syncSingleEntity(
    entity,
    targetPool,
    sourcePool,
    configPool,
    insertOrUpdateQuery,
    updateSyncQuery,
    insertLastSyncQuery,
    syncKey,
    prepareParams,
    dryRun,
    logFn
) {
    const entityId = entity[syncKey];

    try {
        // Prepare parameters for insert/update query
        let params = prepareParams ? prepareParams(entity) : Object.values(entity);

        // Execute insert/update query
        if (!dryRun) {
            await targetPool.query(insertOrUpdateQuery, params);
            logFn(`Synced entity: ${entityId}`, 'success');

            // Update synchronized_at timestamp
            await sourcePool.query(updateSyncQuery, [entityId]);
            logFn(`Updated synchronized_at for entity: ${entityId}`);

            // Update last_sync_update
            await configPool.query(insertLastSyncQuery);
        } else {
            logFn(`[DRY RUN] Would sync entity: ${entityId}`);
            logFn(`[DRY RUN] Would update synchronized_at for entity: ${entityId}`);
        }

        return true;
    } catch (err) {
        logFn(`Error syncing entity ${entityId}: ${err.message}`, 'error');
        throw err;
    }
}

/**
 * Update last sync timestamp
 * @param {Object} configPool - Configuration database connection pool
 * @param {string} insertLastSyncQuery - Insert last sync query
 * @param {boolean} dryRun - Whether to run in dry run mode
 * @param {Function} logFn - Logging function
 */
async function updateLastSyncTimestamp(configPool, insertLastSyncQuery, dryRun, logFn) {
    try {
        if (!dryRun) {
            await configPool.query(insertLastSyncQuery);
        } else {
            logFn(`[DRY RUN] Would update last_sync_update`);
        }
    } catch (err) {
        logFn(`Error updating last_sync_update: ${err.message}`, 'error');
    }
}

/**
 * Update last data update
 * @param {Object} configPool - Configuration database connection poo
 * @param {string} service - Service name
 * @param {boolean} dryRun - Whether to run in dry run mode
 * @param {Function} logFn - Logging function
 */
async function updateLastDataUpdate(configPool, service, dryRun, logFn) {
    const query = `UPDATE synchronisations SET last_data_update = NOW() WHERE service = '${service}'`;
    try {
        if (!dryRun) {
            await configPool.query(query);
        } else {
            logFn(`[DRY RUN] Would update last_data_update`);
        }
    } catch (err) {
        logFn(`Error updating last_data_update: ${err.message}`, 'error');
    }
}


/**
 * Perform bulk operations for test_periodique service
 * @param {Array} entities - Entities to synchronize
 * @param {Object} targetPool - Target database connection pool
 * @param {Object} sourcePool - Source database connection pool
 * @param {Object} configPool - Configuration database connection pool
 * @param {Object} serviceConfig - Service configuration
 * @param {boolean} dryRun - Whether to run in dry run mode
 * @param {Function} logFn - Logging function
 * @returns {Promise<boolean>} - Whether the synchronization was successful
 */
async function performBulkOperations(
    entities,
    targetPool,
    sourcePool,
    configPool,
    serviceConfig,
    dryRun,
    logFn
) {
    try {
        if (entities.length === 0) {
            logFn(`No entities to sync using bulk operations`);

            // Update last_sync_update even if no entities to sync
            if (serviceConfig.flags && serviceConfig.flags.updateLastSyncEvenIfEmpty) {
                await updateLastSyncTimestamp(configPool, serviceConfig.insertLastSync, dryRun, logFn);
            }

            return true;
        }

        logFn(`Found ${entities.length} entities to sync using bulk operations`);

        // Prepare data for bulk insert
        const bulkData = entities.map(entity => [
            entity.pointeuse_id,
            entity.site_id,
            entity.dtarrived
        ]);

        // Execute bulk insert
        if (!dryRun) {
            await targetPool.query(serviceConfig.bulkInsert, [bulkData]);
            logFn(`Inserted ${entities.length} entities in bulk`, 'success');

            // Delete entities from source database
            const ids = entities.map(entity => entity.id);
            const deleteQuery = serviceConfig.bulkDelete(ids);
            await sourcePool.query(deleteQuery);
            logFn(`Deleted ${entities.length} entities from source database`);

            // Update last_sync_update
            await configPool.query(serviceConfig.insertLastSync);
        } else {
            logFn(`[DRY RUN] Would insert ${entities.length} entities in bulk`);
            logFn(`[DRY RUN] Would delete ${entities.length} entities from source database`);
            logFn(`[DRY RUN] Would update last_sync_update`);
        }

        return true;
    } catch (err) {
        logFn(`Error performing bulk operations: ${err.message}`, 'error');
        throw err;
    }
}

/**
 * Update a single entity (for services that use UPDATE instead of INSERT/UPDATE)
 * @param {Object} entity - Entity to update
 * @param {Object} targetPool - Target database connection pool
 * @param {Object} sourcePool - Source database connection pool
 * @param {Object} configPool - Configuration database connection pool
 * @param {string} updateQuery - Update query
 * @param {string} updateSyncQuery - Update sync query
 * @param {string} insertLastSyncQuery - Insert last sync query
 * @param {string} syncKey - Sync key field name
 * @param {Function} prepareParams - Function to prepare parameters
 * @param {boolean} dryRun - Whether to run in dry run mode
 * @param {Function} logFn - Logging function
 * @returns {Promise<boolean>} - Whether the update was successful
 */
async function updateSingleEntity(
    entity,
    targetPool,
    sourcePool,
    configPool,
    updateQuery,
    updateSyncQuery,
    insertLastSyncQuery,
    syncKey,
    prepareParams,
    dryRun,
    logFn
) {
    const entityId = entity[syncKey];

    try {
        // Prepare parameters for update query
        let params = prepareParams ? prepareParams(entity) : Object.values(entity);

        // Execute update query
        if (!dryRun) {
            await targetPool.query(updateQuery, params);
            logFn(`Updated entity: ${entityId}`, 'success');

            // Update synchronized_at timestamp
            await sourcePool.query(updateSyncQuery, [entityId]);
            logFn(`Updated synchronized_at for entity: ${entityId}`);

            // Update last_sync_update
            await configPool.query(insertLastSyncQuery);
        } else {
            logFn(`[DRY RUN] Would update entity: ${entityId}`);
            logFn(`[DRY RUN] Would update synchronized_at for entity: ${entityId}`);
        }

        return true;
    } catch (err) {
        logFn(`Error updating entity ${entityId}: ${err.message}`, 'error');
        throw err;
    }
}

module.exports = {
    connectToDatabase,
    initializeDatabases,
    fetchEntities,
    syncSingleEntity,
    updateLastSyncTimestamp,
    updateLastDataUpdate,
    performBulkOperations,
    updateSingleEntity
};
