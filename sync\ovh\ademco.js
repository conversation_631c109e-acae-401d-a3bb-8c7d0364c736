const moment = require('moment')
const mysql = require('mysql2')
const fs = require("fs");

moment.locale('fr')
const auth = require("../../auth")

const {db_config_maroho, db_config_306, db_config_zo} = auth
const pool_tls = mysql.createPool(process.argv[2] == "306" ? db_config_306 : db_config_maroho)
const pool_ovh = mysql.createPool(db_config_zo)

const pathname = 'logs/sync/agent/' +  moment().format('YYYYMMDDHHmmss') + '.log'
fs.writeFile(pathname, moment().format('LLLL') + '\n\n', (err) => {
	console.error(err)
})
const numTraite = (process.argv[2] == "306" ? "4" : "3")

let reverse = false

const sqlSelectLogTls = "SELECT a.idademco, a.prom, a.checkphone, a.sip, a.messageType, a.eventQualify, a.codeevent, a.partition, " +
    "a.zones, a.dtarrived, a.pointeuse_user_id, a.pointeuse_id, a.loyalty_level, a.transmitter, a.port, a.received_at_ovh, a.istraite " +
    "from ademcomemlog a where istraite = " + numTraite
const sqlSelectLogOvh = "SELECT a.idademco from ademcomemlog a where istraite = " + numTraite


function setDataTraiteTls(){
    const sqlUpdateTraite = "UPDATE ademcomemlog set istraite = " + numTraite + (reverse ? " order by idademco desc  limit 25 " : " limit 250 ")
    reverse = !reverse
    pool_tls.query(sqlUpdateTraite, [], async (err, res) => {
        if(err){
            fs.appendFile(pathname, err.toString(), (err) => {
                if(err) console.error(err);
            })
            console.error(err)
            waitBeforeUpdate(3000)
        }
        else {
            if(moment().isAfter(moment().set({h:6, m:0, s:0})) && moment().isBefore(moment().set({h:18, m:0, s:0})))
                waitBeforeUpdate(3000)
            else
                waitBeforeUpdate(1000)
        }
    })
}
function insertDataOvh(rows){
    console.log("nb data: " + rows.length)
    const sqlInsert = "INSERT INTO ademcomemlog(prom, checkphone, sip, messageType, eventQualify, codeevent, `partition`, zones, " +
        "dtarrived, pointeuse_user_id, pointeuse_id, loyalty_level, transmitter, port, received_at_ovh, istraite) values ?"
        pool_ovh.query(sqlInsert, [rows], async (err, res) => {
        if(err){
            fs.appendFile(pathname, err.toString(), (err) => {
                if(err) console.error(err);
            })
            console.error(err)
        }
        waitBeforeUpdate()
    })
}
function deleteDataTraiteTls() {
    const sqlDeleteTraite = "DELETE from ademcomemlog WHERE istraite = " + numTraite
    pool_tls.query(sqlDeleteTraite, [], async (err, res) => {
        if(err){
            fs.appendFile(pathname, err.toString(), (err) => {
                if(err) console.error(err);
            })
            console.error(err)
        }
        waitBeforeUpdate()
    })
}
function clearDataTraiteOvh() {
    const sqlUpdateTraiteVigilance = "UPDATE ademcomemlog set istraite = 2 WHERE istraite = " + numTraite + " and codeTevent in (603, 1000)"
    pool_ovh.query(sqlUpdateTraiteVigilance, [], async (err, res) => {
        if(err){
            fs.appendFile(pathname, err.toString(), (err) => {
                if(err) console.error(err);
            })
            waitBeforeUpdate()
            console.error(err)
        }
        else {
            const sqlUpdateTraiteAlarm = "UPDATE ademcomemlog set istraite = 1 WHERE istraite = " + numTraite + " and codeTevent not in (603, 1000)"
            pool_ovh.query(sqlUpdateTraiteAlarm, [], async (err, res) => {
                if(err){
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if(err) console.error(err);
                    })
                    console.error(err)
                }
                waitBeforeUpdate()
            })
        }
    })
}

function updateData(){
    console.log(reverse)
    pool_tls.query(sqlSelectLogTls, [], async (err, tls_logs) => {
        if(err){
            fs.appendFile(pathname, err.toString(), (err) => {
                if(err) console.error(err);
            })
            waitBeforeUpdate()
            console.error(err)
        }
        else {
            pool_ovh.query(sqlSelectLogOvh, [], async (err, ovh_logs) => {
                if(err){
                    fs.appendFile(pathname, err.toString(), (err) => {
                        if(err) console.error(err);
                    })
                    waitBeforeUpdate()
                    console.error(err)
                }
                else {
                    const traite_ovh_log = ovh_logs.length
                    const traite_tls_log = tls_logs.length
                    let rows = []
                    tls_logs.forEach(a => {
                        rows.push([a.prom, a.checkphone, a.sip, a.messageType, a.eventQualify, a.codeevent, a.partition, a.zones, a.dtarrived, a.pointeuse_user_id, 
                            a.pointeuse_id, a.loyalty_level, a.transmitter, a.port, a.received_at_ovh, numTraite])
                    });
                    console.log("------------")
                    console.log(traite_tls_log, traite_ovh_log)
                    if(traite_tls_log > 0 && traite_ovh_log == 0){
                        console.log("data to traite is existing")
                        insertDataOvh(rows)
                    }
                    else if(traite_tls_log > 0 && traite_ovh_log > 0){
                        console.log("delete data traite tls")
                        deleteDataTraiteTls()
                    }
                    else if(traite_tls_log == 0 && traite_ovh_log > 0){
                        console.log("clear data traite ovh")
                        clearDataTraiteOvh()
                    }
                    else {
                        console.log("check data to sync...")
                        setDataTraiteTls()
                    }
                }
            })
        }
    })
}
function waitBeforeUpdate(duration){
    setTimeout(() => {
        updateData()
    }, duration ? duration : 100)
}

updateData()