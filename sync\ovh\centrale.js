const moment = require('moment')
const mysql = require('mysql')
const fs = require("fs");

moment.locale('fr')
const auth = require("../../auth")

const {db_config_maroho, db_config_306, db_config_zo} = auth
const pool_tls = mysql.createPool(process.argv[2] == "306" ? db_config_306 : db_config_maroho)
const pool_ovh = mysql.createPool(db_config_zo)

const pathname = 'logs/sync/centrale/' +  moment().format('YYYYMMDDHHmmss') + '.log'
fs.writeFile(pathname, moment().format('LLLL') + '\n\n', (err) => {
	console.error(err)
})

const sqlSelectCentrale = "SELECT t.id, t.prom, t.centrale_id, t.last_transmission " +
    "from type_centrales t order by t.id desc limit 50"
const sqlSelectSite = "SELECT s.idsite, s.last_sms_check " +
    "from sites s where s.prom = ? limit 1"
const sqlUpdateSite = "UPDATE sites set idcentrale = ?, last_sms_check = ? where idsite = ?"
const sqlDeleteCentrale = "DELETE FROM type_centrales where id = ?"

function deleteCentrale(centrales, index, id) {
    pool_tls.query(sqlDeleteCentrale, [id], async (err, res) => {
        if(err){
            fs.appendFile(pathname, err.toString(), (err) => {
                if(err) console.error(err);
            })
            waitBeforeUpdate()
            console.error(err)
        }
        else 
            updateCentrale(centrales, index+1)
    })
}

function updateCentrale(centrales, index) {
    if(index < centrales.length){
        const centrale = centrales[index]
        console.log(centrale)
        pool_ovh.query(sqlSelectSite, [centrale.prom], async (err, sites) => {
            if(err){
                fs.appendFile(pathname, err.toString(), (err) => {
                    if(err) console.error(err);
                })
                waitBeforeUpdate()
                console.error(err)
            }
            else if(sites.length > 0){
                const site = sites[0]
                if(!site.last_sms_check || (site.last_sms_check && moment(centrale.last_transmission).isAfter(moment(site.last_sms_check)))) {
                    console.log("centrale updated successfully!")
                    pool_ovh.query(sqlUpdateSite, [centrale.centrale_id, centrale.last_transmission, site.idsite], async (err, res) => {
                        if(err){
                            fs.appendFile(pathname, err.toString(), (err) => {
                                if(err) console.error(err);
                            })
                            waitBeforeUpdate()
                            console.error(err)
                        }
                        else
                            deleteCentrale(centrales, index, centrale.id)
                    })
                }
                else 
                    deleteCentrale(centrales, index, centrale.id)
            }
            else
                deleteCentrale(centrales, index, centrale.id)

        })
    }
    else
        waitBeforeUpdate()
}

function updateData(){
    console.log("-----------------\n" + moment().format("YYYY-MM-DD HH:mm:ss"))
    pool_tls.query(sqlSelectCentrale, [], async (err, rows) => {
        if(err){
            fs.appendFile(pathname, err.toString(), (err) => {
                if(err) console.error(err);
            })
            waitBeforeUpdate()
            console.error(err)
        }
        else if(rows.length > 0){
            updateCentrale(rows, 0)
        }
        else 
            waitBeforeUpdate()
    })
}
function waitBeforeUpdate(duration){
    setTimeout(() => {
        updateData()
    }, duration ? duration : 10000)
}

updateData()