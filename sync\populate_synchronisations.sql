-- SQL script to handle the synchronisations table
-- This script will:
-- 1. Create the table if it doesn't exist
-- 2. Alter the table if it exists but is missing columns
-- 3. Populate it with all the configured services

-- Check if the synchronisations table exists
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'synchronisations');

-- Create the synchronisations table if it doesn't exist
SET @create_table_sql = IF(@table_exists = 0, '
CREATE TABLE `synchronisations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `service` varchar(255) NOT NULL,
  `last_sync_update` datetime DEFAULT NULL,
  `source_db` varchar(255) NOT NULL,
  `target_db` varchar(255) NOT NULL,
  `source_table` varchar(255) NOT NULL,
  `target_table` varchar(255) NOT NULL,
  `fields` text NOT NULL,
  `field_mappings` text DEFAULT NULL,
  `delete_action` varchar(255) DEFAULT "sync",
  `sync_condition` varchar(255) DEFAULT "admin_updated",
  `specific_date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `service` (`service`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
', 'SELECT "Table already exists" AS message');

PREPARE create_table_stmt FROM @create_table_sql;
EXECUTE create_table_stmt;
DEALLOCATE PREPARE create_table_stmt;

-- Check if field_mappings column exists
SET @field_mappings_exists = (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'synchronisations' AND column_name = 'field_mappings');

-- Add field_mappings column if it doesn't exist
SET @add_field_mappings_sql = IF(@field_mappings_exists = 0, 'ALTER TABLE `synchronisations` ADD COLUMN `field_mappings` text DEFAULT NULL AFTER `fields`', 'SELECT "field_mappings column already exists" AS message');

PREPARE add_field_mappings_stmt FROM @add_field_mappings_sql;
EXECUTE add_field_mappings_stmt;
DEALLOCATE PREPARE add_field_mappings_stmt;

-- Check if delete_action column exists
SET @delete_action_exists = (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'synchronisations' AND column_name = 'delete_action');

-- Add delete_action column if it doesn't exist
SET @add_delete_action_sql = IF(@delete_action_exists = 0, 'ALTER TABLE `synchronisations` ADD COLUMN `delete_action` varchar(255) DEFAULT "sync" AFTER `field_mappings`', 'SELECT "delete_action column already exists" AS message');

PREPARE add_delete_action_stmt FROM @add_delete_action_sql;
EXECUTE add_delete_action_stmt;
DEALLOCATE PREPARE add_delete_action_stmt;

-- Check if sync_condition column exists
SET @sync_condition_exists = (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'synchronisations' AND column_name = 'sync_condition');

-- Add sync_condition column if it doesn't exist
SET @add_sync_condition_sql = IF(@sync_condition_exists = 0, 'ALTER TABLE `synchronisations` ADD COLUMN `sync_condition` varchar(255) DEFAULT "admin_updated" AFTER `delete_action`', 'SELECT "sync_condition column already exists" AS message');

PREPARE add_sync_condition_stmt FROM @add_sync_condition_sql;
EXECUTE add_sync_condition_stmt;
DEALLOCATE PREPARE add_sync_condition_stmt;

-- Check if specific_date column exists
SET @specific_date_exists = (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'synchronisations' AND column_name = 'specific_date');

-- Add specific_date column if it doesn't exist
SET @add_specific_date_sql = IF(@specific_date_exists = 0, 'ALTER TABLE `synchronisations` ADD COLUMN `specific_date` datetime DEFAULT NULL AFTER `sync_condition`', 'SELECT "specific_date column already exists" AS message');

PREPARE add_specific_date_stmt FROM @add_specific_date_sql;
EXECUTE add_specific_date_stmt;
DEALLOCATE PREPARE add_specific_date_stmt;

-- Backup existing data before truncating
CREATE TABLE IF NOT EXISTS `synchronisations_backup` LIKE `synchronisations`;
INSERT INTO `synchronisations_backup` SELECT * FROM `synchronisations`;

-- Clear existing data
TRUNCATE TABLE `synchronisations`;

-- Insert all services
INSERT INTO `synchronisations`
(`service`, `source_db`, `target_db`, `source_table`, `target_table`, `fields`, `field_mappings`, `delete_action`, `sync_condition`, `specific_date`)
VALUES
-- Admin to ZO/TLS services
('agent', 'admin', 'zo', 'employes', 'agents',
'[
    "id",
    "societe_id",
    "numero_stagiaire",
    "numero_employe",
    "num_emp_soit",
    "num_emp_saoi",
    "nom",
    "site_id",
    "real_site_id",
    "date_embauche",
    "date_confirmation",
    "date_conf_soit",
    "date_sortie",
    "soft_delete",
    "fonction_id",
    "sal_forfait",
    "observation",
    "agence_id",
    "last_update",
    "created_at"
]',
NULL, 'sync', 'admin_updated', NULL),

('service24', 'admin', 'zo', 'service24s', 'service24s',
'[
    "id",
    "employe_id",
    "site_id",
    "date_pointage",
    "motif",
    "status"
]',
NULL, 'sync', 'admin_updated', NULL),

('user admin', 'admin', 'zo', 'users', 'admin_users',
'[
    "id",
    "name",
    "email",
    "type",
    "role",
    "flotte",
    "blocked"
]',
NULL, 'sync', 'specific_date', '2025-02-01 00:00:01'),

('site_to_tls', 'admin', 'zo', 'sites', 'sites',
'[
    "idsite",
    "superviseur_id",
    "resp_sup_id",
    "group_planning_id"
]',
NULL, 'sync', 'admin_updated', NULL),

('formation', 'admin', 'tls_formation', 'recrutements', 'agents',
'[
    "id",
    "nom",
    "societe_id",
    "numero_stagiaire",
    "soft_delete",
    "updated_at"
]',
'{"updated_at": "last_update"}', 'sync', 'updated', NULL),

('absence', 'admin', 'ovh', 'absences', 'absences_admin',
'[
    "id",
    "type_absence",
    "employe_id",
    "site_id",
    "depart",
    "retour",
    "motif",
    "superviseur_id",
    "status"
]',
NULL, 'sync', 'admin_updated', NULL),

-- ZO to Admin services
('action', 'zo', 'admin', 'actions', 'actions',
'[
    "id",
    "type_action_id",
    "rapport_id",
    "soft_delete",
    "created_at",
    "updated_at"
]',
NULL, 'delete', 'admin_updated', NULL),

('alarm', 'zo', 'admin', 'ademcotemp', 'alarms',
'[
    "idademco",
    "rapport_id",
    "zones",
    "dtarrived",
    "site_id",
    "agent_id",
    "pointeuse_id",
    "eventQualify",
    "codeTevent"
]',
'{"agent_id": "employe_id"}', 'sync', 'admin_updated', NULL),

('comment_pointage', 'zo', 'admin', 'comment_pointages', 'comment_pointages',
'[
    "id",
    "date_pointage",
    "site_id",
    "comment"
]',
NULL, 'sync', 'admin_updated', NULL),

('contact', 'zo', 'admin', 'contacts', 'contacts',
'[
    "idContact",
    "nom",
    "prenom",
    "adresse",
    "lastupdate",
    "soft_delete"
]',
NULL, 'sync', 'admin_updated', NULL),

('habilite', 'zo', 'admin', 'habilites', 'habilites',
'[
    "idhabilite",
    "idcontact",
    "idsite",
    "idordre",
    "Timedisponible",
    "starttime",
    "stoptime",
    "password",
    "quality",
    "DayDisponible",
    "startdate",
    "stopdate",
    "code",
    "idPartition",
    "lastupdate"
]',
NULL, 'sync', 'admin_updated', NULL),

('horaire', 'zo', 'admin', 'horaire_effectifs', 'horaire_effectifs',
'[
    "id",
    "site_id",
    "day_1",
    "night_1",
    "day_2",
    "night_2",
    "day_3",
    "night_3",
    "day_4",
    "night_4",
    "day_5",
    "night_5",
    "day_6",
    "night_6",
    "day_0",
    "night_0",
    "day_ferie",
    "night_ferie"
]',
NULL, 'sync', 'admin_updated', NULL),

('indice_appel', 'zo', 'admin', 'call_users', 'call_users',
'[
    "id",
    "user_id",
    "call_id",
    "sip",
    "destination",
    "created_at"
]',
NULL, 'sync', 'admin_updated', NULL),

('numero', 'zo', 'admin', 'numeros', 'numeros',
'[
    "id",
    "id_contact",
    "id_site",
    "numero",
    "soft_delete"
]',
NULL, 'sync', 'admin_updated', NULL),

('pointage', 'zo', 'admin', 'pointages', 'pointages',
'[
    "id",
    "site_id",
    "agent_id",
    "date_pointage",
    "vigilance",
    "dtarrived",
    "pointeuse_id",
    "user_id",
    "soft_delete"
]',
'{"agent_id": "employe_id"}', 'sync', 'admin_updated', NULL),

('rapport', 'zo', 'admin', 'rapports', 'rapports',
'[
    "id",
    "type_rapport_id",
    "site_id",
    "eventcode",
    "zone",
    "commentaire",
    "dtarrived",
    "user_id",
    "soft_delete",
    "created_at",
    "updated_at",
    "debut",
    "fin",
    "depart",
    "arrivee",
    "technicien",
    "tache",
    "exported",
    "idademco",
    "intervention_id"
]',
NULL, 'sync', 'admin_updated', NULL),

('reclamation', 'zo', 'admin', 'reclamations', 'reclamations',
'[
    "date_pointage",
    "agent_id",
    "site_id",
    "superviseur_id",
    "agent_not_registered",
    "type",
    "user_id",
    "created_at",
    "updated_at"
]',
'{"agent_id": "employe_id"}', 'sync', 'admin_updated', NULL),

('site_to_admin', 'zo', 'admin', 'sites', 'sites',
'[
    "id",
    "nom",
    "adresse",
    "code_postal",
    "ville",
    "telephone",
    "email",
    "soft_delete"
]',
NULL, 'sync', 'admin_updated', NULL),

('test_periodique', 'zo', 'admin', 'test_periodiques', 'test_periodiques',
'[
    "id",
    "site_id",
    "date_test",
    "resultat",
    "commentaire",
    "soft_delete",
    "created_at",
    "updated_at"
]',
NULL, 'sync', 'none', NULL),

('zone', 'zo', 'admin', 'zonesites', 'zones',
'[
    "NumZone",
    "idsite",
    "idzone",
    "nomZone",
    "idcapteur",
    "soft_delete"
]',
NULL, 'sync', 'admin_updated', NULL),

-- TLS to Admin services
('secteur', 'tls', 'admin', 'secteurs', 'secteurs',
'[
    "id",
    "nom",
    "heure_contrat",
    "group_pointage_id"
]',
NULL, 'sync', 'updated', NULL);

-- Restore any custom services that were in the original table but not in our standard list
-- This will insert services that were in the original table but not in our standard list
-- It will skip services that are already in the table (due to the UNIQUE KEY constraint)
INSERT IGNORE INTO `synchronisations`
SELECT * FROM `synchronisations_backup`
WHERE service NOT IN (SELECT service FROM `synchronisations`);

-- Output a message to indicate completion
SELECT 'Synchronisations table has been updated successfully.' AS message;

-- Note: The synchronisations_backup table is kept for reference
-- You can drop it if you don't need it anymore with:
-- DROP TABLE IF EXISTS `synchronisations_backup`;
