/**
 * Unified Synchronization Service
 *
 * Handles data synchronization between different databases using service-specific logic.
 *
 * Usage: node sync_ser.js <service_name> [options]
 *
 * Options:
 * - dry-run: Run without making actual changes
 * - debug: Enable detailed logging
 * - one-time: Run once and exit
 *
 * Examples:
 * node sync_ser.js numero
 * node sync_ser.js contact dry-run
 * node sync_ser.js agent one-time
 */

const moment = require('moment');
const { argv } = require('process');

const auth = require('../auth');
const { getServiceConfig } = require('./lib/service-registry');
const dbOps = require('./lib/database-ops');
const logger = require('./lib/logger-ops');

moment.locale('fr');

// Global timing configuration for all services
const serviceTiming = {
    recordDelay: 200,     // Delay between processing records
    batchDelay: 3000,     // Delay between batches
    errorDelay: 10000,     // Delay after an error
    connectionErrorDelay: 60000 // Delay after a connection error
};

const emailRecipients = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>"
];

// Command line arguments
const serviceName = argv[2];
const dryRun = argv.includes('dry-run');
const debug = argv.includes('debug');
const oneTime = argv.includes('one-time');

// Special handling for reclamation service with date/time arguments
let dateTimeArg = null;
if (serviceName === 'reclamation') {
    if (argv[3] === 'task') {
        // Task mode - use the last service time
        console.log("Running reclamation in task mode");
    } else if (argv[3] && argv[4] &&
        /^\d{4}-\d{2}-\d{2}$/.test(argv[3]) &&
        /^\d{2}:\d{2}:\d{2}$/.test(argv[4])) {
        // Date/time mode - use the specified date/time
        dateTimeArg = argv[3] + ' ' + argv[4];
        console.log(`Running reclamation with date: ${dateTimeArg}`);
    }
}

// Database configurations
const dbConfigs = {
    zo: auth.db_config_zo,
    ovh: auth.db_config_zo,
    // tls: auth.db_config_zo, // Alias for zo
    admin: auth.db_config_admin,
    tls_formation: auth.db_config_tls_formation,
    maroho: auth.db_config_maroho || auth.db_config_zo, // Fallback to zo if not defined
    '306': auth.db_config_306 || auth.db_config_zo // Fallback to zo if not defined
};

// Global variables
let sourcePool = null;
let targetPool = null;
let configPool = null;
let adminPool = null;  // Dedicated admin pool for email functionality
let logPath = null;
let serviceConfig = null;

// Timestamp tracking for robust synchronization
let lastSuccessfulSyncTimestamp = null;  // Tracks when any sync action completes successfully
let lastDatabaseTimestampUpdate = null;  // Tracks when we last updated the database timestamp

/**
 * Check if 5 minutes have elapsed since the last database timestamp update
 * and update the timestamp if there have been successful synchronizations
 * @param {boolean} dryRun - Whether to run in dry run mode
 * @returns {Promise<boolean>} - Whether the timestamp was updated
 */
async function checkAndUpdateTimestamp(dryRun) {
    const logFunc = logger.createLogFunction(logPath);

    // If we haven't had any successful syncs, don't update the timestamp
    if (!lastSuccessfulSyncTimestamp) {
        return false;
    }

    // Check if 5 minutes have elapsed since the last database update
    const fiveMinutesInMs = 5 * 60 * 1000;
    const currentTime = Date.now();

    // If this is the first run or 5 minutes have elapsed since the last update
    if (!lastDatabaseTimestampUpdate || (currentTime - lastDatabaseTimestampUpdate) >= fiveMinutesInMs) {
        try {
            // Update the timestamp in the database
            await dbOps.updateLastSyncTimestamp(configPool, serviceConfig.insertLastSync, dryRun, logFunc);

            // Update our tracking variables
            lastDatabaseTimestampUpdate = currentTime;

            logFunc(`Updated database timestamp after successful synchronizations`);
            return true;
        } catch (err) {
            logFunc(`Error updating timestamp: ${err.message}`, 'error');
            return false;
        }
    }

    return false;
}

/**
 * Send an email notification for errors
 * @param {Error} err - The error object
 * @param {string} subject - The email subject
 */
function sendErrorEmail(err, subject) {
    // Use adminPool for email functionality if available, otherwise fall back to targetPool
    const emailPool = adminPool;
    logger.sendErrorEmail(err, subject, emailPool, emailRecipients, auth.sendMail, logPath);
}

/**
 * Initialize database connections
 * @param {string} sourceDbName - Source database name
 * @param {string} targetDbName - Target database name
 */
async function initializeDatabases(sourceDbName, targetDbName) {
    try {
        const logFunc = logger.createLogFunction(logPath);

        const pools = await dbOps.initializeDatabases(dbConfigs, sourceDbName, targetDbName, logFunc);
        sourcePool = pools.sourcePool;
        targetPool = pools.targetPool;
        configPool = pools.configPool;

        // Initialize admin pool for email functionality if not already initialized
        if (!adminPool) {
            try {
                adminPool = await dbOps.connectToDatabase(dbConfigs.admin, 'admin (for email)', logFunc);
                logger.logInfo(`Successfully connected to admin database for email functionality`, logPath);
            } catch (adminErr) {
                logger.logError(`Failed to connect to admin database for email functionality: ${adminErr.message}`, logPath);
                // Continue without admin pool - emails will not be sent
            }
        }
    } catch (err) {
        logger.logError(`Failed to initialize databases: ${err.message}`, logPath);
        throw err;
    }
}

/**
 * Synchronize entities based on service configuration
 * @param {Array} entities - Entities to synchronize
 * @param {number} index - Current index in the entities array
 */
async function syncEntity(entities, index) {
    const logFunc = logger.createLogFunction(logPath);
    if (index >= entities.length) {
        // Check if we should exit after synchronization
        if (serviceConfig.flags && serviceConfig.flags.exitAfterSync) {
            logger.logInfo(`Synchronization completed, exiting as configured...`, logPath);
            process.exit(0);
        }
        //update last data update
        await dbOps.updateLastDataUpdate(configPool, serviceName, dryRun, logFunc);
        // All entities processed, wait before next batch
        return waitBeforeUpdate();
    }

    const entity = entities[index];
    const syncKey = serviceConfig.syncKey || 'id';
    const entityId = entity[syncKey];

    try {
        if (debug) {
            logger.logInfo(`Syncing entity ${entityId}`, logPath);
        }

        // Create log function for database operations
        const logFunc = logger.createLogFunction(logPath);

        // Check if this is a soft-deleted entity that needs special handling
        if (serviceConfig.flags && serviceConfig.flags.handleSoftDelete &&
            entity.soft_delete && serviceConfig.processSoftDelete) {

            // Use the service-specific soft delete handler
            await serviceConfig.processSoftDelete(
                entity,
                targetPool,
                sourcePool,
                configPool,
                serviceConfig.updateSync,
                serviceConfig.insertLastSync,
                dryRun,
                logFunc
            );

            // Record successful synchronization
            lastSuccessfulSyncTimestamp = Date.now();
        } else if (serviceConfig.flags && serviceConfig.flags.useUpdateQuery) {
            // Use updateSingleEntity for services that use UPDATE instead of INSERT/UPDATE
            await dbOps.updateSingleEntity(
                entity,
                targetPool,
                sourcePool,
                configPool,
                serviceConfig.updateQuery,
                serviceConfig.updateSync,
                serviceConfig.insertLastSync,
                syncKey,
                serviceConfig.prepareParams,
                dryRun,
                logFunc
            );

            // Record successful synchronization
            lastSuccessfulSyncTimestamp = Date.now();
        } else {
            // Use database operations module to sync entity
            await dbOps.syncSingleEntity(
                entity,
                targetPool,
                sourcePool,
                configPool,
                serviceConfig.insertOrUpdate,
                serviceConfig.updateSync,
                serviceConfig.insertLastSync,
                syncKey,
                serviceConfig.prepareParams,
                dryRun,
                logFunc
            );

            // Record successful synchronization
            lastSuccessfulSyncTimestamp = Date.now();
        }

        // Process next entity after delay
        const recordDelay = serviceConfig.timing?.recordDelay || serviceTiming.recordDelay;
        setTimeout(() => {
            syncEntity(entities, index + 1);
        }, recordDelay);
    } catch (err) {
        logger.logError(`Error syncing entity ${entityId}: ${err.message}`, logPath);

        // Handle error based on service configuration
        if (serviceConfig.handleError) {
            const errorHandling = serviceConfig.handleError(err, entity);

            // Send email notification
            if (errorHandling.emailSubject) {
                sendErrorEmail(err, errorHandling.emailSubject);
            }

            // Retry after delay
            const retryDelay = errorHandling.retryDelay || serviceConfig.timing?.retryDelay || serviceTiming.errorDelay;

            setTimeout(() => {
                syncEntity(entities, index);
            }, retryDelay);
        } else {
            // Default error handling
            const defaultRetryDelay = serviceConfig.timing?.retryDelay || serviceTiming.errorDelay;
            setTimeout(() => {
                syncEntity(entities, index);
            }, defaultRetryDelay);
        }
    }
}

/**
 * Wait before fetching and processing the next batch
 * Also checks if we need to update the timestamp in the database
 */
async function waitBeforeUpdate() {
    // Check if we need to update the timestamp in the database
    await checkAndUpdateTimestamp(dryRun);

    const batchDelay = serviceConfig.timing?.batchDelay || serviceTiming.batchDelay;
    logger.logInfo(`Waiting ${batchDelay / 1000} seconds before next batch...`, logPath);
    setTimeout(fetchAndSyncEntities, batchDelay);
}

/**
 * Fetch entities from source database and start synchronization
 */
async function fetchAndSyncEntities() {
    try {
        // Create log function for database operations
        const logFunc = logger.createLogFunction(logPath);

        // Special handling for test_periodique service (bulk operations)
        if (serviceName === 'test_periodique' && serviceConfig.flags && serviceConfig.flags.useBulkOperations) {
            // Fetch entities
            const entities = await dbOps.fetchEntities(sourcePool, serviceConfig.select, logFunc);

            // Perform bulk operations
            await dbOps.performBulkOperations(
                entities,
                targetPool,
                sourcePool,
                configPool,
                serviceConfig,
                dryRun,
                logFunc
            );

            // Record successful synchronization
            lastSuccessfulSyncTimestamp = Date.now();

            // Wait before next batch or exit if one-time mode
            if (oneTime) {
                logger.logInfo(`One-time synchronization completed, exiting...`, logPath);
                process.exit(0);
            } else {
                return waitBeforeUpdate();
            }
        }

        // Special handling for reclamation service
        if (serviceName === 'reclamation') {
            // Validate that reclamation service is running in one of the two valid modes:
            // 1. Task mode (argv[3] === 'task')
            // 2. With an explicitly provided date parameter (dateTimeArg is set)

            // Check if we're in task mode
            if (argv[3] === 'task') {
                // Check last sync from params table
                const lastSync = await serviceConfig.checkLastSync(sourcePool);
                const datePointage = serviceConfig.getLastService();

                if (lastSync !== datePointage) {
                    logger.logInfo(`Last sync (${lastSync}) doesn't match current service date (${datePointage}), performing sync`, logPath);
                    dateTimeArg = datePointage;
                } else {
                    logger.logInfo(`Sync reclamation already done for ${datePointage}!`, logPath);
                    if (oneTime) {
                        logger.logInfo(`One-time synchronization completed, exiting...`, logPath);
                        process.exit(0);
                    } else {
                        return waitBeforeUpdate();
                    }
                }
            }

            // If we have a dateTimeArg (either from command line or from task mode), use it for the query
            if (dateTimeArg) {
                logger.logInfo(`Using date parameter for reclamation: ${dateTimeArg}`, logPath);
                const entities = await sourcePool.query(serviceConfig.select, [dateTimeArg]);

                logger.logInfo(`Found ${entities.length} reclamations to sync for date ${dateTimeArg}`, logPath);

                if (entities.length > 0) {
                    // Start synchronization
                    syncEntity(entities, 0);

                    // Update last_reclamation_sync in params table
                    if (!dryRun) {
                        await serviceConfig.updateLastSync(sourcePool, dateTimeArg);
                        logger.logInfo(`Updated last_reclamation_sync to ${dateTimeArg}`, logPath);

                        // Record successful synchronization
                        lastSuccessfulSyncTimestamp = Date.now();
                    } else {
                        logger.logInfo(`[DRY RUN] Would update last_reclamation_sync to ${dateTimeArg}`, logPath);
                    }
                } else {
                    logger.logInfo(`No reclamations to sync for date ${dateTimeArg}`, logPath);

                    // Check if we need to update the timestamp in the database
                    if (serviceConfig.flags && serviceConfig.flags.updateLastSyncEvenIfEmpty) {
                        // For empty reclamation syncs, we still want to record a successful sync
                        lastSuccessfulSyncTimestamp = Date.now();
                        await checkAndUpdateTimestamp(dryRun);
                    }

                    if (oneTime) {
                        logger.logInfo(`One-time synchronization completed, exiting...`, logPath);
                        process.exit(0);
                    } else {
                        return waitBeforeUpdate();
                    }
                }

                return;
            } else {
                // Neither task mode nor date parameter provided - this is an invalid configuration
                const errorMsg = `Error: Reclamation service requires either task mode or a date parameter.`;
                logger.logError(errorMsg, logPath);
                logger.logError(`Usage: node sync_ser.js reclamation task`, logPath);
                logger.logError(`   or: node sync_ser.js reclamation YYYY-MM-DD HH:MM:SS`, logPath);

                // Send email notification
                sendErrorEmail(new Error(errorMsg), `Invalid configuration for reclamation service`);

                // Exit with error code
                process.exit(1);
            }
        }

        // For other services, use standard approach
        // Note: Reclamation service should never reach this point due to validation above
        const selectQuery = serviceConfig.select;
        const entities = await dbOps.fetchEntities(sourcePool, selectQuery, logFunc);

        if (entities.length > 0) {
            logger.logInfo(`Found ${entities.length} entities to sync`, logPath);

            // Record successful synchronization when entities are found
            lastSuccessfulSyncTimestamp = Date.now();

            // Check if we need to update the timestamp in the database
            if (serviceConfig.flags && serviceConfig.flags.alwaysUpdateLastSync) {
                await checkAndUpdateTimestamp(dryRun);
            }

            // Start synchronization
            syncEntity(entities, 0);
        } else {
            logger.logInfo('No entities to sync', logPath);

            // Check if we need to update the timestamp in the database
            if (serviceConfig.flags && serviceConfig.flags.updateLastSyncEvenIfEmpty) {
                // For empty syncs, we still want to record a successful sync
                lastSuccessfulSyncTimestamp = Date.now();
                await checkAndUpdateTimestamp(dryRun);
            }

            // Wait before next batch
            waitBeforeUpdate();
        }
    } catch (err) {
        logger.logError(`Error fetching entities: ${err.message}`, logPath);

        // Send email notification
        sendErrorEmail(err, `Error fetching entities for ${serviceName}`);

        // Wait longer before retry on connection errors
        const connectionErrorDelay = serviceConfig.timing?.connectionErrorDelay || serviceTiming.connectionErrorDelay;
        const errorDelay = serviceConfig.timing?.retryDelay || serviceTiming.errorDelay;
        const waitTime = err.message.includes('too many connections') ?
            connectionErrorDelay :
            errorDelay;

        logger.logInfo(`Waiting ${waitTime / 1000} seconds before retry...`, logPath);
        setTimeout(fetchAndSyncEntities, waitTime);
    }
}

/**
 * Main function
 */
async function main() {
    if (!serviceName) {
        console.error('Please provide a service name');
        console.error('Usage: node sync_ser.js <service> [dry-run] [debug] [one-time]');
        process.exit(1);
    }

    try {
        // Get service configuration
        serviceConfig = await getServiceConfig(serviceName);

        // Initialize logging
        logPath = logger.initializeLogging(serviceName);

        logger.logInfo(`Starting synchronization service for: ${serviceName}`, logPath);
        logger.logInfo(`Source DB: ${serviceConfig.sourceDb}, Target DB: ${serviceConfig.targetDb}`, logPath);
        logger.logInfo(`Source Table: ${serviceConfig.sourceTable}, Target Table: ${serviceConfig.targetTable}`, logPath);

        if (dryRun) {
            logger.logInfo(`Running in DRY RUN mode - no actual database changes will be made`, logPath);
        }

        if (debug) {
            logger.logInfo(`Running in DEBUG mode - detailed logging enabled`, logPath);
        }

        if (oneTime) {
            logger.logInfo(`Running in ONE-TIME mode - service will exit after one synchronization`, logPath);
        } else {
            logger.logInfo(`Running in CONTINUOUS mode - service will run indefinitely`, logPath);
        }

        // Initialize database connections
        await initializeDatabases(serviceConfig.sourceDb, serviceConfig.targetDb);

        // Initialize timestamp tracking
        lastSuccessfulSyncTimestamp = null;
        lastDatabaseTimestampUpdate = null;

        // Start synchronization
        await fetchAndSyncEntities();

        // If one-time mode, exit after first batch
        if (oneTime) {
            logger.logInfo(`One-time synchronization completed, exiting...`, logPath);
            process.exit(0);
        }
    } catch (err) {
        logger.logError(`Fatal error: ${err.message}`, logPath);

        // Send email notification
        sendErrorEmail(err, `Fatal error in synchronization service for ${serviceName}`);

        process.exit(1);
    }
}

// Start the application
main();
