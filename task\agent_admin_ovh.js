const moment = require('moment')
const mysql = require('mysql')

moment.locale('fr')

const {db_config_ovh, db_config_admin} = require("../auth")
const pool_admin = mysql.createPool(db_config_admin)
const pool_ovh = mysql.createPool(db_config_ovh)

const sqlSelectAgentOvh = "SELECT a.id, a.nom, a.societe_id, a.numero_stagiaire, a.numero_employe, a.num_emp_soit, " +
    "a.date_embauche, a.date_confirmation, a.date_conf_soit, now() as 'created_at', now() as 'last_update' " +
    "FROM agents a"
const sqlSelectEmployeAdmin = "SELECT e.id FROM employes e"
const sqlInsertEmployeAdmin = "INSERT INTO employes (id, nom, societe_id, numero_stagiaire, numero_employe, num_emp_soit, " +
    "date_embauche, date_confirmation, date_conf_soit, created_at, last_update) VALUES ?"

pool_admin.query(sqlSelectEmployeAdmin, [], async (err, employes) => {
	if(err)
		console.error(err)
	else {
        console.log("Nb employe admin: " + employes.length)
        pool_ovh.query(sqlSelectAgentOvh, [], async (err, agents) => {
            if(err)
                console.error(err)
            else {
                const employeNotFound = []
                console.log("Nb agent ovh: " + agents.length)
                agents.forEach(ag => {
                    if(!employes.map(e => e.id).includes(ag.id)){
                        employeNotFound.push(Object.values(ag))
                    }
                })
                console.log(employeNotFound.length)
                /*pool_admin.query(sqlInsertEmployeAdmin, [employeNotFound], async (err) => {
                    if(err)
                        console.error(err)
                    else {
                        console.log("Multiple insert succefful!")
                        process.exit()
                    }
                })*/
            }
        })
	}
})