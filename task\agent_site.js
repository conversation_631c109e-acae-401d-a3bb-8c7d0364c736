const moment = require('moment')
const mysql = require('mysql')

moment.locale('fr')

const db_config = require("../auth").db_config_ovh
const pool = mysql.createPool(db_config)

const sqlSelectAgentSameSiteGroup = "SELECT a.id, a.site_id, a.site_id " +
	"FROM agents a " +
	"LEFT JOIN sites s1 ON s1.idsite = a.site_id " +
	"LEFT JOIN sites s2 on s2.idsite = a.site_id " +
	"WHERE (a.soft_delete is null or a.soft_delete = 0) and (s1.idsite is not null and s1.idsite != 0) " +
	"and ( " +
		"s2.idsite is null " +
		"or (a.site_id != a.site_id and s2.soft_delete is not null and s2.soft_delete = 1) " +
		"or (a.site_id != a.site_id and s1.group_pointage_id = s2.group_pointage_id) " +
	") "
function sqlUpdateAgent(ids) {
	return "UPDATE agents SET site_id = site_id " +
		"WHERE id in (" + ids.join(',') + ")"
}
function sqlSelectPointage(ids){
	const limitDate = moment().subtract(1, "month").format("YYYY-MM-DD") + " 00:00:00"
	return "SELECT id, site_id, agent_id, date_pointage FROM pointages " +
		"WHERE (soft_delete is null or soft_delete = 0) and date_pointage > '" + limitDate+ "' and agent_id in (" + ids.join(',') + ") " +
		"ORDER BY date_pointage DESC"
}

pool.query(sqlSelectAgentSameSiteGroup, [], async (err, agents) => {
	if(err)
		console.error(err)
	else {
		if(agents && agents.length > 0) {
			console.log("Nb agent: " + agents.length)
			pool.query(sqlSelectPointage(agents.map(a => a.id)), [], async (err, pointages) => {
				console.log("Nb pointage: " + pointages.length)
				agents.forEach(a => {
					a.nb_current_site = 0
					pointages.forEach(p => {
						if(a.id == p.agent_id) {
							if(p.site_id == a.site_id)
								a.nb_current_site ++
						}
					})
				})
				let ids = []
				agents.forEach(a => {
					if(!a.site_id || a.nb_current_site > 8)
						ids.push(a.id)
				})
				if(ids.length > 0){
					console.log("ID agent to update: " + ids.join(', '))
					pool.query(sqlUpdateAgent(ids), [], async (err, r) => {
						if(err)
							console.log(err)
						else {
							console.log("update real site success")
						}
						process.exit(1)
					})
				}
				else {
					console.log("no update")
					process.exit(1)
				}
			})
		}
	}
})