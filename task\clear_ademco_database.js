const moment = require('moment')
const mysql = require('mysql2')

moment.locale('fr')

const {db_config_zo} = require("../auth")
const pool_ovh = mysql.createPool(db_config_zo)

const sqlDeleteAdemcolog = "DELETE FROM ademcolog LIMIT 100000"

const sqlSelectAdemcolog = "SELECT dtarrived FROM ademcolog LIMIT 1"

const deleteData = () => {
    pool_ovh.query(sqlDeleteAdemcolog, [], async (err, res) => {
        if(err)
            console.error(err)
        else {
            pool_ovh.query(sqlSelectAdemcolog, [], async (err, logs) => {
                if(err)
                    console.error(err)
                else {
                    console.log(logs[0].dtarrived)
                    if(moment(logs[0].dtarrived).isBefore(moment("2025-01-01 00:00:00"))){
                        setTimeout(() => {
                            deleteData()
                        }, 500)
                    }
                    else {
                        console.log("process done")
                        process.exit(1)
                    }
                }
            })
        }
    })
}

deleteData()