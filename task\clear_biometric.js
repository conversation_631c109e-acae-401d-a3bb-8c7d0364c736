const moment = require('moment')
const mysql = require('mysql')
const Excel = require("exceljs")
const nodemailer = require("nodemailer")

moment.locale('fr')


const {db_config_ovh, auth_mail_tls} = require("../auth")
const pool = mysql.createPool(db_config_ovh)

let transporter = nodemailer.createTransport({
	host: "ssl0.ovh.net",
	port: 465,
	secure: true, // upgrade later with STARTTLS
	auth: auth_mail_tls,
	tls: {
		rejectUnauthorized: false
	}
  })

const sqlSelectDateDiagExport = "SELECT value FROM params p WHERE p.key = 'last_export_transmitter'"
const sqlUpdateLastDiagExport = "UPDATE params p SET p.value = DATE_FORMAT(NOW(), '%Y-%m-%d') WHERE p.key = 'last_export_transmitter'"

const destination_diag = {
	to: "<EMAIL>,<EMAIL>, <EMAIL>, <EMAIL>, " +
		"<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, " +
		"<EMAIL>, <EMAIL>",
	cc: "<EMAIL>",
}
const destination_test = {
	to: "<EMAIL>",
}

function sendMail(destination, subject, text, attachements, callback){
	const message = {
		from: "<EMAIL>",
		to: destination.to,
		cc: destination.cc,
		subject: subject,
		html: "<p>Bonjour,</p>" + 
			"<p>" + text + "</p>" +
			"<p>Cordialement,</p>",
		attachments: attachements
	};
	transporter.sendMail(message , (err, info) => {
		if(err)
			console.error(err)
		else console.log(info)
		callback()
	})
}

function capitalizeFirstLetter(string) {
	const  arrayString = string.split(' ').map((s) => (
		s.trim().charAt(0).toUpperCase() + s.trim().slice(1).toLowerCase()
	))
	return arrayString.join(' ')
}
function generateAgentDeletedExcelFile(workbook, header, agents){
	const borderStyle = {
		top: {style:'thin'},
		left: {style:'thin'},
		bottom: {style:'thin'},
		right: {style:'thin'}
	}
	const fontHeader = { size: 16, bold: true }
	const fontBold = { bold: true }
	const alignmentStyle = { vertical: 'middle', horizontal: 'center' }
	
	const fillHeader = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'88888888'}
	}
	const fillTitleFade = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'77fce4ec'}
	}
	const fillRed = {
		type: 'pattern',
		pattern:'solid',
		fgColor:{argb:'77e91e63'}
	}
	const worksheet = workbook.addWorksheet(group.name)
	worksheet.getColumn('A').width = 50
	worksheet.getColumn('B').width = 15
	worksheet.getColumn('C').width = 40
	worksheet.getColumn('D').width = 15
	worksheet.getColumn('E').width = 15

	worksheet.mergeCells('A1:E1')
	worksheet.getCell('A1').value = header + " (" + group.sites.length + " sites)"
	worksheet.getCell('A1').font = fontHeader
	worksheet.getCell('A1').alignment = alignmentStyle
	worksheet.getCell('A2').value = "Site"
	worksheet.getCell('A2').border = borderStyle
	worksheet.getCell('A2').fill = fillHeader
	worksheet.getCell('A2').font = fontBold
	worksheet.mergeCells('A2:A3')
	worksheet.getCell('B2').value = "Prom"
	worksheet.getCell('B2').border = borderStyle
	worksheet.getCell('B2').fill = fillHeader
	worksheet.getCell('B2').font = fontBold
	worksheet.getCell('B2').alignment = alignmentStyle
	worksheet.mergeCells('B2:B3')
	worksheet.getCell('C2').value = "Vigilance"
	worksheet.getCell('C2').border = borderStyle
	worksheet.getCell('C2').fill = fillHeader
	worksheet.getCell('C2').font = fontBold
	worksheet.getCell('C2').alignment = alignmentStyle
	worksheet.mergeCells('C2:C3')
	worksheet.getCell('D2').value = "Récépteur"
	worksheet.getCell('D2').border = borderStyle
	worksheet.getCell('D2').fill = fillHeader
	worksheet.getCell('D2').font = fontBold
	worksheet.getCell('D2').alignment = alignmentStyle
	worksheet.mergeCells('D2:E2')
	worksheet.getCell('D3').value = "Actuel"
	worksheet.getCell('D3').border = borderStyle
	worksheet.getCell('D3').fill = fillRed
	worksheet.getCell('D3').font = fontBold
	worksheet.getCell('D3').alignment = alignmentStyle
	worksheet.getCell('E3').value = "Correcte"
	worksheet.getCell('E3').border = borderStyle
	worksheet.getCell('E3').fill = fillHeader
	worksheet.getCell('E3').font = fontBold
	worksheet.getCell('E3').alignment = alignmentStyle
	
	let line = 4
	group.sites.forEach(row => {
		worksheet.getCell('A' + line).value = capitalizeFirstLetter(row.nom)
		worksheet.getCell('A' + line).border = borderStyle
		worksheet.getCell('B' + line).value = row.prom
		worksheet.getCell('B' + line).border = borderStyle
		worksheet.getCell('B' + line).alignment = alignmentStyle
		worksheet.getCell('C' + line).value = (row.vigilance ? row.horaire : '')
		worksheet.getCell('C' + line).border = borderStyle
		worksheet.getCell('C' + line).alignment = alignmentStyle
		worksheet.getCell('D' + line).value = row.transmitter
		worksheet.getCell('D' + line).border = borderStyle
		worksheet.getCell('D' + line).alignment = alignmentStyle
		worksheet.getCell('D' + line).fill = fillTitleFade
		worksheet.getCell('E' + line).value = row.correct_transmitter
		worksheet.getCell('E' + line).border = borderStyle
		worksheet.getCell('E' + line).alignment = alignmentStyle
		line++
	})
}

const sqlSelectAgentDeleted = "SELECT ap.id, ap.pointeuse_id, ap.agent_id, a.nom as 'agent', p.nom as 'pointeuse' " +
	"FROM agent_pointeuses ap " +
	"LEFT JOIN pointeuses p ON p.id = ap.pointeuse_id " +
	"LEFT JOIN agents a ON a.id = ap.agent_id " +
	"WHERE (a.soft_delete is not null and a.soft_delete = 1) " +
    "ORDER BY ap.pointeuse_id ASC"

function doAgentDeleted(){
	console.log("doAgentDeleted")
	pool.query(sqlSelectAgentDeleted, [], async (err, agents) => {
		console.log("after query")
		if(err)
			console.error(err)
		else if(agents.length == 0){
			console.error("no data fetch")
            pool.query(sqlUpdateLastDiagExport, [], (e, r) =>{
                if(e)
                    console.error(e)
                else
                    console.log("update last diag export: " + r)
            })
		}
		else {
			console.log("Nb site: " + agents.length)
			const workbookProvince = new Excel.Workbook()
			generateAgentDeletedExcelFile(workbookProvince, "Empreinte des agents en archive " + moment().format("DD MMMM YYYY"), agents)
			const provinceSiteBuffer = await workbookProvince.xlsx.writeBuffer()
			sendMail(
				(process.argv[2] == 'test') ? destination_test : destination_diag,
				"Récépteur incorrecte " + moment().format('DD-MM-YY'), 
				"Veuillez trouver ci-joint joint la liste des sites dont les recepteurs sont incorrectes.",
				[
					{
						filename: "Récépteur incorrecte PROVINCE " + moment().format("DD-MM-YYYY") + ".xlsx",
						content: provinceSiteBuffer
					},
				], 
				() => {
					if(process.argv[2] == 'task'){
                        pool.query(sqlUpdateLastDiagExport, [], (e, r) =>{
                            if(e)
                                console.error(e)
                            else
                                console.log("update last diag export: " + r)
							process.exit(1)
                        })
					}
					else
						process.exit(1)
				}
			)
		}
	})
}

if(process.argv[2] == 'test'){
	console.log("send test...")
	doAgentDeleted()
}
else if(process.argv[2] == 'task'){
	if(moment().day() == 1 && moment().isAfter(moment().set({hour: 7, minute: 0}))){
		pool.query(sqlSelectDateDiagExport, [], (err, result) => {
			if(err)
				console.error(err)
			else if(result && moment().format("YYYY-MM-DD") == result[0].value){
				console.log("export diag already done!")
				process.exit(1)
			}
			else doAgentDeleted()
		})
	}
	else 
		console.log("skip diag...")
}
else
	console.log("please specify command!")
