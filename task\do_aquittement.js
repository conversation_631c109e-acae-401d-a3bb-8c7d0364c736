const moment = require('moment')
const mysql = require('mysql2')

moment.locale('fr')

const {db_config_zo} = require("../auth")
const pool_ovh = mysql.createPool(db_config_zo)

const sqlSelectMemLog = "SELECT a.idademco, a.prom, a.messageType, a.eventQualify, a.codeevent, a.codeTevent, a.partition, " +
    "a.IdUser, a.zones, 2 as istraite, a.dtarrived, now() as dttraite, a.transmitter, now() as lastupdate, a.received_at,  " +
    "a.agent_id, a.pointeuse_user_id, a.pointeuse_id, a.site_id, a.port, a.rapport_id, a.loyalty_level, a.received_at_ovh, a.is_date_server  " +
    "FROM ademcomemlog a  " +
    "WHERE a.IdUser is not null  and dttraite < '" + moment().subtract(30, "minute").format("YYYY-MM-DD HH:mm:ss") + "' " +
    "and ( " +
    "   codeTevent not in (100,101,111,120,151,110,130,131,132,133,134,137,140)  " +
    "   or (codeTevent in (100,101,111,120,151,110,130,131,132,133,134,137,140) and a.rapport_id is not null) " +
    ") " +
    // "WHERE codeTevent in (301, 302) " +
    // "where (codeTevent not in (100,101,111,120,151,110,130,131,132,133,134,137,140) and dtarrived < '" + moment().subtract(60, "minute").format("YYYY-MM-DD HH:mm:ss") + "') " +
    "LIMIT 50"

const sqlInsertAdemco = "INSERT INTO ademcolog (prom, messageType, eventQualify, codeevent, codeTevent, `partition`, " +
    "IdUser, zones, istraite, dtarrived, dttraite, transmitter, lastupdate, received_at,  " +
    "agent_id, pointeuse_user_id, pointeuse_id, site_id, port, rapport_id, loyalty_level, received_at_ovh, is_date_server) VALUES ?"

const sqlDeleteMemLog = (ids) => "DELETE FROM ademcomemlog WHERE idademco in (" + ids.join(",") + ")"

const doAquittement = () => {
    pool_ovh.query(sqlSelectMemLog, [], async (err, logs) => {
        if(err)
            console.error(err)
        else if(logs.length > 0){
            console.log("----\nNb alarm: " + logs.length)
            const logParams = []
            logs.forEach(a => {
                logParams.push([a.prom, a.messageType, a.eventQualify, a.codeevent, a.codeTevent, a.partition,
                    a.IdUser, a.zones, a.istraite, a.dtarrived, a.dttraite, a.transmitter, a.lastupdate, a.received_at,
                    a.agent_id, a.pointeuse_user_id, a.pointeuse_id, a.site_id, a.port, a.rapport_id, a.loyalty_level, a.received_at_ovh, a.is_date_server])
            });
            pool_ovh.query(sqlInsertAdemco, [logParams], async (err) => {
                if(err)
                    console.error(err)
                else {
                    pool_ovh.query(sqlDeleteMemLog(logs.map(l => l.idademco)), [], async (err) => {
                        if(err)
                            console.error(err)
                        else {
                            console.log("Multiple insert succefful!")
                            setTimeout(() => doAquittement(), 500)
                        }
                    })
                }
            })
        }
        else {
            console.log("No data aquit. ")
            process.exit()
        }
    })
}

doAquittement()