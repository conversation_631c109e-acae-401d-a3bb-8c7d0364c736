const moment = require('moment')
const mysql = require('mysql2')
moment.locale('fr')
const { db_config_zo, db_config_admin, db_config_admin_test } = require("../auth")

const pool_tls = mysql.createPool(db_config_zo);
const pool_admin = mysql.createPool(db_config_admin);
const pool_test = mysql.createPool(db_config_admin_test);

const getLastService = () => {
    const now = moment();
    const currentHour = now.hour();
    if (currentHour >= 6 && currentHour < 18) {
        const start = moment().subtract(1, 'day').set({ hour: 18, minute: 0, second: 0 });
        return start.format('YYYY-MM-DD HH:mm:ss')
    } else {
        const start = moment().set({ hour: 7, minute: 0, second: 0 });
        return start.format('YYYY-MM-DD HH:mm:ss')
    }
}
const sqlSelectLastReclamationSync = "SELECT p.value FROM params p WHERE p.key = 'last_reclamation_sync'"
const sqlUpdateLastReclamationSync = "UPDATE params p SET p.value = ? WHERE p.key = 'last_reclamation_sync'"

const sqlSelectReclamation = `SELECT r.id, r.date_pointage, r.agent_id, r.site_id, r.superviseur_id,
    r.agent_not_registered, r.type, r.user_id, r.created_at, r.updated_at
    FROM reclamations r
    LEFT JOIN pointages ptg ON ptg.agent_id = r.agent_id AND r.date_pointage = ptg.date_pointage
    WHERE r.date_pointage = ? AND (ptg.id is null OR r.agent_id is null)`;

const sqlInsertReclamation = `INSERT INTO reclamations
    (date_pointage, employe_id, site_id, superviseur_id, agent_not_registered, type, user_id, created_at, updated_at, status)
    VALUES ?`
    
const isTask = (process.argv[2] == 'task')

function resultExitTask(date_sync){
    if(isTask)
        pool_tls.query(sqlUpdateLastReclamationSync, [date_sync], (err, res) => {
            if (err) {
                console.error(err)
                process.exit()
            }
            console.log("last sync reclamation updated, process done!")
            process.exit()
        })
    else {
        console.log("process test done!")
        process.exit()
    }
}

function generateReclamation(date_sync, pool_insert) {
    console.log("generateReclamation")
    pool_tls.query(sqlSelectReclamation, [date_sync], async (err, reclamations) => {
        if (err) {
            console.error(err)
            process.exit()
        }
        else {
            console.log("Nb reclamation : " + reclamations.length)
            const params = reclamations.map(reclamation => {
                return [reclamation.date_pointage, reclamation.agent_id, reclamation.site_id, reclamation.superviseur_id, reclamation.agent_not_registered,
                    reclamation.type, reclamation.user_id, reclamation.created_at, reclamation.updated_at, 'demande']
            });
            if(params.length){
                pool_insert.query(sqlInsertReclamation, [params], async (err, res) => {
                    if (err) {
                        console.error(err)
                        process.exit()
                    }
                    else {
                        console.log(params.length + " reclamations inserted successfully!")
                        resultExitTask()
                    }
                })
            }
            else {
                console.log("no reclamation to sync")
                resultExitTask()
            }
        }
    })
}

if (/^\d{4}-\d{2}-\d{2}$/.test(process.argv[2]) && /^\d{2}:\d{2}:\d{2}$/.test(process.argv[3])) {
    console.log("send test...")
    generateReclamation(process.argv[2] + ' ' + process.argv[3], pool_test)
}
else if (process.argv[2] == 'task') {
    pool_tls.query(sqlSelectLastReclamationSync, [], (err, last_sync) => {
        if (err){
            console.error(err)
            process.exit()
        }
        else if (last_sync && last_sync[0]) {
            const syncInfo = last_sync[0]
            let date_pointage = getLastService()
            if (syncInfo.value != date_pointage)
                generateReclamation(date_pointage, pool_admin)
            else {
                console.log("generate reclamation already done!")
                process.exit()
            }
        }
    })
}
else {
    console.log("please specify command!")
}
