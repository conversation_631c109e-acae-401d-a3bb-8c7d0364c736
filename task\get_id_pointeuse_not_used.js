const moment = require('moment')
const mysql = require('mysql')

moment.locale('fr')

const {db_config_ovh} = require("../auth")
const pool_ovh = mysql.createPool(db_config_ovh)

const sqlSelectPointeuse = "SELECT p.id FROM pointeuses p ORDER BY p.id"

pool_ovh.query(sqlSelectPointeuse, [], async (err, pointeuses) => {
    if(err)
        console.error(err)
    else {
        let currentId = 1
        const ids = pointeuses.map(p => p.id)
        const lastId = ids[ids.length - 1]
        while (currentId <= lastId) {
            if(!ids.includes(currentId))
                console.log(currentId)
            currentId++
        }
        console.log("list done")
    }
})