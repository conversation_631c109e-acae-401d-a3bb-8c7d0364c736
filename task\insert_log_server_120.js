
const moment = require('moment')
const mysql = require('mysql')

moment.locale('fr')

const db_config_old = {
	host: "************",
	user: "tls",
	port: "3306",
	database: "tls_al",
	password: "tls_alarm"
}
const db_config_new = {
	host: "************",
	user: "tls",
	port: "3306",
	database: "tls",
	password: "AdmDir2024"
}
const pool_old = mysql.createPool(db_config_old)
const pool_new = mysql.createPool(db_config_old)



const sqlSelectMemLog = "SELECT a.idademco, a.prom, a.messageType, a.eventQualify, a.codeevent, a.codeTevent, a.partition, " +
    "a.IdUser, a.zones, 2 as istraite, a.dtarrived, now() as dttraite, a.transmitter, now() as lastupdate, a.received_at,  " +
    "a.agent_id, a.pointeuse_user_id, a.pointeuse_id, a.site_id, a.port, a.rapport_id, a.loyalty_level, a.received_at_ovh, a.is_date_server  " +
    "FROM ademcomemlog a WHERE dtarrived > '2025-03-25 18:50:00' " +
    "LIMIT 50"
const sqlInsertAdemco = "INSERT INTO ademcomemlog (prom, messageType, eventQualify, codeevent, codeTevent, `partition`, " +
    "IdUser, zones, istraite, dtarrived, dttraite, transmitter, lastupdate, received_at,  " +
    "agent_id, pointeuse_user_id, pointeuse_id, site_id, port, rapport_id, loyalty_level, received_at_ovh, is_date_server) VALUES ?"

const sqlDeleteMemLog = (ids) => "DELETE FROM ademcomemlog WHERE idademco in (" + ids.join(",") + ")"

pool_old.query(sqlSelectMemLog, [], async (err, logs) => {
    if(err)
        console.error(err)
    else if(logs.length > 0){
        console.log("Nb alarm: " + logs.length)
        const logParams = []
        logs.forEach(a => {
            logParams.push([a.prom, a.messageType, a.eventQualify, a.codeevent, a.codeTevent, a.partition,
                a.IdUser, a.zones, a.istraite, a.dtarrived, a.dttraite, a.transmitter, a.lastupdate, a.received_at,
                a.agent_id, a.pointeuse_user_id, a.pointeuse_id, a.site_id, a.port, a.rapport_id, a.loyalty_level, a.received_at_ovh, a.is_date_server])
        });
        pool_new.query(sqlInsertAdemco, [logParams], async (err) => {
            if(err)
                console.error(err)
            else {
                pool_old.query(sqlDeleteMemLog(logs.map(l => l.idademco)), [], async (err) => {
                    if(err)
                        console.error(err)
                    else {
                        console.log("Multiple insert succefful!")
                        process.exit()
                    }
                })
            }
        })
    }
    else {
        
    }
})