const moment = require('moment')
const mysql = require('mysql')

moment.locale('fr')

const {db_config_ovh} = require("../auth")
const { round } = require('lodash')
const pool_ovh = mysql.createPool(db_config_ovh)

const sqlSelectVigilance = "SELECT ad.agent_id, ad.site_id , ad.pointeuse_id, min(dtarrived) as 'dtarrived', ag.soft_delete FROM tls_alarm.ademcotemp ad " +
    "left join agents ag on ag.id = ad.agent_id " +
    //"left join fonctions f on f.id = ag.fonction_id " +
    "where ad.dtarrived > '2024-03-12 05:40:00' and ad.dtarrived < '2024-03-12 06:11:00' " +
    "and ad.agent_id is not null and codeTevent=1000 " +
    "and (ag.sal_forfait = 0 or ag.sal_forfait is null) " +
    "and (ag.soft_delete = 0 or ag.soft_delete is null) " +
    "group by ad.agent_id"
const sqlSelectPointage = "select id FROM pointages WHERE agent_id = ? and date_pointage = '2024-03-12 07:00:00' limit 1"
const sqlSelectMisAPied = "select a.id FROM absences a " +
    "WHERE a.type_absence = 'mis_a_pied' " +
    "and a.employe_id = ? and a.status = 'done' " +
    "and a.depart < now() and a.retour > now() " +
    "limit 1"
const sqlSelectService24 = "select s.id FROM service24s s " +
    "WHERE s.status in ('validation', 'done') and  s.employe_id = ?  " +
    "and s.begin_pointage < DATE_SUB(now(), INTERVAL 90 MINUTE) and s.end_pointage > now() " +
    "limit 1"
const sqlSelectLastPointage = "select max(date_pointage) as 'date_pointage' FROM pointages p " +
    "WHERE p.agent_id = ? and (p.soft_delete is null or p.soft_delete = 0)"
const inserOrUpdatePointage = (vigilances, index) => {
    if(index < vigilances.length){
        const vg = vigilances[index]
        pool_ovh.query(sqlSelectPointage, [vg.agent_id], async (err, pointages) => {
            if(err)
                console.error(err)
            else {
                pool_ovh.query(sqlSelectMisAPied, [vg.agent_id], async (err, absences) => {
                    if(err)
                        console.error(err)
                    else {
                        pool_ovh.query(sqlSelectService24, [vg.agent_id], async (err, service24s) => {
                            if(err)
                                console.error(err)
                            else {
                                pool_ovh.query(sqlSelectLastPointage, [vg.agent_id], async (err, lastPointages) => {
                                    if(err)
                                        console.error(err)
                                    else {
                                        if(
                                            !absences.length
                                            && (
                                                service24s.length
                                                || !lastPointages.length
                                                || moment.duration(moment().diff(lastPointages[0].date_pointage)).asHours() > 20
                                            )
                                        ) {
                                            if(pointages.length && pointages[0].id) {
                                                console.log("--------\nupdate pointage!" +
                                                    (absences.length ? "\n MIS A PIED" : "") +
                                                    "\n - last_pointage : " + (lastPointages.length ? moment(lastPointages[0].date_pointage).format("YYYY-MM-DD HH:mm:ss") : "null") + 
                                                    "\n - durée : " + (lastPointages.length ? round(moment.duration(moment().diff(lastPointages[0].date_pointage)).asHours()) : "null"))
                                                console.log(vg)
                                                
                                                /*pool_ovh.query(sqlUpdatePointage, [vg.pointeuse_id, vg.dtarrived, vg.site_id, vg.agent_id], async (err) => {
                                                    if(err)
                                                        console.error(err)
                                                    else {
                                                        setTimeout(() => {inserOrUpdatePointage(vigilances, index + 1)}, 300)
                                                    }
                                                })*/
                                            }
                                            else {
                                                console.log("--------\ninsert pointage..." +
                                                    (absences.length ? "\n MIS A PIED" : "") +
                                                    "\n - agent_id : " + vg.agent_id +
                                                    "\n - last_pointage : " + (lastPointages.length ? moment(lastPointages[0].date_pointage).format("YYYY-MM-DD HH:mm:ss") : "null") + 
                                                    "\n - durée : " + (lastPointages.length ? round(moment.duration(moment().diff(lastPointages[0].date_pointage)).asHours()) : "null"))
                                                console.log(vg)
                                                
                                                /*pool_ovh.query(sqlInsertPointage, [vg.agent_id, vg.site_id, vg.pointeuse_id, vg.dtarrived], async (err) => {
                                                    if(err)
                                                        console.error(err)
                                                    else {
                                                        setTimeout(() => {inserOrUpdatePointage(vigilances, index + 1)}, 300)
                                                    }
                                                })*/
                                            }
                                        }
                                        else {
                                            if( lastPointages.length && moment.duration(moment().diff(lastPointages[0].date_pointage)).asHours() > 20
                                                && moment(lastPointages[0].date_pointage).format("YYYY-MM-DD HH:mm:ss") != "2024-03-08 18:00:00"
                                            ) {
                                                console.log(
                                                    "--------\nskip pointage" + 
                                                    (absences.length ? "\n MIS A PIED" : "") +
                                                    "\n - last_pointage : " + (lastPointages.length ? moment(lastPointages[0].date_pointage).format("YYYY-MM-DD HH:mm:ss") : "null") + 
                                                    "\n - durée : " + (lastPointages.length ? round(moment.duration(moment().diff(lastPointages[0].date_pointage)).asHours()) : "null"))
                                            }
                                            setTimeout(() => {inserOrUpdatePointage(vigilances, index + 1)}, 300)
                                        }
                                    }
                                })
                            }
                        })
                    }
                })
            }
        })
    }
    else {
        console.log("insert done!!!")
        process.exit()
    }
}


pool_ovh.query(sqlSelectVigilance, [], async (err, vigilances) => {
	if(err)
		console.error(err)
	else {
        inserOrUpdatePointage(vigilances, 0)
	}
})