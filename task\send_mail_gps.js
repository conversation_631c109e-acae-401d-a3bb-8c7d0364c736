
const hbs = require('nodemailer-express-handlebars')
const nodemailer = require("nodemailer")
const path = require('path')
const fs = require('fs')
const moment = require('moment')

const senders = [
    {
        user: "<EMAIL>",
        pass: "AdmDir@2023"
    },
    {
        user: "<EMAIL>",
        pass: "AdmDir@1120"
    },
    {
        user: "<EMAIL>",
        pass: "O$dOOsB!r4J$23@#kOv?./H2Od"
    },
    {
        user: "<EMAIL>",
        pass: "DvLanElec1$DrX2202"
    },
    {
        user: "<EMAIL>",
        pass: "DvDiaElec2$DrX2202"
    },
    {
        user: "<EMAIL>",
        pass: "D$eoVsB!r4J$23@#kOv?./HOUUA"
    },
    {
        user: "<EMAIL>",
        pass: "O$DeCsB!r4J$23@#kOv?./HUUOA"
    },
    {
        user: "<EMAIL>",
        pass: "Tch2420dRX$$23t#kOv?./HL0ASE"
    },
    {
        user: "<EMAIL>",
        pass: "Tech3$DrX2020"
    },
    {
        user: "<EMAIL>",
        pass: "Pa$DrX242odnvo#kOv?./CtR"
    },
    {
        user: "<EMAIL>",
        pass: "W4rKt!NG$23NmD!7"
    },
    {
        user: "<EMAIL>",
        pass: "&)HBGqNWj6R8k%H4#@47"
    },
    {
        user: "<EMAIL>",
        pass: "PllRes20$drx"
    },
    {
        user: "<EMAIL>",
        pass: "R$pR3cvrwt23$DrX"
    },
    {
        user: "<EMAIL>",
        pass: "V!d3o$23NmD!7"
    },
    {
        user: "<EMAIL>",
        pass: "Ph07o$23NmD!6"
    },
    {
        user: "<EMAIL>",
        pass: "C0m0$23NmD!4mJg"
    },
    {
        user: "<EMAIL>",
        pass: "C0m0$23NmD!4dG"
    },
    {
        user: "<EMAIL>",
        pass: "C0m0$23NmD!4nB"
    },
    {
        user: "<EMAIL>",
        pass: "C0m0$23NmD!41"
    },
    {
        user: "<EMAIL>",
        pass: "C0m0$23NmD!42"
    },
    {
        user: "<EMAIL>",
        pass: "C0m0$23NmD!43"
    },
    {
        user: "<EMAIL>",
        pass: "C0m0$23NmD!44"
    },
]

function sendMail(email, indexEmail, content){
    let message = {
        from: "Dirickx Guard <<EMAIL>>",
        to: email,
        subject: "Offre GPS à 35 000 Ar",
        template: "gps",
        attachments: [
            {
                filename: 'gps.jpg',
                path: './export/views/img/gps.jpg',
                cid: 'gps@dirickx'
            },
            {
                filename: 'site-web.png',
                path: './export/views/img/site-web.png',
                cid: 'site@dirickx'
            },
            {
                filename: 'facebook.png',
                path: './export/views/img/facebook.png',
                cid: 'facebook@dirickx'
            },
            {
                filename: 'dirickx-groupe.jpg',
                path: './export/views/img/dirickx-groupe.jpg',
                cid: 'dirickx-groupe@dirickx'
            },
            {
                filename: 'dirickx-guard.png',
                path: './export/views/img/dirickx-guard.png',
                cid: 'dirickx-guard@dirickx'
            },
            {
                filename: 'security-shop.jpg',
                path: './export/views/img/security-shop.jpg',
                cid: 'security-shop@dirickx' //same cid value as in the html img src
            },
        ],
    };

    const transporter = nodemailer.createTransport({
        host: "ssl0.ovh.net",
        port: 465,
        secure: true,
        auth: senders[indexEmail],
        tls: {
            rejectUnauthorized: false
        }
    })

    const handlebarOptions = {
        viewEngine: {
            partialsDir: path.resolve('./export/views/'),
            defaultLayout: false,
        },
        viewPath: path.resolve('./export/views/'),
    };

    transporter.use('compile', hbs(handlebarOptions))

    transporter.sendMail(message , (err, info) => {
        if(err)
            console.error(err)
        else
            console.log(moment().format("HH:mm:ss")+ " > " + "envoyé!")
        fs.writeFileSync(pathToSend, content);
        setTimeout(() => getLineToSend(indexEmail < senders.length - 1 ? indexEmail + 1 : 0), 5000)
    })
}
const pathToSend = 'task/contact/to_send.txt'
const pathSendDone = 'task/contact/sended.txt'

const getLineToSend = (indexEmail) => {
    console.log("------")
    if(moment().isAfter(moment().set({hour: 7, minute: 30})) && moment().isBefore(moment().set({hour: 17, minute: 30}))){
        const dataToSend = fs.readFileSync(pathToSend, { encoding: 'utf8', flag: 'r' })
        if(dataToSend.toString().trim()){
            const firstLine = dataToSend.toString().split('\n')[0].trim().toLowerCase()
            let lines = dataToSend.toString().split('\n')
            lines.shift()
            const newContent = lines.join('\n')
            if(firstLine.match(/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/)){
                console.log(moment().format("HH:mm:ss")+ " > " + firstLine);
                const dataAlreadyRead = fs.readFileSync(pathSendDone, { encoding: 'utf8', flag: 'r' })
                if(!dataAlreadyRead.split('\n').includes(firstLine)){
                    fs.appendFileSync(pathSendDone, firstLine + "\n");
                }
                sendMail(firstLine, indexEmail, newContent)
            }
            else {
                fs.writeFileSync(pathToSend, newContent);
                setTimeout(() => getLineToSend(indexEmail), 100)
            }
        }
        else {
            console.log("task done!")
            process.exit()
        }
    }
    else {
        console.log("is not day")
        setTimeout(() => getLineToSend(indexEmail), 300000)
    }
}

getLineToSend(0)