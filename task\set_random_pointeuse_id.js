var exec = require('child_process').exec;

function getRandomArbitrary(min, max) {
    return Math.random() * (max - min) + min;
  }

const setIds = ["0015", "1258"]

const setIdPointeuse = (newId) => {
    console.log(newId)
    
    setTimeout(() => {
        exec('node biometrique/client_15.js setIdP0015' + newId,
            function (error, stdout, stderr) {
                console.log(stdout);
                if(stderr){
                    console.log('stderr: \n' + stderr);
                    if(/node:events:491/.test(stderr)){
                        exec('systemctl restart tls_biometrique_server_15',
                            function (error, stdout, stderr) {
                                console.log(stdout);
                                if(stderr)
                                    console.log('stderr: \n' + stderr);
                                if (error !== null) {
                                    console.log('exec error: ' + error);
                                }
                            });
                    }
                }
                if (error !== null) {
                    console.log('exec error: ' + error);
                }
                if(newId < 9900){
                    setIdPointeuse(newId+1)
                }
                else {
                    setIdPointeuse(1500)
                }
            });
        }, 5000)
}

setIdPointeuse(Number.parseInt(getRandomArbitrary(1500, 9900)))
