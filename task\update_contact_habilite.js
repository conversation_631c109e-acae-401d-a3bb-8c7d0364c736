const mysql = require('mysql')

const {db_config_ovh} = require("../auth")
const pool_ovh = mysql.createPool(db_config_ovh)

const sqlSelectContact = "SELECT idcontact, phone from contacts where phone regexp '-'"
const sqlUpdateContact = "UPDATE contacts set phone = ? where idcontact  = ?"

const updateData = (contacts, index) => {
    if(index < contacts.length){
        const contact = contacts[index]
        const newPhones = []
        contact.phone.split("-").forEach(c => {
            if(c) newPhones.push(c)
        });
        console.log(contact.phone + " -> " + newPhones.join(','))
        pool_ovh.query(sqlUpdateContact, [newPhones.join(","), contact.idcontact], async (err, result) => {
            if(err)
                console.error(err)
            else {
                setTimeout(() => {
                    updateData(contacts, index+1)
                }, 100)
            }
        })
    }
    else 
        console.log("update done!")
}

pool_ovh.query(sqlSelectContact, [], async (err, contacts) => {
    if(err)
        console.error(err)
    else {
        console.log("nb contact : " + contacts.length)
        updateData(contacts, 0)
    }
})