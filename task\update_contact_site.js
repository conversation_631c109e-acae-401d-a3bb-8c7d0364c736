const mysql = require('mysql')

const {db_config_ovh} = require("../auth")
const pool_ovh = mysql.createPool(db_config_ovh)

const sqlSelectSite = "SELECT idsite, phone_agent from sites where phone_agent regexp '-'"
const sqlUpdateSite = "UPDATE sites set phone_agent = ? where idsite  = ?"

const updateData = (sites, index) => {
    if(index < sites.length){
        const site = sites[index]
        const newPhones = []
        site.phone_agent.split("-").forEach(c => {
            if(c) newPhones.push(c)
        });
        console.log(site.phone_agent + " -> " + newPhones.join(','))
        pool_ovh.query(sqlUpdateSite, [newPhones.join(','), site.idsite], async (err, result) => {
            if(err)
                console.error(err)
            else {
                setTimeout(() => {
                    updateData(sites, index+1)
                }, 100)
            }
        })
    }
    else 
        console.log("update done!")
}

pool_ovh.query(sqlSelectSite, [], async (err, sites) => {
    if(err)
        console.error(err)
    else {
        updateData(sites, 0)
    }
})