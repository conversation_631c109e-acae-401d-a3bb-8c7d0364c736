
const Excel = require("exceljs")
const mysql = require('mysql')

const db_config = require("../auth").db_config_zo
const pool = mysql.createPool(db_config)

var workbook = new Excel.Workbook()
workbook.xlsx.readFile("avril.xlsx")
.then(function() {
    console.log("begin...\n------------------------------------------------------------")
        var worksheet = workbook.getWorksheet("Feuil1");
        const rows = [];
        worksheet.eachRow(function(row) {
            if(row.values[1]) rows.push(row)
        });
        updateRow(rows, 0)
})

function updateRow(rows, index){
    if(index < rows.length){
        const row = rows[index]
        const idsite = row.values[1]
        const nom = row.values[2]
        const total_hour = row.values[6]
        const sqlSelectDateExport = "SELECT s.idsite, s.nom FROM sites s WHERE s.idsite = " + idsite + ' and s.nom like "' + nom + '"'
        pool.query(sqlSelectDateExport, [], (err, result) => {
            if(err)
                console.error(err)
            else if(result.length == 1){
                //console.log("update site :" + idsite)
                const sqlUpdateSite = "UPDATE sites s SET s.total_hour = " + total_hour + " WHERE s.idsite = " + idsite
                pool.query(sqlUpdateSite, [], (e, r) =>{
                    if(e)
                        console.error(e)
                    else
                        setTimeout(() => updateRow(rows, index+1), 200)
                })
            }
            else {
                console.log(row.values)
                setTimeout(() => updateRow(rows, index+1), 200)
                
            }
        })
    }
    else {
        console.log("process done!!!")
        process.exit(1)
    }
}