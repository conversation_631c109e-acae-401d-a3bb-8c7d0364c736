const mysql = require('mysql2')
const moment = require('moment')

const {db_config_zo} = require("../auth")
const pool_ovh = mysql.createPool(db_config_zo)

let currentPointage = ''
if(moment().isAfter(moment().set({hour: 5, minute: 50, second: 0})) && moment().isBefore(moment().set({hour: 17, minute: 50, second: 0})))
    currentPointage = moment().format("YYYY-MM-DD") + " 07:00:00"
else {
    if(moment().isBefore(moment().set({hour: 5, minute: 50, second: 0})))
        currentPointage = moment().subtract(1, "day").format("YYYY-MM-DD") + " 18:00:00"
    else
        currentPointage = moment().format("YYYY-MM-DD") + " 18:00:00"
}
console.log(currentPointage)

const sqlSelectCurrentPointage = "SELECT p.agent_id as 'id', a.last_date_pointage FROM pointages p " +
    "LEFT JOIN agents a ON a.id = p.agent_id " +
    "WHERE a.last_date_pointage != p.date_pointage and date_pointage = '" + currentPointage + "'"

const sqlSelectPointage = "SELECT max(date_pointage) as 'max_date_pointage' FROM pointages " + 
    "where (soft_delete is null or soft_delete = 0) and agent_id = ?"
const sqlUpdateAgent = "UPDATE agents set last_date_pointage = ? where id = ?"

const updateData = (agents, index) => {
    if(index < agents.length){
        const currentAgent = agents[index]
        pool_ovh.query(sqlSelectPointage, [currentAgent.id], async (err, pointages) => {
            if(err)
                console.error(err)
            else if(pointages.length > 0 && pointages[0].max_date_pointage != null 
            && moment(currentAgent.last_date_pointage).format("YYYY-MM-DD HH:mm:ss") != moment(pointages[0].max_date_pointage).format("YYYY-MM-DD HH:mm:ss")){
                const ptg = pointages[0]
                console.log(currentAgent.id + " | ptg: " + moment(ptg.max_date_pointage).format("YYYY-MM-DD HH:mm:ss") + " , agent: " + currentPointage)
                pool_ovh.query(sqlUpdateAgent, [ptg.max_date_pointage, currentAgent.id], async (err, result) => {
                    if(err)
                        console.error(err)
                    else {
                        setTimeout(() => {
                            updateData(agents, index+1)
                        }, 100)
                    }
                })
            }
            else {
                setTimeout(() => {
                    updateData(agents, index+1)
                }, 100)
            }
        })
    }
    else {
        console.log("update done!")
        process.exit(1)
    }
}

pool_ovh.query(sqlSelectCurrentPointage, [], async (err, agents) => {
    if(err)
        console.error(err)
    else {
        console.log("Nb agent: " + agents.length)
        updateData(agents, 0)
    }
})