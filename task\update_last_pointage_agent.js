const mysql = require('mysql2')
const moment = require('moment')

const {db_config_zo} = require("../auth")
const pool_ovh = mysql.createPool(db_config_zo)

const sqlSelectAgent = "SELECT id, last_date_pointage FROM agents WHERE last_date_pointage > ? ORDER BY last_date_pointage DESC"
const sqlSelectPointage = "SELECT max(date_pointage) as 'max_date_pointage' FROM pointages " + 
"where (soft_delete is null or soft_delete = 0) and agent_id = ?"
const sqlUpdateAgent = "UPDATE agents set last_date_pointage = ? where id = ?"

const updateData = (agents, index) => {
    if(index < agents.length){
        const currentAgent=agents[index]
        pool_ovh.query(sqlSelectPointage, [currentAgent.id], async (err, pointages) => {
            if(err)
                console.error(err)
            else if(pointages.length > 0 && pointages[0].max_date_pointage != null 
            && moment(currentAgent.last_date_pointage).format("YYYY-MM-DD HH:mm:ss") != moment(pointages[0].max_date_pointage).format("YYYY-MM-DD HH:mm:ss")){
                const ptg = pointages[0]
                pool_ovh.query(sqlUpdateAgent, [ptg.max_date_pointage, currentAgent.id], async (err, result) => {
                    if(err)
                        console.error(err)
                    else {
                        console.log(currentAgent.id + " | ptg: " + moment(ptg.max_date_pointage).format("YYYY-MM-DD HH:mm:ss") + " , agent: " + moment(currentAgent.last_date_pointage).format("YYYY-MM-DD HH:mm:ss"))
                        setTimeout(() => {
                            updateData(agents, index+1)
                        }, 100)
                    }
                })
            }
            else {
                setTimeout(() => {
                    updateData(agents, index+1)
                }, 100)
            }
        })
    }
    else 
        console.log("update done!")
}

pool_ovh.query(sqlSelectAgent, [moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')], async (err, agents) => {
    if(err)
        console.error(err)
    else {
        console.log("Nb agent: " + agents.length)
        updateData(agents, 0)
    }
})