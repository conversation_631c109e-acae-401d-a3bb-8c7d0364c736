const moment = require('moment')
const mysql = require('mysql')

moment.locale('fr')

const {db_config_admin} = require("../auth")
const pool_admin = mysql.createPool(db_config_admin)

const sqlSelectSalForfait = "SELECT e.id, coalesce(f.sal_forfait, 0) as 'sal_forfait' FROM employes e "
    + "LEFT JOIN fonctions f ON f.id = e.fonction_id "
    + "WHERE coalesce(e.sal_forfait, 0) != coalesce(f.sal_forfait, 0) "
    + "LIMIT 1 "
const sqlUpdateEmploye = "UPDATE employes set sal_forfait = ? where id = ?"

const updateSalForfaitEmploye = () => {
    pool_admin.query(sqlSelectSalForfait, [], async (err, employes) => {
        if(err)
            console.error(err)
        else {
            if(employes.length > 0){
                const employe = employes[0]
                console.log(employe)
                pool_admin.query(sqlUpdateEmploye, [employe.sal_forfait, employe.id], async (err, agents) => {
                    if(err)
                        console.error(err)
                    else {
                        setTimeout(() => {updateSalForfaitEmploye()}, 200)
                    }
                })
            }
            else {
                console.log("process done!!")
                process.exit()
            }
        }
    })
}

updateSalForfaitEmploye()