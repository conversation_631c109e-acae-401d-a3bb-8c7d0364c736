const moment = require('moment')
const mysql = require('mysql')

moment.locale('fr')

const db_config = require("../auth").db_config_ovh
const pool = mysql.createPool(db_config)

const sqlSelectZone = "SELECT idzone, NumZone, idsite FROM zonesites where updated is null limit 1"
const sqlDeleteZone = "DELETE FROM zonesites WHERE NumZone = ? and idsite = ? and idzone != ?"
const sqlUpdateZone = "UPDATE zonesites set updated = true WHERE idzone = ?"


function updateData(){
    pool.query(sqlSelectZone, [], async (err, zones) => {
        if(err)
            console.error(err)
        else if(zones.length > 0){
            const zone = zones[0]
            console.log(zone)
            pool.query(sqlDeleteZone, [zone.NumZone, zone.idsite, zone.idzone], async (err, data) => {
                if(err)
                    console.error(err)
                else {
                    if(data.affectedRows > 0)
                        console.log("doublon : " + data.affectedRows)
                    pool.query(sqlUpdateZone, [zone.idzone], async (err, data) => {
                        if(err)
                            console.error(err)
                        else {
                            setTimeout(() => {
                                updateData()
                            }, 100);
                        }
                    })

                }
            })
        }
        else {
            console.log("update zones done!")
            process.exit(1)
        }
    })
}

updateData()