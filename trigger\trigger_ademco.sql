drop trigger IF EXISTS before_insert_log;
drop trigger IF EXISTS before_insert_ademco;

DELIMITER |
CREATE TRIGGER before_insert_log BEFORE INSERT
ON ademcomemlog 
FOR EACH ROW BEGIN 
	if(NEW.codeevent is not null) then
		begin
			if(NEW.codeevent = 602 and NEW.transmitter in ('0321130211', '0321131392', '0321131393', '0321131394', '0321131398', '0321134410', '0321134411', '0321134415', '0321134433', '0321134434', '0321134436', '0321134439', '0321134497', '0321134538', '0321134540', '0321134437')) then
				begin
					set NEW.codeevent = '601';
					update sites set last_sms_check = NEW.dtarrived where numeropuces = NEW.prom;
				end;
			end if;
			if(NEW.received_at_ovh is not null) then
				begin
					update params set `value` = now() where  `key`='last_transmission_local';
				end;
			end if;
			if(NEW.transmitter in ('0321130738', '0321130739', '0321130741', '0321130742', '0321130743', '0321130744', '0321130745'
				, '0321134413', '0321154685', '0321154686', '0335011323', '0340554622')
			) then
				begin
					update sim_gateways set last_sms_reception = now() where numero = NEW.transmitter;
					if(	NEW.prom in ('0321130211', '0321131392' ,'0321131393' ,'0321131394' ,'0321131398' ,'0321134410' ,'0321134411' ,'0321134415' ,'0321134433' 
						,'0321134434' ,'0321134436' ,'0321134439' ,'0321134497' ,'0321134538' ,'0321134540' ,'0321134437')
					) then
						begin
							set @trans = NEW.prom;
							set NEW.prom = NEW.transmitter;
							set NEW.transmitter = @trans;
						end;
					end if;
				end;
			elseif(NEW.transmitter in ('57.128.20.26', '41.188.49.14')) then
				begin
					update sim_gateways set last_sms_reception = now() where numero = CONCAT(NEW.transmitter, ':', NEW.port);
				end;
			end if;
			if(NEW.pointeuse_id is not null) then
				begin
					if(NEW.codeevent = 120) then
						begin
							set NEW.zones = COALESCE(NEW.loyalty_level, 0);
						end;
					else 
						begin
							set NEW.zones = 0;
						end;
					end if;

					if(NEW.pointeuse_user_id is not null) then
						begin
							set NEW.agent_id = (select agent_id from agent_pointeuses where 
								pointeuse_id = NEW.pointeuse_id and  
								empreinte_id = NEW.pointeuse_user_id
								limit 1);
						end;
					end if;
					if(now() > CONCAT(DATE_FORMAT(now(), '%Y-%m-%d'), ' 05:40:00') and now() < CONCAT(DATE_FORMAT(now(), '%Y-%m-%d'), ' 17:40:00')) then
						begin
							set @limit_date = CONCAT(DATE_FORMAT(now(), '%Y-%m-%d'), ' 05:40:00');
						end;
					elseif(now() > CONCAT(DATE_FORMAT(now(), '%Y-%m-%d'), ' 17:40:00')) then
						begin
							set @limit_date = CONCAT(DATE_FORMAT(now(), '%Y-%m-%d'), ' 17:40:00');
						end;
					else
						begin
							set @limit_date = DATE_SUB(CONCAT(DATE_FORMAT(now(), '%Y-%m-%d'), ' 17:40:00'), INTERVAL 1 DAY);
						end;
					end if;
					set NEW.site_id = (select site_id from pointeuses where id = NEW.pointeuse_id limit 1);
					if(NEW.agent_id is not null and NEW.site_id is not null and now() > @limit_date) then
						begin
							if(	NEW.dtarrived > CONCAT(DATE_FORMAT(NEW.dtarrived, '%Y-%m-%d'), ' 05:40:00')
								and NEW.dtarrived < CONCAT(DATE_FORMAT(NEW.dtarrived, '%Y-%m-%d'), ' 06:20:00')) then
								begin
									set @date_pointage = CONCAT(DATE_FORMAT(NEW.dtarrived, '%Y-%m-%d'), ' 07:00:00');
									set @soft_delete_pointeuse = (select id from pointeuses where id = NEW.pointeuse_id and soft_delete = 1 limit 1);
									set @soft_delete_agent = (select id from agents where id = NEW.agent_id and soft_delete = 1 limit 1);
									set @sal_forfait = (select COALESCE(a.sal_forfait, 0) FROM agents a
										WHERE a.id = NEW.agent_id 
										limit 1);
									set @mis_a_pied = (select a.id FROM absences a
										WHERE a.type_absence = 'mis_a_pied'
										and a.employe_id = NEW.agent_id and a.status = 'done'
										and a.depart <= DATE_SUB(@date_pointage, INTERVAL 1 HOUR) and a.retour > DATE_SUB(@date_pointage, INTERVAL 1 HOUR)
										limit 1);
									set @service_24 = (select s.id FROM service24s s
										WHERE s.status in ('validation', 'done') and  s.employe_id = NEW.agent_id 
										and s.date_pointage = DATE_SUB(@date_pointage, INTERVAL 1 HOUR)
										limit 1);
									set @last_pointage = (select a.last_date_pointage FROM agents a WHERE a.id = NEW.agent_id);

									if(
										@soft_delete_pointeuse is null 
										and @soft_delete_agent is null
										and @sal_forfait = 0 
										and @mis_a_pied is null
										and (
											@service_24 is not null
											or @last_pointage is null
											or TIMESTAMPDIFF(HOUR,  @last_pointage, now()) > 20
										)
									) then
										begin
											set @pointage_id = (select id FROM pointages
												WHERE agent_id = NEW.agent_id 
												and date_pointage = @date_pointage
												limit 1);
											if(@pointage_id is null) then
												begin
													INSERT INTO pointages(agent_id, site_id, date_pointage, pointeuse_id, dtarrived, status, motif, last_update) 
														VALUES(NEW.agent_id, NEW.site_id, @date_pointage, NEW.pointeuse_id, NEW.dtarrived, null, null, now());
												end;
											else
												begin
													UPDATE pointages set pointeuse_id=NEW.pointeuse_id, dtarrived=NEW.dtarrived, site_id = NEW.site_id, user_id=null, status=null, motif=null, last_update=now()
														WHERE agent_id = NEW.agent_id  and date_pointage = @date_pointage;
												end;
											end if;
										end;
									end if;
								end;
							elseif(	NEW.dtarrived > CONCAT(DATE_FORMAT(NEW.dtarrived, '%Y-%m-%d'), ' 17:40:00')
								and NEW.dtarrived < CONCAT(DATE_FORMAT(NEW.dtarrived, '%Y-%m-%d'), ' 18:20:00')) then
								begin
									set @date_pointage = CONCAT(DATE_FORMAT(NEW.dtarrived, '%Y-%m-%d'), ' 18:00:00');
									set @soft_delete_pointeuse = (select id from pointeuses where id = NEW.pointeuse_id and soft_delete = 1 limit 1);
									set @soft_delete_agent = (select id from agents where id = NEW.agent_id and soft_delete = 1 limit 1);
									set @sal_forfait = (select COALESCE(a.sal_forfait, 0) FROM agents a
										WHERE a.id = NEW.agent_id 
										limit 1);
									set @mis_a_pied = (select a.id FROM absences a
										WHERE a.type_absence = 'mis_a_pied'
										and a.employe_id = NEW.agent_id and a.status = 'done'
										and a.depart <= @date_pointage and a.retour > @date_pointage
										limit 1);
									set @service_24 = (select s.id FROM service24s s
										WHERE s.status in ('validation', 'done') and  s.employe_id = NEW.agent_id 
										and s.date_pointage = @date_pointage
										limit 1);
									set @last_pointage = (select a.last_date_pointage FROM agents a WHERE a.id = NEW.agent_id);

									if(
										@soft_delete_pointeuse is null 
										and @soft_delete_agent is null
										and @sal_forfait = 0 
										and @mis_a_pied is null
										and (
											@service_24 is not null
											or @last_pointage is null
											or TIMESTAMPDIFF(HOUR,  @last_pointage, now()) > 20
										)
									) then
										begin
											set @pointage_id = (select id FROM pointages
												WHERE agent_id = NEW.agent_id 
												and date_pointage = @date_pointage
												limit 1);
											if(@pointage_id is null) then
												begin
													INSERT INTO pointages(agent_id, site_id, date_pointage, pointeuse_id, dtarrived, status, motif, last_update) 
														VALUES(NEW.agent_id, NEW.site_id, @date_pointage, NEW.pointeuse_id, NEW.dtarrived, null, null, now());
												end;
											else
												begin
													UPDATE pointages set pointeuse_id=NEW.pointeuse_id, dtarrived=NEW.dtarrived, site_id = NEW.site_id, status=null, motif=null, last_update=now()
														WHERE agent_id = NEW.agent_id and date_pointage = @date_pointage;
												end;
											end if;
										end;
									end if;
								end;
							end if;
						end;
					end if;
					if(NEW.site_id is null) then
						begin
							set NEW.site_id = (select idsite from sites where prom = CAST(NEW.pointeuse_id AS CHAR) limit 1);
						end;
					end if;
				end;
			end if;
			if(NEW.site_id is null) then
				begin
					set NEW.site_id = (select site_id from pointeuses where sim = NEW.prom limit 1);
				end;
			end if;
			if(NEW.site_id is null and NEW.prom is not null) then
				begin
					set NEW.site_id = (select idSite from sites where prom = NEW.prom limit 1);
				end;
			end if;
			if(NEW.site_id is null and NEW.prom is not null) then
				begin
					set NEW.site_id = (select idSite from sites where numeropuces = NEW.prom limit 1);
				end;
			end if;
			if(NEW.site_id is null and NEW.checkphone is not null) then
				begin
					set NEW.site_id = (select idSite from sites where checkphone = NEW.checkphone limit 1);
				end;
			end if;
			if NEW.site_id is not null then
				begin
					set NEW.received_at = now();
					set @diagnostique_id = (select diagnostique_id from sites where idsite = NEW.site_id limit 1);
					set @codeTevent = (select CAST(emit_code AS CHAR) from signales s where 
						(s.zone = 0 or s.zone is null or s.zone = cast(NEW.zones AS signed)) and 
						received_code = NEW.codeevent and
						site_id=NEW.site_id limit 1);

					if @codeTevent is not null then
						begin
							SET NEW.codeTevent = @codeTevent;
						end;
					else 
						begin
							if(NEW.site_id = 769) then
								begin
									if(NEW.codeevent = 132 and NEW.zones = 0) then
										begin
											set NEW.codeTevent = '401';
											set NEW.eventQualify = 1;
										end;
									elseif(NEW.codeevent = 401) then
										begin
											set NEW.codeTevent = '401';
											set NEW.eventQualify = 3;
										end;
									else 
										begin 
											set NEW.codeTevent = NEW.codeevent;
										end;
									end if;
								end;
							else
								begin
									SET NEW.codeTevent = NEW.codeevent;
								end;
							end if;
						end;
					end if;

					if(NEW.pointeuse_id is null) then
						begin
							if(NEW.transmitter REGEXP '^0[2-3][0-9]{8}$') then
								update sites 
								set last_transmission_sms = NEW.dtarrived, transmitter_sms = NEW.transmitter, last_event_sms = NEW.codeTevent
								WHERE idsite = NEW.site_id;
							elseif(NEW.transmitter is not null) then
								update sites 
								set last_transmission_gprs = NEW.dtarrived, transmitter_gprs = CONCAT(NEW.transmitter, ':', NEW.port), last_event_gprs = NEW.codeTevent
								WHERE idsite = NEW.site_id;
							end if;
						end;
					else 
						begin
							if(NEW.transmitter REGEXP '^0[2-3][0-9]{8}$') then
								update pointeuses 
								set last_transmission_sms = NEW.dtarrived, transmitter_sms = NEW.transmitter, last_event_sms = NEW.codeTevent
								WHERE id = NEW.pointeuse_id;
							elseif(NEW.transmitter is not null) then
								update pointeuses 
								set last_transmission_gprs = NEW.dtarrived, transmitter_gprs = CONCAT(NEW.transmitter, ':', NEW.port), last_event_gprs = NEW.codeTevent
								WHERE id = NEW.pointeuse_id;
							end if;
						end;
					end if;

					set @date_report_diag = (select date_report_diag from sites where idsite = NEW.site_id limit 1);
					if(@diagnostique_id = 2 and NEW.codeTevent != 1000) then
						begin
							set @new_diag_id = 2;
							set @new_date = @date_report_diag;
						end;
					else
						begin
							set @new_diag_id = 1;
							if(@diagnostique_id = 1 and @date_report_diag > now()) then
								begin
									set @new_date = @date_report_diag;
								end;
							else
								begin
									set @new_date = now();
								end;
							end if;
						end;
					end if;
					set @transmitter = null;
					set @transmitter_2 = null;
					set @last_transmission_2 = null;
					if(NEW.codeevent != 603) then
						begin
							if(NEW.pointeuse_id is not null) then
								begin
									if(NEW.prom is not null) then
										begin
											UPDATE pointeuses set sim = NEW.prom WHERE id = NEW.pointeuse_id;
										end;
									end if;
									if(NEW.transmitter in ('57.128.20.26', '41.188.49.14')) then
										begin
											set @transmitter = NEW.transmitter;
										end;
									else 
										begin
											set @transmitter_2 = NEW.transmitter;
										end;
									end if;
									UPDATE pointeuses set 
										transmitter = @transmitter,
										transmitter_2 = @transmitter_2,
										port = NEW.port,
										date_last_signal = NEW.dtarrived,
										last_signal = NEW.codeTevent,
										last_connection = now(),
										eventQualify = NEW.eventQualify,
										diagnostique_id = @new_diag_id,
										joignable = 1,
										date_report_diag = @new_date
									WHERE id = NEW.pointeuse_id;
								end;
							elseif(NEW.prom is not null) then
								begin
									set @gprs = (select gprs from sites where idsite = NEW.site_id limit 1);
									set @sms = (select sms from sites where idsite = NEW.site_id limit 1);
									
									if(@gprs = 1) then
										begin
											if(NEW.transmitter in ('57.128.20.26', '41.188.49.14')) then
												begin
													set @transmitter = NEW.transmitter;
												end;
											else 
												begin
													set @transmitter_2 = NEW.transmitter;
												end;
											end if;
										end;
									elseif(@sms = 1) then
										begin
											if(NEW.transmitter = "0321154686") then
												begin
													set @transmitter_2 = NEW.transmitter;
													set @last_transmission_2 = NEW.dtarrived;
												end;
											else
												begin
													set @transmitter = NEW.transmitter;
												end;
											end if;
										end;
									end if;

									if(@transmitter is null) then
										begin					
											set @transmitter = (select transmitter from sites where idsite = NEW.site_id limit 1);
										end;
									end if;
									if(@transmitter_2 is null) then
										begin
											set @transmitter_2 = (select transmitter_2 from sites where idsite = NEW.site_id limit 1);
										end;
									end if;
									if(@last_transmission_2 is null) then
										begin
											set @last_transmission_2 = (select last_transmission_2 from sites where idsite = NEW.site_id limit 1);
										end;
									end if;

									UPDATE sites set 
										transmitter = @transmitter,
										transmitter_2 = @transmitter_2,
										last_transmission_2 = @last_transmission_2,
										port = NEW.port,
										date_last_signal = NEW.dtarrived,
										last_signal = NEW.codeTevent,
										eventQualify = NEW.eventQualify,
										diagnostique_id = @new_diag_id,
										joignable = 1,
										date_report_diag = @new_date
									WHERE idsite = NEW.site_id;
								end;
							end if;
						end;
					end if;
					if(NEW.codeTevent in (400,401,402,403,404,405,406,407,408,409,441,442,454,456)) then
						begin
							if(NEW.eventQualify = '3') then
								begin
									UPDATE sites set 
										arm = 1
									WHERE idsite = NEW.site_id;
								end;
							elseif(NEW.eventQualify = '1') then
								begin
									UPDATE sites set 
										arm = 0
									WHERE idsite = NEW.site_id;
								end;
							end if;
						end;
					end if;
					
					if (NEW.codeTevent in ('602', '601', '600', '604', '1000')) then
						begin
							if(NEW.istraite = 1) then
								begin
									set NEW.istraite = 2;
								end;
							end if;
							insert into ademcolog(
								prom, messageType, eventQualify, codeevent, `partition`, zones, istraite, dtarrived, dttraite, 
								transmitter, port, codeTevent, IdUser, lastupdate, Lat, Lon, gps, received_at,
								site_id, pointeuse_id, pointeuse_user_id, agent_id, loyalty_level, received_at_ovh, sip, checkphone, is_date_server) 
							values (
								NEW.prom, NEW.messageType, NEW.eventQualify, NEW.codeevent, NEW.`partition`, NEW.zones, 2, 
								NEW.dtarrived, now(), NEW.transmitter, NEW.port, NEW.codeTevent, 1, NEW.lastupdate, NEW.Lat, NEW.Lon, NEW.gps, now(),
								NEW.site_id, NEW.pointeuse_id, NEW.pointeuse_user_id, NEW.agent_id, NEW.loyalty_level, NEW.received_at_ovh, 
								NEW.sip, NEW.checkphone, is_date_server);
						end;
					end if;
					if (NEW.codeTevent = '1000') then
						begin
							if(NEW.site_id is not null and NEW.pointeuse_id is null and NEW.checkphone is null) then
								begin
									update sites 
									set last_vigilance = NEW.dtarrived, do_checkphone = null
									where idsite = NEW.site_id;
								end;
							elseif(NEW.pointeuse_id is not null) then
								begin
									update pointeuses 
									set last_vigilance = NEW.dtarrived
									where id = NEW.pointeuse_id;
									if(NEW.agent_id is not null) then
										begin
											update agent_pointeuses 
											set last_vigilance = NEW.dtarrived
											where pointeuse_id = NEW.pointeuse_id
											and empreinte_id = NEW.pointeuse_user_id
											and agent_id = NEW.agent_id;
										end;
									end if;
									if(NEW.site_id is not null) then
										begin
											update sites 
											set do_checkphone = null
											where idsite = NEW.site_id;
										end;
									end if;
								end;
							end if;
						end;
					end if;
					if (NEW.site_id is not null and NEW.pointeuse_id is null and NEW.codeTevent in (100,101,120,151,110,111,130,131,132,133,134,137,140)) then
						begin
							update sites 
							set last_panic = NEW.dtarrived
							where idsite = NEW.site_id;
						end;
					end if;
				end;
			end if;
			if NEW.codeTevent is null then
				begin
					set NEW.codeTevent = NEW.codeevent;
				end;
			end if;
		end;
	else 
		begin
			set NEW.istraite = 2;
		end;
	end if;
END 
| DELIMITER ;

DELIMITER |
CREATE TRIGGER before_insert_ademco BEFORE INSERT
ON ademcolog 
FOR EACH ROW BEGIN 
	set @nb_log = 0;
	if NEW.port in (2511, 2512) then
		begin
			set @nb_log = (select count(idademco) from ademcotemp 
				where prom = NEW.prom and dtarrived = NEW.dtarrived and codeevent=NEW.codeevent and zones = NEW.zones and eventQualify = NEW.eventQualify);
		end;
	end if;
	if NEW.codeevent is not null and NEW.site_id is not null and @nb_log = 0 then
		begin
			INSERT INTO ademcotemp (`prom`, `messageType`, `eventQualify`, `codeevent`, `partition`, `zones`, `istraite`, 
				`dtarrived`, `dttraite`, `transmitter`, `codeTevent`, `IdUser`, `Lat`, `Lon`, `gps`, received_at, port,
				site_id, pointeuse_id, pointeuse_user_id, agent_id, rapport_id, loyalty_level, received_at_ovh, 
				sip, checkphone, is_date_server, panel_user, panel_area)
			VALUES (new.prom, new.messageType, new.eventQualify, new.codeevent, new.`partition`, new.zones, new.istraite, 
				new.dtarrived, new.dttraite, new.transmitter, new.codeTevent, new.IdUser, '0', '0', '0', NEW.received_at, NEW.port,
				NEW.site_id, NEW.pointeuse_id, NEW.pointeuse_user_id, NEW.agent_id, NEW.rapport_id, NEW.loyalty_level, NEW.received_at_ovh, 
				NEW.sip, NEW.checkphone, is_date_server, panel_user, panel_area);
        end;
	end if;
END 
| DELIMITER ;