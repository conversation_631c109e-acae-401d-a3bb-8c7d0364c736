drop trigger IF EXISTS after_insert_pointage;
drop trigger IF EXISTS after_delete_pointage;
drop trigger IF EXISTS after_update_pointage;

DELIMITER |
CREATE TRIGGER after_insert_pointage AFTER INSERT
ON pointages 
FOR EACH ROW BEGIN 
    DECLARE last_date_pointage DATETIME;
    SELECT last_date_pointage INTO last_date_pointage from agents WHERE id = NEW.agent_id;
    if(coalesce(NEW.soft_delete, 0) != 1 and (@last_date_pointage is null or NEW.date_pointage > @last_date_pointage)) then
        UPDATE agents set last_date_pointage = NEW.date_pointage where id = NEW.agent_id;
    end if; 
END 
| DELIMITER ;

DELIMITER |
CREATE TRIGGER after_delete_pointage AFTER DELETE
ON pointages 
FOR EACH ROW BEGIN 
    DECLARE max_date_pointage DATETIME;
    SELECT max(date_pointage) into max_date_pointage from pointages where agent_id = OLD.agent_id and (soft_delete is null or soft_delete = 0);
    UPDATE agents set last_date_pointage = max_date_pointage where id = OLD.agent_id;
END 
| DELIMITER ;

DELIMITER |
CREATE TRIGGER after_update_pointage
AFTER UPDATE
ON pointages FOR EACH ROW
BEGIN
    if(coalesce(NEW.soft_delete, 0) != coalesce(OLD.soft_delete, 0) or NEW.date_pointage != OLD.date_pointage or NEW.agent_id != OLD.agent_id) then
        begin
            DECLARE new_date_pointage DATETIME;
            SELECT max(date_pointage) into new_date_pointage from pointages where agent_id = NEW.agent_id and (soft_delete is null or soft_delete = 0); 
            UPDATE agents set last_date_pointage = new_date_pointage where id = NEW.agent_id;
            if(NEW.agent_id != OLD.agent_id) then
                begin
                    DECLARE old_date_pointage DATETIME;
                    SELECT max(date_pointage) into old_date_pointage from pointages where agent_id = OLD.agent_id and (soft_delete is null or soft_delete = 0); 
                    UPDATE agents set last_date_pointage = old_date_pointage where id = OLD.agent_id;
                end;
            end if;
        end;
    end if;
END
| DELIMITER ;