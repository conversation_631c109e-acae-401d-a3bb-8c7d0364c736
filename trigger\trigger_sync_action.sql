drop trigger IF EXISTS before_update_action;

DELIMITER |
CREATE TRIGGER before_update_action
BEFORE UPDATE
ON actions FOR EACH ROW
BEGIN
    if(NEW.type_action_id != OLD.type_action_id or NEW.rapport_id != OLD.rapport_id or NEW.updated_at != OLD.updated_at
		or NEW.created_at != OLD.created_at or COALESCE(NEW.soft_delete, 0) != COALESCE(OLD.soft_delete, 0)
	) then
		begin
			set NEW.admin_updated_at = now();
        end;
	end if;
END
| DELIMITER ;