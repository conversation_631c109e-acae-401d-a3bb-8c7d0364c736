drop trigger IF EXISTS before_update_contact;

DELIMITER |
CREATE TRIGGER before_update_contact
BEFORE UPDATE
ON contacts FOR EACH ROW
BEGIN
    if(NEW.nom != OLD.nom or NEW.prenom != OLD.prenom or NEW.adresse != OLD.adresse or NEW.lastupdate != OLD.lastupdate
      or coalesce(NEW.soft_delete, 0) != coalesce(OLD.soft_delete, 0)
    ) then
		begin
			set NEW.admin_updated_at = now();
        end;
	end if;
END
| DELIMITER ;
