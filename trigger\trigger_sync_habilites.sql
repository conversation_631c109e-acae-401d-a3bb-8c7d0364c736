DROP TRIGGER IF EXISTS before_update_habilite;

DE<PERSON><PERSON>ITER |

CREATE TRIGGER before_update_habilite
BEFORE UPDATE
ON habilites FOR EACH ROW
BEGIN
    IF (
        NEW.idcontact != OLD.idcontact OR
        NEW.idsite != OLD.idsite OR
        NEW.idordre != OLD.idordre OR
        NEW.Timedisponible != OLD.Timedisponible OR
        NEW.starttime != OLD.starttime OR
        NEW.stoptime != OLD.stoptime OR
        NEW.password != OLD.password OR
        NEW.quality != OLD.quality OR
        NEW.DayDisponible != OLD.DayDisponible OR
        NEW.startdate != OLD.startdate OR
        NEW.stopdate != OLD.stopdate OR
        NEW.code != OLD.code OR
        NEW.idPartition != OLD.idPartition OR
        NEW.lastupdate != OLD.lastupdate OR
        coalesce(NEW.soft_delete, 0) != coalesce(OLD.soft_delete, 0)
    ) THEN
        BEGIN
            SET NEW.admin_updated_at = NOW();
        END;
    END IF;
END |

DELIMITER ;
