drop trigger IF EXISTS before_update_horaire;

DELIMITER |
CREATE TRIGGER before_update_horaire
BEFORE UPDATE
ON horaires FOR EACH ROW
BEGIN
    if(COALESCE(NEW.nom, 0) != COALESCE(OLD.nom, 0)
        or COALESCE(NEW.day_1, 0) != COALESCE(OLD.day_1, 0) or COALESCE(NEW.night_1, 0) != COALESCE(OLD.night_1, 0) 
        or COALESCE(NEW.day_2, 0) != COALESCE(OLD.day_2, 0) or COALESCE(NEW.night_2, 0) != COALESCE(OLD.night_2, 0) 
        or COALESCE(NEW.day_3, 0) != COALESCE(OLD.day_3, 0) or COALESCE(NEW.night_3, 0) != COALESCE(OLD.night_3, 0)
        or COALESCE(NEW.day_4, 0) != COALESCE(OLD.day_4, 0) or COALESCE(NEW.night_4, 0) != COALESCE(OLD.night_4, 0)
        or COALESCE(NEW.day_5, 0) != COALESCE(OLD.day_5, 0) or COALESCE(NEW.night_5, 0) != COALESCE(OLD.night_5, 0)
        or COALESCE(NEW.day_6, 0) != COALESCE(OLD.day_6, 0) or COALESCE(NEW.night_6, 0) != COALESCE(OLD.night_6, 0)
        or COALESCE(NEW.day_0, 0) != COALESCE(OLD.day_0, 0) or COALESCE(NEW.night_0, 0) != COALESCE(OLD.night_0, 0)
        or COALESCE(NEW.day_ferie, 0) != COALESCE(OLD.day_ferie, 0) or COALESCE(NEW.night_ferie, 0) != COALESCE(OLD.night_ferie, 0)
    ) then
		begin
			set NEW.admin_updated_at = now();
        end;
	end if;
END
| DELIMITER ;