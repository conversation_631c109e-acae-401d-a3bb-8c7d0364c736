drop trigger IF EXISTS before_update_numero;

DELIMITER |
CREATE TRIGGER before_update_numero
BEFORE UPDATE
ON numeros FOR EACH ROW
BEGIN
    if(NEW.numero != OLD.numero or NEW.id_site != OLD.id_site or NEW.id_contact != OLD.id_contact or NEW.lastupdate != OLD.lastupdate
      or coalesce(NEW.soft_delete, 0) != coalesce(OLD.soft_delete, 0)
    ) then
		begin
			set NEW.admin_updated_at = now();
        end;
	end if;
END
| DELIMITER ;
