drop trigger IF EXISTS before_update_pointage;

DELIMITER |
CREATE TRIGGER before_update_pointage
BEFORE UPDATE
ON pointages FOR EACH ROW
BEGIN
    if(NEW.site_id != OLD.site_id or NEW.vigilance != OLD.vigilance or NEW.pointeuse_id != OLD.pointeuse_id or NEW.motif != OLD.motif 
      or NEW.dtarrived != OLD.dtarrived or NEW.type_pointage_id != OLD.type_pointage_id or NEW.user_id != OLD.user_id 
      or coalesce(NEW.soft_delete, 0) != coalesce(OLD.soft_delete, 0)
    ) then
		begin
			set NEW.admin_updated_at = now();
        end;
	end if;
END
| DELIMITER ;