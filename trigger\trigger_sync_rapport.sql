drop trigger IF EXISTS before_update_rapport;

DELIMITER |
CREATE TRIGGER before_update_rapport
BEFORE UPDATE
ON rapports FOR EACH ROW
BEGIN
    if(COALESCE(NEW.type_rapport_id) != COALESCE(OLD.type_rapport_id) or NEW.site_id != OLD.site_id or NEW.eventcode != OLD.eventcode 
        or COALESCE(NEW.zone, 0) != COALESCE(OLD.zone, 0) or NEW.commentaire != OLD.commentaire or NEW.dtarrived != OLD.dtarrived 
        or NEW.debut != OLD.debut or NEW.fin != OLD.fin or NEW.depart != OLD.depart or NEW.arrivee != OLD.arrivee  
        or NEW.technicien != OLD.technicien or NEW.tache != OLD.tache or COALESCE(NEW.exported, 0) != COALESCE(OLD.exported, 0)
        or NEW.idademco != OLD.idademco or NEW.intervention_id != OLD.intervention_id or NEW.user_id != OLD.user_id 
        or NEW.soft_delete != OLD.soft_delete or NEW.created_at != OLD.created_at or NEW.updated_at != OLD.updated_at
    ) then
		begin
			set NEW.admin_updated_at = now();
        end;
	end if;
END
| DELIMITER ;