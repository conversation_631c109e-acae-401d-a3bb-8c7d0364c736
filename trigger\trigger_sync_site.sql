drop trigger IF EXISTS before_update_site;

DELIMITER |
CREATE TRIGGER before_update_site
BEFORE UPDATE
ON sites FOR EACH ROW
BEGIN
    if(NEW.pointage is null) then 
        begin
            set NEW.pointage = 0;
        end;
    end if;
    if(NEW.pointeuse is null) then 
        begin
            set NEW.pointeuse = 0;
        end;
    end if;
    if(NEW.vigilance is null) then 
        begin
            set NEW.vigilance = 0;
        end;
    end if;
    if(NEW.intervention is null) then 
        begin
            set NEW.intervention = 0;
        end;
    end if;
    if(NEW.soft_delete is null) then 
        begin
            set NEW.soft_delete = 0;
        end;
    end if;
    if(NEW.nom != OLD.nom or NEW.prom != OLD.prom or NEW.adresse != OLD.adresse or COALESCE(NEW.pointeuse_id, 0) != COALESCE(OLD.pointeuse_id, 0)
        or COALESCE(NEW.pointage, 0) != COALESCE(OLD.pointage, 0) or COALESCE(NEW.pointeuse, 0) != COALESCE(OLD.pointeuse, 0) 
        or COALESCE(NEW.vigilance, 0) != COALESCE(OLD.vigilance, 0) or COALESCE(NEW.intervention, 0) != COALESCE(OLD.intervention, 0)
        or COALESCE(NEW.intervention_id, 0) != COALESCE(OLD.intervention_id, 0) or COALESCE(NEW.group_pointage_id, 0) != COALESCE(OLD.group_pointage_id, 0) 
        or COALESCE(NEW.soft_delete, 0) != COALESCE(OLD.soft_delete, 0) or COALESCE(NEW.group_id, 0) != COALESCE(OLD.group_id, 0)
        or COALESCE(NEW.horaire_pointage_id, 0) != COALESCE(OLD.horaire_pointage_id, 0) or COALESCE(NEW.total_hour, 0) != COALESCE(OLD.total_hour, 0)
        or COALESCE(NEW.nb_agent_night, 0) != COALESCE(OLD.nb_agent_night, 0) or COALESCE(NEW.nb_agent_day, 0) != COALESCE(OLD.nb_agent_day, 0)
        or COALESCE(NEW.secteur_id, 0) != COALESCE(OLD.secteur_id, 0)
    ) then
		begin
			set NEW.admin_updated_at = now();
        end;
	end if;
END
| DELIMITER ;