drop trigger IF EXISTS before_update_zone;

DELIMITER |
CREATE TRIGGER before_update_zone
BEFORE UPDATE
ON zonesites FOR EACH ROW
BEGIN
    if(NEW.NumZone != OLD.NumZone or NEW.nomZone != OLD.nomZone or NEW.idcapteur != OLD.idcapteur or NEW.idsite != OLD.idsite
		or NEW.idzone != OLD.idzone or COALESCE(NEW.soft_delete, 0) != COALESCE(OLD.soft_delete, 0)
	) then
		begin
			set NEW.admin_updated_at = now();
        end;
	end if;
END
| DELIMITER ;