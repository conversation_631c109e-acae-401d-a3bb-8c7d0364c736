unit uGPRS;

interface

Uses IdBaseComponent, IdComponent, IdCustomTCPServer, IdTCPServer, IdContext;

const
  CT16: Array [0..255] of Word =
             ($0000, $C0C1, $C181, $0140, $C301, $03C0, $0280, $C241, $C601, $06C0, $0780,
              $C741, $0500, $C5C1, $C481, $0440, $CC01, $0CC0, $0D80, $CD41, $0F00, $CFC1,
              $CE81, $0E40, $0A00, $CAC1, $CB81, $0B40, $C901, $09C0, $0880, $C841, $D801,
              $18C0, $1980, $D941, $1B00, $DBC1, $DA81, $1A40, $1E00, $DEC1, $DF81, $1F40,
              $DD01, $1DC0, $1C80, $DC41, $1400, $D4C1, $D581, $1540, $D701, $17C0, $1680,
              $D641, $D201, $12C0, $1380, $D341, $1100, $D1C1, $D081, $1040, $F001, $30C0,
              $3180, $F141, $3300, $F3C1, $F281, $3240, $3600, $F6C1, $F781, $3740, $F501,
              $35C0, $3480, $F441, $3C00, $FCC1, $FD81, $3D40, $FF01, $3FC0, $3E80, $FE41,
              $FA01, $3AC0, $3B80, $FB41, $3900, $F9C1, $F881, $3840, $2800, $E8C1, $E981,
              $2940, $EB01, $2BC0, $2A80, $EA41, $EE01, $2EC0, $2F80, $EF41, $2D00, $EDC1,
              $EC81, $2C40, $E401, $24C0, $2580, $E541, $2700, $E7C1, $E681, $2640, $2200,
              $E2C1, $E381, $2340, $E101, $21C0, $2080, $E041, $A001, $60C0, $6180, $A141,
              $6300, $A3C1, $A281, $6240, $6600, $A6C1, $A781, $6740, $A501, $65C0, $6480,
              $A441, $6C00, $ACC1, $AD81, $6D40, $AF01, $6FC0, $6E80, $AE41, $AA01, $6AC0,
              $6B80, $AB41, $6900, $A9C1, $A881, $6840, $7800, $B8C1, $B981, $7940, $BB01,
              $7BC0, $7A80, $BA41, $BE01, $7EC0, $7F80, $BF41, $7D00, $BDC1, $BC81, $7C40,
              $B401, $74C0, $7580, $B541, $7700, $B7C1, $B681, $7640, $7200, $B2C1, $B381,
              $7340, $B101, $71C0, $7080, $B041, $5000, $90C1, $9181, $5140, $9301, $53C0,
              $5280, $9241, $9601, $56C0, $5780, $9741, $5500, $95C1, $9481, $5440, $9C01,
              $5CC0, $5D80, $9D41, $5F00, $9FC1, $9E81, $5E40, $5A00, $9AC1, $9B81, $5B40,
              $9901, $59C0, $5880, $9841, $8801, $48C0, $4980, $8941, $4B00, $8BC1, $8A81,
              $4A40, $4E00, $8EC1, $8F81, $4F40, $8D01, $4DC0, $4C80, $8C41, $4400, $84C1,
              $8581, $4540, $8701, $47C0, $4680, $8641, $8201, $42C0, $4380, $8341, $4100,
              $81C1, $8081, $4040);

Type
  TFrame = Record
    LF, CR: Char;
    CRC, Length, LLL: String[4];
    CMD: String;
    Seq: String[4];
    R, L, Prom, Data: String;
  End;

  TData = Record
    Restore: Integer;
    Prom, Event, Partition, Zone: String;
    DateTime: TDateTime;
  End;

  TOnReceiveData = Procedure(AIPRemote: String; AData: TData) Of Object;
  TOnResultFrame = Procedure(AIPRemote, PROM: String; ADateTime: TDateTime) Of Object;
  TOnConnect = Procedure(AIPRemote: String; ADateTime: TDateTime) Of Object;
  TOnData = Procedure(AIPRemote, AData: String; ADateTime: TDateTime) Of Object;
  TOnSizeReceive = Procedure(AIPRemote, PROM: String; ASize: Integer; ADateTime: TDateTime) Of Object;

  TReceiveGPRS = Class
    Private
      FAckFrame, FNAckFrame, FHeartBeatFrame: TOnResultFrame;
      FConnect, FDisConnect: TOnConnect;
      FSizeReceive: TOnSizeReceive;
      FData: TOnData;
      FPort: Integer;
      FTCP: TIdTCPServer;
      FReceiveData: TOnReceiveData;
      function Crc16(lpBuffer: Pointer; dwSize: Cardinal; wSeed: Word): Word;
      Function ParseData(S: String): TFrame;
      Function PrepareACK(Prom: String; IdSeq: String): String;
      Function PrepareNACK(Prom: String; IdSeq: String): String;
      procedure TCPServerConnect(AContext: TIdContext);
      procedure TCPServerDisconnect(AContext: TIdContext);
      procedure TCPServerExecute(AContext: TIdContext);
      Function FGetPort: Integer;
      Procedure FSetPort(AValue: Integer);
      Function CheckCRC(S: String; A: TFrame): Boolean;
    Public
      Constructor Create;
      Destructor Destroy; Override;
    Published
      Property OnConnect: TOnConnect Read FConnect Write FConnect;
      Property OnDisConnect: TOnConnect Read FDisConnect Write FDisConnect;
      Property OnReceiveData: TOnReceiveData Read FReceiveData Write FReceiveData;
      Property OnAckFrame: TOnResultFrame Read FAckFrame Write FAckFrame;
      Property OnNAckFrame: TOnResultFrame Read FNAckFrame Write FNAckFrame;
      Property OnHeartBeat: TOnResultFrame Read FHeartBeatFrame Write FHeartBeatFrame;
      Property OnData: TOnData Read FData Write FData;
      Property OnSizeReceive: TOnSizeReceive Read FSizeReceive Write FSizeReceive;
      Property Port: Integer Read FGetPort Write FSetPort;
      Procedure StartListen;
      Procedure StopListen;
  End;

implementation

Uses strUtils, sysUtils, Classes, Threading;

Procedure TReceiveGPRS.StartListen;
Begin
  FTCP.Active:= True;
End;

Procedure TReceiveGPRS.StopListen;
Begin
  If FTCP.Active Then Begin
     TTask.Run(Procedure
     Begin
        While FTCP.Active Do Begin
           TRY
              FTCP.Active:= False;
           EXCEPT

           END;
           Sleep(100);
        End;
     End);
  End;
End;

Constructor TReceiveGPRS.Create;
Begin
  FTCP:= TIdTCPServer.Create(NIL);
  FTCP.OnConnect:= TCPServerConnect;
  FTCP.OnDisconnect:= TCPServerDisConnect;
  FTCP.OnExecute:= TCPServerExecute;
End;
Destructor TReceiveGPRS.Destroy;
Begin
  StopListen;
  FTCP.Free;
  Inherited Destroy;
End;

procedure TReceiveGPRS.TCPServerConnect(AContext: TIdContext);
begin
  TThread.Synchronize(NIL, Procedure
  Begin
     If Assigned(FConnect) Then FConnect(AContext.Binding.PeerIP, Now);
  End);
end;

procedure TReceiveGPRS.TCPServerDisconnect(AContext: TIdContext);
begin
  TThread.Synchronize(NIL, Procedure
  Begin
     If Assigned(FDisConnect) Then FDisConnect(AContext.Binding.PeerIP, Now);
  End);
end;

procedure TReceiveGPRS.TCPServerExecute(AContext: TIdContext);
Var S, D, sData: String; A: TFrame; B: TData; Crc: Boolean;
begin
  If NOT AContext.Connection.IOHandler.InputBufferIsEmpty then Begin
     S:= AContext.Connection.IOHandler.InputBuffer.ExtractToString;
     sData:= S;

     If (S <> '') then Begin
       TThread.Synchronize(NIL, Procedure
       Begin
          If Assigned(FData) Then FData(AContext.Binding.PeerIP, S, Now);
       End);
       A:= ParseData(S);

       CRC:= CheckCRC(S, A);
       If CRC Then Begin
          S:= PrepareACK(A.Prom, A.Seq);
          TThread.Synchronize(NIL, Procedure
          Begin
             If Assigned(FAckFrame) Then FAckFrame(AContext.Binding.PeerIP, A.Prom, Now);
          End);
       End Else Begin
          S:= PrepareNACK(A.Prom, A.Seq);
          TThread.Synchronize(NIL, Procedure
          Begin
             If Assigned(FNAckFrame) Then FNAckFrame(AContext.Binding.PeerIP, A.Prom, Now);
          End);
       End;
       AContext.Connection.IOHandler.Write(S);

       If Assigned(FSizeReceive) Then FSizeReceive(AContext.Binding.PeerIP, A.Prom, ByteLength(sData), Now);

       If (A.Data <> '') and AnsiContainsStr(A.CMD, 'ADM-CID') And CRC Then Begin
         D:= AnsiReplaceStr(A.Data, '#'+A.Prom, '');
         D:= AnsiReplaceStr(D, '|', '');
         D:= AnsiReplaceStr(D, ' ', '');;
         B.Restore:= strTOint(D[1]);
         B.Prom:= A.Prom;
         B.Event:= Copy(D, 2, 3);
         B.Partition:= Copy(D, 5, 2);
         B.Zone:= Copy(D, 7, 3);
         B.DateTime:= Now;
         TThread.Synchronize(NIL, Procedure
         Begin
            If Assigned(FReceiveData) Then FReceiveData(AContext.Binding.PeerIP, B);
         End);
       End Else If AnsiContainsStr(A.CMD, 'NULL') And CRC Then Begin
         TThread.Synchronize(NIL, Procedure
         Begin
            If Assigned(FHeartBeatFrame) Then FHeartBeatFrame(AContext.Binding.PeerIP, A.Prom, Now);
         End);
       End;
     End;
  End;
  Sleep(10);
end;

Function HexToInt(Value: string): Integer;
begin
  result:= StrToInt('$'+Value);
end;

Function TReceiveGPRS.CheckCRC(S: String; A: TFrame): Boolean;
Var B, E, F: String; L, i, C: Integer; CRC: Word;
Begin
  Result:= False;
  B:= S;
  B:= AnsiReplaceStr(B, A.LF, '');
  B:= AnsiReplaceStr(B, A.CR, '');
  B:= AnsiReplaceStr(B, A.CRC, '');
  B:= AnsiReplaceStr(B, A.Length, '');
  L:= Length(B);
  If A.Length[1] = '0' Then E:= Copy(A.Length, 2, Length(A.Length) - 1);
  If E[1] = '0' Then E:= Copy(E, 2, Length(E) - 1);
  If E[1] = '0' Then E:= Copy(E, 2, Length(E) - 1);
  C:= HexToInt(E);

  CRC:= Crc16(Pointer(B), L, 0);
  F:= intTOHex(CRC, 0);
  If (C = L) Then Result:= True;
End;

Function TReceiveGPRS.PrepareNACK(Prom: String; IdSeq: String): String;
Var S, Ls, Ss: String; L: Integer; CRC: Word;
Begin
  S:= '"NACK"'+IdSeq+'R0L0#'+PROM+'[]';
  L:= Length(S);
  CRC:= Crc16(Pointer(S), L, 0);
  Ls:= intToHex(L, 0);
  CASE Length(Ls) OF
    1: Ls:= '000'+Ls;
    2: Ls:= '00'+Ls;
    3: Ls:= '0'+Ls;
  END;
  Result:= #$A+intTOHex(CRC, 0) + Ls + S+#$D;
End;

Function TReceiveGPRS.PrepareACK(Prom: String; IdSeq: String): String;
Var S, Ls, Ss: String; L: Integer; CRC: Word;
Begin
  S:= '"ACK"'+IdSeq+'R0L0#'+PROM+'[]';
  L:= Length(S);
  CRC:= Crc16(Pointer(S), L, 0);
  Ls:= intToHex(L, 0);
  CASE Length(Ls) OF
    1: Ls:= '000'+Ls;
    2: Ls:= '00'+Ls;
    3: Ls:= '0'+Ls;
  END;
  Result:= #$A+intTOHex(CRC, 0) + Ls + S+#$D;
End;

Function TReceiveGPRS.ParseData(S: String): TFrame;
Var A: TFrame; Ss, B: String; Debut, Long, i: Integer;
Begin
  Ss:= S;

  A.LF:= Ss[1];
  Long:= Length(S)- Length(A.LF);
  Ss:= Copy(Ss, 2, Long);

  A.CR:= Ss[Length(Ss)];
  Long:= Length(Ss)- Length(A.CR);
  Ss:= Copy(Ss, 1, Long);

  A.CRC:= Copy(Ss, 1, 4);
  Long:= Length(Ss)- Length(A.CRC);
  Ss:= Copy(Ss, Length(A.CRC)+1, Long);

  A.Length:= Copy(Ss, 1, 4);
  Long:= Length(Ss)- Length(A.Length);
  Ss:= Copy(Ss, Length(A.Length)+1, Long);

  If AnsiContainsStr(Ss, 'LLL') Then Begin
     A.LLL:= Copy(Ss, 1, 4);
     Long:= Length(Ss)- Length(A.LLL);
     Ss:= Copy(Ss, Length(A.LLL)+1, Long);
  End;

  A.CMD:= '';
  For i:= 1 to Length(Ss) Do Begin
    If Ss[i] = '"' Then Begin
      If (i <> 1) Then Begin
         Break;
      End;
    End Else Begin
      A.CMD:= A.CMD + Ss[i];
    End;
    Sleep(1);
  End;
  Long:= Length(Ss)- Length(A.CMD);
  Ss:= Copy(Ss, Length(A.CMD)+3, Long);

  A.Seq:= Copy(Ss, 1, 4);
  Long:= Length(Ss)- Length(A.Seq);
  Ss:= Copy(Ss, Length(A.Seq)+1, Long);

  A.R:= Copy(Ss, 1, Pos('L', Ss, 1)-1);
  Long:= Length(Ss)- Length(A.R);
  Ss:= Copy(Ss, Length(A.R)+1, Long);

  A.L:= Copy(Ss, 1, Pos('#', Ss, 1)-1);
  Long:= Length(Ss)- Length(A.L);
  Ss:= Copy(Ss, Length(A.L)+1, Long);

  A.Prom:= Copy(Ss, 2, Pos('[', Ss, 1)-2);
  Long:= Length(Ss)- Length(A.Prom);
  Ss:= Copy(Ss, Length(A.Prom)+2, Long);

  A.Data:= Copy(Ss, 2, Pos(']', Ss, 1)-2);
  Long:= Length(Ss)- Length(A.Data);
  Ss:= Copy(Ss, Length(A.Data)+2, Long);
  Result:= A;
End;

function TReceiveGPRS.Crc16(lpBuffer: Pointer; dwSize: Cardinal; wSeed: Word): Word;
var  lpszBuffer: PChar; dwIndex: Integer;
begin
  lpszBuffer:= lpBuffer; // Cast the data buffer as pchar
  result:= wSeed; // Set the starting seed
  // Calculate the 16 bit crc of the data
  for dwIndex:= 0 to Pred(dwSize) do begin
     result:= Hi(result) xor CT16[Ord(lpszBuffer^)] xor Lo(result); // Update the crc value
     Inc(lpszBuffer); // Push next
     Sleep(1);
  end;
end;

Function TReceiveGPRS.FGetPort: Integer;
Begin
  FPort:= FTCP.DefaultPort;
  Result:= FTCP.DefaultPort;
End;

Procedure TReceiveGPRS.FSetPort(AValue: Integer);
Begin
  FPort:= AValue;
  FTCP.DefaultPort:= AValue;
End;

end.
